/**
 * Regulatory Compliance Routes Tests
 * 
 * This file contains tests for the regulatory compliance routes.
 */

const request = require('supertest');
const express = require('express');
const regulatoryComplianceRoutes = require('../../routes/regulatoryComplianceRoutes');
const regulatoryComplianceController = require('../../controllers/regulatoryComplianceController');

// Mock the controller methods
jest.mock('../../controllers/regulatoryComplianceController');

// Mock the middleware
jest.mock('../../middleware/authMiddleware', () => ({
  authenticate: (req, res, next) => next(),
  hasPermission: () => (req, res, next) => next()
}));

jest.mock('../../middleware/validationMiddleware', () => ({
  validateRequest: () => (req, res, next) => next()
}));

// Create Express app for testing
const app = express();
app.use(express.json());
app.use('/api/compliance', regulatoryComplianceRoutes);

describe('Regulatory Compliance Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/compliance/frameworks', () => {
    it('should call getAllFrameworks controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.getAllFrameworks.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: [{ name: 'GDPR' }]
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/frameworks');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.getAllFrameworks).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('GET /api/compliance/frameworks/:id', () => {
    it('should call getFrameworkById controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.getFrameworkById.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: { name: 'GDPR', _id: req.params.id }
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/frameworks/framework-1');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.getFrameworkById).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('GET /api/compliance/frameworks/code/:code', () => {
    it('should call getFrameworkByCode controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.getFrameworkByCode.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: { name: 'GDPR', code: req.params.code }
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/frameworks/code/GDPR');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.getFrameworkByCode).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('GET /api/compliance/frameworks/:frameworkId/requirements', () => {
    it('should call getRequirementsByFramework controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.getRequirementsByFramework.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: [{ title: 'Requirement 1', framework: req.params.frameworkId }]
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/frameworks/framework-1/requirements');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.getRequirementsByFramework).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('GET /api/compliance/requirements/:id', () => {
    it('should call getRequirementById controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.getRequirementById.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: { title: 'Requirement 1', _id: req.params.id }
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/requirements/requirement-1');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.getRequirementById).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('GET /api/compliance/status/:entityType/:entityId/:frameworkId', () => {
    it('should call getComplianceStatus controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.getComplianceStatus.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: {
            entityType: req.params.entityType,
            entityId: req.params.entityId,
            framework: req.params.frameworkId
          }
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/status/organization/org-123/framework-1');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.getComplianceStatus).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('PATCH /api/compliance/status/:statusId/requirements/:requirementId', () => {
    it('should call updateRequirementStatus controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.updateRequirementStatus.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: {
            _id: req.params.statusId,
            requirementStatuses: [
              {
                requirement: req.params.requirementId,
                status: req.body.status
              }
            ]
          }
        });
      });
      
      // Make request
      const response = await request(app)
        .patch('/api/compliance/status/status-1/requirements/requirement-1')
        .send({
          status: 'compliant',
          notes: 'Implemented'
        });
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.updateRequirementStatus).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('GET /api/compliance/reports/:entityType/:entityId/:frameworkId', () => {
    it('should call generateComplianceReport controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.generateComplianceReport.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: {
            entityType: req.params.entityType,
            entityId: req.params.entityId,
            framework: {
              id: req.params.frameworkId
            }
          }
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/reports/organization/org-123/framework-1');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.generateComplianceReport).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('GET /api/compliance/mapping/:sourceFrameworkId/:targetFrameworkId', () => {
    it('should call mapRequirementsBetweenFrameworks controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.mapRequirementsBetweenFrameworks.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: {
            sourceFramework: {
              id: req.params.sourceFrameworkId
            },
            targetFramework: {
              id: req.params.targetFrameworkId
            }
          }
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/mapping/framework-1/framework-2');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.mapRequirementsBetweenFrameworks).toHaveBeenCalledTimes(1);
    });
  });
  
  describe('GET /api/compliance/updates', () => {
    it('should call getRegulatoryUpdates controller method', async () => {
      // Mock controller response
      regulatoryComplianceController.getRegulatoryUpdates.mockImplementation((req, res) => {
        res.status(200).json({
          success: true,
          data: [{ title: 'Update 1' }]
        });
      });
      
      // Make request
      const response = await request(app).get('/api/compliance/updates');
      
      // Assertions
      expect(response.status).toBe(200);
      expect(regulatoryComplianceController.getRegulatoryUpdates).toHaveBeenCalledTimes(1);
    });
  });
});
