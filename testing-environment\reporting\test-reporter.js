/**
 * Test Reporter for NovaConnect Universal API Connector
 * 
 * This module provides comprehensive test reporting capabilities.
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

class TestReporter {
  /**
   * Create a new TestReporter
   * 
   * @param {Object} options - Reporter options
   */
  constructor(options = {}) {
    this.options = {
      outputDir: options.outputDir || 'reports',
      badgeDir: options.badgeDir || 'badges',
      slackWebhook: options.slackWebhook || process.env.SLACK_WEBHOOK_URL,
      teamsWebhook: options.teamsWebhook || process.env.TEAMS_WEBHOOK_URL,
      projectName: options.projectName || 'NovaConnect'
    };
    
    // Create output directories
    if (!fs.existsSync(this.options.outputDir)) {
      fs.mkdirSync(this.options.outputDir, { recursive: true });
    }
    
    if (!fs.existsSync(this.options.badgeDir)) {
      fs.mkdirSync(this.options.badgeDir, { recursive: true });
    }
  }

  /**
   * Generate a badge
   * 
   * @param {string} type - Badge type
   * @param {string} value - Badge value
   * @param {string} color - Badge color
   * @returns {string} - Path to the badge file
   */
  generateBadge(type, value, color) {
    const badgeData = {
      schemaVersion: 1,
      label: type,
      message: value,
      color: color
    };
    
    const badgePath = path.join(this.options.badgeDir, `${type.toLowerCase()}-badge.json`);
    fs.writeFileSync(badgePath, JSON.stringify(badgeData, null, 2));
    
    return badgePath;
  }

  /**
   * Save test results
   * 
   * @param {Object} results - Test results
   * @returns {string} - Path to the report file
   */
  async saveTestResults(results) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(this.options.outputDir, `test-report-${timestamp}.json`);
    
    const report = {
      timestamp,
      projectName: this.options.projectName,
      summary: {
        total: results.total || 0,
        passed: results.passed || 0,
        failed: results.failed || 0,
        skipped: results.skipped || 0,
        coverage: results.coverage || {}
      },
      details: results.details || []
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Generate badges
    if (results.coverage && results.coverage.lines) {
      this.generateBadge('coverage', `${results.coverage.lines}%`, this.getCoverageColor(results.coverage.lines));
    }
    
    const passRate = results.total ? Math.round((results.passed / results.total) * 100) : 0;
    this.generateBadge('tests', `${passRate}% passing`, this.getPassRateColor(passRate));
    
    // Send notifications if configured and there are failures
    if (results.failed > 0) {
      await this.sendNotifications(report);
    }
    
    return reportPath;
  }

  /**
   * Get color for coverage badge
   * 
   * @param {number} coverage - Coverage percentage
   * @returns {string} - Badge color
   */
  getCoverageColor(coverage) {
    if (coverage >= 90) return 'brightgreen';
    if (coverage >= 80) return 'green';
    if (coverage >= 70) return 'yellowgreen';
    if (coverage >= 60) return 'yellow';
    return 'red';
  }

  /**
   * Get color for pass rate badge
   * 
   * @param {number} passRate - Pass rate percentage
   * @returns {string} - Badge color
   */
  getPassRateColor(passRate) {
    if (passRate >= 95) return 'brightgreen';
    if (passRate >= 90) return 'green';
    if (passRate >= 80) return 'yellowgreen';
    if (passRate >= 70) return 'yellow';
    return 'red';
  }

  /**
   * Send notifications about test results
   * 
   * @param {Object} report - Test report
   */
  async sendNotifications(report) {
    const failureMessage = `⚠️ Test failures in ${report.projectName}: ${report.summary.failed} of ${report.summary.total} tests failed.`;
    
    // Send to Slack if configured
    if (this.options.slackWebhook) {
      try {
        await axios.post(this.options.slackWebhook, {
          text: failureMessage,
          attachments: [{
            color: 'danger',
            fields: [
              {
                title: 'Project',
                value: report.projectName,
                short: true
              },
              {
                title: 'Time',
                value: new Date(report.timestamp).toLocaleString(),
                short: true
              },
              {
                title: 'Failed Tests',
                value: report.summary.failed,
                short: true
              },
              {
                title: 'Coverage',
                value: report.summary.coverage.lines ? `${report.summary.coverage.lines}%` : 'N/A',
                short: true
              }
            ]
          }]
        });
        
        console.log('Slack notification sent');
      } catch (error) {
        console.error('Failed to send Slack notification:', error.message);
      }
    }
    
    // Send to Teams if configured
    if (this.options.teamsWebhook) {
      try {
        await axios.post(this.options.teamsWebhook, {
          "@type": "MessageCard",
          "@context": "http://schema.org/extensions",
          "themeColor": "FF0000",
          "summary": failureMessage,
          "sections": [{
            "activityTitle": failureMessage,
            "facts": [
              {
                "name": "Project",
                "value": report.projectName
              },
              {
                "name": "Time",
                "value": new Date(report.timestamp).toLocaleString()
              },
              {
                "name": "Failed Tests",
                "value": report.summary.failed.toString()
              },
              {
                "name": "Coverage",
                "value": report.summary.coverage.lines ? `${report.summary.coverage.lines}%` : 'N/A'
              }
            ]
          }]
        });
        
        console.log('Teams notification sent');
      } catch (error) {
        console.error('Failed to send Teams notification:', error.message);
      }
    }
  }

  /**
   * Generate a test summary report
   * 
   * @param {Object} results - Test results
   * @returns {string} - HTML report
   */
  generateHtmlReport(results) {
    const passRate = results.total ? Math.round((results.passed / results.total) * 100) : 0;
    const coverage = results.coverage && results.coverage.lines ? results.coverage.lines : 'N/A';
    
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${this.options.projectName} Test Report</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
          }
          h1, h2, h3 {
            color: #0066cc;
          }
          .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
          }
          .summary-card {
            background: #f5f5f5;
            border-radius: 5px;
            padding: 15px;
            width: 23%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .summary-card h3 {
            margin-top: 0;
          }
          .summary-card.passed { background: #e6ffe6; }
          .summary-card.failed { background: #ffe6e6; }
          .summary-card.skipped { background: #e6e6ff; }
          .summary-card.coverage { background: #e6f7ff; }
          
          .test-details {
            margin-top: 30px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
          }
          th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
          }
          th {
            background: #f2f2f2;
          }
          tr.failed {
            background: #ffe6e6;
          }
          tr.passed {
            background: #e6ffe6;
          }
          tr.skipped {
            background: #e6e6ff;
          }
          .timestamp {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 20px;
          }
        </style>
      </head>
      <body>
        <h1>${this.options.projectName} Test Report</h1>
        <div class="timestamp">Generated on: ${new Date().toLocaleString()}</div>
        
        <div class="summary">
          <div class="summary-card passed">
            <h3>Passed</h3>
            <div>${results.passed} / ${results.total} (${passRate}%)</div>
          </div>
          <div class="summary-card failed">
            <h3>Failed</h3>
            <div>${results.failed}</div>
          </div>
          <div class="summary-card skipped">
            <h3>Skipped</h3>
            <div>${results.skipped || 0}</div>
          </div>
          <div class="summary-card coverage">
            <h3>Coverage</h3>
            <div>${coverage}%</div>
          </div>
        </div>
        
        <div class="test-details">
          <h2>Test Details</h2>
          <table>
            <thead>
              <tr>
                <th>Test</th>
                <th>Status</th>
                <th>Duration</th>
                <th>Details</th>
              </tr>
            </thead>
            <tbody>
              ${(results.details || []).map(test => `
                <tr class="${test.status.toLowerCase()}">
                  <td>${test.name}</td>
                  <td>${test.status}</td>
                  <td>${test.duration ? `${test.duration}ms` : 'N/A'}</td>
                  <td>${test.message || ''}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </body>
      </html>
    `;
    
    const reportPath = path.join(this.options.outputDir, 'test-report.html');
    fs.writeFileSync(reportPath, html);
    
    return reportPath;
  }

  /**
   * Combine multiple test reports
   * 
   * @param {Array} reportPaths - Paths to report files
   * @returns {Object} - Combined report
   */
  combineReports(reportPaths) {
    const combinedReport = {
      timestamp: new Date().toISOString(),
      projectName: this.options.projectName,
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        coverage: {}
      },
      details: [],
      reports: []
    };
    
    for (const reportPath of reportPaths) {
      try {
        const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
        
        // Add to summary
        combinedReport.summary.total += report.summary.total;
        combinedReport.summary.passed += report.summary.passed;
        combinedReport.summary.failed += report.summary.failed;
        combinedReport.summary.skipped += (report.summary.skipped || 0);
        
        // Add details
        if (report.details) {
          combinedReport.details.push(...report.details);
        }
        
        // Add report metadata
        combinedReport.reports.push({
          path: reportPath,
          timestamp: report.timestamp,
          summary: report.summary
        });
      } catch (error) {
        console.error(`Error reading report ${reportPath}:`, error.message);
      }
    }
    
    // Calculate average coverage
    const coverageReports = combinedReport.reports.filter(r => 
      r.summary.coverage && r.summary.coverage.lines);
    
    if (coverageReports.length > 0) {
      const totalCoverage = coverageReports.reduce((sum, r) => 
        sum + r.summary.coverage.lines, 0);
      
      combinedReport.summary.coverage.lines = Math.round(totalCoverage / coverageReports.length);
    }
    
    return combinedReport;
  }
}

module.exports = TestReporter;
