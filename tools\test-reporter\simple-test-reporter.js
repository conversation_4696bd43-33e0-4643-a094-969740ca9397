/**
 * NovaFuse Simple Test Reporter
 * 
 * This module generates HTML reports from Jest test results.
 */

const fs = require('fs');
const path = require('path');

/**
 * Generate an HTML report from Jest test results
 * 
 * @param {Object} results - Jest test results
 * @param {string} outputPath - Path to save the HTML report
 */
function generateHtmlReport(results, outputPath) {
  // Create the output directory if it doesn't exist
  const outputDir = path.dirname(outputPath);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Calculate summary statistics
  const totalTests = results.numTotalTests;
  const passedTests = results.numPassedTests;
  const failedTests = results.numFailedTests;
  const pendingTests = results.numPendingTests;
  const passRate = Math.round((passedTests / totalTests) * 100);
  
  // Generate the HTML content
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #0A84FF;
      color: white;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .summary-item {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      flex: 1;
      margin-right: 10px;
      text-align: center;
    }
    .summary-item:last-child {
      margin-right: 0;
    }
    .summary-item.passed {
      background-color: #d4edda;
      color: #155724;
    }
    .summary-item.failed {
      background-color: #f8d7da;
      color: #721c24;
    }
    .summary-item.pending {
      background-color: #fff3cd;
      color: #856404;
    }
    .summary-item.total {
      background-color: #e2e3e5;
      color: #383d41;
    }
    .summary-number {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .test-suite {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .test-suite-header {
      padding: 10px 15px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .test-suite-title {
      margin: 0;
      font-size: 18px;
    }
    .test-suite-status {
      font-weight: bold;
    }
    .test-suite-status.passed {
      color: #28a745;
    }
    .test-suite-status.failed {
      color: #dc3545;
    }
    .test-suite-body {
      padding: 15px;
    }
    .test-case {
      padding: 8px 15px;
      border-bottom: 1px solid #eee;
    }
    .test-case:last-child {
      border-bottom: none;
    }
    .test-case.passed {
      border-left: 4px solid #28a745;
    }
    .test-case.failed {
      border-left: 4px solid #dc3545;
      background-color: #f8f9fa;
    }
    .test-case.pending {
      border-left: 4px solid #ffc107;
      color: #6c757d;
    }
    .test-case-title {
      margin: 0;
      font-size: 16px;
    }
    .test-case-error {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8d7da;
      border-radius: 3px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    .test-duration {
      color: #6c757d;
      font-size: 14px;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6c757d;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <header>
    <h1>NovaFuse Test Report</h1>
    <p>Generated on ${new Date().toLocaleString()}</p>
  </header>
  
  <div class="summary">
    <div class="summary-item passed">
      <h3>Passed</h3>
      <div class="summary-number">${passedTests}</div>
      <div>${Math.round((passedTests / totalTests) * 100)}%</div>
    </div>
    <div class="summary-item failed">
      <h3>Failed</h3>
      <div class="summary-number">${failedTests}</div>
      <div>${Math.round((failedTests / totalTests) * 100)}%</div>
    </div>
    <div class="summary-item pending">
      <h3>Pending</h3>
      <div class="summary-number">${pendingTests}</div>
      <div>${Math.round((pendingTests / totalTests) * 100)}%</div>
    </div>
    <div class="summary-item total">
      <h3>Total</h3>
      <div class="summary-number">${totalTests}</div>
      <div>100%</div>
    </div>
  </div>
  
  <h2>Test Suites</h2>
  
  ${results.testResults.map(testSuite => {
    const suitePassed = testSuite.status === 'passed';
    const suiteStatus = suitePassed ? 'passed' : 'failed';
    const suiteStatusText = suitePassed ? 'Passed' : 'Failed';
    
    return `
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">${path.basename(testSuite.testFilePath)}</h3>
      <span class="test-suite-status ${suiteStatus}">${suiteStatusText}</span>
    </div>
    <div class="test-suite-body">
      ${testSuite.testResults.map(testCase => {
        const testPassed = testCase.status === 'passed';
        const testFailed = testCase.status === 'failed';
        const testPending = testCase.status === 'pending';
        const testStatus = testPassed ? 'passed' : (testPending ? 'pending' : 'failed');
        
        return `
      <div class="test-case ${testStatus}">
        <h4 class="test-case-title">${testCase.title}</h4>
        <div class="test-duration">Duration: ${testCase.duration}ms</div>
        ${testFailed ? `
        <div class="test-case-error">${testCase.failureMessages.join('\n')}</div>
        ` : ''}
      </div>
        `;
      }).join('')}
    </div>
  </div>
    `;
  }).join('')}
  
  <footer>
    <p>NovaFuse Universal Platform &copy; ${new Date().getFullYear()}</p>
  </footer>
</body>
</html>
  `;
  
  // Write the HTML report to the output file
  fs.writeFileSync(outputPath, html);
  
  console.log(`Test report generated at: ${outputPath}`);
}

/**
 * Generate a JSON report from Jest test results
 * 
 * @param {Object} results - Jest test results
 * @param {string} outputPath - Path to save the JSON report
 */
function generateJsonReport(results, outputPath) {
  // Create the output directory if it doesn't exist
  const outputDir = path.dirname(outputPath);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Write the JSON report to the output file
  fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
  
  console.log(`JSON report generated at: ${outputPath}`);
}

module.exports = {
  generateHtmlReport,
  generateJsonReport
};
