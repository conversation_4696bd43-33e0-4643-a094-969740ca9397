/**
 * Unit tests for the Connector API
 */

const { expect } = require('chai');
const sinon = require('sinon');
const supertest = require('supertest');

// Import the modules to test
const connectorApi = require('../../api/connector-api');
const connectorRegistry = require('../../registry/connector-registry');
const authenticationManager = require('../../auth/authentication-manager');
const connectorExecutor = require('../../executor/connector-executor');

describe('Connector API', () => {
  let app;
  let request;
  let sandbox;

  beforeEach(() => {
    // Create a sinon sandbox for stubs
    sandbox = sinon.createSandbox();

    // Stub the dependencies
    sandbox.stub(connectorRegistry, 'initialize').resolves();
    sandbox.stub(authenticationManager, 'initialize').resolves();
    sandbox.stub(connectorExecutor, 'initialize').resolves();

    // Get the Express app
    app = connectorApi.app;
    request = supertest(app);
  });

  afterEach(() => {
    // Restore the stubs
    sandbox.restore();
  });

  describe('initialize()', () => {
    it('should initialize dependencies and start the server', async () => {
      // Stub the app.listen method
      const listenStub = sandbox.stub(app, 'listen').callsFake((port, callback) => {
        callback();
        return { close: () => {} };
      });

      // Call the initialize method
      const result = await connectorApi.initialize();

      // Verify the result
      expect(result).to.be.true;

      // Verify the dependencies were initialized
      expect(connectorRegistry.initialize.calledOnce).to.be.true;
      expect(authenticationManager.initialize.calledOnce).to.be.true;
      expect(connectorExecutor.initialize.calledOnce).to.be.true;

      // Verify the server was started
      expect(listenStub.calledOnce).to.be.true;
    });

    it('should handle initialization errors', async () => {
      // Stub the connectorRegistry.initialize method to throw an error
      connectorRegistry.initialize.rejects(new Error('Initialization error'));

      try {
        // Call the initialize method
        await connectorApi.initialize();
        // If we get here, the test should fail
        expect.fail('Expected initialize to throw an error');
      } catch (error) {
        // Verify the error
        expect(error.message).to.equal('Initialization error');
      }
    });
  });

  describe('GET /health', () => {
    it('should return a 200 status code', async () => {
      // Make a request to the health endpoint
      const response = await request.get('/health');

      // Verify the response
      expect(response.status).to.equal(200);
      expect(response.body).to.deep.equal({ status: 'ok' });
    });
  });

  describe('GET /connectors', () => {
    it('should return a list of connectors', async () => {
      // Stub the connectorRegistry.getAllConnectors method
      const connectors = [
        {
          id: '1',
          name: 'Test Connector',
          type: 'http',
          description: 'Test connector for unit tests',
          status: 'active',
          authentication: {
            fields: {
              apiKey: {
                type: 'string',
                label: 'API Key',
                required: true,
                sensitive: true,
                default: 'test-api-key'
              }
            }
          }
        }
      ];
      sandbox.stub(connectorRegistry, 'getAllConnectors').returns(connectors);

      // Make a request to the connectors endpoint
      const response = await request.get('/connectors');

      // Verify the response
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.lengthOf(1);
      expect(response.body[0].id).to.equal('1');
      expect(response.body[0].name).to.equal('Test Connector');
      expect(response.body[0].authentication.fields.apiKey.default).to.be.undefined;
    });

    it('should filter connectors by category', async () => {
      // Stub the connectorRegistry.getConnectorsByCategory method
      const connectors = [
        {
          id: '1',
          name: 'Test Connector',
          type: 'http',
          category: 'test',
          description: 'Test connector for unit tests',
          status: 'active',
          authentication: {
            fields: {}
          }
        }
      ];
      sandbox.stub(connectorRegistry, 'getConnectorsByCategory').returns(connectors);

      // Make a request to the connectors endpoint with a category filter
      const response = await request.get('/connectors?category=test');

      // Verify the response
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.lengthOf(1);
      expect(response.body[0].id).to.equal('1');
      expect(response.body[0].category).to.equal('test');
    });

    it('should search connectors by query', async () => {
      // Stub the connectorRegistry.searchConnectors method
      const connectors = [
        {
          id: '1',
          name: 'Test Connector',
          type: 'http',
          description: 'Test connector for unit tests',
          status: 'active',
          authentication: {
            fields: {}
          }
        }
      ];
      sandbox.stub(connectorRegistry, 'searchConnectors').returns(connectors);

      // Make a request to the connectors endpoint with a search query
      const response = await request.get('/connectors?query=test');

      // Verify the response
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.lengthOf(1);
      expect(response.body[0].id).to.equal('1');
      expect(response.body[0].name).to.equal('Test Connector');
    });

    it('should handle errors', async () => {
      // Stub the connectorRegistry.getAllConnectors method to throw an error
      sandbox.stub(connectorRegistry, 'getAllConnectors').throws(new Error('Test error'));

      // Make a request to the connectors endpoint
      const response = await request.get('/connectors');

      // Verify the response
      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('error');
      expect(response.body.error).to.equal('Test error');
    });
  });
});
