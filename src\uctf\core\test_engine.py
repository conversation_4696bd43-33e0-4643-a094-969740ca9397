"""
Test Engine for the Universal Compliance Testing Framework.

This module provides the main engine for executing compliance tests.
"""

import logging
import uuid
import datetime
from typing import Dict, List, Any, Optional, Callable

from .test_manager import TestManager
from .result_manager import ResultManager
from .report_manager import ReportManager

# Import shared components
from shared.models.test_result import TestResult
from shared.events.event_bus import event_bus

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestEngine:
    """
    Main engine for executing compliance tests.

    This class orchestrates the execution of compliance tests, manages their results,
    and generates reports.
    """

    def __init__(self):
        """Initialize the Test Engine."""
        logger.info("Initializing Test Engine")

        # Initialize the test manager
        self.test_manager = TestManager()

        # Initialize the result manager
        self.result_manager = ResultManager()

        # Initialize the report manager
        self.report_manager = ReportManager()

        # Dictionary to store test run information
        self.test_runs: Dict[str, Dict[str, Any]] = {}

        # Dictionary to store registered test event handlers
        self.event_handlers: Dict[str, List[Callable]] = {}

        logger.info("Test Engine initialized")

    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        Register a handler for a specific event type.

        Args:
            event_type: The type of event
            handler: The handler function
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []

        self.event_handlers[event_type].append(handler)
        logger.info(f"Registered event handler for event type: {event_type}")

    def unregister_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        Unregister a handler for a specific event type.

        Args:
            event_type: The type of event
            handler: The handler function
        """
        if event_type in self.event_handlers and handler in self.event_handlers[event_type]:
            self.event_handlers[event_type].remove(handler)
            logger.info(f"Unregistered event handler for event type: {event_type}")

    def run_test(self,
                test_id: str,
                parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run a specific test.

        Args:
            test_id: The ID of the test
            parameters: Parameters for the test

        Returns:
            The test result

        Raises:
            ValueError: If the test does not exist
        """
        logger.info(f"Running test: {test_id}")

        # Generate a unique run ID
        run_id = str(uuid.uuid4())

        # Create the test run object
        test_run = {
            'id': run_id,
            'test_id': test_id,
            'parameters': parameters or {},
            'status': 'running',
            'result': None,
            'start_time': self._get_current_timestamp(),
            'end_time': None
        }

        # Store the test run
        self.test_runs[run_id] = test_run

        # Trigger test started event
        self._trigger_event('test_started', test_run)

        try:
            # Execute the test
            result = self.test_manager.execute_test(test_id, parameters or {})

            # Update the test run
            test_run['result'] = result
            test_run['status'] = 'completed'
            test_run['end_time'] = self._get_current_timestamp()

            # Store the result
            self.result_manager.store_result(run_id, result)

            # Trigger test completed event
            self._trigger_event('test_completed', test_run)

            logger.info(f"Test completed: {test_id}, Run ID: {run_id}")

            return test_run

        except Exception as e:
            # Update the test run
            test_run['status'] = 'failed'
            test_run['error'] = str(e)
            test_run['end_time'] = self._get_current_timestamp()

            # Trigger test failed event
            self._trigger_event('test_failed', test_run)

            logger.error(f"Test failed: {test_id}, Run ID: {run_id}, Error: {e}")

            raise

    def run_test_suite(self,
                      suite_id: str,
                      parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run a test suite.

        Args:
            suite_id: The ID of the test suite
            parameters: Parameters for the test suite

        Returns:
            The test suite result

        Raises:
            ValueError: If the test suite does not exist
        """
        logger.info(f"Running test suite: {suite_id}")

        # Generate a unique run ID
        run_id = str(uuid.uuid4())

        # Get the test suite
        suite = self.test_manager.get_test_suite(suite_id)

        # Create the test suite run object
        suite_run = {
            'id': run_id,
            'suite_id': suite_id,
            'parameters': parameters or {},
            'status': 'running',
            'test_runs': [],
            'start_time': self._get_current_timestamp(),
            'end_time': None
        }

        # Store the test suite run
        self.test_runs[run_id] = suite_run

        # Trigger test suite started event
        self._trigger_event('test_suite_started', suite_run)

        try:
            # Execute each test in the suite
            for test_id in suite['tests']:
                # Merge suite parameters with test-specific parameters
                test_parameters = parameters.copy() if parameters else {}
                if test_id in suite.get('test_parameters', {}):
                    test_parameters.update(suite['test_parameters'][test_id])

                # Run the test
                test_run = self.run_test(test_id, test_parameters)

                # Add the test run to the suite run
                suite_run['test_runs'].append(test_run)

            # Update the test suite run
            suite_run['status'] = 'completed'
            suite_run['end_time'] = self._get_current_timestamp()

            # Trigger test suite completed event
            self._trigger_event('test_suite_completed', suite_run)

            logger.info(f"Test suite completed: {suite_id}, Run ID: {run_id}")

            return suite_run

        except Exception as e:
            # Update the test suite run
            suite_run['status'] = 'failed'
            suite_run['error'] = str(e)
            suite_run['end_time'] = self._get_current_timestamp()

            # Trigger test suite failed event
            self._trigger_event('test_suite_failed', suite_run)

            logger.error(f"Test suite failed: {suite_id}, Run ID: {run_id}, Error: {e}")

            raise

    def get_test_run(self, run_id: str) -> Dict[str, Any]:
        """
        Get a test run.

        Args:
            run_id: The ID of the test run

        Returns:
            The test run

        Raises:
            ValueError: If the test run does not exist
        """
        if run_id not in self.test_runs:
            raise ValueError(f"Test run not found: {run_id}")

        return self.test_runs[run_id]

    def get_test_result(self, run_id: str) -> Dict[str, Any]:
        """
        Get a test result.

        Args:
            run_id: The ID of the test run

        Returns:
            The test result

        Raises:
            ValueError: If the test run does not exist
            ValueError: If the test result does not exist
        """
        # Get the test run
        test_run = self.get_test_run(run_id)

        # Get the test result
        if 'result' not in test_run or test_run['result'] is None:
            raise ValueError(f"Test result not found for run: {run_id}")

        return test_run['result']

    def generate_report(self,
                       run_id: str,
                       report_type: str,
                       parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate a report for a test run.

        Args:
            run_id: The ID of the test run
            report_type: The type of report to generate
            parameters: Parameters for the report

        Returns:
            The report

        Raises:
            ValueError: If the test run does not exist
            ValueError: If the report type does not exist
        """
        logger.info(f"Generating report for test run: {run_id}, Report type: {report_type}")

        # Get the test run
        test_run = self.get_test_run(run_id)

        # Generate the report
        report = self.report_manager.generate_report(report_type, test_run, parameters or {})

        logger.info(f"Report generated for test run: {run_id}")

        return report

    def _trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """
        Trigger an event.

        Args:
            event_type: The type of event
            event_data: Data associated with the event
        """
        logger.debug(f"Triggering event: {event_type}")

        # Emit the event using the shared event bus
        event_bus.emit(f"uctf.{event_type}", event_data)

        # Also trigger local event handlers for backward compatibility
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    handler(event_data)
                except Exception as e:
                    logger.error(f"Error in event handler for {event_type}: {e}")

    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.

        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
