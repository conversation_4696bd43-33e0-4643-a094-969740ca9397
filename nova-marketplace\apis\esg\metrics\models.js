/**
 * ESG Metrics API - Models
 * 
 * This file defines the models for the ESG Metrics API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * ESG Metric Schema
 * 
 * Represents an ESG (Environmental, Social, Governance) metric.
 */
const MetricSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Environmental', 'Social', 'Governance']
  },
  subcategory: {
    type: String,
    required: true
  },
  unit: {
    type: String,
    required: true
  },
  dataType: {
    type: String,
    required: true,
    enum: ['Numeric', 'Percentage', 'Boolean', 'Text', 'Date']
  },
  framework: {
    type: String,
    required: true,
    enum: ['GRI', 'SASB', 'TCFD', 'CDP', 'SDG', 'Custom']
  },
  frameworkReference: {
    type: String
  },
  calculationMethod: {
    type: String
  },
  dataSource: {
    type: String
  },
  frequency: {
    type: String,
    enum: ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annually', 'Custom']
  },
  status: {
    type: String,
    required: true,
    enum: ['Active', 'Inactive', 'Deprecated'],
    default: 'Active'
  },
  tags: [{
    type: String
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Metric Value Schema
 * 
 * Represents a value for an ESG metric at a specific point in time.
 */
const MetricValueSchema = new Schema({
  metric: {
    type: Schema.Types.ObjectId,
    ref: 'Metric',
    required: true
  },
  value: {
    type: Schema.Types.Mixed,
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  period: {
    type: String,
    required: true,
    enum: ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annually', 'Custom']
  },
  periodStart: {
    type: Date
  },
  periodEnd: {
    type: Date
  },
  location: {
    type: String
  },
  businessUnit: {
    type: String
  },
  notes: {
    type: String
  },
  source: {
    type: String
  },
  verificationStatus: {
    type: String,
    enum: ['Unverified', 'Verified', 'Rejected'],
    default: 'Unverified'
  },
  verifiedBy: {
    type: String
  },
  verificationDate: {
    type: Date
  },
  verificationNotes: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Target Schema
 * 
 * Represents a target for an ESG metric.
 */
const TargetSchema = new Schema({
  metric: {
    type: Schema.Types.ObjectId,
    ref: 'Metric',
    required: true
  },
  targetValue: {
    type: Schema.Types.Mixed,
    required: true
  },
  targetDate: {
    type: Date,
    required: true
  },
  baselineValue: {
    type: Schema.Types.Mixed
  },
  baselineDate: {
    type: Date
  },
  description: {
    type: String
  },
  status: {
    type: String,
    required: true,
    enum: ['Planned', 'In Progress', 'Achieved', 'Missed', 'Revised'],
    default: 'Planned'
  },
  owner: {
    type: String
  },
  approvedBy: {
    type: String
  },
  approvalDate: {
    type: Date
  },
  notes: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create and export models
const Metric = mongoose.model('Metric', MetricSchema);
const MetricValue = mongoose.model('MetricValue', MetricValueSchema);
const Target = mongoose.model('Target', TargetSchema);

module.exports = {
  Metric,
  MetricValue,
  Target
};
