/**
 * Environment Controller
 * 
 * This controller handles API requests related to environment management.
 */

const EnvironmentService = require('../services/EnvironmentService');
const ConfigService = require('../services/ConfigService');
const { ValidationError } = require('../utils/errors');

class EnvironmentController {
  constructor() {
    this.environmentService = new EnvironmentService();
    this.configService = new ConfigService();
  }

  /**
   * Get all environments
   */
  async getAllEnvironments(req, res, next) {
    try {
      const environments = await this.environmentService.getAllEnvironments();
      res.json(environments);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get environment by ID
   */
  async getEnvironmentById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Environment ID is required');
      }
      
      const environment = await this.environmentService.getEnvironmentById(id);
      res.json(environment);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get default environment
   */
  async getDefaultEnvironment(req, res, next) {
    try {
      const environment = await this.environmentService.getDefaultEnvironment();
      res.json(environment);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new environment
   */
  async createEnvironment(req, res, next) {
    try {
      const environmentData = req.body;
      
      if (!environmentData) {
        throw new ValidationError('Environment data is required');
      }
      
      const environment = await this.environmentService.createEnvironment(environmentData);
      res.status(201).json(environment);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update an environment
   */
  async updateEnvironment(req, res, next) {
    try {
      const { id } = req.params;
      const environmentData = req.body;
      
      if (!id) {
        throw new ValidationError('Environment ID is required');
      }
      
      if (!environmentData) {
        throw new ValidationError('Environment data is required');
      }
      
      const environment = await this.environmentService.updateEnvironment(id, environmentData);
      res.json(environment);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete an environment
   */
  async deleteEnvironment(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Environment ID is required');
      }
      
      const result = await this.environmentService.deleteEnvironment(id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Set default environment
   */
  async setDefaultEnvironment(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Environment ID is required');
      }
      
      const environment = await this.environmentService.setDefaultEnvironment(id);
      res.json(environment);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Promote environment (copy from one environment to another)
   */
  async promoteEnvironment(req, res, next) {
    try {
      const { sourceId, targetId } = req.body;
      
      if (!sourceId) {
        throw new ValidationError('Source environment ID is required');
      }
      
      if (!targetId) {
        throw new ValidationError('Target environment ID is required');
      }
      
      const result = await this.environmentService.promoteEnvironment(sourceId, targetId);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get environment configuration
   */
  async getEnvironmentConfig(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Environment ID is required');
      }
      
      const config = await this.configService.getAllConfig(id);
      res.json(config);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Compare environment configurations
   */
  async compareEnvironmentConfig(req, res, next) {
    try {
      const { sourceId, targetId } = req.body;
      
      if (!sourceId) {
        throw new ValidationError('Source environment ID is required');
      }
      
      if (!targetId) {
        throw new ValidationError('Target environment ID is required');
      }
      
      const comparison = await this.configService.compareConfig(sourceId, targetId);
      res.json(comparison);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new EnvironmentController();
