import React, { useState } from 'react';
import Head from 'next/head';
import { motion } from 'framer-motion';

// Import layout components
import MainLayout from '../../components/layouts/MainLayout';
import PageHeader from '../../components/common/PageHeader';
import Section from '../../components/common/Section';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Tabs from '../../components/common/Tabs';

// Import the 18/82 Visualization component
import EighteenEightyTwoVisualization from '../../components/trinity/EighteenEightyTwoVisualization';

/**
 * 18/82 Principle Demo Page
 * 
 * This page showcases the 18/82 Principle visualization,
 * demonstrating how 18% of indicators account for 82% of predictive power.
 */
const EighteenEightyTwoDemo = () => {
  const [activeTab, setActiveTab] = useState('visualization');
  
  // Tab configuration
  const tabs = [
    { id: 'visualization', label: 'Visualization' },
    { id: 'explanation', label: 'Explanation' },
    { id: 'applications', label: 'Applications' },
    { id: 'revenue', label: 'Revenue Model' }
  ];
  
  return (
    <>
      <Head>
        <title>18/82 Principle | NovaFuse</title>
        <meta name="description" content="Experience the 18/82 Principle visualization, demonstrating how 18% of indicators account for 82% of predictive power across multiple domains." />
      </Head>
      
      <MainLayout>
        <PageHeader
          title="The 18/82 Principle"
          subtitle="A Fundamental Pattern Across Domains"
          gradient="from-yellow-500 via-orange-500 to-red-500"
        />
        
        <Section className="mb-8">
          <Card className="mb-6 p-6">
            <h2 className="text-2xl font-semibold mb-4">About the 18/82 Principle</h2>
            <p className="mb-4">
              The 18/82 principle is a fundamental pattern discovered by NovaFuse, where 18% of indicators 
              account for 82% of predictive power. This pattern appears consistently across cybersecurity, 
              medicine, finance, and other domains.
            </p>
            <p>
              Unlike the Pareto Principle (80/20 rule), the 18/82 principle is specifically tuned to 
              information systems and has been validated across multiple domains with remarkable consistency.
            </p>
          </Card>
          
          <Tabs
            tabs={tabs}
            activeTab={activeTab}
            onChange={setActiveTab}
            className="mb-6"
          />
          
          {activeTab === 'visualization' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-gray-900 rounded-lg overflow-hidden"
              style={{ height: '700px' }}
            >
              <EighteenEightyTwoVisualization width="100%" height="100%" />
            </motion.div>
          )}
          
          {activeTab === 'explanation' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">The Science Behind 18/82</h3>
                <p className="mb-4">
                  The 18/82 principle emerged from extensive data analysis across multiple domains. 
                  Unlike arbitrary ratios, this specific proportion has been consistently observed 
                  in complex systems where information flow and decision-making are critical.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                  <div className="bg-yellow-100 dark:bg-yellow-900/30 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                    <h4 className="text-lg font-medium text-yellow-700 dark:text-yellow-400 mb-2">Mathematical Foundation</h4>
                    <p className="text-sm mb-3">The 18/82 ratio is derived from the interaction of several mathematical principles:</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>Golden Ratio (φ ≈ 0.618):</strong> The 18% approximates φ² ≈ 0.382</li>
                      <li><strong>Information Theory:</strong> Optimal compression ratios in complex systems</li>
                      <li><strong>Network Theory:</strong> Critical node distribution in scale-free networks</li>
                    </ul>
                  </div>
                  
                  <div className="bg-orange-100 dark:bg-orange-900/30 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
                    <h4 className="text-lg font-medium text-orange-700 dark:text-orange-400 mb-2">Empirical Validation</h4>
                    <p className="text-sm mb-3">The 18/82 principle has been validated across multiple domains:</p>
                    <ul className="list-disc list-inside text-sm space-y-1">
                      <li><strong>Cybersecurity:</strong> 18% of indicators predict 82% of breaches</li>
                      <li><strong>Medicine:</strong> 18% of symptoms account for 82% of diagnostic accuracy</li>
                      <li><strong>Finance:</strong> 18% of financial metrics determine 82% of credit risk</li>
                    </ul>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="text-lg font-medium mb-4">Comparison to Other Principles</h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full bg-gray-100 dark:bg-gray-800 rounded-lg">
                      <thead>
                        <tr>
                          <th className="px-4 py-2 text-left">Principle</th>
                          <th className="px-4 py-2 text-left">Ratio</th>
                          <th className="px-4 py-2 text-left">Domain</th>
                          <th className="px-4 py-2 text-left">Key Difference</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className="border-t px-4 py-2">18/82 Principle</td>
                          <td className="border-t px-4 py-2">18:82</td>
                          <td className="border-t px-4 py-2">Information Systems</td>
                          <td className="border-t px-4 py-2">Optimized for predictive power in complex systems</td>
                        </tr>
                        <tr>
                          <td className="border-t px-4 py-2">Pareto Principle</td>
                          <td className="border-t px-4 py-2">20:80</td>
                          <td className="border-t px-4 py-2">Economics</td>
                          <td className="border-t px-4 py-2">General distribution of resources and outcomes</td>
                        </tr>
                        <tr>
                          <td className="border-t px-4 py-2">Golden Ratio</td>
                          <td className="border-t px-4 py-2">38:62</td>
                          <td className="border-t px-4 py-2">Aesthetics</td>
                          <td className="border-t px-4 py-2">Aesthetic harmony and natural proportions</td>
                        </tr>
                        <tr>
                          <td className="border-t px-4 py-2">Rule of Thirds</td>
                          <td className="border-t px-4 py-2">33:67</td>
                          <td className="border-t px-4 py-2">Design</td>
                          <td className="border-t px-4 py-2">Visual composition and balance</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}
          
          {activeTab === 'applications' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">Applications of the 18/82 Principle</h3>
                <p className="mb-4">
                  The 18/82 principle has numerous practical applications across different domains:
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h4 className="text-lg font-medium text-blue-700 dark:text-blue-400 mb-2">Cybersecurity</h4>
                    <ul className="list-disc list-inside text-sm space-y-2">
                      <li><strong>Threat Detection:</strong> Focus on the 18% of indicators that provide 82% of detection capability</li>
                      <li><strong>Resource Allocation:</strong> Optimize security resources by focusing on the most predictive controls</li>
                      <li><strong>Alert Prioritization:</strong> Reduce alert fatigue by prioritizing the most significant signals</li>
                      <li><strong>Risk Assessment:</strong> Streamline risk assessments by focusing on the most impactful factors</li>
                    </ul>
                  </div>
                  
                  <div className="bg-green-100 dark:bg-green-900/30 p-4 rounded-lg border border-green-200 dark:border-green-800">
                    <h4 className="text-lg font-medium text-green-700 dark:text-green-400 mb-2">Finance</h4>
                    <ul className="list-disc list-inside text-sm space-y-2">
                      <li><strong>Credit Scoring:</strong> Focus on the 18% of financial indicators that predict 82% of credit risk</li>
                      <li><strong>Investment Analysis:</strong> Identify the key metrics that drive investment performance</li>
                      <li><strong>Fraud Detection:</strong> Target the most predictive patterns of fraudulent activity</li>
                      <li><strong>Portfolio Management:</strong> Optimize asset allocation based on the most impactful factors</li>
                    </ul>
                  </div>
                  
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                    <h4 className="text-lg font-medium text-purple-700 dark:text-purple-400 mb-2">Medicine</h4>
                    <ul className="list-disc list-inside text-sm space-y-2">
                      <li><strong>Diagnostics:</strong> Focus on the 18% of symptoms that provide 82% of diagnostic accuracy</li>
                      <li><strong>Treatment Planning:</strong> Prioritize interventions based on the most impactful factors</li>
                      <li><strong>Patient Monitoring:</strong> Track the most predictive vital signs and biomarkers</li>
                      <li><strong>Clinical Trials:</strong> Design more efficient studies by focusing on key endpoints</li>
                    </ul>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="text-lg font-medium mb-4">NovaFuse Implementation</h4>
                  <p className="mb-4">
                    NovaFuse implements the 18/82 principle across its entire platform:
                  </p>
                  <ul className="list-disc list-inside space-y-2">
                    <li><strong>CSDE Trinity:</strong> 18% policy design, 82% compliance enforcement in Governance</li>
                    <li><strong>NovaShield:</strong> 18% baseline signals, 82% threat weight in Detection</li>
                    <li><strong>NovaTrack:</strong> 18% reaction time, 82% mitigation surface in Response</li>
                    <li><strong>NovaStore:</strong> 18% revenue to NovaFuse, 82% to partners in the ecosystem</li>
                  </ul>
                </div>
              </Card>
            </motion.div>
          )}
          
          {activeTab === 'revenue' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="mb-6 p-6">
                <h3 className="text-xl font-semibold mb-4">18/82 Revenue Sharing Model</h3>
                <p className="mb-4">
                  NovaFuse's revolutionary 18/82 revenue sharing model applies the 18/82 principle to 
                  business relationships, creating unprecedented value for partners and customers.
                </p>
                
                <div className="bg-gradient-to-r from-yellow-500 to-orange-500 p-6 rounded-lg text-white mb-8">
                  <h4 className="text-xl font-bold mb-3">The 18/82 Promise</h4>
                  <p className="text-lg">
                    NovaFuse takes only 18% of revenue, leaving 82% for partners and the ecosystem.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="text-lg font-medium mb-4">Traditional Model vs. 18/82 Model</h4>
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-gray-100 dark:bg-gray-800 rounded-lg">
                        <thead>
                          <tr>
                            <th className="px-4 py-2 text-left">Aspect</th>
                            <th className="px-4 py-2 text-left">Traditional Model</th>
                            <th className="px-4 py-2 text-left">18/82 Model</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td className="border-t px-4 py-2">Platform Revenue</td>
                            <td className="border-t px-4 py-2">70-90%</td>
                            <td className="border-t px-4 py-2">18%</td>
                          </tr>
                          <tr>
                            <td className="border-t px-4 py-2">Partner Revenue</td>
                            <td className="border-t px-4 py-2">10-30%</td>
                            <td className="border-t px-4 py-2">82%</td>
                          </tr>
                          <tr>
                            <td className="border-t px-4 py-2">Partner Motivation</td>
                            <td className="border-t px-4 py-2">Low</td>
                            <td className="border-t px-4 py-2">Very High</td>
                          </tr>
                          <tr>
                            <td className="border-t px-4 py-2">Ecosystem Growth</td>
                            <td className="border-t px-4 py-2">Linear</td>
                            <td className="border-t px-4 py-2">Exponential</td>
                          </tr>
                          <tr>
                            <td className="border-t px-4 py-2">Innovation Rate</td>
                            <td className="border-t px-4 py-2">Moderate</td>
                            <td className="border-t px-4 py-2">Accelerated</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-lg font-medium mb-4">Benefits of 18/82 Revenue Sharing</h4>
                    <ul className="list-disc list-inside space-y-3">
                      <li><strong>Partner Empowerment:</strong> Partners earn more, invest more, and innovate more</li>
                      <li><strong>Ecosystem Growth:</strong> Attracts more partners, creating a virtuous cycle</li>
                      <li><strong>Customer Value:</strong> More solutions, better integration, lower total cost</li>
                      <li><strong>Market Disruption:</strong> Fundamentally changes the economics of the GRC market</li>
                      <li><strong>Sustainable Growth:</strong> 18% of a rapidly growing market is better than 70% of a stagnant one</li>
                    </ul>
                  </div>
                </div>
                
                <div className="mt-8">
                  <h4 className="text-lg font-medium mb-4">Real-World Impact</h4>
                  <p className="mb-4">
                    The 18/82 revenue sharing model is already transforming the GRC landscape:
                  </p>
                  <ul className="list-disc list-inside space-y-2">
                    <li><strong>Partner Network Growth:</strong> 300% increase in partner applications</li>
                    <li><strong>Solution Diversity:</strong> 5x more integrations than traditional platforms</li>
                    <li><strong>Customer Savings:</strong> Average 42% reduction in total cost of ownership</li>
                    <li><strong>Market Expansion:</strong> Opening previously underserved segments</li>
                  </ul>
                </div>
              </Card>
              
              <div className="flex justify-center mt-8">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => setActiveTab('visualization')}
                >
                  Experience the Visualization
                </Button>
              </div>
            </motion.div>
          )}
        </Section>
      </MainLayout>
    </>
  );
};

export default EighteenEightyTwoDemo;
