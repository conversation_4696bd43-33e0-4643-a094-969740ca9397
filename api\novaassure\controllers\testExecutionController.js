/**
 * Test Execution Controller
 * 
 * This controller handles API requests for test execution.
 */

const testExecutionService = require('../services/testExecutionService');
const logger = require('../utils/logger');

/**
 * Get all test executions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getAllTestExecutions(req, res, next) {
  try {
    const { testPlanId, status, page = 1, limit = 10 } = req.query;
    
    const filters = {};
    
    if (testPlanId) {
      filters.testPlanId = testPlanId;
    }
    
    if (status) {
      filters.status = status;
    }
    
    const result = await testExecutionService.getAllTestExecutions(
      filters,
      parseInt(page, 10),
      parseInt(limit, 10)
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Error getting test executions', error);
    next(error);
  }
}

/**
 * Get test execution by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getTestExecutionById(req, res, next) {
  try {
    const { id } = req.params;
    
    const testExecution = await testExecutionService.getTestExecutionById(id);
    
    res.json(testExecution);
  } catch (error) {
    logger.error(`Error getting test execution ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Start test execution
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function startTestExecution(req, res, next) {
  try {
    const { testPlanId, notes } = req.body;
    const userId = req.user.id;
    
    const testExecution = await testExecutionService.startTestExecution(testPlanId, userId, notes);
    
    res.status(201).json(testExecution);
  } catch (error) {
    logger.error('Error starting test execution', error);
    next(error);
  }
}

/**
 * Complete test execution
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function completeTestExecution(req, res, next) {
  try {
    const { id } = req.params;
    const { results, notes } = req.body;
    
    const testExecution = await testExecutionService.completeTestExecution(id, results, notes);
    
    res.json(testExecution);
  } catch (error) {
    logger.error(`Error completing test execution ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Cancel test execution
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function cancelTestExecution(req, res, next) {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    
    const testExecution = await testExecutionService.cancelTestExecution(id, reason);
    
    res.json(testExecution);
  } catch (error) {
    logger.error(`Error cancelling test execution ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Add test result
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function addTestResult(req, res, next) {
  try {
    const { id } = req.params;
    const result = req.body;
    
    const testExecution = await testExecutionService.addTestResult(id, result);
    
    res.json(testExecution);
  } catch (error) {
    logger.error(`Error adding test result to execution ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Schedule automated test execution
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function scheduleAutomatedTestExecution(req, res, next) {
  try {
    const { testPlanId, schedule, notifyUsers } = req.body;
    
    const result = await testExecutionService.scheduleAutomatedTestExecution(
      testPlanId,
      schedule,
      notifyUsers
    );
    
    res.status(201).json(result);
  } catch (error) {
    logger.error('Error scheduling automated test execution', error);
    next(error);
  }
}

/**
 * Run automated test
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function runAutomatedTest(req, res, next) {
  try {
    const { testPlanId, parameters } = req.body;
    
    // Start automated test asynchronously
    testExecutionService.runAutomatedTest(testPlanId, parameters)
      .catch(error => {
        logger.error(`Error running automated test for test plan ${testPlanId}`, error);
      });
    
    res.status(202).json({
      success: true,
      message: `Automated test for test plan ${testPlanId} started`,
      testPlanId
    });
  } catch (error) {
    logger.error('Error running automated test', error);
    next(error);
  }
}

module.exports = {
  getAllTestExecutions,
  getTestExecutionById,
  startTestExecution,
  completeTestExecution,
  cancelTestExecution,
  addTestResult,
  scheduleAutomatedTestExecution,
  runAutomatedTest
};
