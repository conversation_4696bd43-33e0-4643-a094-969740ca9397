<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>9. 13 NovaFuse Components</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1050px;
            height: 600px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>9. 13 NovaFuse Components</h1>
    
    <div class="diagram-container">
        <!-- NovaFuse Platform -->
        <div class="element" style="top: 50px; left: 350px; width: 300px; background-color: #e6f7ff; font-weight: bold; font-size: 20px;">
            13 Universal NovaFuse Components
            <div class="element-number">1</div>
        </div>
        
        <!-- Core components -->
        <div class="element" style="top: 150px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            NovaCore (Universal Compliance Testing Framework)
            <div class="element-number">2</div>
        </div>
        
        <div class="element" style="top: 220px; left: 150px; width: 250px; background-color: #f6ffed; font-size: 14px;">
            NovaShield (Universal Vendor Risk Management)
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 220px; left: 600px; width: 250px; background-color: #f6ffed; font-size: 14px;">
            NovaTrack (Universal Compliance Tracking)
            <div class="element-number">4</div>
        </div>
        
        <div class="element" style="top: 290px; left: 50px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            NovaLearn (Universal Training)
            <div class="element-number">5</div>
        </div>
        
        <div class="element" style="top: 290px; left: 300px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            NovaThink (Universal Decision Engine)
            <div class="element-number">6</div>
        </div>
        
        <div class="element" style="top: 290px; left: 550px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            NovaConnect (Universal API)
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 290px; left: 800px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            NovaView (Universal Visualization)
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 360px; left: 50px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            NovaVision (Universal UI Framework)
            <div class="element-number">9</div>
        </div>
        
        <div class="element" style="top: 360px; left: 300px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            NovaFlow (Universal Workflow)
            <div class="element-number">10</div>
        </div>
        
        <div class="element" style="top: 360px; left: 550px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            NovaPulse+ (Universal Monitoring)
            <div class="element-number">11</div>
        </div>
        
        <div class="element" style="top: 360px; left: 800px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            NovaStore (Universal Marketplace)
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 430px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            NovaFuse (Universal Platform)
            <div class="element-number">13</div>
        </div>
        
        <!-- Implementation -->
        <div class="element" style="top: 500px; left: 350px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            3-6-9-12-13 Alignment Architecture
            <div class="element-number">14</div>
        </div>
        
        <!-- Connections -->
        <!-- Connect NovaFuse Platform to NovaCore -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 50px; background-color: black;"></div>
        <div class="arrow" style="top: 140px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect NovaCore to second tier -->
        <div class="connection" style="top: 200px; left: 400px; width: 125px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 220px; left: 265px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 200px; left: 600px; width: 125px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 220px; left: 715px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect second tier to third tier -->
        <div class="connection" style="top: 270px; left: 200px; width: 50px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 290px; left: 140px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 270px; left: 250px; width: 150px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 290px; left: 390px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 270px; left: 750px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 290px; left: 590px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 270px; left: 800px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 290px; left: 890px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect third tier to fourth tier -->
        <div class="connection" style="top: 340px; left: 150px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 350px; left: 145px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 340px; left: 400px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 350px; left: 395px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 340px; left: 650px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 350px; left: 645px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 340px; left: 900px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 350px; left: 895px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect fourth tier to NovaFuse -->
        <div class="connection" style="top: 410px; left: 150px; width: 200px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 430px; left: 340px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 410px; left: 400px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 430px; left: 440px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 410px; left: 650px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 430px; left: 540px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 410px; left: 900px; width: 250px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 430px; left: 640px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect NovaFuse to Implementation -->
        <div class="connection" style="top: 480px; left: 500px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 490px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
    </div>
</body>
</html>
