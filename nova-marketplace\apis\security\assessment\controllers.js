const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of security assessments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAssessments = (req, res) => {
  try {
    const { page = 1, limit = 10, type, status, startDate, endDate, sortBy = 'title', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter assessments based on query parameters
    let filteredAssessments = [...models.securityAssessments];
    
    if (type) {
      filteredAssessments = filteredAssessments.filter(assessment => assessment.type === type);
    }
    
    if (status) {
      filteredAssessments = filteredAssessments.filter(assessment => assessment.status === status);
    }
    
    if (startDate) {
      filteredAssessments = filteredAssessments.filter(assessment => assessment.startDate >= startDate);
    }
    
    if (endDate) {
      filteredAssessments = filteredAssessments.filter(assessment => assessment.endDate <= endDate);
    }
    
    // Sort assessments
    filteredAssessments.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedAssessments = filteredAssessments.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalAssessments = filteredAssessments.length;
    const totalPages = Math.ceil(totalAssessments / limitNum);
    
    res.json({
      data: paginatedAssessments,
      pagination: {
        total: totalAssessments,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getAssessments:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific security assessment by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAssessmentById = (req, res) => {
  try {
    const { id } = req.params;
    const assessment = models.securityAssessments.find(a => a.id === id);
    
    if (!assessment) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Security assessment with ID ${id} not found`
      });
    }
    
    res.json({ data: assessment });
  } catch (error) {
    console.error('Error in getAssessmentById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new security assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createAssessment = (req, res) => {
  try {
    const { title, description, type, status, scope, startDate, endDate, assignedTo } = req.body;
    
    // Create a new assessment with a unique ID
    const newAssessment = {
      id: `sa-${uuidv4().substring(0, 8)}`,
      title,
      description,
      type,
      status,
      scope,
      startDate,
      endDate,
      findings: [],
      assignedTo,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the new assessment to the collection
    models.securityAssessments.push(newAssessment);
    
    res.status(201).json({
      data: newAssessment,
      message: 'Security assessment created successfully'
    });
  } catch (error) {
    console.error('Error in createAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing security assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateAssessment = (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, type, status, scope, startDate, endDate, assignedTo } = req.body;
    
    // Find the assessment to update
    const assessmentIndex = models.securityAssessments.findIndex(a => a.id === id);
    
    if (assessmentIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Security assessment with ID ${id} not found`
      });
    }
    
    // Update the assessment
    const updatedAssessment = {
      ...models.securityAssessments[assessmentIndex],
      title: title || models.securityAssessments[assessmentIndex].title,
      description: description || models.securityAssessments[assessmentIndex].description,
      type: type || models.securityAssessments[assessmentIndex].type,
      status: status || models.securityAssessments[assessmentIndex].status,
      scope: scope || models.securityAssessments[assessmentIndex].scope,
      startDate: startDate || models.securityAssessments[assessmentIndex].startDate,
      endDate: endDate || models.securityAssessments[assessmentIndex].endDate,
      assignedTo: assignedTo || models.securityAssessments[assessmentIndex].assignedTo,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old assessment with the updated one
    models.securityAssessments[assessmentIndex] = updatedAssessment;
    
    res.json({
      data: updatedAssessment,
      message: 'Security assessment updated successfully'
    });
  } catch (error) {
    console.error('Error in updateAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a security assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteAssessment = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the assessment to delete
    const assessmentIndex = models.securityAssessments.findIndex(a => a.id === id);
    
    if (assessmentIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Security assessment with ID ${id} not found`
      });
    }
    
    // Remove the assessment from the collection
    models.securityAssessments.splice(assessmentIndex, 1);
    
    res.json({
      message: 'Security assessment deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get findings for a specific security assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAssessmentFindings = (req, res) => {
  try {
    const { id } = req.params;
    const assessment = models.securityAssessments.find(a => a.id === id);
    
    if (!assessment) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Security assessment with ID ${id} not found`
      });
    }
    
    res.json({ data: assessment.findings });
  } catch (error) {
    console.error('Error in getAssessmentFindings:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a finding to a security assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addFindingToAssessment = (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, severity, status, remediation, assignedTo, dueDate } = req.body;
    
    // Find the assessment
    const assessmentIndex = models.securityAssessments.findIndex(a => a.id === id);
    
    if (assessmentIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Security assessment with ID ${id} not found`
      });
    }
    
    // Create a new finding
    const newFinding = {
      id: `sf-${uuidv4().substring(0, 8)}`,
      title,
      description,
      severity,
      status,
      remediation,
      assignedTo,
      dueDate,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the finding to the assessment
    models.securityAssessments[assessmentIndex].findings.push(newFinding);
    
    // Also add to the separate findings collection
    models.securityFindings.push(newFinding);
    
    // Update the assessment's updatedAt timestamp
    models.securityAssessments[assessmentIndex].updatedAt = new Date().toISOString();
    
    res.status(201).json({
      data: newFinding,
      message: 'Finding added to assessment successfully'
    });
  } catch (error) {
    console.error('Error in addFindingToAssessment:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of all security findings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFindings = (req, res) => {
  try {
    const { page = 1, limit = 10, severity, status, sortBy = 'title', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter findings based on query parameters
    let filteredFindings = [...models.securityFindings];
    
    if (severity) {
      filteredFindings = filteredFindings.filter(finding => finding.severity === severity);
    }
    
    if (status) {
      filteredFindings = filteredFindings.filter(finding => finding.status === status);
    }
    
    // Sort findings
    filteredFindings.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedFindings = filteredFindings.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalFindings = filteredFindings.length;
    const totalPages = Math.ceil(totalFindings / limitNum);
    
    res.json({
      data: paginatedFindings,
      pagination: {
        total: totalFindings,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getFindings:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific security finding by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getFindingById = (req, res) => {
  try {
    const { id } = req.params;
    const finding = models.securityFindings.find(f => f.id === id);
    
    if (!finding) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Security finding with ID ${id} not found`
      });
    }
    
    res.json({ data: finding });
  } catch (error) {
    console.error('Error in getFindingById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update a security finding
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateFinding = (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, severity, status, remediation, assignedTo, dueDate } = req.body;
    
    // Find the finding to update
    const findingIndex = models.securityFindings.findIndex(f => f.id === id);
    
    if (findingIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Security finding with ID ${id} not found`
      });
    }
    
    // Update the finding
    const updatedFinding = {
      ...models.securityFindings[findingIndex],
      title: title || models.securityFindings[findingIndex].title,
      description: description || models.securityFindings[findingIndex].description,
      severity: severity || models.securityFindings[findingIndex].severity,
      status: status || models.securityFindings[findingIndex].status,
      remediation: remediation || models.securityFindings[findingIndex].remediation,
      assignedTo: assignedTo || models.securityFindings[findingIndex].assignedTo,
      dueDate: dueDate || models.securityFindings[findingIndex].dueDate,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old finding with the updated one
    models.securityFindings[findingIndex] = updatedFinding;
    
    // Also update the finding in any assessments that contain it
    models.securityAssessments.forEach(assessment => {
      const assessmentFindingIndex = assessment.findings.findIndex(f => f.id === id);
      if (assessmentFindingIndex !== -1) {
        assessment.findings[assessmentFindingIndex] = updatedFinding;
        assessment.updatedAt = new Date().toISOString();
      }
    });
    
    res.json({
      data: updatedFinding,
      message: 'Security finding updated successfully'
    });
  } catch (error) {
    console.error('Error in updateFinding:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get assessment types
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAssessmentTypes = (req, res) => {
  try {
    res.json({ data: models.assessmentTypes });
  } catch (error) {
    console.error('Error in getAssessmentTypes:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getAssessments,
  getAssessmentById,
  createAssessment,
  updateAssessment,
  deleteAssessment,
  getAssessmentFindings,
  addFindingToAssessment,
  getFindings,
  getFindingById,
  updateFinding,
  getAssessmentTypes
};
