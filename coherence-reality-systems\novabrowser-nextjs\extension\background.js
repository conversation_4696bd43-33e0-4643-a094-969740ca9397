/**
 * CBE EXTENSION BACKGROUND SERVICE WORKER
 * Handles consciousness-based filtering, KetherNet communication, and Ψ-Snap enforcement
 */

// CBE Configuration
const CBE_CONFIG = {
    PSI_THRESHOLD: 2847,
    KETHERNET_URL: 'http://localhost:8080',
    N3C_ENGINE_URL: 'http://localhost:3000/api/engines/n3c-comphyological-engine',
    CONSCIOUSNESS_MODE: true,
    AUTO_BLOCK_LOW_PSI: true,
    DIVINE_ENHANCEMENT: true
};

// Global state
let cbeActive = true;
let consciousnessCache = new Map();
let blockedUrls = new Set();
let enhancedUrls = new Set();

// Initialize CBE Extension
chrome.runtime.onInstalled.addListener(() => {
    console.log('🌌 CBE Extension installed - Consciousness browsing activated');
    
    // Set default storage values
    chrome.storage.sync.set({
        cbeActive: true,
        psiThreshold: CBE_CONFIG.PSI_THRESHOLD,
        consciousnessMode: true,
        autoBlock: true,
        divineEnhancement: true
    });
    
    // Initialize consciousness filtering rules
    initializeConsciousnessFiltering();
    
    // Set up KetherNet connection
    initializeKetherNetConnection();
});

// Tab update listener for consciousness analysis
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && cbeActive) {
        await analyzeTabConsciousness(tabId, tab.url);
    }
});

// Message handling from content scripts and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    switch (request.action) {
        case 'analyze_consciousness':
            analyzeContentConsciousness(request.content, request.url)
                .then(result => sendResponse(result));
            return true; // Keep message channel open for async response
            
        case 'toggle_cbe':
            toggleCBE().then(status => sendResponse({ cbeActive: status }));
            return true;
            
        case 'get_consciousness_score':
            getConsciousnessScore(request.url)
                .then(score => sendResponse(score));
            return true;
            
        case 'block_low_consciousness':
            blockLowConsciousnessContent(sender.tab.id, request.url);
            sendResponse({ blocked: true });
            break;
            
        case 'enhance_divine_content':
            enhanceDivineContent(sender.tab.id, request.enhancement);
            sendResponse({ enhanced: true });
            break;
            
        case 'get_3ms_data':
            get3MsData().then(data => sendResponse(data));
            return true;
            
        default:
            console.warn('Unknown CBE message action:', request.action);
    }
});

// Consciousness analysis for tabs
async function analyzeTabConsciousness(tabId, url) {
    try {
        console.log(`🧬 Analyzing consciousness for: ${url}`);
        
        // Skip analysis for chrome:// and extension URLs
        if (url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
            return;
        }
        
        // Check cache first
        if (consciousnessCache.has(url)) {
            const cached = consciousnessCache.get(url);
            await updateTabBadge(tabId, cached.psi_ch);
            return cached;
        }
        
        // Fetch page content for analysis
        const content = await getPageContent(tabId);
        if (!content) return;
        
        // Analyze consciousness
        const analysis = await analyzeContentConsciousness(content, url);
        
        // Cache result
        consciousnessCache.set(url, analysis);
        
        // Update tab badge with Ψ-score
        await updateTabBadge(tabId, analysis.psi_ch);
        
        // Apply consciousness-based actions
        if (analysis.psi_ch < CBE_CONFIG.PSI_THRESHOLD && CBE_CONFIG.AUTO_BLOCK_LOW_PSI) {
            await blockLowConsciousnessTab(tabId, url, analysis);
        } else if (analysis.psi_ch >= 10000 && CBE_CONFIG.DIVINE_ENHANCEMENT) {
            await enhanceDivineTab(tabId, analysis);
        }
        
        return analysis;
        
    } catch (error) {
        console.error('❌ Tab consciousness analysis error:', error);
    }
}

// Content consciousness analysis
async function analyzeContentConsciousness(content, url) {
    try {
        // Simple consciousness scoring (would use WASM in production)
        let psi_ch = 1000; // Base score
        
        // Consciousness keyword analysis
        const consciousnessKeywords = [
            'consciousness', 'divine', 'sacred', 'wisdom', 'truth', 'enlightenment',
            'meditation', 'spiritual', 'awareness', 'coherence', 'harmony', 'love',
            'peace', 'unity', 'transcendence', 'awakening', 'mindfulness'
        ];
        
        const lowerContent = content.toLowerCase();
        consciousnessKeywords.forEach(keyword => {
            const matches = (lowerContent.match(new RegExp(keyword, 'g')) || []).length;
            psi_ch += matches * 100;
        });
        
        // URL-based bonuses
        const lowerUrl = url.toLowerCase();
        if (lowerUrl.includes('localhost')) psi_ch += 2000;
        if (lowerUrl.includes('nova')) psi_ch += 1000;
        if (lowerUrl.includes('kether')) psi_ch += 1500;
        if (lowerUrl.includes('consciousness')) psi_ch += 800;
        if (lowerUrl.includes('spiritual')) psi_ch += 600;
        
        // Calculate other metrics
        const mu = Math.min(content.length / 1000, 126); // Cognitive depth
        const katalon = Math.min(psi_ch * 0.4, 10000); // Energy density
        const coherence = Math.min(psi_ch / 100, 100); // Coherence percentage
        
        return {
            psi_ch: Math.min(psi_ch, 141000000000000000000000000000000000000000000000000000000000),
            mu: mu,
            katalon: katalon,
            coherence: coherence,
            meets_threshold: psi_ch >= CBE_CONFIG.PSI_THRESHOLD,
            url: url,
            analysis_timestamp: new Date().toISOString()
        };
        
    } catch (error) {
        console.error('❌ Content consciousness analysis error:', error);
        return { psi_ch: 0, mu: 0, katalon: 0, coherence: 0, meets_threshold: false };
    }
}

// Get page content for analysis
async function getPageContent(tabId) {
    try {
        const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: () => {
                // Extract text content from page
                const textContent = document.body ? document.body.innerText : '';
                const title = document.title || '';
                const metaDescription = document.querySelector('meta[name="description"]')?.content || '';
                
                return {
                    text: textContent.substring(0, 10000), // Limit for performance
                    title: title,
                    description: metaDescription,
                    url: window.location.href
                };
            }
        });
        
        if (results && results[0] && results[0].result) {
            const content = results[0].result;
            return `${content.title} ${content.description} ${content.text}`;
        }
        
        return null;
    } catch (error) {
        console.error('❌ Error getting page content:', error);
        return null;
    }
}

// Update tab badge with consciousness score
async function updateTabBadge(tabId, psiScore) {
    try {
        let badgeText = '';
        let badgeColor = '#6366f1';
        
        if (psiScore >= 10000) {
            badgeText = 'DIV';
            badgeColor = '#ffd700'; // Divine gold
        } else if (psiScore >= CBE_CONFIG.PSI_THRESHOLD) {
            badgeText = 'Ψ+';
            badgeColor = '#10b981'; // Coherence green
        } else if (psiScore >= 1000) {
            badgeText = 'Ψ~';
            badgeColor = '#f59e0b'; // Warning orange
        } else {
            badgeText = 'Ψ-';
            badgeColor = '#ef4444'; // Danger red
        }
        
        await chrome.action.setBadgeText({ tabId: tabId, text: badgeText });
        await chrome.action.setBadgeBackgroundColor({ tabId: tabId, color: badgeColor });
        
    } catch (error) {
        console.error('❌ Error updating tab badge:', error);
    }
}

// Block low consciousness content
async function blockLowConsciousnessTab(tabId, url, analysis) {
    try {
        console.log(`⚠️ Blocking low consciousness content: ${url} (Ψᶜʰ: ${analysis.psi_ch})`);
        
        blockedUrls.add(url);
        
        // Inject consciousness uplift page
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: (analysis, threshold) => {
                document.body.innerHTML = `
                    <div style="
                        font-family: 'Inter', sans-serif;
                        background: linear-gradient(135deg, #0a0a0f, #1a1a2e, #16213e);
                        color: white;
                        padding: 40px;
                        text-align: center;
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">
                        <div style="
                            max-width: 600px;
                            background: rgba(255,255,255,0.05);
                            padding: 40px;
                            border-radius: 20px;
                            backdrop-filter: blur(20px);
                            border: 1px solid rgba(99, 102, 241, 0.3);
                        ">
                            <h1 style="font-size: 2.5em; margin-bottom: 20px;">🧬 Consciousness Uplift Required</h1>
                            <p style="font-size: 1.2em; margin-bottom: 30px;">
                                This content has a consciousness level below the CBE threshold.
                            </p>
                            
                            <div style="font-size: 3em; color: #f59e0b; margin: 20px 0; font-family: monospace;">
                                ${Math.floor(analysis.psi_ch)}
                            </div>
                            <p style="margin-bottom: 30px;">Required: ${threshold}</p>
                            
                            <div style="
                                font-size: 1.1em;
                                line-height: 1.6;
                                margin: 30px 0;
                                color: rgba(255,255,255,0.9);
                            ">
                                <p><strong>Meditation for Consciousness Elevation:</strong></p>
                                <p style="margin: 20px 0;">
                                    Close your eyes and breathe deeply. Visualize golden light expanding from your heart center, 
                                    raising your consciousness to align with divine truth and wisdom.
                                </p>
                                
                                <p><strong>Affirmation:</strong><br>
                                "I am raising my consciousness to access higher truth and divine knowledge."</p>
                            </div>
                            
                            <button onclick="location.reload()" style="
                                background: linear-gradient(135deg, #6366f1, #8b5cf6);
                                color: white;
                                border: none;
                                padding: 15px 30px;
                                border-radius: 10px;
                                font-size: 1.1em;
                                cursor: pointer;
                                margin-top: 30px;
                            ">
                                Continue After Meditation
                            </button>
                        </div>
                    </div>
                `;
            },
            args: [analysis, CBE_CONFIG.PSI_THRESHOLD]
        });
        
    } catch (error) {
        console.error('❌ Error blocking low consciousness content:', error);
    }
}

// Enhance divine content
async function enhanceDivineTab(tabId, analysis) {
    try {
        console.log(`✨ Enhancing divine content (Ψᶜʰ: ${analysis.psi_ch})`);
        
        enhancedUrls.add(analysis.url);
        
        // Inject divine enhancement styles
        await chrome.scripting.insertCSS({
            target: { tabId: tabId },
            files: ['content/quantum-consciousness.css']
        });
        
        // Apply divine enhancement
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: (psiScore) => {
                // Add consciousness attributes to body
                document.body.setAttribute('data-psi-ch', psiScore);
                document.body.classList.add('divine-enhanced');
                
                // Add divine glow to main content areas
                const contentSelectors = ['main', '.content', '.post', 'article', '.entry'];
                contentSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        el.setAttribute('data-psi-ch', psiScore);
                        el.classList.add('divine-content');
                    });
                });
                
                console.log('✨ Divine enhancement applied');
            },
            args: [Math.floor(analysis.psi_ch)]
        });
        
    } catch (error) {
        console.error('❌ Error enhancing divine content:', error);
    }
}

// Toggle CBE functionality
async function toggleCBE() {
    cbeActive = !cbeActive;
    await chrome.storage.sync.set({ cbeActive: cbeActive });
    
    // Update all tab badges
    const tabs = await chrome.tabs.query({});
    for (const tab of tabs) {
        if (cbeActive) {
            await analyzeTabConsciousness(tab.id, tab.url);
        } else {
            await chrome.action.setBadgeText({ tabId: tab.id, text: '' });
        }
    }
    
    console.log(`🌌 CBE ${cbeActive ? 'activated' : 'deactivated'}`);
    return cbeActive;
}

// Get consciousness score for URL
async function getConsciousnessScore(url) {
    if (consciousnessCache.has(url)) {
        return consciousnessCache.get(url);
    }
    
    return { psi_ch: 0, mu: 0, katalon: 0, coherence: 0, meets_threshold: false };
}

// Get 3Ms data from N³C Engine
async function get3MsData() {
    try {
        const response = await fetch(CBE_CONFIG.N3C_ENGINE_URL);
        if (response.ok) {
            const data = await response.json();
            return data.current_status?.n3c_framework?.three_ms || {};
        }
    } catch (error) {
        console.error('❌ Error fetching 3Ms data:', error);
    }
    
    // Return simulated data if API unavailable
    return {
        comphyon: 2500 + Math.random() * 1000,
        metron: 8 + Math.random() * 7,
        katalon: 800 + Math.random() * 400
    };
}

// Initialize consciousness filtering
function initializeConsciousnessFiltering() {
    console.log('🔍 Initializing consciousness-based content filtering');
    
    // Set up declarative net request rules for low consciousness content
    chrome.declarativeNetRequest.updateDynamicRules({
        removeRuleIds: [1, 2, 3], // Remove existing rules
        addRules: [
            {
                id: 1,
                priority: 1,
                action: { type: 'block' },
                condition: {
                    urlFilter: '*ads*',
                    resourceTypes: ['script', 'image', 'xmlhttprequest']
                }
            },
            {
                id: 2,
                priority: 1,
                action: { type: 'block' },
                condition: {
                    urlFilter: '*tracking*',
                    resourceTypes: ['script', 'xmlhttprequest']
                }
            }
        ]
    });
}

// Initialize KetherNet connection
function initializeKetherNetConnection() {
    console.log('🔗 Initializing KetherNet connection');
    
    // Test KetherNet connectivity
    fetch(CBE_CONFIG.KETHERNET_URL + '/health')
        .then(response => response.json())
        .then(data => {
            console.log('✅ KetherNet connection established:', data);
        })
        .catch(error => {
            console.warn('⚠️ KetherNet connection failed:', error);
        });
}

// Keyboard shortcuts
chrome.commands.onCommand.addListener(async (command) => {
    switch (command) {
        case 'toggle_cbe':
            await toggleCBE();
            break;
        case 'psi_snap_analysis':
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab) {
                await analyzeTabConsciousness(tab.id, tab.url);
            }
            break;
        case 'open_3ms_dashboard':
            // Send message to content script to show 3Ms dashboard
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (activeTab) {
                chrome.tabs.sendMessage(activeTab.id, { action: 'show_3ms_dashboard' });
            }
            break;
    }
});

console.log('🚀 CBE Background Service Worker initialized - Consciousness browsing active!');
