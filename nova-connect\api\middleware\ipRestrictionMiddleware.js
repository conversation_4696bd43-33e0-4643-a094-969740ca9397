/**
 * IP Restriction Middleware
 * 
 * This middleware provides IP-based access restrictions for API endpoints.
 */

const IpRestrictionService = require('../services/IpRestrictionService');
const { AuthorizationError } = require('../utils/errors');

/**
 * Create IP restriction middleware
 */
const createIpRestrictionMiddleware = () => {
  const ipRestrictionService = new IpRestrictionService();
  
  return async (req, res, next) => {
    try {
      // Get client IP address
      const ip = req.ip || req.connection.remoteAddress;
      
      // Check if IP is allowed
      const isAllowed = await ipRestrictionService.isAllowed(ip);
      
      if (!isAllowed) {
        throw new AuthorizationError('Access denied: IP address is not allowed');
      }
      
      next();
    } catch (error) {
      if (error instanceof AuthorizationError) {
        res.status(403).json({
          success: false,
          error: {
            code: 'IP_RESTRICTED',
            message: error.message
          }
        });
      } else {
        next(error);
      }
    }
  };
};

module.exports = createIpRestrictionMiddleware;
