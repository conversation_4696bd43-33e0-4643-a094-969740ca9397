const express = require('express');
const router = express.Router();

// Mock route handlers
router.get('/frameworks', (req, res) => {
  res.json({ data: [], message: 'Mock response for frameworks' });
});

router.get('/frameworks/:id', (req, res) => {
  res.json({ data: { id: req.params.id }, message: 'Mock response for framework by ID' });
});

router.get('/frameworks/:frameworkId/controls/:controlId', (req, res) => {
  res.json({ data: { id: req.params.controlId }, message: 'Mock response for control by ID' });
});

module.exports = router;