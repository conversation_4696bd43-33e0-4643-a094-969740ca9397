82a152771d9fc96c46f4ae662670415c
/**
 * NovaFuse Universal API Connector Database Configuration
 * 
 * This module provides MongoDB connection management for the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const logger = require('./logger');

// Default connection options
const DEFAULT_POOL_SIZE = 10;
const DEFAULT_CONNECT_TIMEOUT_MS = 30000;
const DEFAULT_SOCKET_TIMEOUT_MS = 45000;
const DEFAULT_HEARTBEAT_FREQUENCY_MS = 10000;

/**
 * Database connection manager
 */
class DatabaseManager {
  constructor(config = {}) {
    this.config = {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/novafuse-uac',
      dbName: process.env.MONGODB_DB_NAME || 'novafuse-uac',
      poolSize: parseInt(process.env.MONGODB_POOL_SIZE) || DEFAULT_POOL_SIZE,
      connectTimeoutMS: parseInt(process.env.MONGODB_CONNECT_TIMEOUT_MS) || DEFAULT_CONNECT_TIMEOUT_MS,
      socketTimeoutMS: parseInt(process.env.MONGODB_SOCKET_TIMEOUT_MS) || DEFAULT_SOCKET_TIMEOUT_MS,
      heartbeatFrequencyMS: parseInt(process.env.MONGODB_HEARTBEAT_FREQUENCY_MS) || DEFAULT_HEARTBEAT_FREQUENCY_MS,
      retryWrites: process.env.MONGODB_RETRY_WRITES !== 'false',
      retryReads: process.env.MONGODB_RETRY_READS !== 'false',
      ...config
    };
    this.connection = null;
    this.isConnected = false;
    this.connectionPromise = null;
  }

  /**
   * Connect to MongoDB
   * @returns {Promise<mongoose.Connection>} MongoDB connection
   */
  async connect() {
    // If already connecting, return the existing promise
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    // If already connected, return the existing connection
    if (this.isConnected && this.connection) {
      return this.connection;
    }

    // Create connection promise
    this.connectionPromise = new Promise(async (resolve, reject) => {
      try {
        logger.info('Connecting to MongoDB...', {
          uri: this.config.uri,
          dbName: this.config.dbName
        });

        // Configure mongoose
        mongoose.set('strictQuery', true);

        // Connect to MongoDB
        await mongoose.connect(this.config.uri, {
          dbName: this.config.dbName,
          maxPoolSize: this.config.poolSize,
          connectTimeoutMS: this.config.connectTimeoutMS,
          socketTimeoutMS: this.config.socketTimeoutMS,
          heartbeatFrequencyMS: this.config.heartbeatFrequencyMS,
          retryWrites: this.config.retryWrites,
          retryReads: this.config.retryReads
        });
        this.connection = mongoose.connection;
        this.isConnected = true;

        // Set up connection event handlers
        this.connection.on('error', err => {
          logger.error('MongoDB connection error:', {
            error: err.message
          });
          this.isConnected = false;
        });
        this.connection.on('disconnected', () => {
          logger.warn('MongoDB disconnected');
          this.isConnected = false;
        });
        this.connection.on('reconnected', () => {
          logger.info('MongoDB reconnected');
          this.isConnected = true;
        });
        logger.info('Connected to MongoDB successfully');
        resolve(this.connection);
      } catch (error) {
        logger.error('Failed to connect to MongoDB:', {
          error: error.message
        });
        this.isConnected = false;
        this.connectionPromise = null;
        reject(error);
      }
    });
    return this.connectionPromise;
  }

  /**
   * Disconnect from MongoDB
   * @returns {Promise<void>}
   */
  async disconnect() {
    if (!this.isConnected || !this.connection) {
      logger.warn('Not connected to MongoDB, nothing to disconnect');
      return;
    }
    try {
      logger.info('Disconnecting from MongoDB...');
      await mongoose.disconnect();
      this.isConnected = false;
      this.connection = null;
      this.connectionPromise = null;
      logger.info('Disconnected from MongoDB successfully');
    } catch (error) {
      logger.error('Failed to disconnect from MongoDB:', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get MongoDB connection
   * @returns {Promise<mongoose.Connection>} MongoDB connection
   */
  async getConnection() {
    if (!this.isConnected || !this.connection) {
      return this.connect();
    }
    return this.connection;
  }

  /**
   * Check if connected to MongoDB
   * @returns {boolean} Connection status
   */
  isConnected() {
    return this.isConnected;
  }

  /**
   * Create indexes for collections
   * @returns {Promise<void>}
   */
  async createIndexes() {
    if (!this.isConnected || !this.connection) {
      await this.connect();
    }
    logger.info('Creating indexes...');

    // In a real implementation, this would create indexes for all collections
    // For now, we'll just log a message

    logger.info('Indexes created successfully');
  }
}

// Create singleton instance
const databaseManager = new DatabaseManager();
module.exports = databaseManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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