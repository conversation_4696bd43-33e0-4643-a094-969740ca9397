<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Patent Diagrams</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .diagram-list {
            list-style-type: none;
            padding: 0;
        }
        .diagram-item {
            margin-bottom: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        .diagram-item:hover {
            background-color: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .diagram-link {
            display: block;
            color: #0A84FF;
            text-decoration: none;
            font-weight: bold;
            font-size: 16px;
        }
        .diagram-description {
            margin-top: 5px;
            color: #666;
            font-size: 14px;
        }
        .instructions {
            margin-top: 30px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .instructions h2 {
            margin-top: 0;
            font-size: 18px;
        }
        .instructions ol {
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NovaFuse Patent Diagrams</h1>

        <ul class="diagram-list">
            <li class="diagram-item">
                <a href="cyber-safety-architecture-fixed.html" class="diagram-link">FIG. 1: Cyber-Safety Protocol Architecture</a>
                <div class="diagram-description">Shows the overall architecture of the Cyber-Safety Protocol</div>
            </li>
            <li class="diagram-item">
                <a href="12-pillars.html" class="diagram-link">FIG. 2: 12 Pillars of Cyber-Safety Framework</a>
                <div class="diagram-description">Illustrates the 12 foundational pillars of the Cyber-Safety Framework</div>
            </li>
            <li class="diagram-item">
                <a href="12-novas.html" class="diagram-link">FIG. 3: 12+1 Universal Novas</a>
                <div class="diagram-description">Shows the 12 Universal Novas plus NovaStore as the 13th Nova</div>
            </li>
            <li class="diagram-item">
                <a href="9-continuances.html" class="diagram-link">FIG. 4: 9 Industry-Specific Continuances</a>
                <div class="diagram-description">Displays the 9 industry-specific continuances and their integration with Pillars and Novas</div>
            </li>
            <li class="diagram-item">
                <a href="alignment-architecture-final.html" class="diagram-link">FIG. 5: 3-6-9-12-13 Alignment Architecture</a>
                <div class="diagram-description">Illustrates the complete alignment architecture of the Cyber-Safety Framework</div>
            </li>
            <li class="diagram-item">
                <a href="novastore-final.html" class="diagram-link">FIG. 6: NovaStore Partner Empowerment Model</a>
                <div class="diagram-description">Shows the 82/18 revenue split model and component verification pipeline</div>
            </li>
            <li class="diagram-item">
                <a href="nist-crosswalk.html" class="diagram-link">FIG. 7: NovaConnect NIST Crosswalk</a>
                <div class="diagram-description">Demonstrates how NovaConnect exceeds NIST standards across multiple frameworks</div>
            </li>
            <li class="diagram-item">
                <a href="performance-benchmarks-fixed.html" class="diagram-link">FIG. 8: NovaConnect Performance Benchmarks</a>
                <div class="diagram-description">Illustrates NovaConnect's performance advantages compared to industry standards</div>
            </li>
        </ul>

        <div class="instructions">
            <h2>Instructions for Taking Screenshots:</h2>
            <ol>
                <li>Click on any diagram link above to open the diagram</li>
                <li>Use your operating system's screenshot tool to capture the diagram</li>
                <li>For Windows: Use Windows+Shift+S to open the snipping tool</li>
                <li>For Mac: Use Command+Shift+4 to capture a selected area</li>
                <li>Save the screenshot with the figure number and name</li>
            </ol>
            <p>These diagrams are designed to be patent-compliant with clear component relationships and data flows. All diagrams include "Inventor: David Nigel Irvin" as required for patent filing.</p>
        </div>
    </div>
</body>
</html>
