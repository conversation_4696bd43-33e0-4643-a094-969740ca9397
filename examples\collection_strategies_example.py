"""
Example of using collection strategies with the Collector Manager.

This example demonstrates how to use different collection strategies
(full, incremental, delta) with the enhanced Collector Manager.
"""

import os
import sys
import json
import datetime
import logging

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.collector_manager import CollectorManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the collection strategies example."""
    # Create a collector manager
    collector_manager = CollectorManager()
    
    # Define parameters for ServiceNow collection
    collector_id = "servicenow"
    parameters = {
        "instance_url": "https://your-instance.service-now.com",
        "username": "your-username",
        "password": "YOUR_PASSWORD",  # Replace with your ServiceNow password
        "table": "incident",
        "query": "category=software"
    }
    
    try:
        # Full collection strategy
        logger.info("Collecting with FULL strategy...")
        full_evidence = collector_manager.collect_with_strategy(
            collector_id=collector_id,
            parameters=parameters,
            strategy=collector_manager.COLLECTION_STRATEGIES['FULL']
        )
        
        logger.info(f"Full collection result: {len(full_evidence['records'])} records")
        
        # Store the collection time for incremental and delta collections
        last_collection_time = datetime.datetime.now(datetime.timezone.utc)
        
        # Wait a moment to simulate time passing
        logger.info("Waiting for a moment...")
        
        # Incremental collection strategy
        logger.info("Collecting with INCREMENTAL strategy...")
        incremental_parameters = parameters.copy()
        
        incremental_evidence = collector_manager.collect_with_strategy(
            collector_id=collector_id,
            parameters=incremental_parameters,
            strategy=collector_manager.COLLECTION_STRATEGIES['INCREMENTAL'],
            last_collection_time=last_collection_time
        )
        
        logger.info(f"Incremental collection result: {len(incremental_evidence['records'])} records")
        
        # Delta collection strategy
        logger.info("Collecting with DELTA strategy...")
        delta_parameters = parameters.copy()
        
        delta_evidence = collector_manager.collect_with_strategy(
            collector_id=collector_id,
            parameters=delta_parameters,
            strategy=collector_manager.COLLECTION_STRATEGIES['DELTA'],
            last_collection_time=last_collection_time
        )
        
        logger.info(f"Delta collection result: {len(delta_evidence['records'])} records")
        
        # Compare the results
        logger.info("\nCollection Strategy Comparison:")
        logger.info(f"Full strategy: {len(full_evidence['records'])} records")
        logger.info(f"Incremental strategy: {len(incremental_evidence['records'])} records")
        logger.info(f"Delta strategy: {len(delta_evidence['records'])} records")
        
        # Show the collection metadata
        logger.info("\nCollection Metadata:")
        logger.info(f"Full strategy: {json.dumps(full_evidence['metadata'], indent=2)}")
        logger.info(f"Incremental strategy: {json.dumps(incremental_evidence['metadata'], indent=2)}")
        logger.info(f"Delta strategy: {json.dumps(delta_evidence['metadata'], indent=2)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
