42c94f63ee45fc8777a293122419b738
// Mock AuditService
_getJestObj().mock('../../../api/services/AuditService');
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Authentication Audit Service Tests
 */

const AuthAuditService = require('../../../api/services/AuthAuditService');
const AuditService = require('../../../api/services/AuditService');
const path = require('path');
describe('AuthAuditService', () => {
  let authAuditService;
  const testDataDir = path.join(__dirname, 'test-data');
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock AuditService implementation
    AuditService.mockImplementation(() => ({
      logEvent: jest.fn().mockResolvedValue({
        id: 'test-log-id'
      }),
      getAuditLogs: jest.fn().mockResolvedValue({
        logs: [],
        total: 0,
        page: 1,
        limit: 10
      })
    }));

    // Create a new instance for each test
    authAuditService = new AuthAuditService(testDataDir);
  });
  describe('constructor', () => {
    it('should initialize with the correct data directory', () => {
      expect(AuditService).toHaveBeenCalledWith(testDataDir);
      expect(authAuditService.resourceType).toBe('auth');
    });
  });
  describe('logLoginAttempt', () => {
    it('should log successful login attempt', async () => {
      const loginData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        success: true,
        method: 'password'
      };
      await authAuditService.logLoginAttempt(loginData);
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: 'LOGIN',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          method: 'password',
          success: true,
          reason: null
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
    it('should log failed login attempt', async () => {
      const loginData = {
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        success: false,
        reason: 'Invalid password',
        method: 'password'
      };
      await authAuditService.logLoginAttempt(loginData);
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: null,
        action: 'LOGIN',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          method: 'password',
          success: false,
          reason: 'Invalid password'
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'failure',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
    it('should include additional details', async () => {
      const loginData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        success: true,
        method: 'oauth2',
        details: {
          provider: 'google',
          email: '<EMAIL>'
        }
      };
      await authAuditService.logLoginAttempt(loginData);
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: 'LOGIN',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          method: 'oauth2',
          success: true,
          reason: null,
          provider: 'google',
          email: '<EMAIL>'
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
  });
  describe('logLogout', () => {
    it('should log logout event', async () => {
      const logoutData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0'
      };
      await authAuditService.logLogout(logoutData);
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: 'LOGOUT',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {},
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
  });
  describe('logRegistration', () => {
    it('should log successful registration', async () => {
      const registrationData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        success: true,
        details: {
          email: '<EMAIL>'
        }
      };
      await authAuditService.logRegistration(registrationData);
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: 'REGISTER',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          success: true,
          reason: null,
          email: '<EMAIL>'
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
  });
  describe('logTwoFactorAuth', () => {
    it('should log two-factor authentication setup', async () => {
      const twoFactorData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        action: 'setup',
        success: true
      };
      await authAuditService.logTwoFactorAuth(twoFactorData);
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: '2FA_SETUP',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          success: true,
          reason: null
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
    it('should log failed two-factor authentication verification', async () => {
      const twoFactorData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        action: 'verify',
        success: false,
        reason: 'Invalid token'
      };
      await authAuditService.logTwoFactorAuth(twoFactorData);
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: '2FA_VERIFY',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          success: false,
          reason: 'Invalid token'
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'failure',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
  });
  describe('getAuthAuditLogs', () => {
    it('should get authentication audit logs with filters', async () => {
      const filters = {
        userId: 'user-123',
        startDate: '2023-01-01',
        endDate: '2023-01-31',
        action: 'LOGIN',
        status: 'success',
        page: 1,
        limit: 10
      };
      await authAuditService.getAuthAuditLogs(filters);
      expect(authAuditService.auditService.getAuditLogs).toHaveBeenCalledWith({
        userId: 'user-123',
        startDate: '2023-01-01',
        endDate: '2023-01-31',
        action: 'LOGIN',
        status: 'success',
        page: 1,
        limit: 10,
        resourceType: 'auth'
      });
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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