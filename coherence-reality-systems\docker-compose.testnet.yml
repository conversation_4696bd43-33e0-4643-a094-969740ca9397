version: '3.8'

services:
  node1:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kethernet-node1
    environment:
      - NODE_ID=1
      - PORT=8080
      - VALIDATOR_PRIVATE_KEY=${VALIDATOR1_PRIVATE_KEY}
      - BOOTNODES=node2,node3
      - NODE_ENV=testnet
    ports:
      - "8080:8080"
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 4G
    networks:
      - kethernet-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  node2:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kethernet-node2
    environment:
      - NODE_ID=2
      - PORT=8081
      - VALIDATOR_PRIVATE_KEY=${VALIDATOR2_PRIVATE_KEY}
      - BOOTNODES=node1,node3
      - NODE_ENV=testnet
    ports:
      - "8081:8080"
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 4G
    networks:
      - kethernet-net
    depends_on:
      - node1

  node3:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kethernet-node3
    environment:
      - NODE_ID=3
      - PORT=8082
      - VALIDATOR_PRIVATE_KEY=${VALIDATOR3_PRIVATE_KEY}
      - BOOTNODES=node1,node2
      - NODE_ENV=testnet
    ports:
      - "8082:8080"
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 4G
    networks:
      - kethernet-net
    depends_on:
      - node1
      - node2

networks:
  kethernet-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
