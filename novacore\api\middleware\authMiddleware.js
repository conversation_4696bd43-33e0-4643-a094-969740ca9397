/**
 * NovaCore Authentication Middleware
 * 
 * This middleware handles authentication and authorization for the NovaCore API.
 */

const jwt = require('jsonwebtoken');
const config = require('../../config');
const logger = require('../../config/logger');
const { AuthenticationError, AuthorizationError } = require('../utils/errors');

/**
 * Authenticate user using JWT token or API key
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticate = (req, res, next) => {
  try {
    // Check for API key
    const apiKey = req.headers['x-api-key'];
    
    if (apiKey) {
      // Validate API key
      if (apiKey === config.auth.apiKey) {
        // Set user info for API key
        req.user = {
          id: 'api',
          role: 'api',
          permissions: ['*'] // API key has all permissions
        };
        
        return next();
      }
      
      throw new AuthenticationError('Invalid API key');
    }
    
    // Check for JWT token
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('No token provided');
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify token
    jwt.verify(token, config.auth.jwtSecret, (err, decoded) => {
      if (err) {
        throw new AuthenticationError('Invalid token');
      }
      
      // Set user info from token
      req.user = decoded;
      
      next();
    });
  } catch (error) {
    logger.error('Authentication error:', { error });
    
    if (error instanceof AuthenticationError) {
      return res.status(401).json({
        success: false,
        error: {
          message: error.message,
          code: 'AUTHENTICATION_ERROR'
        }
      });
    }
    
    return res.status(401).json({
      success: false,
      error: {
        message: 'Authentication failed',
        code: 'AUTHENTICATION_ERROR'
      }
    });
  }
};

/**
 * Authorize user based on permissions
 * @param {string} permission - Required permission
 * @returns {Function} - Express middleware
 */
const authorize = (permission) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }
      
      // Check if user has wildcard permission
      if (req.user.permissions && req.user.permissions.includes('*')) {
        return next();
      }
      
      // Check if user has required permission
      if (req.user.permissions && req.user.permissions.includes(permission)) {
        return next();
      }
      
      throw new AuthorizationError(`User does not have permission: ${permission}`);
    } catch (error) {
      logger.error('Authorization error:', { error, permission });
      
      if (error instanceof AuthorizationError) {
        return res.status(403).json({
          success: false,
          error: {
            message: error.message,
            code: 'AUTHORIZATION_ERROR'
          }
        });
      }
      
      return res.status(403).json({
        success: false,
        error: {
          message: 'Authorization failed',
          code: 'AUTHORIZATION_ERROR'
        }
      });
    }
  };
};

module.exports = {
  authenticate,
  authorize
};
