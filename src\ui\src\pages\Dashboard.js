import React from 'react';
import { Box, Grid, Typography, Card, CardContent, LinearProgress } from '@mui/material';
import VisualizationRenderer from '../components/visualizations/VisualizationRenderer';
import { useControl } from '../contexts/ControlContext';

function Dashboard() {
  const { getControlValue } = useControl();

  // Get tensor health and entropy values
  const tensorHealth = getControlValue('tensor-health') || 0;
  const tensorEntropy = getControlValue('tensor-entropy') || 0;

  // Mock system metrics
  const systemMetrics = {
    'System Performance': 0.87,
    'Visualization Update Rate': 30,
    'Active Connections': 5,
    'Memory Usage': 0.45,
    'CPU Usage': 0.32
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        System Dashboard
      </Typography>

      <Grid container spacing={3}>
        {/* Tensor Health Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Tensor Health
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body1" sx={{ mr: 1, minWidth: 40 }}>
                  {Math.round(tensorHealth * 100)}%
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={tensorHealth * 100}
                  color={tensorHealth > 0.7 ? 'success' : tensorHealth > 0.3 ? 'warning' : 'error'}
                  sx={{ flexGrow: 1, height: 10, borderRadius: 5 }}
                />
              </Box>

              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                Entropy Containment
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="body1" sx={{ mr: 1, minWidth: 40 }}>
                  {Math.round(tensorEntropy * 100)}%
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={tensorEntropy * 100}
                  color={tensorEntropy < 0.3 ? 'success' : tensorEntropy < 0.7 ? 'warning' : 'error'}
                  sx={{ flexGrow: 1, height: 10, borderRadius: 5 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Metrics Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Metrics
              </Typography>

              {Object.entries(systemMetrics).map(([metric, value]) => (
                <Box key={metric} sx={{ mb: 2 }}>
                  <Typography variant="body1" gutterBottom>
                    {metric}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {typeof value === 'number' ? (
                      <>
                        <Typography variant="body1" sx={{ mr: 1, minWidth: 40 }}>
                          {value > 1 ? Math.round(value) : Math.round(value * 100) + '%'}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={value > 1 ? (value / 100) * 100 : value * 100}
                          sx={{ flexGrow: 1, height: 10, borderRadius: 5 }}
                        />
                      </>
                    ) : (
                      <Typography variant="body1">
                        {value}
                      </Typography>
                    )}
                  </Box>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Visualization Preview Card */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Visualization Preview
              </Typography>
              <Box
                sx={{
                  height: 300,
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body1" color="text.secondary">
                  Visualization preview will be displayed here
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
