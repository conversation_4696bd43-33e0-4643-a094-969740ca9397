/**
 * GraphQL Schema
 * 
 * This file defines the GraphQL schema for NovaConnect.
 */

const { gql } = require('apollo-server-express');

const typeDefs = gql`
  # Connector types
  type Connector {
    id: ID!
    name: String!
    description: String
    category: String
    version: String
    author: String
    authentication: Authentication
    endpoints: [Endpoint]
    createdAt: String
    updatedAt: String
  }

  type Authentication {
    type: String!
    fields: [AuthField]
  }

  type AuthField {
    id: String!
    name: String!
    description: String
    type: String!
    required: Boolean
    sensitive: Boolean
  }

  type Endpoint {
    id: ID!
    name: String!
    description: String
    method: String!
    path: String!
    parameters: [Parameter]
    response: Response
  }

  type Parameter {
    id: String!
    name: String!
    description: String
    type: String!
    required: Boolean
    default: String
  }

  type Response {
    type: String!
    schema: String
  }

  # Credential types
  type Credential {
    id: ID!
    connectorId: String!
    name: String
    createdAt: String
    updatedAt: String
  }

  # Execution types
  type ExecutionResult {
    id: ID!
    connectorId: String!
    endpointId: String!
    status: String!
    data: JSON
    error: String
    startTime: String
    endTime: String
    duration: Int
  }

  # RBAC types
  type Role {
    id: ID!
    name: String!
    description: String
    permissions: [String]
    isSystem: Boolean
    createdAt: String
    updatedAt: String
  }

  type Permission {
    id: ID!
    name: String!
    description: String
    resource: String!
    action: String!
    createdAt: String
    updatedAt: String
  }

  # Billing types
  type BillingPlan {
    id: ID!
    name: String!
    price: Float
    features: JSON
  }

  type BillingCycle {
    id: ID!
    name: String!
    months: Int
    discount: Float
  }

  type Subscription {
    id: ID!
    tenantId: String!
    planId: String!
    planName: String!
    cycleId: String
    cycleName: String
    price: Float
    features: JSON
    startDate: String
    endDate: String
    status: String!
    createdAt: String
    updatedAt: String
  }

  # Marketplace types
  type MarketplacePlan {
    id: ID!
    name: String!
    description: String
    features: JSON
  }

  type Tenant {
    id: ID!
    name: String!
    planId: String!
    planName: String!
    features: JSON
    status: String!
    createdAt: String
    updatedAt: String
  }

  # Custom scalar for JSON
  scalar JSON

  # Queries
  type Query {
    # Connector queries
    connectors: [Connector]
    connector(id: ID!): Connector
    connectorsByCategory(category: String!): [Connector]
    searchConnectors(query: String!): [Connector]

    # Credential queries
    credentials: [Credential]
    credential(id: ID!): Credential

    # RBAC queries
    roles: [Role]
    role(id: ID!): Role
    permissions: [Permission]
    permission(id: ID!): Permission
    userRoles(userId: ID!): [Role]
    userPermissions(userId: ID!): [String]
    hasPermission(userId: ID!, permissionId: String!): Boolean

    # Billing queries
    billingPlans: [BillingPlan]
    billingPlan(id: ID!): BillingPlan
    billingCycles: [BillingCycle]
    billingCycle(id: ID!): BillingCycle
    subscriptions: [Subscription]
    subscription(id: ID!): Subscription
    calculatePrice(planId: ID!, cycleId: ID!): JSON

    # Marketplace queries
    marketplacePlans: [MarketplacePlan]
    marketplacePlan(id: ID!): MarketplacePlan
    tenants: [Tenant]
    tenant(id: ID!): Tenant
    tenantStatus(id: ID!): JSON
  }

  # Mutations
  type Mutation {
    # Connector mutations
    registerConnector(input: ConnectorInput!): Connector
    updateConnector(id: ID!, input: ConnectorInput!): Connector
    deleteConnector(id: ID!): Boolean

    # Credential mutations
    storeCredentials(connectorId: ID!, credentials: JSON!): Credential
    deleteCredentials(id: ID!): Boolean
    testConnection(id: ID!, connectorId: ID!): JSON

    # Execution mutations
    executeEndpoint(connectorId: ID!, endpointId: ID!, credentialId: ID!, parameters: JSON): ExecutionResult

    # RBAC mutations
    createRole(input: RoleInput!): Role
    updateRole(id: ID!, input: RoleInput!): Role
    deleteRole(id: ID!): Boolean
    createPermission(input: PermissionInput!): Permission
    updatePermission(id: ID!, input: PermissionInput!): Permission
    deletePermission(id: ID!): Boolean
    assignRoleToUser(userId: ID!, roleId: ID!): Boolean
    removeRoleFromUser(userId: ID!, roleId: ID!): Boolean

    # Billing mutations
    createSubscription(tenantId: ID!, planId: ID!, cycleId: ID!): Subscription
    updateSubscription(id: ID!, planId: ID!, cycleId: ID!): Subscription
    cancelSubscription(id: ID!): Boolean
    reportUsage(subscriptionId: ID!, metricId: String!, quantity: Float!): Boolean

    # Marketplace mutations
    provisionTenant(input: TenantInput!): Tenant
    updateTenantPlan(tenantId: ID!, planId: ID!): Tenant
    deprovisionTenant(tenantId: ID!): Boolean
  }

  # Input types
  input ConnectorInput {
    name: String!
    description: String
    category: String
    version: String
    author: String
    authentication: AuthenticationInput
    endpoints: [EndpointInput]
  }

  input AuthenticationInput {
    type: String!
    fields: [AuthFieldInput]
  }

  input AuthFieldInput {
    id: String!
    name: String!
    description: String
    type: String!
    required: Boolean
    sensitive: Boolean
  }

  input EndpointInput {
    id: ID!
    name: String!
    description: String
    method: String!
    path: String!
    parameters: [ParameterInput]
    response: ResponseInput
  }

  input ParameterInput {
    id: String!
    name: String!
    description: String
    type: String!
    required: Boolean
    default: String
  }

  input ResponseInput {
    type: String!
    schema: String
  }

  input RoleInput {
    name: String!
    description: String
    permissions: [String]!
  }

  input PermissionInput {
    name: String!
    description: String
    resource: String!
    action: String!
  }

  input TenantInput {
    tenantId: ID
    name: String!
    planId: ID!
  }

  # Subscriptions
  type Subscription {
    executionCompleted(connectorId: ID, endpointId: ID): ExecutionResult
  }
`;

module.exports = typeDefs;
