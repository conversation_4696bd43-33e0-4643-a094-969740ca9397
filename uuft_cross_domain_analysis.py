#!/usr/bin/env python3
# UUFT Cross-Domain Analysis
# This script analyzes patterns across different domains to identify universal patterns

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import logging
import json
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("uuft_cross_domain.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("UUFT_Cross_Domain")

# Base directory for all data
BASE_DIR = "D:/Archives"
RESULTS_DIR = os.path.join(BASE_DIR, "Results")

# Load results from individual pattern tests
def load_results():
    """Load results from individual pattern tests."""
    logger.info("Loading results from individual pattern tests...")
    
    results = {}
    
    # Load 18/82 pattern results
    results_file = os.path.join(RESULTS_DIR, "1882_pattern_results.json")
    if os.path.exists(results_file):
        with open(results_file, 'r') as f:
            results["1882_pattern"] = json.load(f)
    
    # Load Pi relationships results
    results_file = os.path.join(RESULTS_DIR, "pi_relationships_results.json")
    if os.path.exists(results_file):
        with open(results_file, 'r') as f:
            results["pi_relationships"] = json.load(f)
    
    # Load Trinity patterns results
    results_file = os.path.join(RESULTS_DIR, "trinity_patterns_results.json")
    if os.path.exists(results_file):
        with open(results_file, 'r') as f:
            results["trinity_patterns"] = json.load(f)
    
    # Load Nested/Fractal patterns results
    results_file = os.path.join(RESULTS_DIR, "nested_patterns_results.json")
    if os.path.exists(results_file):
        with open(results_file, 'r') as f:
            results["nested_patterns"] = json.load(f)
    
    return results

# Analyze pattern presence across domains
def analyze_pattern_presence(results):
    """Analyze the presence of patterns across domains."""
    logger.info("Analyzing pattern presence across domains...")
    
    # Initialize pattern presence matrix
    domains = ["cosmological", "biological", "social", "technological"]
    patterns = ["1882_pattern", "pi_relationships", "trinity_patterns", "nested_patterns"]
    
    presence_matrix = pd.DataFrame(0, index=domains, columns=patterns)
    
    # Count pattern presence in each domain
    for pattern in patterns:
        if pattern in results:
            for domain in domains:
                if domain in results[pattern]:
                    # Count datasets with pattern present
                    pattern_present_count = 0
                    total_datasets = 0
                    
                    for dataset_result in results[pattern][domain]:
                        if "error" not in dataset_result:
                            total_datasets += 1
                            
                            if pattern == "1882_pattern" and dataset_result.get("is_pattern_present", False):
                                pattern_present_count += 1
                            elif pattern == "pi_relationships" and (dataset_result.get("is_pi_pattern_present", False) or 
                                                                  dataset_result.get("is_pi_10e3_pattern_present", False)):
                                pattern_present_count += 1
                            elif pattern == "trinity_patterns" and dataset_result.get("is_trinity_pattern_present", False):
                                pattern_present_count += 1
                            elif pattern == "nested_patterns" and (dataset_result.get("is_nested_pattern_present", False) or 
                                                                 dataset_result.get("is_fractal_pattern_present", False)):
                                pattern_present_count += 1
                    
                    # Calculate percentage of datasets with pattern present
                    if total_datasets > 0:
                        presence_matrix.loc[domain, pattern] = pattern_present_count / total_datasets * 100
    
    # Create heatmap visualization
    plt.figure(figsize=(10, 8))
    sns.heatmap(presence_matrix, annot=True, cmap="YlGnBu", fmt=".1f")
    plt.title("Pattern Presence Across Domains (%)")
    plt.tight_layout()
    
    # Save visualization
    os.makedirs(RESULTS_DIR, exist_ok=True)
    plt.savefig(os.path.join(RESULTS_DIR, "pattern_presence_heatmap.png"))
    plt.close()
    
    return presence_matrix

# Analyze pattern strength across domains
def analyze_pattern_strength(results):
    """Analyze the strength of patterns across domains."""
    logger.info("Analyzing pattern strength across domains...")
    
    # Initialize pattern strength data
    domains = ["cosmological", "biological", "social", "technological"]
    pattern_strength = {
        "1882_pattern": {domain: [] for domain in domains},
        "pi_relationships": {domain: [] for domain in domains},
        "trinity_patterns": {domain: [] for domain in domains},
        "nested_patterns": {domain: [] for domain in domains}
    }
    
    # Extract pattern strength metrics
    for pattern in pattern_strength.keys():
        if pattern in results:
            for domain in domains:
                if domain in results[pattern]:
                    for dataset_result in results[pattern][domain]:
                        if "error" not in dataset_result:
                            # Extract strength metrics based on pattern type
                            if pattern == "1882_pattern":
                                # Use proximity to ideal percentages (lower is better)
                                if "proximity_to_18_percent" in dataset_result and "proximity_to_82_percent" in dataset_result:
                                    avg_proximity = (dataset_result["proximity_to_18_percent"] + dataset_result["proximity_to_82_percent"]) / 2
                                    # Convert to strength (100 - proximity)
                                    strength = max(0, 100 - avg_proximity)
                                    pattern_strength[pattern][domain].append(strength)
                            
                            elif pattern == "pi_relationships":
                                # Use count of relationships found
                                total_relationships = dataset_result.get("total_pi_relationships", 0) + dataset_result.get("total_pi_10e3_relationships", 0)
                                # Normalize to 0-100 scale (assuming max of 20 relationships is 100%)
                                strength = min(100, total_relationships * 5)
                                pattern_strength[pattern][domain].append(strength)
                            
                            elif pattern == "trinity_patterns":
                                # Use count of trinity patterns found
                                trinity_count = dataset_result.get("trinity_patterns_count", 0)
                                # Normalize to 0-100 scale (assuming max of 5 patterns is 100%)
                                strength = min(100, trinity_count * 20)
                                pattern_strength[pattern][domain].append(strength)
                            
                            elif pattern == "nested_patterns":
                                # Combine nested and fractal pattern presence
                                is_nested = dataset_result.get("is_nested_pattern_present", False)
                                is_fractal = dataset_result.get("is_fractal_pattern_present", False)
                                
                                # Use Hurst exponent as strength metric
                                hurst = dataset_result.get("hurst_exponent", 0.5)
                                # Convert to strength (0-100 scale)
                                # Hurst of 0.5 = 0%, Hurst of 1.0 = 100%
                                hurst_strength = max(0, min(100, (abs(hurst - 0.5) / 0.5) * 100))
                                
                                # If patterns are present, boost strength
                                strength = hurst_strength
                                if is_nested:
                                    strength += 25
                                if is_fractal:
                                    strength += 25
                                
                                pattern_strength[pattern][domain].append(min(100, strength))
    
    # Calculate average strength for each pattern and domain
    avg_strength = pd.DataFrame(index=domains, columns=list(pattern_strength.keys()))
    
    for pattern in pattern_strength.keys():
        for domain in domains:
            values = pattern_strength[pattern][domain]
            if values:
                avg_strength.loc[domain, pattern] = np.mean(values)
            else:
                avg_strength.loc[domain, pattern] = 0
    
    # Create bar chart visualization
    plt.figure(figsize=(12, 8))
    
    # Set width of bars
    bar_width = 0.2
    index = np.arange(len(domains))
    
    # Plot bars for each pattern
    for i, pattern in enumerate(pattern_strength.keys()):
        plt.bar(index + i*bar_width, avg_strength[pattern], bar_width, 
                label=pattern.replace('_', ' ').title())
    
    plt.xlabel('Domain')
    plt.ylabel('Average Pattern Strength (%)')
    plt.title('Pattern Strength Across Domains')
    plt.xticks(index + bar_width * 1.5, domains)
    plt.legend()
    plt.tight_layout()
    
    # Save visualization
    plt.savefig(os.path.join(RESULTS_DIR, "pattern_strength_comparison.png"))
    plt.close()
    
    return avg_strength

# Analyze cross-domain correlations
def analyze_cross_domain_correlations(results):
    """Analyze correlations of pattern presence across domains."""
    logger.info("Analyzing cross-domain correlations...")
    
    # Extract pattern presence data
    domains = ["cosmological", "biological", "social", "technological"]
    patterns = ["1882_pattern", "pi_relationships", "trinity_patterns", "nested_patterns"]
    
    # Create a dataset mapping
    dataset_mapping = {}
    
    for pattern in patterns:
        if pattern in results:
            for domain in domains:
                if domain in results[pattern]:
                    for dataset_result in results[pattern][domain]:
                        if "dataset_name" in dataset_result and "error" not in dataset_result:
                            dataset_key = f"{domain}_{dataset_result['dataset_name']}"
                            
                            if dataset_key not in dataset_mapping:
                                dataset_mapping[dataset_key] = {
                                    "domain": domain,
                                    "dataset_name": dataset_result["dataset_name"]
                                }
                            
                            # Record pattern presence
                            if pattern == "1882_pattern":
                                dataset_mapping[dataset_key]["1882_present"] = dataset_result.get("is_pattern_present", False)
                            elif pattern == "pi_relationships":
                                dataset_mapping[dataset_key]["pi_present"] = dataset_result.get("is_pi_pattern_present", False) or dataset_result.get("is_pi_10e3_pattern_present", False)
                            elif pattern == "trinity_patterns":
                                dataset_mapping[dataset_key]["trinity_present"] = dataset_result.get("is_trinity_pattern_present", False)
                            elif pattern == "nested_patterns":
                                dataset_mapping[dataset_key]["nested_present"] = dataset_result.get("is_nested_pattern_present", False) or dataset_result.get("is_fractal_pattern_present", False)
    
    # Convert to DataFrame
    df = pd.DataFrame.from_dict(dataset_mapping, orient='index')
    
    # Fill missing values with False
    for col in ["1882_present", "pi_present", "trinity_present", "nested_present"]:
        if col in df.columns:
            df[col] = df[col].fillna(False)
    
    # Calculate correlation matrix
    pattern_cols = [col for col in ["1882_present", "pi_present", "trinity_present", "nested_present"] if col in df.columns]
    
    if len(pattern_cols) > 1:
        corr_matrix = df[pattern_cols].corr()
        
        # Create correlation heatmap
        plt.figure(figsize=(10, 8))
        sns.heatmap(corr_matrix, annot=True, cmap="coolwarm", vmin=-1, vmax=1)
        plt.title("Pattern Correlation Matrix")
        plt.tight_layout()
        
        # Save visualization
        plt.savefig(os.path.join(RESULTS_DIR, "pattern_correlation_matrix.png"))
        plt.close()
        
        return corr_matrix
    else:
        logger.warning("Insufficient pattern data for correlation analysis")
        return None

# Generate cross-domain analysis report
def generate_report(presence_matrix, strength_matrix, correlation_matrix):
    """Generate a comprehensive cross-domain analysis report."""
    logger.info("Generating cross-domain analysis report...")
    
    report = {
        "timestamp": datetime.now().isoformat(),
        "pattern_presence": presence_matrix.to_dict(),
        "pattern_strength": strength_matrix.to_dict(),
        "pattern_correlations": correlation_matrix.to_dict() if correlation_matrix is not None else None,
        "summary": {
            "domains_analyzed": list(presence_matrix.index),
            "patterns_analyzed": list(presence_matrix.columns),
            "overall_pattern_presence": float(presence_matrix.values.mean()),
            "strongest_pattern": presence_matrix.columns[presence_matrix.values.mean(axis=0).argmax()],
            "strongest_domain": presence_matrix.index[presence_matrix.values.mean(axis=1).argmax()],
            "pattern_correlation": float(correlation_matrix.values.mean()) if correlation_matrix is not None else None
        },
        "conclusions": {
            "universal_patterns_indicated": float(presence_matrix.values.mean()) > 50,
            "cross_domain_consistency": float(presence_matrix.values.std()) < 25,
            "pattern_interdependence": float(correlation_matrix.values.mean()) > 0.5 if correlation_matrix is not None else None
        }
    }
    
    # Save report to JSON file
    report_file = os.path.join(RESULTS_DIR, "cross_domain_analysis_report.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Cross-domain analysis report saved to {report_file}")
    
    return report

# Main function
def main():
    """Main function to perform cross-domain analysis."""
    logger.info("Starting cross-domain analysis...")
    
    # Create results directory if it doesn't exist
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    # Load results from individual pattern tests
    results = load_results()
    
    if not results:
        logger.error("No results found. Please run the individual pattern tests first.")
        return
    
    # Analyze pattern presence across domains
    presence_matrix = analyze_pattern_presence(results)
    
    # Analyze pattern strength across domains
    strength_matrix = analyze_pattern_strength(results)
    
    # Analyze cross-domain correlations
    correlation_matrix = analyze_cross_domain_correlations(results)
    
    # Generate comprehensive report
    report = generate_report(presence_matrix, strength_matrix, correlation_matrix)
    
    logger.info("Cross-domain analysis complete.")
    
    # Print summary
    print("\n=== UUFT Cross-Domain Analysis Summary ===")
    print(f"Overall Pattern Presence: {report['summary']['overall_pattern_presence']:.1f}%")
    print(f"Strongest Pattern: {report['summary']['strongest_pattern'].replace('_', ' ').title()}")
    print(f"Strongest Domain: {report['summary']['strongest_domain'].capitalize()}")
    
    if report['summary']['pattern_correlation'] is not None:
        print(f"Pattern Correlation: {report['summary']['pattern_correlation']:.2f}")
    
    print("\nConclusions:")
    print(f"Universal Patterns Indicated: {report['conclusions']['universal_patterns_indicated']}")
    print(f"Cross-Domain Consistency: {report['conclusions']['cross_domain_consistency']}")
    
    if report['conclusions']['pattern_interdependence'] is not None:
        print(f"Pattern Interdependence: {report['conclusions']['pattern_interdependence']}")
    
    print("\nDetailed results and visualizations saved to:", RESULTS_DIR)

if __name__ == "__main__":
    main()
