const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');

// Test configuration
const CONFIG = {
  serverUrl: 'http://localhost:8080',
  testDuration: 45000, // 45 seconds
  nodeCount: 5,
  logFile: 'coherium-test.log',
  resultsFile: 'test-results.json'
};

// Test results storage
const testResults = {
  startTime: new Date().toISOString(),
  endTime: null,
  duration: 0,
  nodes: [],
  networkStats: {
    initial: {},
    final: {}
  },
  testCases: {
    nodeRegistration: { passed: 0, failed: 0 },
    validation: { passed: 0, failed: 0 },
    balanceCheck: { passed: 0, failed: 0 },
    rewardClaim: { passed: 0, failed: 0 },
    networkStats: { passed: 0, failed: 0 }
  },
  errors: []
};

// Logger
const logger = {
  log: (message, data = null) => {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    
    if (data) {
      console.log(JSON.stringify(data, null, 2));
      fs.appendFileSync(CONFIG.logFile, `${logEntry}\n${JSON.stringify(data, null, 2)}\n`);
    } else {
      fs.appendFileSync(CONFIG.logFile, `${logEntry}\n`);
    }
  },
  error: (message, error = null) => {
    const timestamp = new Date().toISOString();
    const errorEntry = `[${timestamp}] ERROR: ${message}`;
    console.error(errorEntry);
    
    if (error) {
      console.error(error);
      testResults.errors.push({
        message,
        error: error.message || String(error),
        timestamp: new Date().toISOString()
      });
      fs.appendFileSync(CONFIG.logFile, `${errorEntry}\n${JSON.stringify(error, null, 2)}\n`);
    } else {
      fs.appendFileSync(CONFIG.logFile, `${errorEntry}\n`);
    }
  }
};

// Initialize log file
fs.writeFileSync(CONFIG.logFile, `=== Coherium Test Run - ${new Date().toISOString()} ===\n\n`);

// Test case: Node Registration
const testNodeRegistration = async (node) => {
  try {
    const response = await axios.post(
      `${CONFIG.serverUrl}/crown-consensus`,
      { content_id: `reg-test-${Date.now()}` },
      {
        headers: {
          'x-node-id': node.id,
          'x-neural-level': node.neural,
          'x-informational-level': node.informational,
          'x-coherence-level': node.coherence
        }
      }
    );
    
    if (response.data && response.data.consensus_achieved !== undefined) {
      testResults.testCases.nodeRegistration.passed++;
      return true;
    }
    
    throw new Error('Invalid response format');
  } catch (error) {
    testResults.testCases.nodeRegistration.failed++;
    logger.error(`Node registration failed for ${node.id}`, error);
    return false;
  }
};

// Test case: Node Validation
const testNodeValidation = async (node) => {
  try {
    const response = await axios.post(
      `${CONFIG.serverUrl}/crown-consensus`,
      { content_id: `val-test-${Date.now()}` },
      {
        headers: {
          'x-node-id': node.id,
          'x-neural-level': node.neural,
          'x-informational-level': node.informational,
          'x-coherence-level': node.coherence
        }
      }
    );
    
    if (response.data && response.data.consensus_achieved !== undefined) {
      testResults.testCases.validation.passed++;
      node.validations++;
      return true;
    }
    
    throw new Error('Invalid response format');
  } catch (error) {
    testResults.testCases.validation.failed++;
    logger.error(`Validation failed for node ${node.id}`, error);
    return false;
  }
};

// Test case: Balance Check
const testBalanceCheck = async (node) => {
  try {
    const response = await axios.get(`${CONFIG.serverUrl}/coherium/balance`, {
      headers: { 'x-node-id': node.id }
    });
    
    if (response.data && response.data.success) {
      node.balance = response.data.balance || 0;
      node.pending = response.data.pending || 0;
      testResults.testCases.balanceCheck.passed++;
      return true;
    }
    
    throw new Error('Invalid response format');
  } catch (error) {
    testResults.testCases.balanceCheck.failed++;
    logger.error(`Balance check failed for node ${node.id}`, error);
    return false;
  }
};

// Test case: Reward Claim
const testRewardClaim = async (node) => {
  try {
    const response = await axios.post(
      `${CONFIG.serverUrl}/coherium/claim`,
      {},
      { headers: { 'x-node-id': node.id } }
    );
    
    if (response.data && response.data.success) {
      const amount = response.data.amount || 0;
      if (amount > 0) {
        node.rewards += amount;
        node.balance = response.data.newBalance;
        testResults.testCases.rewardClaim.passed++;
        return true;
      }
      return false; // No rewards to claim is not an error
    }
    
    throw new Error('Invalid response format');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      // No rewards to claim is not an error
      return false;
    }
    testResults.testCases.rewardClaim.failed++;
    logger.error(`Reward claim failed for node ${node.id}`, error);
    return false;
  }
};

// Test case: Network Stats
const testNetworkStats = async () => {
  try {
    const response = await axios.get(`${CONFIG.serverUrl}/coherium/network`);
    
    if (response.data && response.data.success) {
      testResults.testCases.networkStats.passed++;
      return response.data.stats;
    }
    
    throw new Error('Invalid response format');
  } catch (error) {
    testResults.testCases.networkStats.failed++;
    logger.error('Failed to get network stats', error);
    return null;
  }
};

// Generate test nodes with random consciousness levels
const generateTestNodes = (count) => {
  return Array.from({ length: count }, (_, i) => {
    const nodeId = `test-node-${crypto.randomBytes(4).toString('hex')}`;
    const neural = 2 + Math.random() * 2; // 2.0 - 4.0
    const informational = 1.5 + Math.random() * 1.5; // 1.5 - 3.0
    const coherence = 1.8 + Math.random() * 1.2; // 1.8 - 3.0
    
    const node = {
      id: nodeId,
      neural,
      informational,
      coherence,
      balance: 0,
      pending: 0,
      rewards: 0,
      validations: 0,
      lastActive: 0,
      testResults: {
        registrations: 0,
        validations: 0,
        balanceChecks: 0,
        rewardClaims: 0,
        lastError: null
      }
    };
    
    logger.log(`Generated test node: ${nodeId}`, {
      neural: neural.toFixed(2),
      informational: informational.toFixed(2),
      coherence: coherence.toFixed(2)
    });
    
    return node;
  });
};

// Run a random test for a node
const runRandomTest = async (node) => {
  const now = Date.now();
  const timeSinceLastActivity = now - (node.lastActive || 0);
  
  // Ensure nodes don't perform actions too frequently
  if (timeSinceLastActivity < 100) {
    return false;
  }
  
  node.lastActive = now;
  
  // Randomly select a test to run
  const testType = Math.random();
  
  try {
    if (testType < 0.05) {
      // 5% chance: Register node (if not already registered)
      if (node.registered !== true) {
        const success = await testNodeRegistration(node);
        if (success) {
          node.registered = true;
          node.testResults.registrations++;
        }
      }
      return true;
    } else if (testType < 0.6) {
      // 55% chance: Perform validation
      if (node.registered) {
        const success = await testNodeValidation(node);
        if (success) {
          node.testResults.validations++;
        }
        return success;
      }
    } else if (testType < 0.9) {
      // 30% chance: Check balance
      if (node.registered) {
        const success = await testBalanceCheck(node);
        if (success) {
          node.testResults.balanceChecks++;
        }
        return success;
      }
    } else {
      // 10% chance: Claim rewards
      if (node.registered) {
        const success = await testRewardClaim(node);
        if (success) {
          node.testResults.rewardClaims++;
        }
        return success;
      }
    }
    
    return false;
  } catch (error) {
    node.testResults.lastError = error.message || String(error);
    logger.error(`Test failed for node ${node.id}`, error);
    return false;
  }
};

// Network stats helper (using the testNetworkStats function instead)

// Generate test report
const generateReport = (nodes, startTime, endTime, initialStats, finalStats) => {
  const duration = (endTime - startTime) / 1000; // in seconds
  
  const report = {
    testRun: {
      startTime: new Date(startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      duration: `${duration.toFixed(2)} seconds`,
      nodes: nodes.length,
      blocksMined: finalStats.block_height - initialStats.block_height,
      blocksPerMinute: ((finalStats.block_height - initialStats.block_height) / (duration / 60)).toFixed(2)
    },
    network: {
      initial: initialStats,
      final: finalStats,
      supplyChange: finalStats.total_supply - initialStats.total_supply
    },
    nodes: nodes.map(node => ({
      id: node.id,
      consciousness: {
        neural: node.neural.toFixed(2),
        informational: node.informational.toFixed(2),
        coherence: node.coherence.toFixed(2)
      },
      balance: node.balance,
      pending: node.pending,
      rewards: node.rewards,
      validations: node.validations,
      testResults: node.testResults
    })),
    testResults: testResults.testCases,
    successRate: {
      nodeRegistration: (testResults.testCases.nodeRegistration.passed / 
        (testResults.testCases.nodeRegistration.passed + testResults.testCases.nodeRegistration.failed) * 100 || 0).toFixed(2) + '%',
      validation: (testResults.testCases.validation.passed / 
        (testResults.testCases.validation.passed + testResults.testCases.validation.failed) * 100 || 0).toFixed(2) + '%',
      balanceCheck: (testResults.testCases.balanceCheck.passed / 
        (testResults.testCases.balanceCheck.passed + testResults.testCases.balanceCheck.failed) * 100 || 0).toFixed(2) + '%',
      rewardClaim: (testResults.testCases.rewardClaim.passed / 
        (testResults.testCases.rewardClaim.passed + testResults.testCases.rewardClaim.failed) * 100 || 0).toFixed(2) + '%',
      networkStats: (testResults.testCases.networkStats.passed / 
        (testResults.testCases.networkStats.passed + testResults.testCases.networkStats.failed) * 100 || 0).toFixed(2) + '%'
    }
  };
  
  return report;
};

// Main test function
const runTest = async () => {
  logger.log('🚀 Starting Coherium Rewards System Test');
  logger.log('=======================================');
  
  // Generate test nodes
  const nodes = generateTestNodes(CONFIG.nodeCount);
  logger.log(`🌐 Generated ${nodes.length} test nodes`);
  
  try {
    // Initial network stats
    logger.log('\n📊 Getting initial network stats...');
    const initialStats = await testNetworkStats();
    testResults.networkStats.initial = initialStats;
    
    if (!initialStats) {
      throw new Error('Failed to get initial network stats');
    }
    
    logger.log('Initial Network Stats:', initialStats);
    
    // Simulate activity
    logger.log(`\n🔁 Simulating node activity for ${CONFIG.testDuration/1000} seconds...`);
    
    const startTime = Date.now();
    const endTime = startTime + CONFIG.testDuration;
    
    // Run simulation with parallel operations
    const simulationPromises = [];
    
    while (Date.now() < endTime) {
      // Run tests in parallel for better simulation of real network conditions
      const nodePromises = nodes.map(node => runRandomTest(node));
      simulationPromises.push(...nodePromises);
      
      // Add some delay between batches
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    // Wait for all operations to complete
    await Promise.all(simulationPromises);
    
    // Final network stats
    logger.log('\n📊 Getting final network stats...');
    const finalStats = await testNetworkStats();
    testResults.networkStats.final = finalStats;
    
    if (!finalStats) {
      throw new Error('Failed to get final network stats');
    }
    
    // Generate and save test report
    const report = generateReport(nodes, startTime, endTime, initialStats, finalStats);
    
    // Save detailed results to file
    fs.writeFileSync(CONFIG.resultsFile, JSON.stringify(report, null, 2));
    
    // Print summary
    console.log('\n' + '='.repeat(80));
    console.log('✅ TEST COMPLETED SUCCESSFULLY');
    console.log('='.repeat(80));
    console.log(`\n📊 Test Summary (${report.testRun.duration}):`);
    console.log(`- Nodes: ${nodes.length}`);
    console.log(`- Blocks Mined: ${report.testRun.blocksMined}`);
    console.log(`- Blocks/Minute: ${report.testRun.blocksPerMinute}`);
    console.log(`- Coherium Minted: ${report.network.supplyChange} κ`);
    
    console.log('\n🧪 Test Results:');
    Object.entries(report.testResults).forEach(([test, { passed, failed }]) => {
      const total = passed + failed;
      const successRate = total > 0 ? (passed / total * 100).toFixed(1) : 0;
      console.log(`- ${test.padEnd(20)}: ${passed.toString().padStart(4)} passed, ${failed.toString().padStart(4)} failed (${successRate}%)`);
    });
    
    console.log('\n💎 Node Balances:');
    report.nodes.forEach(node => {
      console.log(`- ${node.id.substring(0, 8)}: ${node.balance.toString().padStart(8)} κ (${node.validations} validations, ${node.rewards} κ earned)`);
    });
    
    console.log('\n📝 Detailed results saved to:', CONFIG.resultsFile);
    console.log('📋 Log file:', CONFIG.logFile);
    
    // Check for critical errors
    const hasCriticalErrors = report.nodes.some(node => node.testResults.lastError) || 
                             Object.values(report.testResults).some(t => t.failed > 0);
    
    if (hasCriticalErrors) {
      console.log('\n⚠️  Some tests failed. Check the log file for details.');
      process.exit(1);
    }
    
    console.log('\n✨ All tests passed successfully!');
    
  } catch (error) {
    logger.error('Test failed with error:', error);
    console.error('\n❌ TEST FAILED');
    console.error('Check the log file for details:', CONFIG.logFile);
    process.exit(1);
  }
};

// Run the test
runTest().catch(console.error);
