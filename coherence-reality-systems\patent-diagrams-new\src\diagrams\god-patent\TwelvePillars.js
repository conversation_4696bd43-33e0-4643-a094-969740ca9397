import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const TwelvePillars = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel>CYBER-SAFETY FRAMEWORK: 12 PILLARS</ContainerLabel>
      </ContainerBox>
      
      {/* Top Row - Pillars 1-4 */}
      <ComponentBox left="100px" top="80px" width="150px" height="100px">
        <ComponentNumber>201</ComponentNumber>
        <ComponentLabel>Pillar 1</ComponentLabel>
        Universal Cyber-Safety Kernel
      </ComponentBox>
      
      <ComponentBox left="270px" top="80px" width="150px" height="100px">
        <ComponentNumber>202</ComponentNumber>
        <ComponentLabel>Pillar 2</ComponentLabel>
        Regulation-Specific ZK Batch Prover
      </ComponentBox>
      
      <ComponentBox left="440px" top="80px" width="150px" height="100px">
        <ComponentNumber>203</ComponentNumber>
        <ComponentLabel>Pillar 3</ComponentLabel>
        Self-Destructing Compliance Servers
      </ComponentBox>
      
      <ComponentBox left="610px" top="80px" width="150px" height="100px">
        <ComponentNumber>204</ComponentNumber>
        <ComponentLabel>Pillar 4</ComponentLabel>
        GDPR-by-Default Compiler
      </ComponentBox>
      
      {/* Middle Row - Pillars 5-8 */}
      <ComponentBox left="100px" top="200px" width="150px" height="100px">
        <ComponentNumber>205</ComponentNumber>
        <ComponentLabel>Pillar 5</ComponentLabel>
        Blockchain-Based Compliance Reconstruction
      </ComponentBox>
      
      <ComponentBox left="270px" top="200px" width="150px" height="100px">
        <ComponentNumber>206</ComponentNumber>
        <ComponentLabel>Pillar 6</ComponentLabel>
        Cost-aware Compliance Optimizer
      </ComponentBox>
      
      <ComponentBox left="440px" top="200px" width="150px" height="100px">
        <ComponentNumber>207</ComponentNumber>
        <ComponentLabel>Pillar 7</ComponentLabel>
        Clean-Room Regulatory Training Data
      </ComponentBox>
      
      <ComponentBox left="610px" top="200px" width="150px" height="100px">
        <ComponentNumber>208</ComponentNumber>
        <ComponentLabel>Pillar 8</ComponentLabel>
        Three-Layer AI/Human Dispute Resolution
      </ComponentBox>
      
      {/* Bottom Row - Pillars 9-12 */}
      <ComponentBox left="100px" top="320px" width="150px" height="100px">
        <ComponentNumber>209</ComponentNumber>
        <ComponentLabel>Pillar 9</ComponentLabel>
        Post-Quantum Immutable Compliance Journal
      </ComponentBox>
      
      <ComponentBox left="270px" top="320px" width="150px" height="100px">
        <ComponentNumber>210</ComponentNumber>
        <ComponentLabel>Pillar 10</ComponentLabel>
        Game-Theoretic Regulatory Negotiators
      </ComponentBox>
      
      <ComponentBox left="440px" top="320px" width="150px" height="100px">
        <ComponentNumber>211</ComponentNumber>
        <ComponentLabel>Pillar 11</ComponentLabel>
        Temporal Compliance Markov Engine
      </ComponentBox>
      
      <ComponentBox left="610px" top="320px" width="150px" height="100px">
        <ComponentNumber>212</ComponentNumber>
        <ComponentLabel>Pillar 12</ComponentLabel>
        C-Suite Directive to Code Compiler
      </ComponentBox>
      
      {/* Core Methodology Container */}
      <ContainerBox width="700px" height="120px" left="80px" top="450px">
        <ContainerLabel>CORE METHODOLOGY</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="490px" width="150px" height="60px">
        <ComponentNumber>213</ComponentNumber>
        <ComponentLabel>Unified Data Model</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="270px" top="490px" width="150px" height="60px">
        <ComponentNumber>214</ComponentNumber>
        <ComponentLabel>Native Unification Engine</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="440px" top="490px" width="150px" height="60px">
        <ComponentNumber>215</ComponentNumber>
        <ComponentLabel>Dynamic UI Enforcement</ComponentLabel>
      </ComponentBox>
      
      <ComponentBox left="610px" top="490px" width="150px" height="60px">
        <ComponentNumber>216</ComponentNumber>
        <ComponentLabel>Cross-Domain Intelligence</ComponentLabel>
      </ComponentBox>
      
      {/* Connecting arrows */}
      <Arrow left="175px" top="180px" width="2px" height="20px" />
      <Arrow left="345px" top="180px" width="2px" height="20px" />
      <Arrow left="515px" top="180px" width="2px" height="20px" />
      <Arrow left="685px" top="180px" width="2px" height="20px" />
      
      <Arrow left="175px" top="300px" width="2px" height="20px" />
      <Arrow left="345px" top="300px" width="2px" height="20px" />
      <Arrow left="515px" top="300px" width="2px" height="20px" />
      <Arrow left="685px" top="300px" width="2px" height="20px" />
      
      <Arrow left="175px" top="420px" width="2px" height="30px" />
      <Arrow left="345px" top="420px" width="2px" height="30px" />
      <Arrow left="515px" top="420px" width="2px" height="30px" />
      <Arrow left="685px" top="420px" width="2px" height="30px" />
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>12 Pillars of Cyber-Safety</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Core Methodology</LegendText>
        </LegendItem>
      </DiagramLegend>
      
      {/* Inventor Label */}
      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default TwelvePillars;
