# Universal Compliance Testing Framework (UCTF)

The Universal Compliance Testing Framework (UCTF) is a powerful framework for defining, executing, and reporting on compliance tests.

## Overview

The UCTF provides a flexible and extensible framework for testing compliance with various regulatory frameworks and standards. It allows organizations to define custom tests, execute them against their systems, and generate comprehensive reports.

## Key Features

- **Test Definition**: Define compliance tests with a flexible, JSON-based format
- **Test Execution**: Execute tests individually or as part of test suites
- **Result Management**: Store and analyze test results
- **Report Generation**: Generate various types of reports from test results
- **Extensibility**: Easily add custom tests, report generators, and event handlers

## Architecture

The UCTF consists of several core components:

- **Test Engine**: The main engine that orchestrates test execution and report generation
- **Test Manager**: Manages test definitions and executes tests
- **Result Manager**: Manages test results and provides analysis capabilities
- **Report Manager**: Generates reports from test results

## Supported Compliance Frameworks

The UCTF includes support for testing compliance with several common frameworks:

- **GDPR**: General Data Protection Regulation
- **HIPAA**: Health Insurance Portability and Accountability Act
- **SOC 2**: Service Organization Control 2

## Usage

Here's a simple example of how to use the UCTF:

```python
from uctf import TestEngine

# Initialize the Test Engine
engine = TestEngine()

# Run a GDPR data protection test
test_run = engine.run_test('gdpr_data_protection', {
    'strict_mode': True,
    'target_system': 'customer_database'
})

# Print the test result
print(f"Test passed: {test_run['result']['passed']}")
print(f"Test score: {test_run['result']['score']}")

# Generate a summary report
report = engine.generate_report('summary', test_run['id'], {})

# Print the report
print(f"Report ID: {report['id']}")
print(f"Compliance status: {report['compliance_status']}")
```

## Extending the Framework

### Adding a Custom Test

```python
from uctf import TestEngine

# Initialize the Test Engine
engine = TestEngine()

# Define a custom test function
def custom_test(parameters):
    # Custom implementation to test compliance
    # ...
    return {
        'test_id': 'custom_test',
        'passed': True,
        'score': 90,
        'findings': [
            {
                'id': 'CUSTOM-1',
                'description': 'Custom test finding',
                'status': 'passed'
            }
        ]
    }

# Register the custom test
engine.test_manager.register_test('custom_test', custom_test)
```

### Adding a Custom Report Generator

```python
from uctf import TestEngine

# Initialize the Test Engine
engine = TestEngine()

# Define a custom report generator
def generate_custom_report(test_run, parameters):
    # Custom implementation to generate a report
    # ...
    return {
        'id': f"custom_{test_run['id']}",
        'type': 'custom',
        'test_run_id': test_run['id'],
        'custom_field': 'Custom value',
        'generated_at': '2023-06-15T10:30:00Z'
    }

# Register the custom report generator
engine.report_manager.register_report_generator('custom', generate_custom_report)
```

### Adding a Custom Event Handler

```python
from uctf import TestEngine

# Initialize the Test Engine
engine = TestEngine()

# Define a custom event handler
def custom_event_handler(event_data):
    print(f"Custom event handler: {event_data}")

# Register the custom event handler
engine.register_event_handler('test_completed', custom_event_handler)
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
