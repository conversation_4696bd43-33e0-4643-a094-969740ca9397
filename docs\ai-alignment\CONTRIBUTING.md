# Contributing to NovaAlign

Thank you for your interest in contributing to NovaAlign! This guide will help you get started with contributing to our project.

## Table of Contents
- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Code Style](#code-style)
- [Documentation](#documentation)
- [Testing](#testing)
- [Pull Requests](#pull-requests)
- [Reporting Issues](#reporting-issues)
- [Community](#community)

## Code of Conduct

By participating in this project, you agree to abide by our [Code of Conduct](CODE_OF_CONDUCT.md). Please read it before making any contributions.

## Getting Started

1. **Fork** the repository on GitHub
2. **Clone** your fork locally
   ```bash
   git clone https://github.com/your-username/nova-align.git
   cd nova-align
   ```
3. **Set up** the development environment
   ```bash
   # Install dependencies
   npm install
   pip install -r requirements-dev.txt
   
   # Set up pre-commit hooks
   pre-commit install
   ```
4. **Create a branch** for your changes
   ```bash
   git checkout -b feature/your-feature-name
   ```

## Development Workflow

1. **Update** your local `main` branch
   ```bash
   git checkout main
   git pull upstream main
   ```

2. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes** following the code style guidelines

4. **Run tests** locally
   ```bash
   npm test
   python -m pytest
   ```

5. **Commit your changes** with a descriptive message
   ```bash
   git commit -m "Add feature X"
   ```

6. **Push** to your fork
   ```bash
   git push origin feature/your-feature-name
   ```

7. **Open a Pull Request** against the `main` branch

## Code Style

### Python
- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/)
- Use type hints for all new code
- Maximum line length: 88 characters (Black default)
- Use `black` for code formatting
- Use `isort` for import sorting

### JavaScript/TypeScript
- Follow [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use TypeScript for all new code
- Maximum line length: 100 characters
- Use ESLint and Prettier for code formatting

### Git Commit Messages

Use the following format:

```
<type>(<scope>): <subject>

[optional body]

[optional footer]
```

**Types**:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code changes that neither fix bugs nor add features
- `perf`: Performance improvements
- `test`: Adding or modifying tests
- `chore`: Changes to build process or auxiliary tools

**Example**:
```
feat(auth): add OAuth2 support

- Add Google OAuth2 authentication
- Update user model to support OAuth
- Add tests for OAuth flow

Closes #123
```

## Documentation

### Writing Documentation
- Use [Markdown](https://www.markdownguide.org/) for all documentation
- Follow the [Google Developer Documentation Style Guide](https://developers.google.com/style)
- Keep lines to 100 characters maximum
- Use active voice
- Be concise but informative

### Documentation Structure

```
docs/
  ai-alignment/
    getting-started/
      introduction.md
      quick-start.md
    user-guide/
      dashboard.md
      features.md
    api/
      authentication.md
      endpoints.md
      examples.md
    deployment/
      installation.md
      configuration.md
      maintenance.md
```

### Running Documentation Locally

1. Install MkDocs and required plugins:
   ```bash
   pip install mkdocs mkdocs-material mkdocstrings[python] mkdocs-redirects
   ```

2. Start the development server:
   ```bash
   mkdocs serve
   ```

3. Open http://localhost:8000 in your browser

## Testing

### Running Tests

```bash
# Run all tests
npm test
pytest

# Run specific test file
pytest tests/test_auth.py

# Run with coverage
pytest --cov=src tests/
```

### Writing Tests
- Write tests for all new features and bug fixes
- Follow the Arrange-Act-Assert pattern
- Use descriptive test names
- Test edge cases and error conditions

## Pull Requests

1. **Keep PRs focused** on a single feature or fix
2. **Update documentation** as part of the PR
3. **Include tests** for new features and bug fixes
4. **Run all tests** before submitting
5. **Squash commits** into logical units
6. **Reference issues** that the PR addresses
7. **Request reviews** from appropriate team members

## Reporting Issues

When reporting issues, please include:

1. **Description** of the issue
2. **Steps to reproduce**
3. **Expected behavior**
4. **Actual behavior**
5. **Environment** (OS, browser, version, etc.)
6. **Screenshots** if applicable
7. **Error messages** or logs

## Community

- **Discord**: Join our [Discord server](https://discord.gg/novaalign)
- **Twitter**: Follow [@NovaAlign](https://twitter.com/NovaAlign)
- **Blog**: Read our [blog](https://blog.novaalign.ai)
- **Newsletter**: Subscribe to our [newsletter](https://novaalign.ai/newsletter)

## License

By contributing to NovaAlign, you agree that your contributions will be licensed under the [MIT License](LICENSE).
