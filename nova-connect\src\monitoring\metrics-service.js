/**
 * NovaFuse Universal API Connector - Metrics Service
 * 
 * This module provides a service for collecting and exposing metrics.
 */

const { createLogger } = require('../utils/logger');
const logger = createLogger('metrics-service');

/**
 * Metrics Service class for collecting and exposing metrics
 */
class MetricsService {
  constructor() {
    // Initialize metrics storage
    this.metrics = {
      counters: new Map(),
      gauges: new Map(),
      histograms: new Map(),
      summaries: new Map()
    };

    // Initialize metric metadata
    this.metricMetadata = new Map();

    // Initialize default labels
    this.defaultLabels = {
      service: 'nova-connect',
      version: process.env.SERVICE_VERSION || '1.0.0'
    };

    logger.info('Metrics service initialized');
  }

  /**
   * Register a metric with metadata
   * 
   * @param {string} type - The metric type (counter, gauge, histogram, summary)
   * @param {string} name - The metric name
   * @param {string} description - The metric description
   * @param {Array<string>} labelNames - The label names for this metric
   * @returns {string} - The full metric name
   */
  _registerMetric(type, name, description, labelNames = []) {
    const fullName = `nova_connect_${name}`;
    
    // Store metadata
    this.metricMetadata.set(fullName, {
      type,
      name: fullName,
      description,
      labelNames
    });
    
    // Initialize storage based on type
    if (type === 'counter' && !this.metrics.counters.has(fullName)) {
      this.metrics.counters.set(fullName, new Map());
    } else if (type === 'gauge' && !this.metrics.gauges.has(fullName)) {
      this.metrics.gauges.set(fullName, new Map());
    } else if (type === 'histogram' && !this.metrics.histograms.has(fullName)) {
      this.metrics.histograms.set(fullName, new Map());
    } else if (type === 'summary' && !this.metrics.summaries.has(fullName)) {
      this.metrics.summaries.set(fullName, new Map());
    }
    
    logger.debug(`Registered ${type} metric: ${fullName}`);
    return fullName;
  }

  /**
   * Create a label key from label values
   * 
   * @param {Object} labels - The label values
   * @returns {string} - The label key
   */
  _getLabelKey(labels = {}) {
    // Combine with default labels
    const allLabels = { ...this.defaultLabels, ...labels };
    
    // Sort keys for consistent ordering
    const sortedKeys = Object.keys(allLabels).sort();
    
    // Create key string
    return sortedKeys.map(key => `${key}:${allLabels[key]}`).join(',');
  }

  /**
   * Register a counter metric
   * 
   * @param {string} name - The metric name
   * @param {string} description - The metric description
   * @param {Array<string>} labelNames - The label names for this metric
   * @returns {Object} - The counter methods
   */
  registerCounter(name, description, labelNames = []) {
    const fullName = this._registerMetric('counter', name, description, labelNames);
    
    return {
      /**
       * Increment the counter
       * 
       * @param {number} value - The value to increment by (default: 1)
       * @param {Object} labels - The label values
       */
      inc: (value = 1, labels = {}) => {
        const labelKey = this._getLabelKey(labels);
        const counters = this.metrics.counters.get(fullName);
        
        // Initialize counter if it doesn't exist
        if (!counters.has(labelKey)) {
          counters.set(labelKey, 0);
        }
        
        // Increment counter
        counters.set(labelKey, counters.get(labelKey) + value);
      }
    };
  }

  /**
   * Register a gauge metric
   * 
   * @param {string} name - The metric name
   * @param {string} description - The metric description
   * @param {Array<string>} labelNames - The label names for this metric
   * @returns {Object} - The gauge methods
   */
  registerGauge(name, description, labelNames = []) {
    const fullName = this._registerMetric('gauge', name, description, labelNames);
    
    return {
      /**
       * Set the gauge value
       * 
       * @param {number} value - The value to set
       * @param {Object} labels - The label values
       */
      set: (value, labels = {}) => {
        const labelKey = this._getLabelKey(labels);
        const gauges = this.metrics.gauges.get(fullName);
        
        // Set gauge value
        gauges.set(labelKey, value);
      },
      
      /**
       * Increment the gauge
       * 
       * @param {number} value - The value to increment by (default: 1)
       * @param {Object} labels - The label values
       */
      inc: (value = 1, labels = {}) => {
        const labelKey = this._getLabelKey(labels);
        const gauges = this.metrics.gauges.get(fullName);
        
        // Initialize gauge if it doesn't exist
        if (!gauges.has(labelKey)) {
          gauges.set(labelKey, 0);
        }
        
        // Increment gauge
        gauges.set(labelKey, gauges.get(labelKey) + value);
      },
      
      /**
       * Decrement the gauge
       * 
       * @param {number} value - The value to decrement by (default: 1)
       * @param {Object} labels - The label values
       */
      dec: (value = 1, labels = {}) => {
        const labelKey = this._getLabelKey(labels);
        const gauges = this.metrics.gauges.get(fullName);
        
        // Initialize gauge if it doesn't exist
        if (!gauges.has(labelKey)) {
          gauges.set(labelKey, 0);
        }
        
        // Decrement gauge
        gauges.set(labelKey, gauges.get(labelKey) - value);
      }
    };
  }

  /**
   * Register a histogram metric
   * 
   * @param {string} name - The metric name
   * @param {string} description - The metric description
   * @param {Array<string>} labelNames - The label names for this metric
   * @param {Array<number>} buckets - The histogram buckets
   * @returns {Object} - The histogram methods
   */
  registerHistogram(name, description, labelNames = [], buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10]) {
    const fullName = this._registerMetric('histogram', name, description, labelNames);
    
    // Store bucket configuration
    this.metricMetadata.get(fullName).buckets = buckets;
    
    return {
      /**
       * Observe a value
       * 
       * @param {number} value - The value to observe
       * @param {Object} labels - The label values
       */
      observe: (value, labels = {}) => {
        const labelKey = this._getLabelKey(labels);
        const histograms = this.metrics.histograms.get(fullName);
        
        // Initialize histogram if it doesn't exist
        if (!histograms.has(labelKey)) {
          histograms.set(labelKey, {
            sum: 0,
            count: 0,
            buckets: buckets.reduce((acc, bucket) => {
              acc[bucket] = 0;
              return acc;
            }, {})
          });
        }
        
        const histogram = histograms.get(labelKey);
        
        // Update sum and count
        histogram.sum += value;
        histogram.count += 1;
        
        // Update buckets
        for (const bucket of buckets) {
          if (value <= bucket) {
            histogram.buckets[bucket] += 1;
          }
        }
      }
    };
  }

  /**
   * Register a summary metric
   * 
   * @param {string} name - The metric name
   * @param {string} description - The metric description
   * @param {Array<string>} labelNames - The label names for this metric
   * @param {Array<number>} percentiles - The percentiles to track
   * @returns {Object} - The summary methods
   */
  registerSummary(name, description, labelNames = [], percentiles = [0.01, 0.05, 0.5, 0.9, 0.95, 0.99]) {
    const fullName = this._registerMetric('summary', name, description, labelNames);
    
    // Store percentile configuration
    this.metricMetadata.get(fullName).percentiles = percentiles;
    
    return {
      /**
       * Observe a value
       * 
       * @param {number} value - The value to observe
       * @param {Object} labels - The label values
       */
      observe: (value, labels = {}) => {
        const labelKey = this._getLabelKey(labels);
        const summaries = this.metrics.summaries.get(fullName);
        
        // Initialize summary if it doesn't exist
        if (!summaries.has(labelKey)) {
          summaries.set(labelKey, {
            sum: 0,
            count: 0,
            values: []
          });
        }
        
        const summary = summaries.get(labelKey);
        
        // Update sum and count
        summary.sum += value;
        summary.count += 1;
        
        // Store value for percentile calculation
        // Note: In a production implementation, we would use a more efficient
        // algorithm like t-digest or HDR Histogram
        summary.values.push(value);
        
        // Limit the number of stored values to prevent memory issues
        if (summary.values.length > 1000) {
          summary.values = summary.values.slice(-1000);
        }
      }
    };
  }

  /**
   * Get metrics in Prometheus format
   * 
   * @returns {string} - The metrics in Prometheus format
   */
  getMetricsAsPrometheusFormat() {
    const lines = [];
    
    // Process counters
    for (const [name, counters] of this.metrics.counters.entries()) {
      const metadata = this.metricMetadata.get(name);
      
      // Add metadata
      lines.push(`# HELP ${name} ${metadata.description}`);
      lines.push(`# TYPE ${name} counter`);
      
      // Add metrics
      for (const [labelKey, value] of counters.entries()) {
        const labelString = this._formatLabelsForPrometheus(labelKey);
        lines.push(`${name}${labelString} ${value}`);
      }
    }
    
    // Process gauges
    for (const [name, gauges] of this.metrics.gauges.entries()) {
      const metadata = this.metricMetadata.get(name);
      
      // Add metadata
      lines.push(`# HELP ${name} ${metadata.description}`);
      lines.push(`# TYPE ${name} gauge`);
      
      // Add metrics
      for (const [labelKey, value] of gauges.entries()) {
        const labelString = this._formatLabelsForPrometheus(labelKey);
        lines.push(`${name}${labelString} ${value}`);
      }
    }
    
    // Process histograms
    for (const [name, histograms] of this.metrics.histograms.entries()) {
      const metadata = this.metricMetadata.get(name);
      
      // Add metadata
      lines.push(`# HELP ${name} ${metadata.description}`);
      lines.push(`# TYPE ${name} histogram`);
      
      // Add metrics
      for (const [labelKey, histogram] of histograms.entries()) {
        const labelString = this._formatLabelsForPrometheus(labelKey);
        
        // Add bucket metrics
        for (const [bucket, count] of Object.entries(histogram.buckets)) {
          lines.push(`${name}_bucket${this._formatLabelsForPrometheus(labelKey, { le: bucket })} ${count}`);
        }
        
        // Add sum and count metrics
        lines.push(`${name}_sum${labelString} ${histogram.sum}`);
        lines.push(`${name}_count${labelString} ${histogram.count}`);
      }
    }
    
    // Process summaries
    for (const [name, summaries] of this.metrics.summaries.entries()) {
      const metadata = this.metricMetadata.get(name);
      
      // Add metadata
      lines.push(`# HELP ${name} ${metadata.description}`);
      lines.push(`# TYPE ${name} summary`);
      
      // Add metrics
      for (const [labelKey, summary] of summaries.entries()) {
        const labelString = this._formatLabelsForPrometheus(labelKey);
        
        // Calculate percentiles
        const sortedValues = [...summary.values].sort((a, b) => a - b);
        
        // Add percentile metrics
        for (const percentile of metadata.percentiles) {
          const index = Math.max(0, Math.floor(percentile * sortedValues.length) - 1);
          const value = sortedValues[index] || 0;
          
          lines.push(`${name}${this._formatLabelsForPrometheus(labelKey, { quantile: percentile })} ${value}`);
        }
        
        // Add sum and count metrics
        lines.push(`${name}_sum${labelString} ${summary.sum}`);
        lines.push(`${name}_count${labelString} ${summary.count}`);
      }
    }
    
    return lines.join('\n');
  }

  /**
   * Format labels for Prometheus
   * 
   * @param {string} labelKey - The label key
   * @param {Object} additionalLabels - Additional labels to include
   * @returns {string} - The formatted labels
   */
  _formatLabelsForPrometheus(labelKey, additionalLabels = {}) {
    // Parse label key
    const labels = {};
    labelKey.split(',').forEach(pair => {
      const [key, value] = pair.split(':');
      labels[key] = value;
    });
    
    // Add additional labels
    Object.assign(labels, additionalLabels);
    
    // Format labels
    const labelPairs = Object.entries(labels).map(([key, value]) => `${key}="${value}"`);
    
    if (labelPairs.length === 0) {
      return '';
    }
    
    return `{${labelPairs.join(',')}}`;
  }

  /**
   * Reset all metrics
   */
  resetMetrics() {
    this.metrics.counters.clear();
    this.metrics.gauges.clear();
    this.metrics.histograms.clear();
    this.metrics.summaries.clear();
    
    logger.info('All metrics reset');
  }
}

// Create singleton instance
const metricsService = new MetricsService();

module.exports = metricsService;
