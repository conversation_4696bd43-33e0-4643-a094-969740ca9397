import React, { useRef, useEffect, useState } from 'react';
import { Box, CircularProgress, Typography, Paper, Grid, Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled components
const MapContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  height: '100%',
  boxShadow: theme.shadows[3],
  borderRadius: '8px',
  overflow: 'hidden'
}));

const MapCell = styled(Box)(({ theme, riskLevel, controlCoverage, gapLevel }) => {
  // Calculate background color based on risk level
  const getRiskColor = (level) => {
    if (level >= 0.8) return 'rgba(244, 67, 54, 0.8)'; // High risk - red
    if (level >= 0.5) return 'rgba(255, 152, 0, 0.8)'; // Medium risk - orange
    if (level >= 0.3) return 'rgba(255, 235, 59, 0.8)'; // Low risk - yellow
    return 'rgba(76, 175, 80, 0.8)'; // Very low risk - green
  };
  
  // Calculate border color based on control coverage
  const getControlColor = (coverage) => {
    if (coverage >= 0.8) return 'rgba(76, 175, 80, 1)'; // High coverage - green
    if (coverage >= 0.5) return 'rgba(255, 235, 59, 1)'; // Medium coverage - yellow
    if (coverage >= 0.3) return 'rgba(255, 152, 0, 1)'; // Low coverage - orange
    return 'rgba(244, 67, 54, 1)'; // Very low coverage - red
  };
  
  // Calculate border width based on gap level
  const getBorderWidth = (gap) => {
    if (gap >= 0.5) return '3px'; // High gap
    if (gap >= 0.3) return '2px'; // Medium gap
    return '1px'; // Low gap
  };
  
  return {
    width: '100%',
    height: '100%',
    backgroundColor: getRiskColor(riskLevel),
    border: `${getBorderWidth(gapLevel)} solid ${getControlColor(controlCoverage)}`,
    borderRadius: '4px',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    '&:hover': {
      transform: 'scale(1.05)',
      zIndex: 1
    }
  };
});

const LegendItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(0.5)
}));

const LegendColor = styled(Box)(({ theme, color }) => ({
  width: 16,
  height: 16,
  backgroundColor: color,
  marginRight: theme.spacing(1),
  borderRadius: '2px'
}));

/**
 * RiskControlFusionMap component
 * 
 * Creates a visualization that maps risks across domains to controls and shows coverage.
 * Features include:
 * - Heat map showing risk concentration across domains
 * - Control coverage overlay showing protection levels
 * - Gap analysis highlighting unaddressed risks
 * - Efficiency indicators showing redundant controls
 */
function RiskControlFusionMap({
  riskData = {
    // Format: domain -> category -> risk level (0-1)
    grc: {
      governance: 0.7,
      risk: 0.5,
      compliance: 0.8
    },
    it: {
      infrastructure: 0.6,
      applications: 0.4,
      data: 0.7
    },
    cybersecurity: {
      prevention: 0.8,
      detection: 0.5,
      response: 0.3
    }
  },
  controlData = {
    // Format: domain -> category -> control coverage (0-1)
    grc: {
      governance: 0.8,
      risk: 0.6,
      compliance: 0.9
    },
    it: {
      infrastructure: 0.7,
      applications: 0.5,
      data: 0.6
    },
    cybersecurity: {
      prevention: 0.7,
      detection: 0.4,
      response: 0.2
    }
  },
  options = {
    showLegend: true,
    showEfficiencyIndicators: true,
    highlightGaps: true,
    showTooltips: true
  },
  width = '100%',
  height = '100%'
}) {
  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for calculated values
  const [gapAnalysis, setGapAnalysis] = useState({});
  const [efficiencyAnalysis, setEfficiencyAnalysis] = useState({});
  const [selectedCell, setSelectedCell] = useState(null);
  
  // Calculate gap analysis and efficiency metrics
  useEffect(() => {
    try {
      const gaps = {};
      const efficiency = {};
      
      // Calculate gaps and efficiency for each domain and category
      Object.keys(riskData).forEach(domain => {
        gaps[domain] = {};
        efficiency[domain] = {};
        
        Object.keys(riskData[domain]).forEach(category => {
          const riskLevel = riskData[domain][category];
          const controlCoverage = controlData[domain][category];
          
          // Calculate gap (risk not covered by controls)
          // Higher values indicate bigger gaps
          const gap = Math.max(0, riskLevel - controlCoverage);
          gaps[domain][category] = gap;
          
          // Calculate efficiency (control coverage relative to risk)
          // Values > 1 indicate potential over-control (inefficiency)
          // Values < 1 indicate under-control (gap)
          // Values close to 1 indicate optimal control
          const efficiencyRatio = controlCoverage / Math.max(0.1, riskLevel);
          efficiency[domain][category] = efficiencyRatio;
        });
      });
      
      setGapAnalysis(gaps);
      setEfficiencyAnalysis(efficiency);
      setIsLoading(false);
    } catch (err) {
      console.error('Error calculating risk-control fusion map:', err);
      setError(err.message || 'Error calculating risk-control fusion map');
      setIsLoading(false);
    }
  }, [riskData, controlData]);
  
  // Helper function to get efficiency status
  const getEfficiencyStatus = (ratio) => {
    if (ratio > 1.5) return { status: 'Over-controlled', color: '#9c27b0' }; // Purple
    if (ratio > 1.2) return { status: 'Slightly over-controlled', color: '#3f51b5' }; // Indigo
    if (ratio >= 0.8) return { status: 'Optimal', color: '#4caf50' }; // Green
    if (ratio >= 0.5) return { status: 'Under-controlled', color: '#ff9800' }; // Orange
    return { status: 'Severely under-controlled', color: '#f44336' }; // Red
  };
  
  // Helper function to get tooltip content
  const getTooltipContent = (domain, category) => {
    if (!domain || !category) return '';
    
    const riskLevel = riskData[domain][category];
    const controlCoverage = controlData[domain][category];
    const gap = gapAnalysis[domain]?.[category] || 0;
    const efficiencyRatio = efficiencyAnalysis[domain]?.[category] || 0;
    const efficiencyInfo = getEfficiencyStatus(efficiencyRatio);
    
    return (
      <Box sx={{ p: 1, maxWidth: 200 }}>
        <Typography variant="subtitle2" gutterBottom>
          {domain.toUpperCase()}: {category}
        </Typography>
        <Typography variant="body2">
          Risk Level: {(riskLevel * 100).toFixed(0)}%
        </Typography>
        <Typography variant="body2">
          Control Coverage: {(controlCoverage * 100).toFixed(0)}%
        </Typography>
        <Typography variant="body2">
          Gap: {(gap * 100).toFixed(0)}%
        </Typography>
        <Typography variant="body2" sx={{ color: efficiencyInfo.color }}>
          Efficiency: {efficiencyInfo.status} ({efficiencyRatio.toFixed(1)}x)
        </Typography>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        width,
        height,
        position: 'relative',
        p: 2
      }}
    >
      {isLoading ? (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%'
          }}
        >
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: 'error.main'
          }}
        >
          <Typography variant="body1" color="error">
            Error: {error}
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {/* Main Map */}
          <Grid item xs={12} md={options.showLegend ? 9 : 12}>
            <MapContainer>
              <Typography variant="h6" gutterBottom>
                Risk-Control Fusion Map
              </Typography>
              
              <Grid container spacing={1} sx={{ height: 'calc(100% - 40px)' }}>
                {/* Domain Headers */}
                <Grid item xs={3}>
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    <Typography variant="subtitle2" sx={{ transform: 'rotate(-90deg)', whiteSpace: 'nowrap' }}>
                      Categories
                    </Typography>
                  </Box>
                </Grid>
                
                {Object.keys(riskData).map((domain, domainIndex) => (
                  <Grid item xs={3} key={domainIndex}>
                    <Box sx={{ textAlign: 'center', mb: 1 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {domain.toUpperCase()}
                      </Typography>
                    </Box>
                    
                    <Grid container spacing={1} sx={{ height: 'calc(100% - 32px)' }}>
                      {Object.keys(riskData[domain]).map((category, categoryIndex) => {
                        const riskLevel = riskData[domain][category];
                        const controlCoverage = controlData[domain][category];
                        const gapLevel = gapAnalysis[domain]?.[category] || 0;
                        
                        return (
                          <Grid item xs={12} key={categoryIndex} sx={{ height: '33.33%' }}>
                            {options.showTooltips ? (
                              <Tooltip
                                title={getTooltipContent(domain, category)}
                                arrow
                                placement="top"
                              >
                                <MapCell
                                  riskLevel={riskLevel}
                                  controlCoverage={controlCoverage}
                                  gapLevel={gapLevel}
                                  onClick={() => setSelectedCell({ domain, category })}
                                  sx={{
                                    boxShadow: selectedCell?.domain === domain && selectedCell?.category === category 
                                      ? '0 0 0 2px #2196f3' : 'none'
                                  }}
                                >
                                  <Box sx={{ 
                                    height: '100%', 
                                    display: 'flex', 
                                    alignItems: 'center', 
                                    justifyContent: 'center',
                                    flexDirection: 'column'
                                  }}>
                                    <Typography variant="caption" sx={{ color: '#fff', fontWeight: 'bold' }}>
                                      {category}
                                    </Typography>
                                    
                                    {options.showEfficiencyIndicators && (
                                      <Typography variant="caption" sx={{ 
                                        color: '#fff',
                                        backgroundColor: getEfficiencyStatus(efficiencyAnalysis[domain]?.[category] || 0).color,
                                        px: 0.5,
                                        borderRadius: '4px',
                                        fontSize: '0.6rem'
                                      }}>
                                        {efficiencyAnalysis[domain]?.[category].toFixed(1)}x
                                      </Typography>
                                    )}
                                  </Box>
                                </MapCell>
                              </Tooltip>
                            ) : (
                              <MapCell
                                riskLevel={riskLevel}
                                controlCoverage={controlCoverage}
                                gapLevel={gapLevel}
                                onClick={() => setSelectedCell({ domain, category })}
                                sx={{
                                  boxShadow: selectedCell?.domain === domain && selectedCell?.category === category 
                                    ? '0 0 0 2px #2196f3' : 'none'
                                }}
                              >
                                <Box sx={{ 
                                  height: '100%', 
                                  display: 'flex', 
                                  alignItems: 'center', 
                                  justifyContent: 'center',
                                  flexDirection: 'column'
                                }}>
                                  <Typography variant="caption" sx={{ color: '#fff', fontWeight: 'bold' }}>
                                    {category}
                                  </Typography>
                                  
                                  {options.showEfficiencyIndicators && (
                                    <Typography variant="caption" sx={{ 
                                      color: '#fff',
                                      backgroundColor: getEfficiencyStatus(efficiencyAnalysis[domain]?.[category] || 0).color,
                                      px: 0.5,
                                      borderRadius: '4px',
                                      fontSize: '0.6rem'
                                    }}>
                                      {efficiencyAnalysis[domain]?.[category].toFixed(1)}x
                                    </Typography>
                                  )}
                                </Box>
                              </MapCell>
                            )}
                          </Grid>
                        );
                      })}
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            </MapContainer>
          </Grid>
          
          {/* Legend */}
          {options.showLegend && (
            <Grid item xs={12} md={3}>
              <MapContainer>
                <Typography variant="h6" gutterBottom>
                  Legend
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Risk Levels (Background)
                  </Typography>
                  <LegendItem>
                    <LegendColor color="rgba(244, 67, 54, 0.8)" />
                    <Typography variant="body2">High Risk (≥80%)</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color="rgba(255, 152, 0, 0.8)" />
                    <Typography variant="body2">Medium Risk (≥50%)</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color="rgba(255, 235, 59, 0.8)" />
                    <Typography variant="body2">Low Risk (≥30%)</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color="rgba(76, 175, 80, 0.8)" />
                    <Typography variant="body2">Very Low Risk (<30%)</Typography>
                  </LegendItem>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Control Coverage (Border)
                  </Typography>
                  <LegendItem>
                    <LegendColor color="rgba(76, 175, 80, 1)" />
                    <Typography variant="body2">High Coverage (≥80%)</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color="rgba(255, 235, 59, 1)" />
                    <Typography variant="body2">Medium Coverage (≥50%)</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color="rgba(255, 152, 0, 1)" />
                    <Typography variant="body2">Low Coverage (≥30%)</Typography>
                  </LegendItem>
                  <LegendItem>
                    <LegendColor color="rgba(244, 67, 54, 1)" />
                    <Typography variant="body2">Very Low Coverage (<30%)</Typography>
                  </LegendItem>
                </Box>
                
                {options.showEfficiencyIndicators && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Efficiency Ratio
                    </Typography>
                    <LegendItem>
                      <LegendColor color="#9c27b0" />
                      <Typography variant="body2">Over-controlled (>1.5x)</Typography>
                    </LegendItem>
                    <LegendItem>
                      <LegendColor color="#3f51b5" />
                      <Typography variant="body2">Slightly over-controlled (>1.2x)</Typography>
                    </LegendItem>
                    <LegendItem>
                      <LegendColor color="#4caf50" />
                      <Typography variant="body2">Optimal (0.8x-1.2x)</Typography>
                    </LegendItem>
                    <LegendItem>
                      <LegendColor color="#ff9800" />
                      <Typography variant="body2">Under-controlled (0.5x-0.8x)</Typography>
                    </LegendItem>
                    <LegendItem>
                      <LegendColor color="#f44336" />
                      <Typography variant="body2">Severely under-controlled (<0.5x)</Typography>
                    </LegendItem>
                  </Box>
                )}
              </MapContainer>
            </Grid>
          )}
        </Grid>
      )}
    </Box>
  );
}

export default RiskControlFusionMap;
