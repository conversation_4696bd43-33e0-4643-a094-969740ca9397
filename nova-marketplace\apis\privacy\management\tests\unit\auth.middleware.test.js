/**
 * Authentication Middleware Tests
 * 
 * This file contains unit tests for the authentication middleware.
 */

const { authenticate, authorize } = require('../../middleware/auth');
const { verifyToken } = require('../../services/authService');

// Mock the authService
jest.mock('../../services/authService');

describe('Authentication Middleware', () => {
  let req, res, next;
  
  beforeEach(() => {
    req = {
      headers: {}
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    
    next = jest.fn();
    
    // Reset mocks
    jest.clearAllMocks();
  });
  
  describe('authenticate', () => {
    it('should return 401 if no authorization header is provided', async () => {
      await authenticate(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
      expect(next).not.toHaveBeenCalled();
    });
    
    it('should return 401 if authorization header has wrong format', async () => {
      req.headers.authorization = 'InvalidFormat';
      
      await authenticate(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
      expect(next).not.toHaveBeenCalled();
    });
    
    it('should return 401 if token is not provided', async () => {
      req.headers.authorization = 'Bearer ';
      
      await authenticate(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Authentication token is required'
      });
      expect(next).not.toHaveBeenCalled();
    });
    
    it('should return 401 if token verification fails', async () => {
      req.headers.authorization = 'Bearer invalid-token';
      
      // Mock verifyToken to throw an error
      verifyToken.mockRejectedValue({
        name: 'AuthenticationError',
        message: 'Invalid token'
      });
      
      await authenticate(req, res, next);
      
      expect(verifyToken).toHaveBeenCalledWith('invalid-token');
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Invalid token'
      });
      expect(next).not.toHaveBeenCalled();
    });
    
    it('should attach user to request and call next() if token is valid', async () => {
      req.headers.authorization = 'Bearer valid-token';
      
      // Mock verifyToken to return a decoded token
      verifyToken.mockResolvedValue({
        sub: 'user-123',
        username: 'test-user',
        role: 'admin'
      });
      
      await authenticate(req, res, next);
      
      expect(verifyToken).toHaveBeenCalledWith('valid-token');
      expect(req.user).toEqual({
        id: 'user-123',
        username: 'test-user',
        role: 'admin'
      });
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
    
    it('should pass other errors to next()', async () => {
      req.headers.authorization = 'Bearer valid-token';
      
      // Mock verifyToken to throw a non-authentication error
      const error = new Error('Database error');
      verifyToken.mockRejectedValue(error);
      
      await authenticate(req, res, next);
      
      expect(verifyToken).toHaveBeenCalledWith('valid-token');
      expect(next).toHaveBeenCalledWith(error);
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
  });
  
  describe('authorize', () => {
    it('should return 401 if user is not authenticated', () => {
      const middleware = authorize(['admin']);
      
      middleware(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
      expect(next).not.toHaveBeenCalled();
    });
    
    it('should return 403 if user does not have required role', () => {
      const middleware = authorize(['admin']);
      
      req.user = {
        id: 'user-123',
        username: 'test-user',
        role: 'user'
      };
      
      middleware(req, res, next);
      
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Forbidden',
        message: 'Insufficient permissions'
      });
      expect(next).not.toHaveBeenCalled();
    });
    
    it('should call next() if user has required role', () => {
      const middleware = authorize(['admin']);
      
      req.user = {
        id: 'user-123',
        username: 'test-user',
        role: 'admin'
      };
      
      middleware(req, res, next);
      
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
    
    it('should call next() if no roles are required', () => {
      const middleware = authorize([]);
      
      req.user = {
        id: 'user-123',
        username: 'test-user',
        role: 'user'
      };
      
      middleware(req, res, next);
      
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
    
    it('should call next() if user has one of multiple required roles', () => {
      const middleware = authorize(['admin', 'manager']);
      
      req.user = {
        id: 'user-123',
        username: 'test-user',
        role: 'manager'
      };
      
      middleware(req, res, next);
      
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
      expect(res.json).not.toHaveBeenCalled();
    });
  });
});
