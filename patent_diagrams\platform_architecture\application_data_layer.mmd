# Application/Data Layer (NEPI, NovaFold, etc.)
# File: application_data_layer.mmd
# Description: Illustrates the application and data processing layer with NEPI, NovaFold, and other components
# Created: 2025-07-06

%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%

graph TD
    subgraph "NovaFuse Application/Data Layer"
        A["Raw / Domain-Specific Data"] --> B{"Data Ingestion & Pre-processing"}
        B --> C("Data Coherence Buffer")
        C --> D["NEPI Engine (Pattern Identification)"]
        C --> E["NovaFold Engine (Protein Coherence)"]
        C --> F["NECE Engine (Material Coherence)"]
        C --> G["Other Specialized CSEs / Novas"]

        D -- Coherent Patterns --> H("Comphyology Core / Governance Layer")
        E -- Optimized Structures --> H
        F -- Material Properties --> H
        G -- Processed Data --> H

        H --> I["Coherent Output / Action"]
    end

    %% Styling for USPTO compliance (black and white)
    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
    classDef data fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:parallelogram
    classDef process fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:rectangle
    classDef buffer fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:rect,style=rounded
    classDef engine fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:box3d
    classDef core fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:doublecircle
    classDef output fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:box3d,style=rounded
    
    class A,I data
    class B process
    class C buffer
    class D,E,F,G engine
    class H core
    class I output
