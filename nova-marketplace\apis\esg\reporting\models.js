/**
 * ESG Reporting API - Models
 * 
 * This file defines the models for the ESG Reporting API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Report Template Schema
 * 
 * Represents a template for ESG reports.
 */
const ReportTemplateSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  framework: {
    type: String,
    required: true,
    enum: ['GRI', 'SASB', 'TCFD', 'CDP', 'SDG', 'Custom']
  },
  sections: [{
    title: {
      type: String,
      required: true
    },
    description: {
      type: String
    },
    metrics: [{
      metric: {
        type: Schema.Types.ObjectId,
        ref: 'Metric'
      },
      required: {
        type: Boolean,
        default: true
      },
      displayOrder: {
        type: Number
      }
    }]
  }],
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'Active', 'Archived'],
    default: 'Draft'
  },
  createdBy: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Report Schema
 * 
 * Represents an ESG report.
 */
const ReportSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  template: {
    type: Schema.Types.ObjectId,
    ref: 'ReportTemplate',
    required: true
  },
  reportingPeriod: {
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    }
  },
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'In Progress', 'Completed', 'Published', 'Archived'],
    default: 'Draft'
  },
  sections: [{
    title: {
      type: String,
      required: true
    },
    description: {
      type: String
    },
    content: {
      type: String
    },
    metrics: [{
      metric: {
        type: Schema.Types.ObjectId,
        ref: 'Metric'
      },
      value: {
        type: Schema.Types.Mixed
      },
      notes: {
        type: String
      }
    }]
  }],
  approvals: [{
    approver: {
      type: String,
      required: true
    },
    status: {
      type: String,
      required: true,
      enum: ['Pending', 'Approved', 'Rejected']
    },
    date: {
      type: Date,
      default: Date.now
    },
    comments: {
      type: String
    }
  }],
  publishDate: {
    type: Date
  },
  createdBy: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create and export models
const ReportTemplate = mongoose.model('ReportTemplate', ReportTemplateSchema);
const Report = mongoose.model('Report', ReportSchema);

module.exports = {
  ReportTemplate,
  Report
};
