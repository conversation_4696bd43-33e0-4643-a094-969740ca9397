import os
import zipfile
from datetime import datetime

def create_epub():
    """Create the EPUB file from the generated files"""
    # Create META-INF directory and container.xml
    os.makedirs('epub/META-INF', exist_ok=True)
    
    with open('epub/META-INF/container.xml', 'w', encoding='utf-8') as f:
        f.write("""
<?xml version="1.0" encoding="UTF-8"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
    <rootfiles>
        <rootfile full-path="content.opf" media-type="application/oebps-package+xml"/>
    </rootfiles>
</container>
        """)
    
    # Create mimetype file
    with open('epub/mimetype', 'w', encoding='utf-8') as f:
        f.write('application/epub+zip')
    
    # Create EPUB file
    epub_filename = f'Dictionary_of_Comphyology_{datetime.now().strftime("%Y%m%d")}.epub'
    with zipfile.ZipFile(epub_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add mimetype first (must be first in EPUB)
        zipf.write('epub/mimetype', 'mimetype')
        
        # Add META-INF directory
        for root, dirs, files in os.walk('epub/META-INF'):
            for file in files:
                if file != 'container.xml':
                    zipf.write(os.path.join(root, file), os.path.relpath(os.path.join(root, file), 'epub'))
        
        # Add container.xml
        zipf.write('epub/META-INF/container.xml', 'META-INF/container.xml')
        
        # Add content.opf
        zipf.write('epub/content.opf', 'content.opf')
        
        # Add CSS
        zipf.write('epub/css/style.css', 'css/style.css')
        
        # Add text files
        for root, dirs, files in os.walk('epub/text'):
            for file in files:
                zipf.write(os.path.join(root, file), os.path.relpath(os.path.join(root, file), 'epub'))
        
        # Add images
        for root, dirs, files in os.walk('epub/images'):
            for file in files:
                zipf.write(os.path.join(root, file), os.path.relpath(os.path.join(root, file), 'epub'))
    
    print(f"EPUB file created: {epub_filename}")

def main():
    create_epub()

if __name__ == "__main__":
    main()
