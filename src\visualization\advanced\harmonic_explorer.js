/**
 * Harmonic Pattern Explorer
 * 
 * This module provides a visualization for exploring harmonic patterns
 * in the Comphyological system, particularly the 3-6-9-12-13 pattern
 * and golden ratio relationships.
 */

/**
 * HarmonicExplorer class
 */
class HarmonicExplorer {
  /**
   * Constructor
   * @param {HTMLElement} container - Container element
   * @param {Object} options - Configuration options
   */
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      width: container.clientWidth,
      height: container.clientHeight,
      backgroundColor: '#111133',
      foregroundColor: '#ffffff',
      goldenRatio: 0.618033988749895,
      showGoldenRatio: true,
      show369Pattern: true,
      showFibonacci: true,
      animationDuration: 1000,
      ...options
    };
    
    // Initialize state
    this.state = {
      isInitialized: false,
      patterns: [],
      selectedPattern: null,
      hoveredPoint: null,
      zoomLevel: 1,
      panOffset: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 }
    };
    
    // Initialize canvas
    this.initCanvas();
    
    // Initialize patterns
    this.initPatterns();
    
    // Initialize event listeners
    this.initEventListeners();
    
    // Start animation loop
    this.animate();
    
    // Mark as initialized
    this.state.isInitialized = true;
  }
  
  /**
   * Initialize canvas
   */
  initCanvas() {
    // Create canvas
    this.canvas = document.createElement('canvas');
    this.canvas.width = this.options.width;
    this.canvas.height = this.options.height;
    this.canvas.style.display = 'block';
    this.canvas.style.backgroundColor = this.options.backgroundColor;
    
    this.ctx = this.canvas.getContext('2d');
    
    // Add canvas to container
    this.container.appendChild(this.canvas);
  }
  
  /**
   * Initialize patterns
   */
  initPatterns() {
    // 3-6-9-12-13 Pattern
    const pattern369 = {
      name: '3-6-9-12-13 Pattern',
      description: 'The fundamental resonance pattern of the Comphyological system',
      points: [
        { x: 0, y: 0, value: 3, label: '3 - Foundation' },
        { x: 1, y: 0, value: 6, label: '6 - Capacity' },
        { x: 0, y: 1, value: 9, label: '9 - Engine' },
        { x: 1, y: 1, value: 12, label: '12 - Integration' },
        { x: 0.5, y: 0.5, value: 13, label: '13 - Component' }
      ],
      connections: [
        [0, 1], [0, 2], [1, 3], [2, 3], [0, 4], [1, 4], [2, 4], [3, 4]
      ],
      color: '#ff7700'
    };
    
    // Golden Ratio Pattern
    const goldenRatioPattern = {
      name: 'Golden Ratio Pattern',
      description: 'The divine proportion (φ = 0.618033988749895)',
      points: [
        { x: 0, y: 0, value: 1, label: '1' },
        { x: this.options.goldenRatio, y: 0, value: this.options.goldenRatio, label: 'φ' },
        { x: 1, y: 0, value: 1, label: '1' },
        { x: 0, y: this.options.goldenRatio, value: this.options.goldenRatio, label: 'φ' },
        { x: this.options.goldenRatio, y: this.options.goldenRatio, value: this.options.goldenRatio * this.options.goldenRatio, label: 'φ²' },
        { x: 1, y: this.options.goldenRatio, value: this.options.goldenRatio, label: 'φ' },
        { x: 0, y: 1, value: 1, label: '1' },
        { x: this.options.goldenRatio, y: 1, value: this.options.goldenRatio, label: 'φ' },
        { x: 1, y: 1, value: 1, label: '1' }
      ],
      connections: [
        [0, 1], [1, 2], [0, 3], [3, 6], [1, 4], [4, 7], [2, 5], [5, 8], [6, 7], [7, 8],
        [0, 4], [4, 8], [2, 4], [4, 6]
      ],
      color: '#00aaff'
    };
    
    // Fibonacci Spiral
    const fibonacciPattern = {
      name: 'Fibonacci Spiral',
      description: 'The spiral pattern formed by Fibonacci squares',
      points: [],
      connections: [],
      color: '#22cc44',
      drawFunction: this.drawFibonacciSpiral.bind(this)
    };
    
    // Add patterns to state
    this.state.patterns = [pattern369, goldenRatioPattern, fibonacciPattern];
    this.state.selectedPattern = pattern369;
  }
  
  /**
   * Initialize event listeners
   */
  initEventListeners() {
    // Handle window resize
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // Handle container resize
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.target === this.container) {
          this.onContainerResize();
        }
      }
    });
    
    resizeObserver.observe(this.container);
    
    // Handle mouse events
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.onWheel.bind(this));
    
    // Handle touch events
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this));
  }
  
  /**
   * Handle window resize
   */
  onWindowResize() {
    this.onContainerResize();
  }
  
  /**
   * Handle container resize
   */
  onContainerResize() {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;
    
    this.options.width = width;
    this.options.height = height;
    
    this.canvas.width = width;
    this.canvas.height = height;
    
    // Redraw
    this.draw();
  }
  
  /**
   * Handle mouse down
   * @param {MouseEvent} event - Mouse event
   */
  onMouseDown(event) {
    this.state.isDragging = true;
    this.state.dragStart = {
      x: event.clientX - this.state.panOffset.x,
      y: event.clientY - this.state.panOffset.y
    };
  }
  
  /**
   * Handle mouse move
   * @param {MouseEvent} event - Mouse event
   */
  onMouseMove(event) {
    if (this.state.isDragging) {
      this.state.panOffset = {
        x: event.clientX - this.state.dragStart.x,
        y: event.clientY - this.state.dragStart.y
      };
    }
    
    // Check for hover
    const rect = this.canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    this.checkHover(x, y);
  }
  
  /**
   * Handle mouse up
   * @param {MouseEvent} event - Mouse event
   */
  onMouseUp(event) {
    this.state.isDragging = false;
  }
  
  /**
   * Handle wheel
   * @param {WheelEvent} event - Wheel event
   */
  onWheel(event) {
    event.preventDefault();
    
    const delta = event.deltaY > 0 ? -0.1 : 0.1;
    this.state.zoomLevel = Math.max(0.1, Math.min(5, this.state.zoomLevel + delta));
  }
  
  /**
   * Handle touch start
   * @param {TouchEvent} event - Touch event
   */
  onTouchStart(event) {
    if (event.touches.length === 1) {
      this.state.isDragging = true;
      this.state.dragStart = {
        x: event.touches[0].clientX - this.state.panOffset.x,
        y: event.touches[0].clientY - this.state.panOffset.y
      };
    }
  }
  
  /**
   * Handle touch move
   * @param {TouchEvent} event - Touch event
   */
  onTouchMove(event) {
    if (event.touches.length === 1 && this.state.isDragging) {
      this.state.panOffset = {
        x: event.touches[0].clientX - this.state.dragStart.x,
        y: event.touches[0].clientY - this.state.dragStart.y
      };
    }
  }
  
  /**
   * Handle touch end
   * @param {TouchEvent} event - Touch event
   */
  onTouchEnd(event) {
    this.state.isDragging = false;
  }
  
  /**
   * Check for hover
   * @param {number} x - X coordinate
   * @param {number} y - Y coordinate
   */
  checkHover(x, y) {
    if (!this.state.selectedPattern) {
      return;
    }
    
    const { width, height } = this.options;
    const { zoomLevel, panOffset } = this.state;
    
    // Calculate center
    const centerX = width / 2 + panOffset.x;
    const centerY = height / 2 + panOffset.y;
    
    // Check each point
    for (const point of this.state.selectedPattern.points) {
      const pointX = centerX + (point.x - 0.5) * width * zoomLevel;
      const pointY = centerY + (point.y - 0.5) * height * zoomLevel;
      
      const distance = Math.sqrt(Math.pow(x - pointX, 2) + Math.pow(y - pointY, 2));
      
      if (distance < 15) {
        this.state.hoveredPoint = point;
        return;
      }
    }
    
    this.state.hoveredPoint = null;
  }
  
  /**
   * Animation loop
   */
  animate() {
    requestAnimationFrame(this.animate.bind(this));
    
    // Draw
    this.draw();
  }
  
  /**
   * Draw visualization
   */
  draw() {
    const { width, height, backgroundColor, foregroundColor } = this.options;
    const { selectedPattern, zoomLevel, panOffset, hoveredPoint } = this.state;
    
    // Clear canvas
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(0, 0, width, height);
    
    // Draw selected pattern
    if (selectedPattern) {
      // Calculate center
      const centerX = width / 2 + panOffset.x;
      const centerY = height / 2 + panOffset.y;
      
      // Draw custom pattern if available
      if (selectedPattern.drawFunction) {
        selectedPattern.drawFunction(centerX, centerY, zoomLevel);
        return;
      }
      
      // Draw connections
      this.ctx.strokeStyle = selectedPattern.color;
      this.ctx.lineWidth = 2;
      
      for (const [fromIndex, toIndex] of selectedPattern.connections) {
        const fromPoint = selectedPattern.points[fromIndex];
        const toPoint = selectedPattern.points[toIndex];
        
        const fromX = centerX + (fromPoint.x - 0.5) * width * zoomLevel;
        const fromY = centerY + (fromPoint.y - 0.5) * height * zoomLevel;
        const toX = centerX + (toPoint.x - 0.5) * width * zoomLevel;
        const toY = centerY + (toPoint.y - 0.5) * height * zoomLevel;
        
        this.ctx.beginPath();
        this.ctx.moveTo(fromX, fromY);
        this.ctx.lineTo(toX, toY);
        this.ctx.stroke();
      }
      
      // Draw points
      for (const point of selectedPattern.points) {
        const pointX = centerX + (point.x - 0.5) * width * zoomLevel;
        const pointY = centerY + (point.y - 0.5) * height * zoomLevel;
        
        // Draw point
        this.ctx.fillStyle = point === hoveredPoint ? '#ffffff' : selectedPattern.color;
        this.ctx.beginPath();
        this.ctx.arc(pointX, pointY, point === hoveredPoint ? 10 : 5, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Draw label
        if (point === hoveredPoint) {
          this.ctx.fillStyle = '#ffffff';
          this.ctx.font = '14px Arial';
          this.ctx.textAlign = 'center';
          this.ctx.textBaseline = 'bottom';
          this.ctx.fillText(point.label, pointX, pointY - 15);
          
          // Draw value
          this.ctx.fillText(point.value.toString(), pointX, pointY + 25);
        }
      }
      
      // Draw pattern name
      this.ctx.fillStyle = foregroundColor;
      this.ctx.font = 'bold 16px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.textBaseline = 'top';
      this.ctx.fillText(selectedPattern.name, 10, 10);
      
      // Draw pattern description
      this.ctx.font = '14px Arial';
      this.ctx.fillText(selectedPattern.description, 10, 30);
    }
  }
  
  /**
   * Draw Fibonacci spiral
   * @param {number} centerX - Center X coordinate
   * @param {number} centerY - Center Y coordinate
   * @param {number} zoomLevel - Zoom level
   */
  drawFibonacciSpiral(centerX, centerY, zoomLevel) {
    const { width, height } = this.options;
    const scale = Math.min(width, height) * 0.4 * zoomLevel;
    
    // Fibonacci sequence
    const fib = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89];
    
    // Draw squares
    this.ctx.strokeStyle = this.state.selectedPattern.color;
    this.ctx.lineWidth = 2;
    
    let x = centerX;
    let y = centerY;
    let angle = 0;
    
    for (let i = 0; i < fib.length - 1; i++) {
      const size = fib[i] * scale / fib[fib.length - 1];
      
      // Draw square
      this.ctx.beginPath();
      this.ctx.rect(x, y, size, size);
      this.ctx.stroke();
      
      // Calculate next position
      if (i % 4 === 0) {
        x = x;
        y = y - fib[i + 1] * scale / fib[fib.length - 1];
      } else if (i % 4 === 1) {
        x = x + fib[i] * scale / fib[fib.length - 1];
        y = y - fib[i + 1] * scale / fib[fib.length - 1] + fib[i] * scale / fib[fib.length - 1];
      } else if (i % 4 === 2) {
        x = x + fib[i] * scale / fib[fib.length - 1] - fib[i + 1] * scale / fib[fib.length - 1];
        y = y;
      } else {
        x = x - fib[i + 1] * scale / fib[fib.length - 1];
        y = y;
      }
    }
    
    // Draw spiral
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    
    x = centerX;
    y = centerY;
    angle = 0;
    
    this.ctx.beginPath();
    this.ctx.moveTo(x, y);
    
    for (let t = 0; t <= Math.PI * 2 * 4; t += 0.01) {
      const radius = Math.pow(this.options.goldenRatio, t / Math.PI) * scale / 10;
      const spiralX = x + radius * Math.cos(t);
      const spiralY = y + radius * Math.sin(t);
      
      this.ctx.lineTo(spiralX, spiralY);
    }
    
    this.ctx.stroke();
    
    // Draw pattern name
    this.ctx.fillStyle = this.options.foregroundColor;
    this.ctx.font = 'bold 16px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'top';
    this.ctx.fillText(this.state.selectedPattern.name, 10, 10);
    
    // Draw pattern description
    this.ctx.font = '14px Arial';
    this.ctx.fillText(this.state.selectedPattern.description, 10, 30);
  }
  
  /**
   * Set selected pattern
   * @param {string} patternName - Pattern name
   */
  setSelectedPattern(patternName) {
    const pattern = this.state.patterns.find(p => p.name === patternName);
    
    if (pattern) {
      this.state.selectedPattern = pattern;
    }
  }
  
  /**
   * Reset view
   */
  resetView() {
    this.state.zoomLevel = 1;
    this.state.panOffset = { x: 0, y: 0 };
  }
  
  /**
   * Dispose
   */
  dispose() {
    // Remove event listeners
    window.removeEventListener('resize', this.onWindowResize);
    
    // Remove canvas
    if (this.canvas.parentNode) {
      this.canvas.parentNode.removeChild(this.canvas);
    }
  }
}

// Export
if (typeof module !== 'undefined') {
  module.exports = { HarmonicExplorer };
}
