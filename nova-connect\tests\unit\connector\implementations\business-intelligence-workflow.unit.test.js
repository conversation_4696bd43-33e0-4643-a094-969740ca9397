/**
 * Unit tests for the Business Intelligence & Workflow Connector
 */

const axios = require('axios');
const BusinessIntelligenceWorkflowConnector = require('../../../../connector/implementations/business-intelligence-workflow');

// Mock axios
jest.mock('axios');

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));

describe('BusinessIntelligenceWorkflowConnector', () => {
  let connector;
  let mockConfig;
  let mockCredentials;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock config and credentials
    mockConfig = {
      baseUrl: 'https://api.test.com'
    };
    
    mockCredentials = {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    };
    
    // Create connector instance
    connector = new BusinessIntelligenceWorkflowConnector(mockConfig, mockCredentials);
  });
  
  describe('constructor', () => {
    it('should initialize with provided config and credentials', () => {
      expect(connector.config).toEqual(mockConfig);
      expect(connector.credentials).toEqual(mockCredentials);
      expect(connector.baseUrl).toBe(mockConfig.baseUrl);
    });
    
    it('should use default baseUrl if not provided', () => {
      const connectorWithDefaults = new BusinessIntelligenceWorkflowConnector();
      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');
    });
  });
  
  describe('initialize', () => {
    it('should authenticate if credentials are provided', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockResolvedValue();
      
      await connector.initialize();
      
      expect(connector.authenticate).toHaveBeenCalled();
    });
    
    it('should not authenticate if credentials are not provided', async () => {
      // Create connector without credentials
      const connectorWithoutCredentials = new BusinessIntelligenceWorkflowConnector(mockConfig, {});
      
      // Mock authenticate method
      connectorWithoutCredentials.authenticate = jest.fn().mockResolvedValue();
      
      await connectorWithoutCredentials.initialize();
      
      expect(connectorWithoutCredentials.authenticate).not.toHaveBeenCalled();
    });
  });
  
  describe('authenticate', () => {
    it('should make a POST request to the token endpoint', async () => {
      // Mock axios post response
      axios.post.mockResolvedValue({
        data: {
          access_token: 'test-access-token',
          expires_in: 3600
        }
      });
      
      await connector.authenticate();
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/oauth2/token`,
        {
          grant_type: 'client_credentials',
          client_id: mockCredentials.clientId,
          client_secret: mockCredentials.clientSecret,
          scope: 'read:dashboards write:dashboards read:reports write:reports read:workflows write:workflows'
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(connector.accessToken).toBe('test-access-token');
      expect(connector.tokenExpiry).toBeDefined();
    });
    
    it('should throw an error if authentication fails', async () => {
      // Mock axios post error
      const errorMessage = 'Authentication failed';
      axios.post.mockRejectedValue(new Error(errorMessage));
      
      await expect(connector.authenticate()).rejects.toThrow(`Authentication failed: ${errorMessage}`);
    });
  });
  
  describe('getAuthHeaders', () => {
    it('should return authorization headers with access token', async () => {
      // Set access token and expiry
      connector.accessToken = 'test-access-token';
      connector.tokenExpiry = Date.now() + 3600000; // 1 hour from now
      
      const headers = await connector.getAuthHeaders();
      
      expect(headers).toEqual({
        'Authorization': 'Bearer test-access-token'
      });
    });
    
    it('should authenticate if access token is not set', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockImplementation(() => {
        connector.accessToken = 'new-access-token';
        connector.tokenExpiry = Date.now() + 3600000;
      });
      
      const headers = await connector.getAuthHeaders();
      
      expect(connector.authenticate).toHaveBeenCalled();
      expect(headers).toEqual({
        'Authorization': 'Bearer new-access-token'
      });
    });
  });
  
  describe('listDashboards', () => {
    it('should make a GET request to the dashboards endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'dashboard-1', name: 'Dashboard 1' },
            { id: 'dashboard-2', name: 'Dashboard 2' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { folder: 'Finance', limit: 50 };
      const result = await connector.listDashboards(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/dashboards`,
        {
          params,
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if the request fails', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get error
      const errorMessage = 'Request failed';
      axios.get.mockRejectedValue(new Error(errorMessage));
      
      await expect(connector.listDashboards()).rejects.toThrow(`Error listing dashboards: ${errorMessage}`);
    });
  });
  
  describe('getDashboard', () => {
    it('should make a GET request to the specific dashboard endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'dashboard-123',
          name: 'Test Dashboard',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const dashboardId = 'dashboard-123';
      const result = await connector.getDashboard(dashboardId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/dashboards/${dashboardId}`,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if dashboardId is not provided', async () => {
      await expect(connector.getDashboard()).rejects.toThrow('Dashboard ID is required');
    });
  });
  
  describe('executeReport', () => {
    it('should make a POST request to execute the report', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios post response
      const mockResponse = {
        data: {
          executionId: 'exec-123',
          status: 'success',
          data: [
            { region: 'North America', revenue: 1250000 },
            { region: 'Europe', revenue: 980000 }
          ]
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      
      const reportId = 'report-123';
      const options = {
        parameters: {
          startDate: '2023-01-01',
          endDate: '2023-03-31'
        },
        format: 'json'
      };
      
      const result = await connector.executeReport(reportId, options);
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/reports/${reportId}/execute`,
        options,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if reportId is not provided', async () => {
      await expect(connector.executeReport()).rejects.toThrow('Report ID is required');
    });
  });
  
  describe('listWorkflows', () => {
    it('should make a GET request to the workflows endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'workflow-1', name: 'Workflow 1' },
            { id: 'workflow-2', name: 'Workflow 2' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { status: 'active', category: 'Finance' };
      const result = await connector.listWorkflows(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/workflows`,
        {
          params,
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
  });
  
  describe('executeWorkflow', () => {
    it('should make a POST request to execute the workflow', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios post response
      const mockResponse = {
        data: {
          executionId: 'exec-123',
          status: 'queued',
          startTime: '2023-06-01T10:15:30Z'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      
      const workflowId = 'workflow-123';
      const options = {
        input: {
          invoiceId: 'INV-12345',
          amount: 1500
        },
        async: true
      };
      
      const result = await connector.executeWorkflow(workflowId, options);
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/workflows/${workflowId}/execute`,
        options,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if workflowId is not provided', async () => {
      await expect(connector.executeWorkflow()).rejects.toThrow('Workflow ID is required');
    });
  });
});
