/**
 * mTOR Activation Monitoring
 * 
 * This module implements the mTOR Activation Monitoring component of the CSME.
 * It tracks mTOR pathway activity and its effects on cellular metabolism.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * MTORActivationMonitoring class
 */
class MTORActivationMonitoring extends EventEmitter {
  /**
   * Create a new MTORActivationMonitoring instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 8000, // ms
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      thresholds: {
        activation: {
          low: 0.3,
          moderate: 0.6,
          high: 0.8,
          excessive: 0.9
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      activationLevel: 0.5, // Normalized mTOR activation level (0-1)
      activationHistory: [],
      activationStatus: 'moderate', // low, moderate, high, excessive
      modulators: new Map(), // id -> modulator object
      downstreamEffects: {
        proteinSynthesis: 0.5,
        cellGrowth: 0.5,
        autophagy: 0.5,
        metabolism: 0.5
      },
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      modulatorsAdded: 0,
      modulatorsRemoved: 0,
      statusChanges: 0
    };
    
    if (this.options.enableLogging) {
      console.log('MTORActivationMonitoring initialized');
    }
  }
  
  /**
   * Start the mTOR activation monitoring
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('MTORActivationMonitoring is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('MTORActivationMonitoring started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the mTOR activation monitoring
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('MTORActivationMonitoring is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('MTORActivationMonitoring stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Set mTOR activation level
   * @param {number} level - Activation level (0-1)
   * @param {Object} metadata - Additional metadata
   * @returns {number} - Updated activation level
   */
  setActivationLevel(level, metadata = {}) {
    const startTime = performance.now();
    
    if (typeof level !== 'number' || level < 0 || level > 1) {
      throw new Error('Activation level must be a number between 0 and 1');
    }
    
    // Update state
    this.state.activationLevel = level;
    this.state.lastUpdateTime = Date.now();
    
    // Update activation status
    this._updateActivationStatus();
    
    // Update downstream effects
    this._updateDownstreamEffects();
    
    // Add to history
    this.state.activationHistory.push({
      activationLevel: this.state.activationLevel,
      activationStatus: this.state.activationStatus,
      downstreamEffects: { ...this.state.downstreamEffects },
      timestamp: Date.now(),
      metadata
    });
    
    // Limit history size
    if (this.state.activationHistory.length > this.options.historySize) {
      this.state.activationHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('activation-update', {
      activationLevel: this.state.activationLevel,
      activationStatus: this.state.activationStatus,
      downstreamEffects: { ...this.state.downstreamEffects },
      timestamp: Date.now()
    });
    
    return this.state.activationLevel;
  }
  
  /**
   * Add mTOR modulator
   * @param {Object} modulator - Modulator object
   * @returns {Object} - Added modulator
   */
  addModulator(modulator) {
    const startTime = performance.now();
    
    if (!modulator || typeof modulator !== 'object') {
      throw new Error('Modulator must be an object');
    }
    
    if (!modulator.id) {
      modulator.id = `mod-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    modulator = {
      type: 'generic', // nutrient, drug, stress, etc.
      effect: 'activation', // activation or inhibition
      strength: 0.5, // 0-1 (strength of effect)
      duration: 24, // hours
      status: 'active', // active, inactive
      startedAt: Date.now(),
      ...modulator
    };
    
    // Add to state
    this.state.modulators.set(modulator.id, modulator);
    
    // Update activation level
    this._updateActivationLevel();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.modulatorsAdded++;
    
    // Emit event
    this.emit('modulator-added', modulator);
    
    if (this.options.enableLogging) {
      console.log(`MTORActivationMonitoring: Added ${modulator.type} modulator ${modulator.id}`);
    }
    
    return modulator;
  }
  
  /**
   * Remove mTOR modulator
   * @param {string} modulatorId - Modulator ID
   * @returns {boolean} - Success status
   */
  removeModulator(modulatorId) {
    const startTime = performance.now();
    
    if (!modulatorId || !this.state.modulators.has(modulatorId)) {
      return false;
    }
    
    // Get modulator
    const modulator = this.state.modulators.get(modulatorId);
    
    // Remove from state
    this.state.modulators.delete(modulatorId);
    
    // Update activation level
    this._updateActivationLevel();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.modulatorsRemoved++;
    
    // Emit event
    this.emit('modulator-removed', modulator);
    
    if (this.options.enableLogging) {
      console.log(`MTORActivationMonitoring: Removed modulator ${modulatorId}`);
    }
    
    return true;
  }
  
  /**
   * Get activation level
   * @returns {number} - Current activation level
   */
  getActivationLevel() {
    return this.state.activationLevel;
  }
  
  /**
   * Get activation status
   * @returns {string} - Current activation status
   */
  getActivationStatus() {
    return this.state.activationStatus;
  }
  
  /**
   * Get downstream effects
   * @returns {Object} - Current downstream effects
   */
  getDownstreamEffects() {
    return { ...this.state.downstreamEffects };
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return {
      activationLevel: this.state.activationLevel,
      activationStatus: this.state.activationStatus,
      downstreamEffects: { ...this.state.downstreamEffects },
      modulatorCount: this.state.modulators.size,
      activationHistory: [...this.state.activationHistory],
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get modulators
   * @param {string} type - Optional type filter
   * @returns {Array} - Modulators
   */
  getModulators(type) {
    const modulators = Array.from(this.state.modulators.values());
    
    if (type) {
      return modulators.filter(m => m.type === type);
    }
    
    return modulators;
  }
  
  /**
   * Update activation status
   * @private
   */
  _updateActivationStatus() {
    const { activationLevel } = this.state;
    const { thresholds } = this.options;
    
    let newStatus = 'moderate';
    
    if (activationLevel >= thresholds.activation.excessive) {
      newStatus = 'excessive';
    } else if (activationLevel >= thresholds.activation.high) {
      newStatus = 'high';
    } else if (activationLevel <= thresholds.activation.low) {
      newStatus = 'low';
    }
    
    // If status changed, emit event
    if (newStatus !== this.state.activationStatus) {
      this.state.activationStatus = newStatus;
      this.metrics.statusChanges++;
      
      // Emit status change event
      this.emit('status-change', {
        activationStatus: this.state.activationStatus,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`MTORActivationMonitoring: Activation status changed to ${this.state.activationStatus}`);
      }
    }
  }
  
  /**
   * Update downstream effects
   * @private
   */
  _updateDownstreamEffects() {
    const { activationLevel } = this.state;
    
    // Update protein synthesis (directly proportional to activation)
    this.state.downstreamEffects.proteinSynthesis = activationLevel;
    
    // Update cell growth (directly proportional to activation)
    this.state.downstreamEffects.cellGrowth = activationLevel;
    
    // Update autophagy (inversely proportional to activation)
    this.state.downstreamEffects.autophagy = 1 - activationLevel;
    
    // Update metabolism (complex relationship with activation)
    // High at moderate activation, lower at extremes
    this.state.downstreamEffects.metabolism = 1 - Math.abs(activationLevel - 0.5) * 2;
  }
  
  /**
   * Update activation level based on modulators
   * @private
   */
  _updateActivationLevel() {
    // Start with baseline activation (0.5)
    let activationLevel = 0.5;
    
    // Apply modulator effects
    for (const modulator of this.state.modulators.values()) {
      if (modulator.status === 'active') {
        if (modulator.effect === 'activation') {
          // Activators increase activation level
          activationLevel += modulator.strength * 0.5;
        } else if (modulator.effect === 'inhibition') {
          // Inhibitors decrease activation level
          activationLevel -= modulator.strength * 0.5;
        }
      }
    }
    
    // Ensure activation level is between 0 and 1
    activationLevel = Math.max(0, Math.min(1, activationLevel));
    
    // Update activation level
    this.setActivationLevel(activationLevel, { source: 'modulator_update' });
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // In a real implementation, this would fetch real-time data
        // For now, just simulate some changes
        this._simulateChanges();
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Simulate changes
   * @private
   */
  _simulateChanges() {
    // Simulate random changes to modulators
    const rand = Math.random();
    
    if (rand < 0.1) {
      // Add a new modulator
      this.addModulator({
        type: this._randomModulatorType(),
        effect: Math.random() < 0.5 ? 'activation' : 'inhibition',
        strength: Math.random() * 0.5,
        duration: Math.floor(Math.random() * 24) + 1
      });
    } else if (rand < 0.2 && this.state.modulators.size > 0) {
      // Remove a random modulator
      const modulators = Array.from(this.state.modulators.keys());
      const randomModulatorId = modulators[Math.floor(Math.random() * modulators.length)];
      this.removeModulator(randomModulatorId);
    }
  }
  
  /**
   * Generate random modulator type
   * @returns {string} - Random modulator type
   * @private
   */
  _randomModulatorType() {
    const types = [
      'nutrient',
      'drug',
      'stress',
      'growth_factor',
      'hormone'
    ];
    
    return types[Math.floor(Math.random() * types.length)];
  }
}

module.exports = MTORActivationMonitoring;
