/**
 * NovaFuse Splunk Connector
 * 
 * This module provides integration between NovaFuse and Splunk,
 * allowing NovaFuse events to be sent to Splunk for monitoring and analysis.
 */

const axios = require('axios');
const crypto = require('crypto');

class SplunkConnector {
  /**
   * Create a new Splunk connector
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      splunkUrl: options.splunkUrl || 'https://localhost:8088',
      token: options.token || '',
      index: options.index || 'novafuse',
      sourcetype: options.sourcetype || 'novafuse:json',
      source: options.source || 'novafuse',
      batchSize: options.batchSize || 100,
      batchInterval: options.batchInterval || 5000, // 5 seconds
      retryCount: options.retryCount || 3,
      retryInterval: options.retryInterval || 1000, // 1 second
      enableCompression: options.enableCompression !== false,
      logLevel: options.logLevel || 'info',
      ...options
    };
    
    this.eventQueue = [];
    this.isSending = false;
    this.batchTimer = null;
    
    // Start batch timer
    this._startBatchTimer();
    
    console.log(`Splunk connector initialized with index: ${this.options.index}`);
  }
  
  /**
   * Send an event to Splunk
   * @param {Object} event - Event data
   * @param {Object} [metadata] - Additional metadata
   * @returns {Promise<boolean>} - Whether the event was queued successfully
   */
  async sendEvent(event, metadata = {}) {
    try {
      // Add metadata
      const enrichedEvent = {
        ...event,
        _time: Math.floor(Date.now() / 1000),
        _metadata: {
          source: this.options.source,
          sourcetype: this.options.sourcetype,
          index: this.options.index,
          ...metadata
        }
      };
      
      // Add to queue
      this.eventQueue.push(enrichedEvent);
      
      // Send batch if queue is full
      if (this.eventQueue.length >= this.options.batchSize) {
        this._sendBatch();
      }
      
      return true;
    } catch (error) {
      console.error('Error queueing event for Splunk:', error);
      return false;
    }
  }
  
  /**
   * Send Trinity CSDE event to Splunk
   * @param {Object} trinityEvent - Trinity CSDE event data
   * @returns {Promise<boolean>} - Whether the event was queued successfully
   */
  async sendTrinityEvent(trinityEvent) {
    return this.sendEvent(trinityEvent, {
      sourcetype: 'novafuse:trinity:json',
      component: 'trinity_csde'
    });
  }
  
  /**
   * Send Quantum Inference event to Splunk
   * @param {Object} quantumEvent - Quantum Inference event data
   * @returns {Promise<boolean>} - Whether the event was queued successfully
   */
  async sendQuantumEvent(quantumEvent) {
    return this.sendEvent(quantumEvent, {
      sourcetype: 'novafuse:quantum:json',
      component: 'quantum_inference'
    });
  }
  
  /**
   * Send security event to Splunk
   * @param {Object} securityEvent - Security event data
   * @returns {Promise<boolean>} - Whether the event was queued successfully
   */
  async sendSecurityEvent(securityEvent) {
    return this.sendEvent(securityEvent, {
      sourcetype: 'novafuse:security:json',
      component: 'security'
    });
  }
  
  /**
   * Flush all queued events
   * @returns {Promise<boolean>} - Whether the flush was successful
   */
  async flush() {
    if (this.eventQueue.length === 0) {
      return true;
    }
    
    return this._sendBatch();
  }
  
  /**
   * Start batch timer
   * @private
   */
  _startBatchTimer() {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }
    
    this.batchTimer = setInterval(() => {
      if (this.eventQueue.length > 0 && !this.isSending) {
        this._sendBatch();
      }
    }, this.options.batchInterval);
  }
  
  /**
   * Send batch of events to Splunk
   * @private
   * @returns {Promise<boolean>} - Whether the batch was sent successfully
   */
  async _sendBatch() {
    if (this.isSending || this.eventQueue.length === 0) {
      return false;
    }
    
    this.isSending = true;
    
    try {
      // Get events to send
      const events = this.eventQueue.splice(0, this.options.batchSize);
      
      // Prepare payload
      const payload = events.map(event => ({
        event,
        sourcetype: event._metadata.sourcetype || this.options.sourcetype,
        source: event._metadata.source || this.options.source,
        index: event._metadata.index || this.options.index,
        time: event._time
      }));
      
      // Send to Splunk
      const response = await this._sendToSplunk(payload);
      
      this.isSending = false;
      return response.status === 200;
    } catch (error) {
      console.error('Error sending batch to Splunk:', error);
      this.isSending = false;
      return false;
    }
  }
  
  /**
   * Send data to Splunk
   * @private
   * @param {Array} payload - Data to send
   * @param {number} [retryCount=0] - Current retry count
   * @returns {Promise<Object>} - Axios response
   */
  async _sendToSplunk(payload, retryCount = 0) {
    try {
      const headers = {
        'Authorization': `Splunk ${this.options.token}`,
        'Content-Type': 'application/json'
      };
      
      if (this.options.enableCompression) {
        headers['Content-Encoding'] = 'gzip';
      }
      
      const response = await axios.post(
        `${this.options.splunkUrl}/services/collector`,
        payload,
        { headers }
      );
      
      return response;
    } catch (error) {
      if (retryCount < this.options.retryCount) {
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.options.retryInterval));
        
        // Retry
        return this._sendToSplunk(payload, retryCount + 1);
      }
      
      throw error;
    }
  }
  
  /**
   * Close the connector
   */
  close() {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
      this.batchTimer = null;
    }
    
    // Flush remaining events
    if (this.eventQueue.length > 0) {
      this._sendBatch().catch(error => {
        console.error('Error flushing events on close:', error);
      });
    }
  }
}

module.exports = SplunkConnector;
