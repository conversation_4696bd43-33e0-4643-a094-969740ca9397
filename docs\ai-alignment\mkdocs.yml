site_name: NovaAlign Documentation
site_url: https://docs.novaalign.ai
site_author: NovaAlign Team
site_description: Documentation for NovaAlign AI Alignment Framework

# Documentation directory
docs_dir: docs
site_dir: site

# Repository
repo_name: nova-align/docs
repo_url: https://github.com/your-org/nova-align
edit_uri: edit/main/docs/ai-alignment/

theme:
  name: material
  features:
    - navigation.tabs
    - navigation.indexes
    - navigation.top
    - navigation.sections
    - navigation.expand
    - navigation.footer
    - search.highlight
    - search.suggest
    - toc.integrate
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/weather-sunny
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: indigo
      accent: indigo
      toggle:
        icon: material/weather-night
        name: Switch to light mode

# Extensions
markdown_extensions:
  - admonition
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.tabbed
  - pymdownx.tilde
  - toc:
      permalink: true

# Navigation
nav:
  - Home: index.md
  - Getting Started:
    - Introduction: getting-started/introduction.md
    - Quick Start: getting-started/quick-start.md
    - Installation: getting-started/installation.md
  - User Guide:
    - Dashboard: user-guide/dashboard.md
    - Features: user-guide/features.md
    - Best Practices: user-guide/best-practices.md
  - API Reference:
    - Authentication: api/authentication.md
    - Endpoints: api/endpoints.md
    - Examples: api/examples.md
  - Deployment:
    - Installation: deployment/installation.md
    - Configuration: deployment/configuration.md
    - Maintenance: deployment/maintenance.md
  - Contributing: CONTRIBUTING.md

# Plugins
plugins:
  - search
  - mkdocstrings:
      default_handler: python
      handlers:
        python:
          options:
            docstring_style: google
            show_root_heading: false
            show_root_full_path: false
  - minify:
      minify_html: true
      minify_js: true
      minify_css: true

# Extra
extra_css:
  - css/extra.css

extra_javascript:
  - js/extra.js

# Copyright
copyright: Copyright &copy; 2025 NovaAlign Team
