describe('NovaConcierge Chat', () => {
  beforeEach(() => {
    // Visit the homepage
    cy.visit('/');
  });

  it('should display the NovaConcierge chat button', () => {
    cy.get('[data-testid="nova-concierge-button"]').should('be.visible');
  });

  it('should open the chat window when clicked', () => {
    cy.get('[data-testid="nova-concierge-button"]').click();
    cy.get('[data-testid="nova-concierge-window"]').should('be.visible');
    cy.contains('NovaConcierge').should('be.visible');
    cy.contains('Your API integration assistant').should('be.visible');
  });

  it('should display welcome message', () => {
    cy.get('[data-testid="nova-concierge-button"]').click();
    cy.get('[data-testid="chat-messages"]').contains("Hello! I'm NovaConcierge").should('be.visible');
  });

  it('should allow sending a message', () => {
    cy.get('[data-testid="nova-concierge-button"]').click();
    
    // Type and send a message
    cy.get('[data-testid="chat-input"]').type('Tell me about NovaConnect');
    cy.get('[data-testid="chat-form"]').submit();
    
    // Check that the message appears in the chat
    cy.get('[data-testid="chat-messages"]').contains('Tell me about NovaConnect').should('be.visible');
    
    // Check that the AI responds
    cy.get('[data-testid="chat-messages"]').contains('NovaConnect is our Universal API Connector', { timeout: 10000 }).should('be.visible');
  });

  it('should display suggestion buttons', () => {
    cy.get('[data-testid="nova-concierge-button"]').click();
    cy.get('[data-testid="chat-suggestions"]').should('be.visible');
    cy.get('[data-testid="chat-suggestions"]').contains('Compare NovaFuse to competitors').should('be.visible');
  });

  it('should respond to suggestion clicks', () => {
    cy.get('[data-testid="nova-concierge-button"]').click();
    
    // Click a suggestion
    cy.get('[data-testid="chat-suggestions"]').contains('Compare NovaFuse to competitors').click();
    
    // Check that the suggestion appears as a user message
    cy.get('[data-testid="chat-messages"]').contains('Compare NovaFuse to competitors').should('be.visible');
    
    // Check that the AI responds with comparison data
    cy.get('[data-testid="chat-messages"]').contains('NovaFuse vs. Traditional GRC Solutions', { timeout: 10000 }).should('be.visible');
  });

  it('should close the chat window', () => {
    cy.get('[data-testid="nova-concierge-button"]').click();
    cy.get('[data-testid="close-chat-button"]').click();
    cy.get('[data-testid="nova-concierge-window"]').should('not.exist');
  });
});
