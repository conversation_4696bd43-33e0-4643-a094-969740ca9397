const axios = require('axios');

console.log('🔥 STARTING CONTINUOUS KETHERNET TRAFFIC...');
console.log('🌐 Simulating real-world blockchain activity...\n');

let requestCount = 0;
let crownNodes = 0;
let successCount = 0;

async function simulateTraffic() {
  try {
    // Random consciousness validation
    const consciousnessData = {
      neural: Math.floor(Math.random() * 20) + 5,
      information: Math.floor(Math.random() * 25) + 10,
      coherence: Math.floor(Math.random() * 30) + 15
    };
    
    const response = await axios.post('http://localhost:8080/consciousness/validate', consciousnessData);
    requestCount++;
    successCount++;
    
    if (response.data.isValid) {
      crownNodes++;
      console.log(`👑 NEW CROWN NODE #${crownNodes}: UUFT=${response.data.uuftScore} (threshold: ${response.data.threshold})`);
    } else {
      console.log(`🔍 Candidate Node: UUFT=${response.data.uuftScore} (below threshold)`);
    }
    
    // Every 10 requests, show stats
    if (requestCount % 10 === 0) {
      const statsResponse = await axios.get('http://localhost:8080/stats');
      console.log(`📊 Block Height: ${statsResponse.data.blockchain.blockHeight} | Crown Nodes: ${crownNodes} | Total Requests: ${requestCount}`);
      console.log(`💚 Success Rate: ${((successCount/requestCount)*100).toFixed(1)}% | Coherium Supply: ${statsResponse.data.tokens.coherium.supply.toLocaleString()}`);
      console.log('---');
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    requestCount++;
  }
}

// Start continuous traffic
setInterval(simulateTraffic, 2000);

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n🎯 FINAL TRAFFIC STATS:');
  console.log(`   Total Requests: ${requestCount}`);
  console.log(`   Successful Requests: ${successCount}`);
  console.log(`   Crown Nodes Created: ${crownNodes}`);
  console.log(`   Success Rate: ${((successCount/requestCount)*100).toFixed(1)}%`);
  console.log('\n🛑 Traffic simulation stopped');
  process.exit(0);
});
