'use client';

interface ConsciousnessDashboardProps {
  consciousnessData: {
    overall: number;
    content: number;
    intent: number;
    coherence: number;
    psiSnap: boolean;
    analysisTime: number;
  };
}

export default function ConsciousnessDashboard({ consciousnessData }: ConsciousnessDashboardProps) {
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-500/10 border-green-400/30';
    if (score >= 70) return 'bg-yellow-500/10 border-yellow-400/30';
    return 'bg-red-500/10 border-red-400/30';
  };

  return (
    <aside className="w-80 border-r border-purple-500/20 bg-black/10 backdrop-blur-xl p-6 overflow-y-auto">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-xl font-bold text-white mb-2">🧬 Consciousness Analysis</h3>
          <p className="text-sm text-purple-300">Real-time CBE monitoring</p>
        </div>

        {/* Overall Coherence */}
        <div className={`rounded-xl p-6 border ${getScoreBackground(consciousnessData.overall)}`}>
          <div className="text-center">
            <div className="text-xs text-gray-400 mb-2">Overall Coherence</div>
            <div className={`text-4xl font-bold font-mono mb-2 ${getScoreColor(consciousnessData.overall)}`}>
              {consciousnessData.overall}%
            </div>
            <div className="text-xs text-gray-500">Structural • Functional • Relational</div>
          </div>
        </div>

        {/* Ψ-Snap Status */}
        <div className={`rounded-xl p-6 border ${consciousnessData.psiSnap ? 'bg-green-500/10 border-green-400/30' : 'bg-red-500/10 border-red-400/30'}`}>
          <div className="text-center">
            <div className="text-xs text-gray-400 mb-2">Ψ-Snap Status</div>
            <div className={`text-2xl font-bold font-mono mb-2 ${consciousnessData.psiSnap ? 'text-green-400' : 'text-red-400'}`}>
              {consciousnessData.psiSnap ? 'ACTIVE' : 'INACTIVE'}
            </div>
            <div className="text-xs text-gray-500">82/18 Comphyological Model</div>
          </div>
        </div>

        {/* Detailed Metrics */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-white">Detailed Metrics</h4>
          
          {/* Content Analysis */}
          <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Content Quality</span>
              <span className={`font-mono text-sm ${getScoreColor(consciousnessData.content)}`}>
                {consciousnessData.content}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  consciousnessData.content >= 90 ? 'bg-green-400' :
                  consciousnessData.content >= 70 ? 'bg-yellow-400' : 'bg-red-400'
                }`}
                style={{ width: `${consciousnessData.content}%` }}
              />
            </div>
          </div>

          {/* Intent Analysis */}
          <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Intent Clarity</span>
              <span className={`font-mono text-sm ${getScoreColor(consciousnessData.intent)}`}>
                {consciousnessData.intent}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  consciousnessData.intent >= 90 ? 'bg-green-400' :
                  consciousnessData.intent >= 70 ? 'bg-yellow-400' : 'bg-red-400'
                }`}
                style={{ width: `${consciousnessData.intent}%` }}
              />
            </div>
          </div>

          {/* Coherence Score */}
          <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Coherence Level</span>
              <span className={`font-mono text-sm ${getScoreColor(consciousnessData.coherence)}`}>
                {consciousnessData.coherence}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  consciousnessData.coherence >= 90 ? 'bg-green-400' :
                  consciousnessData.coherence >= 70 ? 'bg-yellow-400' : 'bg-red-400'
                }`}
                style={{ width: `${consciousnessData.coherence}%` }}
              />
            </div>
          </div>
        </div>

        {/* Analysis Info */}
        <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-sm font-semibold text-white mb-3">Analysis Details</h4>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-400">Analysis Time:</span>
              <span className="text-purple-300 font-mono">{consciousnessData.analysisTime}ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Engine:</span>
              <span className="text-purple-300">N³C WASM</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Threshold:</span>
              <span className="text-purple-300 font-mono">2847</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Mode:</span>
              <span className="text-green-400">CBE Active</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-3">
          <h4 className="text-lg font-semibold text-white">Quick Actions</h4>
          <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
            🔍 Deep Consciousness Scan
          </button>
          <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
            ⚡ Ψ-Snap Analysis
          </button>
          <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
            🌌 Divine Enhancement
          </button>
        </div>

        {/* Live Status */}
        <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-lg p-4 border border-purple-400/30">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-semibold text-white">Live Monitoring</span>
          </div>
          <p className="text-xs text-gray-300">
            CBE is actively monitoring consciousness patterns and providing real-time analysis.
          </p>
        </div>
      </div>
    </aside>
  );
}
