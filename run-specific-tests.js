/**
 * <PERSON><PERSON>t to run specific tests
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const testFiles = [
  'tests/unit/connectors/connector-model.test.js',
  'tests/unit/utils/cache.test.js',
  'tests/unit/connectors/connector-registry.test.js'
];

// Create results directory if it doesn't exist
const resultsDir = path.join(__dirname, 'test-results');
if (!fs.existsSync(resultsDir)) {
  fs.mkdirSync(resultsDir, { recursive: true });
}

// Run each test file
console.log('Running specific tests...');
let passedTests = 0;
let failedTests = 0;

testFiles.forEach(testFile => {
  try {
    console.log(`\nRunning ${testFile}...`);
    execSync(`npx jest ${testFile} --verbose`, { stdio: 'inherit' });
    console.log(`✅ ${testFile} passed`);
    passedTests++;
  } catch (error) {
    console.error(`❌ ${testFile} failed`);
    failedTests++;
  }
});

// Print summary
console.log('\n--- Test Summary ---');
console.log(`Total: ${testFiles.length}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${failedTests}`);

// Exit with appropriate code
process.exit(failedTests > 0 ? 1 : 0);
