/**
 * Analytics Service
 *
 * This service handles collection and retrieval of API usage and performance analytics.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class AnalyticsService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.analyticsDir = path.join(this.dataDir, 'analytics');
    this.usageFile = path.join(this.analyticsDir, 'usage.json');
    this.performanceFile = path.join(this.analyticsDir, 'performance.json');
    this.errorFile = path.join(this.analyticsDir, 'errors.json');
    this.reportsFile = path.join(this.analyticsDir, 'reports.json');
    this.reportTemplatesFile = path.join(this.analyticsDir, 'report_templates.json');
    this.retentionDays = 30; // Keep analytics data for 30 days
    this.ensureDataDir();

    // Clean up old analytics data once a day
    setInterval(() => this.cleanupOldData(), 24 * 60 * 60 * 1000);
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.analyticsDir, { recursive: true });

      // Initialize files if they don't exist
      await this.initializeFile(this.usageFile);
      await this.initializeFile(this.performanceFile);
      await this.initializeFile(this.errorFile);
      await this.initializeFile(this.reportsFile);
      await this.initializeFile(this.reportTemplatesFile, this.getDefaultReportTemplates());
    } catch (error) {
      console.error('Error creating analytics directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with empty data if it doesn't exist
   */
  async initializeFile(filePath, defaultData = []) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Track API usage
   */
  async trackUsage(data) {
    try {
      const usageData = await this.loadData(this.usageFile);

      // Create usage record
      const record = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        endpoint: data.endpoint,
        method: data.method,
        userId: data.userId || null,
        apiKeyId: data.apiKeyId || null,
        userAgent: data.userAgent || null,
        ip: data.ip || null,
        responseStatus: data.responseStatus,
        responseTime: data.responseTime || null,
        requestSize: data.requestSize || null,
        responseSize: data.responseSize || null
      };

      usageData.push(record);

      // Limit the size of the usage data
      if (usageData.length > 10000) {
        usageData.splice(0, usageData.length - 10000);
      }

      await this.saveData(this.usageFile, usageData);

      return record;
    } catch (error) {
      console.error('Error tracking usage:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Track API performance
   */
  async trackPerformance(data) {
    try {
      const performanceData = await this.loadData(this.performanceFile);

      // Create performance record
      const record = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        endpoint: data.endpoint,
        method: data.method,
        responseTime: data.responseTime,
        dbQueryTime: data.dbQueryTime || null,
        processingTime: data.processingTime || null,
        memoryUsage: process.memoryUsage().heapUsed,
        cpuUsage: process.cpuUsage()
      };

      performanceData.push(record);

      // Limit the size of the performance data
      if (performanceData.length > 10000) {
        performanceData.splice(0, performanceData.length - 10000);
      }

      await this.saveData(this.performanceFile, performanceData);

      return record;
    } catch (error) {
      console.error('Error tracking performance:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Track API errors
   */
  async trackError(data) {
    try {
      const errorData = await this.loadData(this.errorFile);

      // Create error record
      const record = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        endpoint: data.endpoint,
        method: data.method,
        userId: data.userId || null,
        apiKeyId: data.apiKeyId || null,
        userAgent: data.userAgent || null,
        ip: data.ip || null,
        errorCode: data.errorCode || null,
        errorMessage: data.errorMessage,
        errorStack: data.errorStack || null,
        requestBody: data.requestBody || null
      };

      errorData.push(record);

      // Limit the size of the error data
      if (errorData.length > 5000) {
        errorData.splice(0, errorData.length - 5000);
      }

      await this.saveData(this.errorFile, errorData);

      return record;
    } catch (error) {
      console.error('Error tracking error:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Get usage analytics
   */
  async getUsageAnalytics(filters = {}) {
    const usageData = await this.loadData(this.usageFile);

    // Apply filters
    let filteredData = usageData;

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) >= startDate);
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) <= endDate);
    }

    if (filters.endpoint) {
      filteredData = filteredData.filter(record => record.endpoint.includes(filters.endpoint));
    }

    if (filters.method) {
      filteredData = filteredData.filter(record => record.method === filters.method);
    }

    if (filters.userId) {
      filteredData = filteredData.filter(record => record.userId === filters.userId);
    }

    if (filters.apiKeyId) {
      filteredData = filteredData.filter(record => record.apiKeyId === filters.apiKeyId);
    }

    if (filters.responseStatus) {
      filteredData = filteredData.filter(record => record.responseStatus === filters.responseStatus);
    }

    // Calculate summary statistics
    const summary = {
      totalRequests: filteredData.length,
      uniqueEndpoints: new Set(filteredData.map(record => record.endpoint)).size,
      uniqueUsers: new Set(filteredData.filter(record => record.userId).map(record => record.userId)).size,
      uniqueApiKeys: new Set(filteredData.filter(record => record.apiKeyId).map(record => record.apiKeyId)).size,
      averageResponseTime: filteredData.length > 0
        ? filteredData.reduce((sum, record) => sum + (record.responseTime || 0), 0) / filteredData.length
        : 0,
      successRate: filteredData.length > 0
        ? (filteredData.filter(record => record.responseStatus >= 200 && record.responseStatus < 300).length / filteredData.length) * 100
        : 0,
      errorRate: filteredData.length > 0
        ? (filteredData.filter(record => record.responseStatus >= 400).length / filteredData.length) * 100
        : 0
    };

    // Get top endpoints by usage
    const endpointCounts = {};
    filteredData.forEach(record => {
      endpointCounts[record.endpoint] = (endpointCounts[record.endpoint] || 0) + 1;
    });

    const topEndpoints = Object.entries(endpointCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([endpoint, count]) => ({ endpoint, count }));

    // Get usage by time
    const usageByHour = Array(24).fill(0);
    const usageByDay = Array(7).fill(0);
    const usageByDate = {};

    filteredData.forEach(record => {
      const date = new Date(record.timestamp);
      const hour = date.getHours();
      const day = date.getDay();
      const dateStr = date.toISOString().split('T')[0];

      usageByHour[hour]++;
      usageByDay[day]++;
      usageByDate[dateStr] = (usageByDate[dateStr] || 0) + 1;
    });

    // Format usage by date for chart
    const usageByDateArray = Object.entries(usageByDate)
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([date, count]) => ({ date, count }));

    return {
      summary,
      topEndpoints,
      usageByHour,
      usageByDay,
      usageByDate: usageByDateArray,
      recentRequests: filteredData.slice(-100).reverse() // Last 100 requests
    };
  }

  /**
   * Get performance analytics
   */
  async getPerformanceAnalytics(filters = {}) {
    const performanceData = await this.loadData(this.performanceFile);

    // Apply filters
    let filteredData = performanceData;

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) >= startDate);
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) <= endDate);
    }

    if (filters.endpoint) {
      filteredData = filteredData.filter(record => record.endpoint.includes(filters.endpoint));
    }

    if (filters.method) {
      filteredData = filteredData.filter(record => record.method === filters.method);
    }

    // Calculate summary statistics
    const summary = {
      totalRequests: filteredData.length,
      averageResponseTime: filteredData.length > 0
        ? filteredData.reduce((sum, record) => sum + record.responseTime, 0) / filteredData.length
        : 0,
      minResponseTime: filteredData.length > 0
        ? Math.min(...filteredData.map(record => record.responseTime))
        : 0,
      maxResponseTime: filteredData.length > 0
        ? Math.max(...filteredData.map(record => record.responseTime))
        : 0,
      p95ResponseTime: filteredData.length > 0
        ? this.calculatePercentile(filteredData.map(record => record.responseTime), 95)
        : 0,
      averageMemoryUsage: filteredData.length > 0
        ? filteredData.reduce((sum, record) => sum + record.memoryUsage, 0) / filteredData.length
        : 0
    };

    // Get top endpoints by response time
    const endpointResponseTimes = {};
    const endpointCounts = {};

    filteredData.forEach(record => {
      if (!endpointResponseTimes[record.endpoint]) {
        endpointResponseTimes[record.endpoint] = 0;
        endpointCounts[record.endpoint] = 0;
      }

      endpointResponseTimes[record.endpoint] += record.responseTime;
      endpointCounts[record.endpoint]++;
    });

    const endpointAverages = Object.entries(endpointResponseTimes)
      .map(([endpoint, totalTime]) => ({
        endpoint,
        averageResponseTime: totalTime / endpointCounts[endpoint]
      }))
      .sort((a, b) => b.averageResponseTime - a.averageResponseTime);

    const slowestEndpoints = endpointAverages.slice(0, 10);

    // Get response time by time
    const responseTimeByHour = Array(24).fill(null).map(() => ({ count: 0, total: 0 }));
    const responseTimeByDay = Array(7).fill(null).map(() => ({ count: 0, total: 0 }));
    const responseTimeByDate = {};

    filteredData.forEach(record => {
      const date = new Date(record.timestamp);
      const hour = date.getHours();
      const day = date.getDay();
      const dateStr = date.toISOString().split('T')[0];

      responseTimeByHour[hour].count++;
      responseTimeByHour[hour].total += record.responseTime;

      responseTimeByDay[day].count++;
      responseTimeByDay[day].total += record.responseTime;

      if (!responseTimeByDate[dateStr]) {
        responseTimeByDate[dateStr] = { count: 0, total: 0 };
      }

      responseTimeByDate[dateStr].count++;
      responseTimeByDate[dateStr].total += record.responseTime;
    });

    // Calculate averages
    const avgResponseTimeByHour = responseTimeByHour.map(data =>
      data.count > 0 ? data.total / data.count : 0
    );

    const avgResponseTimeByDay = responseTimeByDay.map(data =>
      data.count > 0 ? data.total / data.count : 0
    );

    // Format response time by date for chart
    const avgResponseTimeByDate = Object.entries(responseTimeByDate)
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([date, data]) => ({
        date,
        averageResponseTime: data.count > 0 ? data.total / data.count : 0
      }));

    return {
      summary,
      slowestEndpoints,
      avgResponseTimeByHour,
      avgResponseTimeByDay,
      avgResponseTimeByDate,
      recentPerformance: filteredData.slice(-100).reverse() // Last 100 records
    };
  }

  /**
   * Get error analytics
   */
  async getErrorAnalytics(filters = {}) {
    const errorData = await this.loadData(this.errorFile);

    // Apply filters
    let filteredData = errorData;

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) >= startDate);
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredData = filteredData.filter(record => new Date(record.timestamp) <= endDate);
    }

    if (filters.endpoint) {
      filteredData = filteredData.filter(record => record.endpoint.includes(filters.endpoint));
    }

    if (filters.method) {
      filteredData = filteredData.filter(record => record.method === filters.method);
    }

    if (filters.userId) {
      filteredData = filteredData.filter(record => record.userId === filters.userId);
    }

    if (filters.errorCode) {
      filteredData = filteredData.filter(record => record.errorCode === filters.errorCode);
    }

    // Calculate summary statistics
    const summary = {
      totalErrors: filteredData.length,
      uniqueEndpoints: new Set(filteredData.map(record => record.endpoint)).size,
      uniqueErrorCodes: new Set(filteredData.filter(record => record.errorCode).map(record => record.errorCode)).size,
      uniqueUsers: new Set(filteredData.filter(record => record.userId).map(record => record.userId)).size
    };

    // Get top endpoints by errors
    const endpointCounts = {};
    filteredData.forEach(record => {
      endpointCounts[record.endpoint] = (endpointCounts[record.endpoint] || 0) + 1;
    });

    const topErrorEndpoints = Object.entries(endpointCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([endpoint, count]) => ({ endpoint, count }));

    // Get top error codes
    const errorCodeCounts = {};
    filteredData.forEach(record => {
      if (record.errorCode) {
        errorCodeCounts[record.errorCode] = (errorCodeCounts[record.errorCode] || 0) + 1;
      }
    });

    const topErrorCodes = Object.entries(errorCodeCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([code, count]) => ({ code, count }));

    // Get errors by time
    const errorsByHour = Array(24).fill(0);
    const errorsByDay = Array(7).fill(0);
    const errorsByDate = {};

    filteredData.forEach(record => {
      const date = new Date(record.timestamp);
      const hour = date.getHours();
      const day = date.getDay();
      const dateStr = date.toISOString().split('T')[0];

      errorsByHour[hour]++;
      errorsByDay[day]++;
      errorsByDate[dateStr] = (errorsByDate[dateStr] || 0) + 1;
    });

    // Format errors by date for chart
    const errorsByDateArray = Object.entries(errorsByDate)
      .sort((a, b) => a[0].localeCompare(b[0]))
      .map(([date, count]) => ({ date, count }));

    return {
      summary,
      topErrorEndpoints,
      topErrorCodes,
      errorsByHour,
      errorsByDay,
      errorsByDate: errorsByDateArray,
      recentErrors: filteredData.slice(-100).reverse() // Last 100 errors
    };
  }

  /**
   * Calculate percentile value
   */
  calculatePercentile(values, percentile) {
    if (values.length === 0) return 0;

    // Sort values
    const sortedValues = [...values].sort((a, b) => a - b);

    // Calculate index
    const index = Math.ceil((percentile / 100) * sortedValues.length) - 1;

    return sortedValues[index];
  }

  /**
   * Get all report templates
   */
  async getReportTemplates() {
    try {
      return await this.loadData(this.reportTemplatesFile);
    } catch (error) {
      console.error('Error getting report templates:', error);
      throw error;
    }
  }

  /**
   * Get report template by ID
   */
  async getReportTemplateById(id) {
    try {
      const templates = await this.loadData(this.reportTemplatesFile);
      const template = templates.find(t => t.id === id);

      if (!template) {
        throw new Error(`Report template with ID ${id} not found`);
      }

      return template;
    } catch (error) {
      console.error(`Error getting report template ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create report template
   */
  async createReportTemplate(data) {
    try {
      // Validate required fields
      if (!data.name) {
        throw new Error('Report template name is required');
      }

      if (!data.type) {
        throw new Error('Report template type is required');
      }

      if (!data.visualizations || !Array.isArray(data.visualizations) || data.visualizations.length === 0) {
        throw new Error('At least one visualization is required');
      }

      // Create template
      const template = {
        id: data.id || uuidv4(),
        name: data.name,
        description: data.description || '',
        type: data.type,
        schedule: data.schedule || null,
        filters: data.filters || {},
        visualizations: data.visualizations,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      };

      // Save template
      const templates = await this.loadData(this.reportTemplatesFile);

      // Check if template with same ID already exists
      const existingIndex = templates.findIndex(t => t.id === template.id);

      if (existingIndex !== -1) {
        // Update existing template
        templates[existingIndex] = template;
      } else {
        // Add new template
        templates.push(template);
      }

      await this.saveData(this.reportTemplatesFile, templates);

      return template;
    } catch (error) {
      console.error('Error creating report template:', error);
      throw error;
    }
  }

  /**
   * Update report template
   */
  async updateReportTemplate(id, data) {
    try {
      const templates = await this.loadData(this.reportTemplatesFile);
      const index = templates.findIndex(t => t.id === id);

      if (index === -1) {
        throw new Error(`Report template with ID ${id} not found`);
      }

      const template = templates[index];

      // Update template
      const updatedTemplate = {
        ...template,
        name: data.name || template.name,
        description: data.description !== undefined ? data.description : template.description,
        type: data.type || template.type,
        schedule: data.schedule !== undefined ? data.schedule : template.schedule,
        filters: data.filters || template.filters,
        visualizations: data.visualizations || template.visualizations,
        updated: new Date().toISOString()
      };

      templates[index] = updatedTemplate;
      await this.saveData(this.reportTemplatesFile, templates);

      return updatedTemplate;
    } catch (error) {
      console.error(`Error updating report template ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete report template
   */
  async deleteReportTemplate(id) {
    try {
      const templates = await this.loadData(this.reportTemplatesFile);
      const index = templates.findIndex(t => t.id === id);

      if (index === -1) {
        throw new Error(`Report template with ID ${id} not found`);
      }

      // Remove template
      templates.splice(index, 1);
      await this.saveData(this.reportTemplatesFile, templates);

      return { success: true, message: `Report template ${id} deleted` };
    } catch (error) {
      console.error(`Error deleting report template ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get default report templates
   */
  getDefaultReportTemplates() {
    return [
      {
        id: 'api-usage-summary',
        name: 'API Usage Summary',
        description: 'Summary of API usage over time',
        type: 'usage',
        schedule: null, // Not scheduled by default
        filters: {},
        visualizations: [
          {
            type: 'summary',
            title: 'Usage Summary',
            config: {}
          },
          {
            type: 'bar',
            title: 'Top Endpoints',
            config: {
              dataKey: 'topEndpoints',
              xAxis: 'endpoint',
              yAxis: 'count',
              limit: 10
            }
          },
          {
            type: 'line',
            title: 'Usage Over Time',
            config: {
              dataKey: 'usageByDate',
              xAxis: 'date',
              yAxis: 'count',
              timeframe: 'daily'
            }
          }
        ]
      },
      {
        id: 'performance-analysis',
        name: 'Performance Analysis',
        description: 'Analysis of API performance metrics',
        type: 'performance',
        schedule: null, // Not scheduled by default
        filters: {},
        visualizations: [
          {
            type: 'summary',
            title: 'Performance Summary',
            config: {}
          },
          {
            type: 'bar',
            title: 'Slowest Endpoints',
            config: {
              dataKey: 'slowestEndpoints',
              xAxis: 'endpoint',
              yAxis: 'averageResponseTime',
              limit: 10
            }
          },
          {
            type: 'line',
            title: 'Response Time Trend',
            config: {
              dataKey: 'avgResponseTimeByDate',
              xAxis: 'date',
              yAxis: 'averageResponseTime',
              timeframe: 'daily'
            }
          }
        ]
      },
      {
        id: 'error-analysis',
        name: 'Error Analysis',
        description: 'Analysis of API errors',
        type: 'error',
        schedule: null, // Not scheduled by default
        filters: {},
        visualizations: [
          {
            type: 'summary',
            title: 'Error Summary',
            config: {}
          },
          {
            type: 'pie',
            title: 'Errors by Code',
            config: {
              dataKey: 'topErrorCodes',
              nameKey: 'code',
              valueKey: 'count'
            }
          },
          {
            type: 'bar',
            title: 'Top Error Endpoints',
            config: {
              dataKey: 'topErrorEndpoints',
              xAxis: 'endpoint',
              yAxis: 'count',
              limit: 10
            }
          }
        ]
      }
    ];
  }

  /**
   * Generate report from template
   */
  async generateReport(templateId, customFilters = {}) {
    try {
      // Get template
      const template = await this.getReportTemplateById(templateId);

      // Merge template filters with custom filters
      const filters = { ...template.filters, ...customFilters };

      // Get data based on report type
      let data;
      switch (template.type) {
        case 'usage':
          data = await this.getUsageAnalytics(filters);
          break;
        case 'performance':
          data = await this.getPerformanceAnalytics(filters);
          break;
        case 'error':
          data = await this.getErrorAnalytics(filters);
          break;
        default:
          throw new Error(`Unknown report type: ${template.type}`);
      }

      // Create report
      const report = {
        id: uuidv4(),
        templateId,
        name: template.name,
        description: template.description,
        type: template.type,
        filters,
        data,
        visualizations: template.visualizations,
        generated: new Date().toISOString()
      };

      // Save report
      await this.saveReport(report);

      return report;
    } catch (error) {
      console.error(`Error generating report from template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Save report
   */
  async saveReport(report) {
    try {
      const reports = await this.loadData(this.reportsFile);

      // Add new report
      reports.push(report);

      // Limit the number of saved reports
      if (reports.length > 100) {
        reports.sort((a, b) => new Date(b.generated) - new Date(a.generated));
        reports.splice(100);
      }

      await this.saveData(this.reportsFile, reports);

      return report;
    } catch (error) {
      console.error('Error saving report:', error);
      throw error;
    }
  }

  /**
   * Get all reports
   */
  async getReports(filters = {}) {
    try {
      let reports = await this.loadData(this.reportsFile);

      // Apply filters
      if (filters.type) {
        reports = reports.filter(r => r.type === filters.type);
      }

      if (filters.templateId) {
        reports = reports.filter(r => r.templateId === filters.templateId);
      }

      if (filters.startDate) {
        const startDate = new Date(filters.startDate);
        reports = reports.filter(r => new Date(r.generated) >= startDate);
      }

      if (filters.endDate) {
        const endDate = new Date(filters.endDate);
        reports = reports.filter(r => new Date(r.generated) <= endDate);
      }

      // Sort by generation date (newest first)
      reports.sort((a, b) => new Date(b.generated) - new Date(a.generated));

      return reports;
    } catch (error) {
      console.error('Error getting reports:', error);
      throw error;
    }
  }

  /**
   * Get report by ID
   */
  async getReportById(id) {
    try {
      const reports = await this.loadData(this.reportsFile);
      const report = reports.find(r => r.id === id);

      if (!report) {
        throw new Error(`Report with ID ${id} not found`);
      }

      return report;
    } catch (error) {
      console.error(`Error getting report ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete report
   */
  async deleteReport(id) {
    try {
      const reports = await this.loadData(this.reportsFile);
      const index = reports.findIndex(r => r.id === id);

      if (index === -1) {
        throw new Error(`Report with ID ${id} not found`);
      }

      // Remove report
      reports.splice(index, 1);
      await this.saveData(this.reportsFile, reports);

      return { success: true, message: `Report ${id} deleted` };
    } catch (error) {
      console.error(`Error deleting report ${id}:`, error);
      throw error;
    }
  }

  /**
   * Schedule report generation
   */
  async scheduleReport(templateId, schedule) {
    try {
      // Validate schedule
      if (!schedule || !schedule.frequency) {
        throw new Error('Schedule frequency is required');
      }

      // Get template
      const template = await this.getReportTemplateById(templateId);

      // Update template with schedule
      const updatedTemplate = {
        ...template,
        schedule: {
          frequency: schedule.frequency,
          time: schedule.time || '00:00',
          dayOfWeek: schedule.dayOfWeek || 1, // Monday
          dayOfMonth: schedule.dayOfMonth || 1,
          recipients: schedule.recipients || [],
          nextRun: this.calculateNextRun(schedule),
          active: true
        },
        updated: new Date().toISOString()
      };

      // Save updated template
      await this.updateReportTemplate(templateId, updatedTemplate);

      return updatedTemplate;
    } catch (error) {
      console.error(`Error scheduling report for template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate next run time for scheduled report
   */
  calculateNextRun(schedule) {
    const now = new Date();
    let nextRun = new Date();

    // Parse time
    const [hours, minutes] = schedule.time ? schedule.time.split(':').map(Number) : [0, 0];

    nextRun.setHours(hours, minutes, 0, 0);

    // If the time today has already passed, start from tomorrow
    if (nextRun <= now) {
      nextRun.setDate(nextRun.getDate() + 1);
    }

    // Adjust based on frequency
    switch (schedule.frequency) {
      case 'daily':
        // Already set for next day
        break;

      case 'weekly':
        // Adjust to next occurrence of day of week
        const dayOfWeek = schedule.dayOfWeek || 1; // Default to Monday
        const daysUntilNext = (dayOfWeek - nextRun.getDay() + 7) % 7;

        if (daysUntilNext > 0 || (daysUntilNext === 0 && nextRun <= now)) {
          nextRun.setDate(nextRun.getDate() + daysUntilNext);
        }
        break;

      case 'monthly':
        // Adjust to the specified day of month
        const dayOfMonth = Math.min(schedule.dayOfMonth || 1, 28); // Avoid month overflow

        nextRun.setDate(dayOfMonth);

        // If this date has already passed this month, move to next month
        if (nextRun <= now) {
          nextRun.setMonth(nextRun.getMonth() + 1);
        }
        break;

      default:
        throw new Error(`Unknown frequency: ${schedule.frequency}`);
    }

    return nextRun.toISOString();
  }

  /**
   * Clean up old analytics data
   */
  async cleanupOldData() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);

      // Clean up usage data
      const usageData = await this.loadData(this.usageFile);
      const filteredUsageData = usageData.filter(record =>
        new Date(record.timestamp) >= cutoffDate
      );

      if (filteredUsageData.length < usageData.length) {
        await this.saveData(this.usageFile, filteredUsageData);
      }

      // Clean up performance data
      const performanceData = await this.loadData(this.performanceFile);
      const filteredPerformanceData = performanceData.filter(record =>
        new Date(record.timestamp) >= cutoffDate
      );

      if (filteredPerformanceData.length < performanceData.length) {
        await this.saveData(this.performanceFile, filteredPerformanceData);
      }

      // Clean up error data
      const errorData = await this.loadData(this.errorFile);
      const filteredErrorData = errorData.filter(record =>
        new Date(record.timestamp) >= cutoffDate
      );

      if (filteredErrorData.length < errorData.length) {
        await this.saveData(this.errorFile, filteredErrorData);
      }

      // Clean up old reports
      const reports = await this.loadData(this.reportsFile);
      const filteredReports = reports.filter(report =>
        new Date(report.generated) >= cutoffDate
      );

      if (filteredReports.length < reports.length) {
        await this.saveData(this.reportsFile, filteredReports);
      }

      console.log(`Cleaned up analytics data older than ${cutoffDate.toISOString()}`);
    } catch (error) {
      console.error('Error cleaning up old analytics data:', error);
    }
  }
}

module.exports = AnalyticsService;
