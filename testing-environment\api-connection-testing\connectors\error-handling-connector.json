{"metadata": {"name": "Error Handling Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for error handling", "author": "NovaGRC", "tags": ["test", "error-handling"]}, "authentication": {"type": "API_KEY", "fields": {"apiKey": {"type": "string", "description": "API Key", "required": true, "sensitive": true}, "headerName": {"type": "string", "description": "Header name for the API key", "required": true, "default": "X-API-Key"}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 5000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "badRequest", "name": "Bad Request", "path": "/error/400", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}, {"id": "unauthorized", "name": "Unauthorized", "path": "/error/401", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}, {"id": "forbidden", "name": "Forbidden", "path": "/error/403", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}, {"id": "notFound", "name": "Not Found", "path": "/error/404", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}, {"id": "rateLimited", "name": "Rate Limited", "path": "/error/429", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}, {"id": "serverError", "name": "Server Error", "path": "/error/500", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}, {"id": "timeout", "name": "Timeout", "path": "/error/timeout", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}, {"id": "malformed<PERSON><PERSON>", "name": "Malformed JSON", "path": "/error/malformed-json", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [], "events": {"webhooks": [], "polling": []}}