/**
 * Integration Layer Tests
 * 
 * This module contains tests for the Comphyon Integration Layer.
 */

const { TestSuite, assertions } = require('./test-framework');
const { ComphyonIntegrationLayer } = require('../comphyon-core');

/**
 * Create a test suite for the Integration Layer
 * @returns {TestSuite} - Test suite
 */
function createIntegrationLayerTestSuite() {
  const suite = new TestSuite('Integration Layer Tests');
  
  // Test variables
  let integrationLayer;
  let mockCSDE;
  let mockCSFE;
  let mockCSME;
  let mockBridge;
  
  // Setup
  suite.beforeEach(() => {
    // Create mock components
    mockCSDE = {
      name: 'CSDE',
      domain: 'cyber',
      processData: (data) => {
        return { success: true, domain: 'cyber', result: data };
      }
    };
    
    mockCSFE = {
      name: 'CSFE',
      domain: 'financial',
      processData: (data) => {
        return { success: true, domain: 'financial', result: data };
      }
    };
    
    mockCSME = {
      name: 'CSME',
      domain: 'biological',
      processData: (data) => {
        return { success: true, domain: 'biological', result: data };
      }
    };
    
    mockBridge = {
      name: 'Bridge',
      domain: 'universal',
      translateData: (sourceDomain, targetDomain, data) => {
        return { success: true, sourceDomain, targetDomain, result: data };
      }
    };
    
    // Create integration layer
    integrationLayer = new ComphyonIntegrationLayer({
      enableLogging: false,
      enableMetrics: true
    });
  });
  
  // Test: Component Registration
  suite.test('should register components', async () => {
    // Register components
    const csdeResult = integrationLayer.registerComponent('csde', mockCSDE, {
      type: 'engine',
      domain: 'cyber',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    const csfeResult = integrationLayer.registerComponent('csfe', mockCSFE, {
      type: 'engine',
      domain: 'financial',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    const csmeResult = integrationLayer.registerComponent('csme', mockCSME, {
      type: 'engine',
      domain: 'biological',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    const bridgeResult = integrationLayer.registerComponent('bridge', mockBridge, {
      type: 'integration',
      domain: 'universal',
      version: '1.0.0',
      interfaces: ['translateData'],
      dependencies: ['csde', 'csfe', 'csme']
    });
    
    // Assert
    assertions.isTrue(csdeResult, 'CSDE registration failed');
    assertions.isTrue(csfeResult, 'CSFE registration failed');
    assertions.isTrue(csmeResult, 'CSME registration failed');
    assertions.isTrue(bridgeResult, 'Bridge registration failed');
    
    // Check component count
    const components = integrationLayer.getAllComponents();
    assertions.equal(components.length, 4, 'Incorrect number of components registered');
    
    // Check component retrieval
    const csde = integrationLayer.getComponent('csde');
    assertions.isTrue(csde, 'Failed to retrieve CSDE component');
    assertions.equal(csde.metadata.domain, 'cyber', 'Incorrect CSDE domain');
  });
  
  // Test: Connection Creation
  suite.test('should create connections between components', async () => {
    // Register components
    integrationLayer.registerComponent('csde', mockCSDE, {
      type: 'engine',
      domain: 'cyber',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    integrationLayer.registerComponent('bridge', mockBridge, {
      type: 'integration',
      domain: 'universal',
      version: '1.0.0',
      interfaces: ['translateData']
    });
    
    // Create connection
    const connectionId = integrationLayer.createConnection('csde', 'bridge', {
      type: 'bidirectional',
      protocol: 'method',
      metadata: {
        description: 'CSDE to Bridge connection'
      }
    });
    
    // Assert
    assertions.isTrue(connectionId, 'Connection creation failed');
    
    // Check connection retrieval
    const connection = integrationLayer.getConnection(connectionId);
    assertions.isTrue(connection, 'Failed to retrieve connection');
    assertions.equal(connection.sourceComponent, 'csde', 'Incorrect source component');
    assertions.equal(connection.targetComponent, 'bridge', 'Incorrect target component');
    assertions.equal(connection.type, 'bidirectional', 'Incorrect connection type');
    
    // Check connection count
    const connections = integrationLayer.getAllConnections();
    assertions.equal(connections.length, 1, 'Incorrect number of connections');
  });
  
  // Test: Data Flow Creation
  suite.test('should create data flows between components', async () => {
    // Register components
    integrationLayer.registerComponent('csde', mockCSDE, {
      type: 'engine',
      domain: 'cyber',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    integrationLayer.registerComponent('bridge', mockBridge, {
      type: 'integration',
      domain: 'universal',
      version: '1.0.0',
      interfaces: ['translateData']
    });
    
    // Create data flow
    const flowId = integrationLayer.createDataFlow('csde', 'bridge', {
      dataType: 'cyber',
      direction: 'forward',
      priority: 'high',
      transformations: [
        (data) => {
          return { ...data, transformed: true };
        }
      ],
      filters: [
        (data) => {
          return data.value > 0.5;
        }
      ],
      metadata: {
        description: 'CSDE to Bridge data flow'
      }
    });
    
    // Assert
    assertions.isTrue(flowId, 'Data flow creation failed');
    
    // Check data flow retrieval
    const dataFlow = integrationLayer.getDataFlow(flowId);
    assertions.isTrue(dataFlow, 'Failed to retrieve data flow');
    assertions.equal(dataFlow.sourceComponent, 'csde', 'Incorrect source component');
    assertions.equal(dataFlow.targetComponent, 'bridge', 'Incorrect target component');
    assertions.equal(dataFlow.dataType, 'cyber', 'Incorrect data type');
    
    // Check data flow count
    const dataFlows = integrationLayer.getAllDataFlows();
    assertions.equal(dataFlows.length, 1, 'Incorrect number of data flows');
  });
  
  // Test: Data Flow Processing
  suite.test('should process data through data flows', async () => {
    // Register components
    integrationLayer.registerComponent('csde', mockCSDE, {
      type: 'engine',
      domain: 'cyber',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    integrationLayer.registerComponent('bridge', mockBridge, {
      type: 'integration',
      domain: 'universal',
      version: '1.0.0',
      interfaces: ['translateData']
    });
    
    // Create data flow
    const flowId = integrationLayer.createDataFlow('csde', 'bridge', {
      dataType: 'cyber',
      direction: 'forward',
      transformations: [
        (data) => {
          return { ...data, transformed: true };
        }
      ],
      filters: [
        (data) => {
          return data.value > 0.5;
        }
      ]
    });
    
    // Process data that passes filter
    const data1 = { type: 'policy_entropy', value: 0.7 };
    const result1 = integrationLayer.processDataFlow(flowId, data1);
    
    // Process data that doesn't pass filter
    const data2 = { type: 'policy_entropy', value: 0.3 };
    const result2 = integrationLayer.processDataFlow(flowId, data2);
    
    // Assert
    assertions.isTrue(result1, 'Data flow processing failed for valid data');
    assertions.equal(result1.type, 'policy_entropy', 'Incorrect data type');
    assertions.equal(result1.value, 0.7, 'Incorrect data value');
    assertions.isTrue(result1.transformed, 'Transformation not applied');
    
    assertions.equal(result2, null, 'Data flow processing should return null for filtered data');
  });
  
  // Test: Tensor Operations
  suite.test('should perform tensor operations', async () => {
    // Tensor product
    const tensorA = [0.5, 0.6, 0.7];
    const tensorB = [0.8, 0.9];
    
    const tensorProduct = integrationLayer.applyTensorOperation(tensorA, tensorB, 'product');
    const tensorSum = integrationLayer.applyTensorOperation(tensorA, tensorB, 'sum');
    const tensorFusion = integrationLayer.applyTensorOperation(tensorA, tensorB, 'fusion');
    
    // Assert
    assertions.isTrue(Array.isArray(tensorProduct), 'Tensor product should return an array');
    assertions.equal(tensorProduct.length, 6, 'Tensor product should have 6 elements');
    assertions.approximately(tensorProduct[0], 0.4, 0.001, 'Incorrect tensor product value');
    
    assertions.isTrue(Array.isArray(tensorSum), 'Tensor sum should return an array');
    assertions.equal(tensorSum.length, 3, 'Tensor sum should have 3 elements');
    assertions.approximately(tensorSum[0], 1.3, 0.001, 'Incorrect tensor sum value');
    
    assertions.isTrue(Array.isArray(tensorFusion), 'Tensor fusion should return an array');
    assertions.equal(tensorFusion.length, 3, 'Tensor fusion should have 3 elements');
    
    // Check metrics
    const metrics = integrationLayer.getMetrics();
    assertions.isTrue(metrics.tensorOperations >= 3, 'Tensor operations not counted in metrics');
  });
  
  // Test: UUFT Formula
  suite.test('should apply UUFT formula', async () => {
    const tensorA = [0.5, 0.6, 0.7];
    const tensorB = [0.8, 0.9];
    const tensorC = [0.4, 0.5, 0.6];
    
    const result = integrationLayer.applyUUFTFormula(tensorA, tensorB, tensorC);
    
    // Assert
    assertions.isTrue(Array.isArray(result), 'UUFT formula should return an array');
    assertions.isTrue(result.length > 0, 'UUFT formula result should not be empty');
    
    // Check metrics
    const metrics = integrationLayer.getMetrics();
    assertions.isTrue(metrics.fusionOperations >= 1, 'Fusion operations not counted in metrics');
  });
  
  // Test: Component Unregistration
  suite.test('should unregister components', async () => {
    // Register component
    integrationLayer.registerComponent('csde', mockCSDE, {
      type: 'engine',
      domain: 'cyber',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    // Unregister component
    const result = integrationLayer.unregisterComponent('csde');
    
    // Assert
    assertions.isTrue(result, 'Component unregistration failed');
    
    // Check component count
    const components = integrationLayer.getAllComponents();
    assertions.equal(components.length, 0, 'Component not unregistered');
    
    // Check component retrieval
    const csde = integrationLayer.getComponent('csde');
    assertions.equal(csde, null, 'Component still retrievable after unregistration');
  });
  
  // Test: Connection Removal
  suite.test('should remove connections', async () => {
    // Register components
    integrationLayer.registerComponent('csde', mockCSDE, {
      type: 'engine',
      domain: 'cyber',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    integrationLayer.registerComponent('bridge', mockBridge, {
      type: 'integration',
      domain: 'universal',
      version: '1.0.0',
      interfaces: ['translateData']
    });
    
    // Create connection
    const connectionId = integrationLayer.createConnection('csde', 'bridge', {
      type: 'bidirectional',
      protocol: 'method'
    });
    
    // Remove connection
    const result = integrationLayer.removeConnection(connectionId);
    
    // Assert
    assertions.isTrue(result, 'Connection removal failed');
    
    // Check connection count
    const connections = integrationLayer.getAllConnections();
    assertions.equal(connections.length, 0, 'Connection not removed');
    
    // Check connection retrieval
    const connection = integrationLayer.getConnection(connectionId);
    assertions.equal(connection, null, 'Connection still retrievable after removal');
  });
  
  // Test: Data Flow Removal
  suite.test('should remove data flows', async () => {
    // Register components
    integrationLayer.registerComponent('csde', mockCSDE, {
      type: 'engine',
      domain: 'cyber',
      version: '1.0.0',
      interfaces: ['processData']
    });
    
    integrationLayer.registerComponent('bridge', mockBridge, {
      type: 'integration',
      domain: 'universal',
      version: '1.0.0',
      interfaces: ['translateData']
    });
    
    // Create data flow
    const flowId = integrationLayer.createDataFlow('csde', 'bridge', {
      dataType: 'cyber',
      direction: 'forward'
    });
    
    // Remove data flow
    const result = integrationLayer.removeDataFlow(flowId);
    
    // Assert
    assertions.isTrue(result, 'Data flow removal failed');
    
    // Check data flow count
    const dataFlows = integrationLayer.getAllDataFlows();
    assertions.equal(dataFlows.length, 0, 'Data flow not removed');
    
    // Check data flow retrieval
    const dataFlow = integrationLayer.getDataFlow(flowId);
    assertions.equal(dataFlow, null, 'Data flow still retrievable after removal');
  });
  
  return suite;
}

module.exports = {
  createIntegrationLayerTestSuite
};
