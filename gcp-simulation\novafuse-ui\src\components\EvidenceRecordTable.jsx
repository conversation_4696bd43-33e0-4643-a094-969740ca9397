import React, { useState } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  Box,
  Typography
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DownloadIcon from '@mui/icons-material/Download';
import LinkIcon from '@mui/icons-material/Link';

/**
 * EvidenceRecordTable Component
 * 
 * Displays a table of evidence records with pagination and actions.
 * 
 * @param {Object} props - Component props
 * @param {Array} props.records - Array of evidence records
 * @param {Function} props.onViewRecord - Function to call when viewing a record
 * @param {Function} props.onDownloadRecord - Function to call when downloading a record
 * @param {Function} props.onLinkRecord - Function to call when linking a record to a control
 */
const EvidenceRecordTable = ({ records = [], onViewRecord, onDownloadRecord, onLinkRecord }) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Get chip color based on evidence type
  const getTypeColor = (type) => {
    const typeColors = {
      'IAM_POLICY': 'primary',
      'AUDIT_LOG': 'secondary',
      'STORAGE_POLICY': 'warning',
      'DATA_CATALOG': 'info',
      'DLP_SCAN': 'success',
      'KMS_CONFIG': 'error',
      'VPC_CONFIG': 'default',
      'FIREWALL_CONFIG': 'primary',
      'LOGGING_CONFIG': 'secondary',
      'MONITORING_ALERT': 'warning'
    };
    
    return typeColors[type] || 'default';
  };

  // If no records, show empty state
  if (records.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          No evidence records found.
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label="evidence records table">
          <TableHead>
            <TableRow>
              <TableCell>Type</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Resource</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Expires</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {records
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((record) => (
                <TableRow hover key={record.id}>
                  <TableCell>
                    <Chip 
                      label={record.type} 
                      color={getTypeColor(record.type)} 
                      size="small" 
                    />
                  </TableCell>
                  <TableCell>{record.name}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="body2">{record.resource.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {record.resource.type}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{formatDate(record.timestamp)}</TableCell>
                  <TableCell>{formatDate(record.expiryDate)}</TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                      <Tooltip title="View Evidence">
                        <IconButton 
                          size="small" 
                          onClick={() => onViewRecord(record.id)}
                          color="primary"
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Download Evidence">
                        <IconButton 
                          size="small" 
                          onClick={() => onDownloadRecord(record.id)}
                          color="secondary"
                        >
                          <DownloadIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Link to Control">
                        <IconButton 
                          size="small" 
                          onClick={() => onLinkRecord(record.id)}
                          color="default"
                        >
                          <LinkIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={records.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};

export default EvidenceRecordTable;
