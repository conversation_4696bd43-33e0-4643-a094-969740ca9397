/**
 * Tenant Audit Isolation Test
 * 
 * This test verifies that audit logs are properly isolated between tenants.
 */

const AuditService = require('../../api/services/AuditService');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

// Test configuration
const TEST_DIR = path.join(__dirname, '../../test-data');
const TENANT_IDS = ['test-tenant-1', 'test-tenant-2', 'test-tenant-3'];
const ACTIONS_PER_TENANT = 5;

describe('Tenant Audit Isolation', () => {
  let auditService;
  
  beforeAll(async () => {
    // Create test directory
    await fs.mkdir(path.join(TEST_DIR, 'audit'), { recursive: true });
    
    // Initialize audit service with test directory
    auditService = new AuditService(TEST_DIR);
    
    // Mock BigQuery for testing
    auditService.bigQueryEnabled = true;
    auditService.bigquery = {
      dataset: jest.fn().mockImplementation((datasetId) => ({
        table: jest.fn().mockImplementation((tableId) => ({
          get: jest.fn().mockResolvedValue([{}]),
          insert: jest.fn().mockResolvedValue([{}])
        }))
      }))
    };
  });
  
  afterAll(async () => {
    // Clean up test data
    try {
      await fs.unlink(path.join(TEST_DIR, 'audit', 'audit_log.json'));
      await fs.rmdir(path.join(TEST_DIR, 'audit'));
      await fs.rmdir(TEST_DIR);
    } catch (error) {
      console.error('Error cleaning up test data:', error);
    }
  });
  
  test('should log events for multiple tenants', async () => {
    // Generate audit events for each tenant
    for (const tenantId of TENANT_IDS) {
      for (let i = 0; i < ACTIONS_PER_TENANT; i++) {
        await auditService.logTenantEvent(tenantId, {
          userId: `user-${i}`,
          action: ['GET', 'POST', 'PUT', 'DELETE'][i % 4],
          resourceType: 'test-resource',
          resourceId: uuidv4(),
          details: { test: true, iteration: i },
          ip: '127.0.0.1',
          userAgent: 'Jest Test Runner',
          status: 'success',
          teamId: 'test-team',
          environmentId: 'test'
        });
      }
    }
    
    // Verify that events were logged
    const allLogs = await auditService.getAuditLogs();
    expect(allLogs.total).toBe(TENANT_IDS.length * ACTIONS_PER_TENANT);
    
    // Verify tenant isolation in logs
    for (const tenantId of TENANT_IDS) {
      const tenantLogs = allLogs.logs.filter(log => log.tenantId === tenantId);
      expect(tenantLogs.length).toBe(ACTIONS_PER_TENANT);
      
      // Verify that each log has the correct tenant ID
      tenantLogs.forEach(log => {
        expect(log.tenantId).toBe(tenantId);
      });
      
      // Verify that BigQuery was called with the correct dataset
      expect(auditService.bigquery.dataset).toHaveBeenCalledWith(`tenant_${tenantId}`);
    }
  });
  
  test('should not expose logs across tenants', async () => {
    // For each tenant, verify they can only see their own logs
    for (const tenantId of TENANT_IDS) {
      // Get logs for this tenant
      const tenantLogs = await auditService.getAuditLogs({ tenantId });
      
      // Verify that all logs belong to this tenant
      tenantLogs.logs.forEach(log => {
        expect(log.tenantId).toBe(tenantId);
      });
      
      // Verify that the count matches expected
      expect(tenantLogs.total).toBe(ACTIONS_PER_TENANT);
    }
  });
  
  test('should handle requests without tenant ID', async () => {
    // Log an event without tenant ID
    await auditService.logEvent({
      userId: 'system',
      action: 'SYSTEM',
      resourceType: 'maintenance',
      resourceId: null,
      details: { scheduled: true },
      status: 'success'
    });
    
    // Verify that the event was logged
    const allLogs = await auditService.getAuditLogs();
    expect(allLogs.total).toBe((TENANT_IDS.length * ACTIONS_PER_TENANT) + 1);
    
    // Find the system log
    const systemLog = allLogs.logs.find(log => log.action === 'SYSTEM');
    expect(systemLog).toBeDefined();
    expect(systemLog.tenantId).toBeNull();
    
    // Verify that BigQuery global dataset was used
    expect(auditService.bigquery.dataset).toHaveBeenCalledWith(auditService.datasetId);
  });
  
  test('should handle tenant-specific middleware', async () => {
    // Create mock request and response
    const req = {
      user: { id: 'test-user' },
      method: 'GET',
      path: '/api/resources/123',
      params: { id: '123' },
      query: { include: 'details' },
      body: null,
      ip: '127.0.0.1',
      headers: {
        'user-agent': 'Test Agent',
        'x-tenant-id': 'test-tenant-1',
        'x-team-id': 'test-team',
        'x-environment-id': 'test'
      },
      connection: { remoteAddress: '127.0.0.1' }
    };
    
    const res = {
      statusCode: 200,
      end: jest.fn()
    };
    
    const next = jest.fn();
    
    // Create middleware
    const middleware = auditService.createAuditMiddleware();
    
    // Call middleware
    middleware(req, res, next);
    
    // Call the end method to trigger audit logging
    res.end();
    
    // Verify that next was called
    expect(next).toHaveBeenCalled();
    
    // Verify that the event was logged with tenant ID
    const allLogs = await auditService.getAuditLogs();
    const latestLog = allLogs.logs[0]; // Most recent log
    
    expect(latestLog.tenantId).toBe('test-tenant-1');
    expect(latestLog.userId).toBe('test-user');
    expect(latestLog.action).toBe('GET');
    expect(latestLog.resourceType).toBe('resources');
    expect(latestLog.resourceId).toBe('123');
  });
});
