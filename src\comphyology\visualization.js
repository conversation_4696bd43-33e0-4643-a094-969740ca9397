/**
 * Comphyology Visualization Module
 *
 * This module provides visualization tools for Comphyology concepts, generating
 * data that can be used to create charts and diagrams to illustrate the framework.
 *
 * The visualizations include:
 * 1. Morphological Resonance Field - Shows how structural complexity interacts with environmental factors
 * 2. Quantum Phase Space Map - Visualizes entropy-phase relationships and pattern detection
 * 3. Ethical Tensor Projection - Displays ethical dimensions and decision boundaries
 * 4. Trinity Integration Diagram - Shows how Comphyology enhances Trinity CSDE
 */

const { ComphyologyCore } = require('./index');

/**
 * Comphyology Visualization Generator
 *
 * Generates visualization data for Comphyology concepts.
 */
class ComphyologyVisualization {
  /**
   * Constructor for the Comphyology Visualization Generator
   *
   * @param {Object} options - Configuration options
   * @param {boolean} options.enableLogging - Whether to enable logging
   * @param {number} options.resolution - Resolution of visualization data (default: 50)
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging || false,
      resolution: options.resolution || 50,
      ...options
    };

    // Initialize Comphyology Core
    this.comphyologyCore = new ComphyologyCore(options);

    // Constants
    this.PI = Math.PI;
    this.PHI = (1 + Math.sqrt(5)) / 2;

    if (this.options.enableLogging) {
      console.log('Comphyology Visualization Generator initialized');
    }
  }

  /**
   * Generate Morphological Resonance Field visualization data
   *
   * @param {Object} options - Visualization options
   * @param {number} options.complexityRange - Range of complexity values [0-1]
   * @param {number} options.adaptabilityRange - Range of adaptability values [0-1]
   * @param {number} options.environmentalPressureRange - Range of environmental pressure values [0-1]
   * @param {boolean} options.useCache - Whether to use cached data (default: true)
   * @param {boolean} options.adaptiveResolution - Whether to use adaptive resolution (default: true)
   * @param {string} options.performanceMode - Performance mode: 'high', 'balanced', 'low' (default: 'balanced')
   * @returns {Object} - Visualization data
   */
  generateMorphologicalResonanceField(options = {}) {
    // Extract options with defaults
    const useCache = options.useCache !== undefined ? options.useCache : true;
    const adaptiveResolution = options.adaptiveResolution !== undefined ? options.adaptiveResolution : true;
    const performanceMode = options.performanceMode || 'balanced';

    // Determine resolution based on performance mode
    let resolution;
    if (adaptiveResolution) {
      switch (performanceMode) {
        case 'high':
          resolution = Math.min(20, this.options.resolution);
          break;
        case 'balanced':
          resolution = Math.min(35, this.options.resolution);
          break;
        case 'low':
          resolution = options.resolution || this.options.resolution;
          break;
        default:
          resolution = Math.min(35, this.options.resolution);
      }
    } else {
      resolution = options.resolution || this.options.resolution;
    }

    const complexityRange = options.complexityRange || 1;
    const adaptabilityRange = options.adaptabilityRange || 1;
    const environmentalPressureRange = options.environmentalPressureRange || 1;

    // Generate cache key
    const cacheKey = `morphological_${resolution}_${complexityRange}_${adaptabilityRange}_${environmentalPressureRange}`;

    // Check cache if enabled
    if (useCache && this._visualizationCache && this._visualizationCache[cacheKey]) {
      if (this.options.enableLogging) {
        console.log(`Using cached Morphological Resonance Field data for key: ${cacheKey}`);
      }
      return this._visualizationCache[cacheKey];
    }

    // Performance monitoring
    const startTime = performance.now();

    // Generate grid data
    const data = [];

    // Determine pressure sample count based on performance mode
    const pressureSamples = performanceMode === 'high' ? 3 : 5;

    for (let i = 0; i < resolution; i++) {
      const complexity = (i / (resolution - 1)) * complexityRange;
      const row = [];

      for (let j = 0; j < resolution; j++) {
        const adaptability = (j / (resolution - 1)) * adaptabilityRange;

        // Calculate resonance for different environmental pressures
        const resonanceValues = [];
        const pressureValues = [];

        for (let k = 0; k < pressureSamples; k++) {
          const environmentalPressure = (k / (pressureSamples - 1)) * environmentalPressureRange;
          pressureValues.push(environmentalPressure);

          // Create system state
          const systemState = {
            structure: {
              complexity,
              adaptability,
              resilience: 0.7
            },
            environment: {
              volatility: environmentalPressure,
              uncertainty: environmentalPressure,
              complexity: environmentalPressure,
              ambiguity: environmentalPressure
            }
          };

          // Calculate morphological resonance
          const morphologicalResult = this.comphyologyCore.morphologicalComponent(systemState);
          resonanceValues.push(morphologicalResult.resonance);
        }

        row.push({
          complexity,
          adaptability,
          resonanceValues,
          pressureValues,
          averageResonance: resonanceValues.reduce((sum, val) => sum + val, 0) / resonanceValues.length
        });
      }

      data.push(row);
    }

    // Generate contour data - only if not in high performance mode
    const contourData = performanceMode === 'high' ? [] : this._generateContourData(data, 'averageResonance');

    // Create result
    const result = {
      type: 'morphological_resonance_field',
      data,
      contourData,
      xLabel: 'Complexity',
      yLabel: 'Adaptability',
      zLabel: 'Resonance',
      colorScale: 'viridis',
      title: 'Morphological Resonance Field',
      metadata: {
        resolution,
        performanceMode,
        generationTime: performance.now() - startTime,
        dataPoints: resolution * resolution * pressureSamples
      }
    };

    // Cache result if caching is enabled
    if (useCache) {
      if (!this._visualizationCache) {
        this._visualizationCache = {};
      }
      this._visualizationCache[cacheKey] = result;

      if (this.options.enableLogging) {
        console.log(`Cached Morphological Resonance Field data for key: ${cacheKey}`);
        console.log(`Generation time: ${result.metadata.generationTime.toFixed(2)}ms for ${result.metadata.dataPoints} data points`);
      }
    }

    return result;
  }

  /**
   * Generate Quantum Phase Space Map visualization data
   *
   * @param {Object} options - Visualization options
   * @param {number} options.entropyRange - Range of entropy values [0-1]
   * @param {number} options.phaseRange - Range of phase values [0-2π]
   * @returns {Object} - Visualization data
   */
  generateQuantumPhaseSpaceMap(options = {}) {
    const resolution = options.resolution || this.options.resolution;
    const entropyRange = options.entropyRange || 1;
    const phaseRange = options.phaseRange || 2 * Math.PI;

    // Generate grid data
    const data = [];

    for (let i = 0; i < resolution; i++) {
      const entropy = (i / (resolution - 1)) * entropyRange;
      const row = [];

      for (let j = 0; j < resolution; j++) {
        const phase = (j / (resolution - 1)) * phaseRange;

        // Create system state
        const systemState = {
          quantum: {
            entropy: {
              value: entropy,
              gradient: 0.1,
              threshold: 0.5
            },
            phase: {
              value: phase / (2 * Math.PI),
              coherence: 0.8,
              stability: 0.7
            },
            superposition: {
              states: 2,
              amplitude: [Math.cos(phase), Math.sin(phase)],
              phase: [0, Math.PI/2]
            }
          }
        };

        // Calculate quantum components
        const phaseSpace = this.comphyologyCore._mapEntropyToPhaseSpace(systemState);
        const patterns = this.comphyologyCore._detectPatternsInPhaseSpace(phaseSpace, {
          entropyMean: entropy,
          phaseMean: phase / (2 * Math.PI)
        });
        const certainty = this.comphyologyCore._calculateCertaintyFromPatterns(patterns, {
          entropyMean: entropy,
          phaseMean: phase / (2 * Math.PI)
        });

        row.push({
          entropy,
          phase,
          phaseSpace,
          patterns,
          certainty
        });
      }

      data.push(row);
    }

    // Generate vector field data
    const vectorField = this._generateVectorField(data, 'entropy', 'phase', 'patterns', 'certainty');

    // Generate contour data
    const contourData = this._generateContourData(data, 'certainty');

    return {
      type: 'quantum_phase_space_map',
      data,
      vectorField,
      contourData,
      xLabel: 'Entropy',
      yLabel: 'Phase',
      zLabel: 'Certainty',
      colorScale: 'plasma',
      title: 'Quantum Phase Space Map'
    };
  }

  /**
   * Generate Ethical Tensor Projection visualization data
   *
   * @param {Object} options - Visualization options
   * @param {number} options.fairnessRange - Range of fairness values [0-1]
   * @param {number} options.transparencyRange - Range of transparency values [0-1]
   * @returns {Object} - Visualization data
   */
  generateEthicalTensorProjection(options = {}) {
    const resolution = options.resolution || this.options.resolution;
    const fairnessRange = options.fairnessRange || 1;
    const transparencyRange = options.transparencyRange || 1;

    // Generate grid data
    const data = [];

    for (let i = 0; i < resolution; i++) {
      const fairness = (i / (resolution - 1)) * fairnessRange;
      const row = [];

      for (let j = 0; j < resolution; j++) {
        const transparency = (j / (resolution - 1)) * transparencyRange;

        // Create system state with different accountability values
        const tensorValues = [];
        const accountabilityValues = [];

        for (let k = 0; k < 5; k++) {
          const accountability = k / 4;
          accountabilityValues.push(accountability);

          // Create system state
          const systemState = {
            decision: {
              options: ['option1', 'option2', 'option3'],
              utilities: [0.7, 0.5, 0.3],
              risks: [0.3, 0.5, 0.7]
            },
            context: {
              criticality: 0.8,
              uncertainty: 0.3,
              time_pressure: 0.5
            },
            ethics: {
              fairness,
              transparency,
              accountability
            }
          };

          // Calculate ethical tensor
          const ethicalTensor = this.comphyologyCore._calculateEthicalTensor(systemState);
          tensorValues.push(ethicalTensor.value);
        }

        row.push({
          fairness,
          transparency,
          tensorValues,
          accountabilityValues,
          averageTensorValue: tensorValues.reduce((sum, val) => sum + val, 0) / tensorValues.length
        });
      }

      data.push(row);
    }

    // Generate decision boundaries
    const decisionBoundaries = this._generateDecisionBoundaries(data, 'fairness', 'transparency', 'averageTensorValue');

    // Generate contour data
    const contourData = this._generateContourData(data, 'averageTensorValue');

    return {
      type: 'ethical_tensor_projection',
      data,
      decisionBoundaries,
      contourData,
      xLabel: 'Fairness',
      yLabel: 'Transparency',
      zLabel: 'Ethical Tensor Value',
      colorScale: 'cividis',
      title: 'Ethical Tensor Projection'
    };
  }

  /**
   * Generate Trinity Integration Diagram visualization data
   *
   * @param {Object} options - Visualization options
   * @returns {Object} - Visualization data
   */
  generateTrinityIntegrationDiagram(options = {}) {
    // Generate node data
    const nodes = [
      { id: 'uuft', label: 'UUFT', group: 'foundation', size: 30 },
      { id: 'comphyology', label: 'Comphyology (Ψᶜ)', group: 'framework', size: 40 },
      { id: 'trinity_csde', label: 'Trinity CSDE', group: 'implementation', size: 35 },
      { id: 'father', label: 'Father (πG)', group: 'component', size: 25 },
      { id: 'son', label: 'Son (ϕD)', group: 'component', size: 25 },
      { id: 'spirit', label: 'Spirit ((ℏ+c⁻¹)R)', group: 'component', size: 25 },
      { id: 'morphological', label: 'Computational Morphogenesis', group: 'concept', size: 20 },
      { id: 'quantum', label: 'Quantum-Inspired Tensor Dynamics', group: 'concept', size: 20 },
      { id: 'emergent', label: 'Emergent Logic Modeling', group: 'concept', size: 20 }
    ];

    // Generate edge data
    const edges = [
      { source: 'uuft', target: 'comphyology', value: 5, label: 'Provides mathematical foundation' },
      { source: 'comphyology', target: 'trinity_csde', value: 5, label: 'Enhances' },
      { source: 'trinity_csde', target: 'father', value: 4, label: 'Implements' },
      { source: 'trinity_csde', target: 'son', value: 4, label: 'Implements' },
      { source: 'trinity_csde', target: 'spirit', value: 4, label: 'Implements' },
      { source: 'comphyology', target: 'morphological', value: 3, label: 'Defines' },
      { source: 'comphyology', target: 'quantum', value: 3, label: 'Defines' },
      { source: 'comphyology', target: 'emergent', value: 3, label: 'Defines' },
      { source: 'morphological', target: 'father', value: 2, label: 'Enhances' },
      { source: 'quantum', target: 'son', value: 2, label: 'Enhances' },
      { source: 'emergent', target: 'spirit', value: 2, label: 'Enhances' }
    ];

    return {
      type: 'trinity_integration_diagram',
      nodes,
      edges,
      title: 'Trinity Integration Diagram'
    };
  }

  /**
   * Generate contour data from grid data
   *
   * @param {Array} data - Grid data
   * @param {string} valueField - Field to use for contour values
   * @returns {Array} - Contour data
   * @private
   */
  _generateContourData(data, valueField) {
    const contourLevels = 10;
    const contourData = [];

    // Find min and max values
    let minValue = Infinity;
    let maxValue = -Infinity;

    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < data[i].length; j++) {
        const value = data[i][j][valueField];
        minValue = Math.min(minValue, value);
        maxValue = Math.max(maxValue, value);
      }
    }

    // Generate contour levels
    const levelStep = (maxValue - minValue) / contourLevels;

    for (let level = 0; level < contourLevels; level++) {
      const contourValue = minValue + level * levelStep;
      contourData.push({
        level,
        value: contourValue,
        points: this._generateContourPoints(data, valueField, contourValue)
      });
    }

    return contourData;
  }

  /**
   * Generate contour points for a specific contour value
   *
   * @param {Array} data - Grid data
   * @param {string} valueField - Field to use for contour values
   * @param {number} contourValue - Contour value
   * @returns {Array} - Contour points
   * @private
   */
  _generateContourPoints(data, valueField, contourValue) {
    // Simplified contour generation - in a real implementation, this would use
    // a proper contouring algorithm like marching squares
    const points = [];

    for (let i = 0; i < data.length - 1; i++) {
      for (let j = 0; j < data[i].length - 1; j++) {
        const v1 = data[i][j][valueField];
        const v2 = data[i+1][j][valueField];
        const v3 = data[i+1][j+1][valueField];
        const v4 = data[i][j+1][valueField];

        // Check if contour passes through this cell
        if ((v1 <= contourValue && contourValue <= v3) ||
            (v3 <= contourValue && contourValue <= v1)) {

          // Add a point in the middle of the cell
          points.push({
            x: i + 0.5,
            y: j + 0.5
          });
        }
      }
    }

    return points;
  }

  /**
   * Generate vector field data from grid data
   *
   * @param {Array} data - Grid data
   * @param {string} xField - Field to use for x-coordinates
   * @param {string} yField - Field to use for y-coordinates
   * @param {string} uField - Field to use for x-components of vectors
   * @param {string} vField - Field to use for y-components of vectors
   * @returns {Array} - Vector field data
   * @private
   */
  _generateVectorField(data, xField, yField, uField, vField) {
    const vectorField = [];
    const vectorDensity = Math.max(5, Math.floor(data.length / 10));

    for (let i = 0; i < data.length; i += vectorDensity) {
      for (let j = 0; j < data[i].length; j += vectorDensity) {
        const x = data[i][j][xField];
        const y = data[i][j][yField];
        const u = data[i][j][uField];
        const v = data[i][j][vField];

        // Calculate vector magnitude and normalize
        const magnitude = Math.sqrt(u * u + v * v);
        const normalizedU = magnitude > 0 ? u / magnitude : 0;
        const normalizedV = magnitude > 0 ? v / magnitude : 0;

        vectorField.push({
          x,
          y,
          u: normalizedU,
          v: normalizedV,
          magnitude
        });
      }
    }

    return vectorField;
  }

  /**
   * Generate decision boundaries from grid data
   *
   * @param {Array} data - Grid data
   * @param {string} xField - Field to use for x-coordinates
   * @param {string} yField - Field to use for y-coordinates
   * @param {string} valueField - Field to use for decision values
   * @returns {Array} - Decision boundary data
   * @private
   */
  _generateDecisionBoundaries(data, xField, yField, valueField) {
    const thresholds = [0.3, 0.5, 0.7, 0.9];
    const boundaries = [];

    for (const threshold of thresholds) {
      const boundary = {
        threshold,
        points: []
      };

      // Find points where value crosses threshold
      for (let i = 0; i < data.length - 1; i++) {
        for (let j = 0; j < data[i].length - 1; j++) {
          const v1 = data[i][j][valueField];
          const v2 = data[i+1][j][valueField];
          const v3 = data[i+1][j+1][valueField];
          const v4 = data[i][j+1][valueField];

          // Check if boundary passes through this cell
          if ((v1 <= threshold && threshold <= v3) ||
              (v3 <= threshold && threshold <= v1)) {

            // Add a point in the middle of the cell
            boundary.points.push({
              x: data[i][j][xField] + (data[i+1][j][xField] - data[i][j][xField]) / 2,
              y: data[i][j][yField] + (data[i][j+1][yField] - data[i][j][yField]) / 2
            });
          }
        }
      }

      boundaries.push(boundary);
    }

    return boundaries;
  }
}

module.exports = ComphyologyVisualization;
