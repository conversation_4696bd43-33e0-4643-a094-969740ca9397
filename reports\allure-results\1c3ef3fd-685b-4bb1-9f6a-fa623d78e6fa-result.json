{"name": "test_coherence_initialization", "status": "passed", "description": "Test that NovaCortex initializes with proper coherence.", "start": 1752966148861, "stop": 1752966148887, "uuid": "5ad3ddf6-81d2-4508-8793-3548d968d3bf", "historyId": "eb275396222761379a7275217f61df9f", "testCaseId": "eb275396222761379a7275217f61df9f", "fullName": "tests.novacortex.test_integration#test_coherence_initialization", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.novacortex"}, {"name": "suite", "value": "test_integration"}, {"name": "host", "value": "d1cae64bda82"}, {"name": "thread", "value": "1-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.novacortex.test_integration"}]}