/**
 * Connector Validation Schemas
 * 
 * This file contains validation schemas for connector-related endpoints.
 */

const Joi = require('joi');
const { commonSchemas } = require('../common/commonSchemas');

/**
 * Create connector schema
 */
const createConnectorSchema = {
  body: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().allow('').optional(),
    type: commonSchemas.connectorType.required(),
    config: Joi.object().required(),
    credentials: Joi.object().required(),
    tags: Joi.array().items(Joi.string()).optional(),
    teamId: commonSchemas.id.optional(),
    environmentId: commonSchemas.id.optional(),
    isActive: Joi.boolean().default(true),
    schedule: Joi.object({
      enabled: Joi.boolean().default(false),
      type: Joi.string().valid('interval', 'cron').when('enabled', {
        is: true,
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      interval: Joi.number().min(1).when('type', {
        is: 'interval',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      intervalUnit: Joi.string().valid('minutes', 'hours', 'days').when('type', {
        is: 'interval',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      cronExpression: Joi.string().when('type', {
        is: 'cron',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      timezone: Joi.string().optional()
    }).optional(),
    metadata: Joi.object().optional()
  })
};

/**
 * Update connector schema
 */
const updateConnectorSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required()
  }),
  body: Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().allow('').optional(),
    config: Joi.object().optional(),
    credentials: Joi.object().optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    teamId: commonSchemas.id.optional(),
    environmentId: commonSchemas.id.optional(),
    isActive: Joi.boolean().optional(),
    schedule: Joi.object({
      enabled: Joi.boolean().default(false),
      type: Joi.string().valid('interval', 'cron').when('enabled', {
        is: true,
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      interval: Joi.number().min(1).when('type', {
        is: 'interval',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      intervalUnit: Joi.string().valid('minutes', 'hours', 'days').when('type', {
        is: 'interval',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      cronExpression: Joi.string().when('type', {
        is: 'cron',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      timezone: Joi.string().optional()
    }).optional(),
    metadata: Joi.object().optional()
  }).min(1)
};

/**
 * Get connector schema
 */
const getConnectorSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required()
  })
};

/**
 * Delete connector schema
 */
const deleteConnectorSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required()
  })
};

/**
 * List connectors schema
 */
const listConnectorsSchema = {
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('name', 'type', 'createdAt', 'updatedAt', 'lastRunAt').default('createdAt'),
    sortOrder: commonSchemas.sortOrder,
    search: Joi.string().allow('').optional(),
    type: commonSchemas.connectorType.optional(),
    status: commonSchemas.connectorStatus.optional(),
    teamId: commonSchemas.id.optional(),
    environmentId: commonSchemas.id.optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    isActive: Joi.boolean().optional()
  })
};

/**
 * Test connector schema
 */
const testConnectorSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.optional()
  }),
  body: Joi.object({
    type: commonSchemas.connectorType.when('connectorId', {
      is: Joi.exist(),
      then: Joi.optional(),
      otherwise: Joi.required()
    }),
    config: Joi.object().when('connectorId', {
      is: Joi.exist(),
      then: Joi.optional(),
      otherwise: Joi.required()
    }),
    credentials: Joi.object().when('connectorId', {
      is: Joi.exist(),
      then: Joi.optional(),
      otherwise: Joi.required()
    })
  })
};

/**
 * Run connector schema
 */
const runConnectorSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required()
  }),
  body: Joi.object({
    parameters: Joi.object().optional()
  })
};

/**
 * Get connector runs schema
 */
const getConnectorRunsSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required()
  }),
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('startedAt', 'endedAt', 'status', 'duration').default('startedAt'),
    sortOrder: commonSchemas.sortOrder,
    status: Joi.string().valid('success', 'failure', 'running', 'pending', 'cancelled').optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
  })
};

/**
 * Get connector run schema
 */
const getConnectorRunSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required(),
    runId: commonSchemas.id.required()
  })
};

/**
 * Cancel connector run schema
 */
const cancelConnectorRunSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required(),
    runId: commonSchemas.id.required()
  })
};

/**
 * Get connector logs schema
 */
const getConnectorLogsSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required(),
    runId: commonSchemas.id.optional()
  }),
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    level: Joi.string().valid('debug', 'info', 'warn', 'error').optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
  })
};

/**
 * Clone connector schema
 */
const cloneConnectorSchema = {
  params: Joi.object({
    connectorId: commonSchemas.id.required()
  }),
  body: Joi.object({
    name: Joi.string().optional(),
    teamId: commonSchemas.id.optional(),
    environmentId: commonSchemas.id.optional()
  })
};

/**
 * Bulk update connectors schema
 */
const bulkUpdateConnectorsSchema = {
  body: Joi.object({
    connectorIds: commonSchemas.idArray.required(),
    updates: Joi.object({
      isActive: Joi.boolean().optional(),
      teamId: commonSchemas.id.optional(),
      environmentId: commonSchemas.id.optional(),
      tags: Joi.array().items(Joi.string()).optional()
    }).min(1).required()
  })
};

/**
 * Bulk delete connectors schema
 */
const bulkDeleteConnectorsSchema = {
  body: Joi.object({
    connectorIds: commonSchemas.idArray.required()
  })
};

module.exports = {
  createConnectorSchema,
  updateConnectorSchema,
  getConnectorSchema,
  deleteConnectorSchema,
  listConnectorsSchema,
  testConnectorSchema,
  runConnectorSchema,
  getConnectorRunsSchema,
  getConnectorRunSchema,
  cancelConnectorRunSchema,
  getConnectorLogsSchema,
  cloneConnectorSchema,
  bulkUpdateConnectorsSchema,
  bulkDeleteConnectorsSchema
};
