/**
 * NovaVision Security Module
 *
 * This module integrates RBAC and NIST security requirements for NovaVision.
 * It provides a unified interface for securing NovaVision components.
 */

const { NovaVisionRBAC, ROLES, PERMISSIONS } = require('./rbac');
const { NovaVisionNISTSecurity } = require('./nist');

/**
 * NovaVision Security Manager
 *
 * Integrates RBAC and NIST security for NovaVision
 */
class NovaVisionSecurityManager {
  constructor(options = {}) {
    this.options = {
      enableRBAC: true,
      enableNIST: true,
      auditLogSize: 1000,
      enableAuditLog: true,
      ...options
    };

    // Initialize RBAC
    this.rbac = new NovaVisionRBAC(options.rbacOptions || {});

    // Initialize NIST security
    this.nist = new NovaVisionNISTSecurity(options.nistOptions || {});

    // Initialize security log
    this.securityLog = [];

    // Initialize audit log
    this.auditLog = [];

    // Initialize metrics
    this.metrics = {
      accessDenied: 0,
      accessGranted: 0,
      rbacChecks: 0,
      nistChecks: 0,
      securityViolations: 0,
      lastViolationTimestamp: null
    };
  }

  /**
   * Secure UI schema with both RBAC and NIST controls
   * @param {Object} uiSchema - UI schema to secure
   * @param {string} userId - User ID
   * @returns {Object} - Secured UI schema
   */
  secureUISchema(uiSchema, userId) {
    if (!uiSchema) {
      return uiSchema;
    }

    let securedSchema = { ...uiSchema };

    // Apply NIST security controls
    if (this.options.enableNIST) {
      securedSchema = this.nist.secureUISchema(securedSchema);
    }

    // Apply RBAC
    if (this.options.enableRBAC && userId) {
      securedSchema = this._applyRBACToSchema(securedSchema, userId);
    }

    // Log security action
    this._logSecurityAction('UI schema secured', {
      schemaId: uiSchema.id || 'unknown',
      userId,
      rbacApplied: this.options.enableRBAC,
      nistApplied: this.options.enableNIST
    });

    return securedSchema;
  }

  /**
   * Secure rendered content with both RBAC and NIST controls
   * @param {string|Object} content - Content to secure
   * @param {string} userId - User ID
   * @param {string} contentType - Content type (e.g., 'trinity_csde', 'optimization')
   * @returns {string|Object} - Secured content
   */
  secureRenderedContent(content, userId, contentType) {
    if (!content) {
      return content;
    }

    // Check RBAC permissions first
    if (this.options.enableRBAC && userId && contentType) {
      const hasPermission = this._checkContentTypePermission(userId, contentType);
      if (!hasPermission) {
        // Return restricted content
        return this._getRestrictedContent(contentType);
      }
    }

    // Apply NIST security controls
    let securedContent = content;
    if (this.options.enableNIST) {
      securedContent = this.nist.secureRenderedContent(securedContent);
    }

    // Log security action
    this._logSecurityAction('Content secured', {
      userId,
      contentType,
      rbacApplied: this.options.enableRBAC,
      nistApplied: this.options.enableNIST
    });

    return securedContent;
  }

  /**
   * Validate user access to a component
   * @param {string} userId - User ID
   * @param {string} componentType - Component type
   * @returns {boolean} - Whether access is allowed
   */
  validateAccess(userId, componentType, options = {}) {
    if (!this.options.enableRBAC || !userId) {
      return true;
    }

    const { action = 'view', sessionId, requestId } = options;

    // Create audit entry for access validation
    const auditEntry = {
      timestamp: new Date().toISOString(),
      operation: 'access_validation',
      userId: userId || 'anonymous',
      sessionId: sessionId || 'none',
      requestId: requestId || `req-${Date.now()}`,
      componentType,
      action
    };

    // Check direct component permission
    const hasComponentPermission = this._checkComponentPermission(userId, componentType);

    // Check if user has admin role (always grant access)
    const isAdmin = this.rbac.hasRole(userId, 'ADMIN');

    // Check for specific permission mappings
    const hasSpecificPermission = this._checkSpecificPermission(userId, componentType);

    // Get user roles for logging
    const userRoles = this.rbac.getUserRoles(userId);

    // Determine final access decision
    const hasPermission = hasComponentPermission || isAdmin || hasSpecificPermission;

    // Determine reason for decision
    const reason = isAdmin ? 'admin_role' :
                  hasComponentPermission ? 'component_permission' :
                  hasSpecificPermission ? 'specific_permission' : 'access_denied';

    // Update audit entry
    auditEntry.allowed = hasPermission;
    auditEntry.reason = reason;
    auditEntry.roles = userRoles;

    // Log access validation with detailed reason
    this._logSecurityAction('Access validation', {
      userId,
      componentType,
      action,
      allowed: hasPermission,
      reason,
      roles: userRoles
    });

    // Add to security audit log
    this._addToAuditLog(auditEntry);

    return hasPermission;
  }

  /**
   * Get security report
   * @returns {Object} - Security report
   */
  getSecurityReport() {
    return {
      timestamp: new Date().toISOString(),
      rbacEnabled: this.options.enableRBAC,
      nistEnabled: this.options.enableNIST,
      nistCompliance: this.options.enableNIST ? this.nist.getNISTComplianceReport() : null,
      securityLog: this.securityLog.slice(-100) // Last 100 entries
    };
  }

  /**
   * Apply RBAC to UI schema
   * @param {Object} schema - UI schema
   * @param {string} userId - User ID
   * @returns {Object} - RBAC-secured schema
   * @private
   */
  _applyRBACToSchema(schema, userId) {
    // Create a deep copy
    const securedSchema = JSON.parse(JSON.stringify(schema));

    // Filter components based on permissions
    if (securedSchema.components) {
      securedSchema.components = securedSchema.components.filter(component => {
        // Check if component has permission requirements
        if (component.requiredPermission) {
          return this.rbac.hasPermission(userId, component.requiredPermission);
        }

        // Check if component has role requirements
        if (component.requiredRole) {
          return this.rbac.hasRole(userId, component.requiredRole);
        }

        // No requirements, allow by default
        return true;
      });
    }

    // Filter sections based on permissions
    if (securedSchema.sections) {
      securedSchema.sections = securedSchema.sections.filter(section => {
        // Check if section has permission requirements
        if (section.requiredPermission) {
          return this.rbac.hasPermission(userId, section.requiredPermission);
        }

        // Check if section has role requirements
        if (section.requiredRole) {
          return this.rbac.hasRole(userId, section.requiredRole);
        }

        // No requirements, allow by default
        return true;
      });
    }

    return securedSchema;
  }

  /**
   * Check permission for content type
   * @param {string} userId - User ID
   * @param {string} contentType - Content type
   * @returns {boolean} - Whether user has permission
   * @private
   */
  _checkContentTypePermission(userId, contentType) {
    // Map content types to permissions
    const contentTypePermissions = {
      'trinity_csde': PERMISSIONS.VIEW_TRINITY_CSDE,
      'optimization': PERMISSIONS.VIEW_OPTIMIZATION,
      'uuft': PERMISSIONS.VIEW_UUFT,
      'security': PERMISSIONS.VIEW_SECURITY,
      'compliance': PERMISSIONS.VIEW_COMPLIANCE,
      'financial': PERMISSIONS.VIEW_FINANCIAL,
      'strategic': PERMISSIONS.VIEW_STRATEGIC,
      'audit_logs': PERMISSIONS.VIEW_AUDIT_LOGS
    };

    // Get required permission for content type
    const requiredPermission = contentTypePermissions[contentType];

    // If no mapping exists, default to VIEW_DETAILED
    if (!requiredPermission) {
      return this.rbac.hasPermission(userId, PERMISSIONS.VIEW_DETAILED);
    }

    // Check permission
    return this.rbac.hasPermission(userId, requiredPermission);
  }

  /**
   * Check permission for component
   * @param {string} userId - User ID
   * @param {string} componentType - Component type
   * @returns {boolean} - Whether user has permission
   * @private
   */
  _checkComponentPermission(userId, componentType) {
    // Map component types to permissions
    const componentPermissions = {
      'trinity_dashboard': PERMISSIONS.VIEW_TRINITY_CSDE,
      'optimization_chart': PERMISSIONS.VIEW_OPTIMIZATION,
      'uuft_visualization': PERMISSIONS.VIEW_UUFT,
      'security_dashboard': PERMISSIONS.VIEW_SECURITY,
      'compliance_report': PERMISSIONS.VIEW_COMPLIANCE,
      'financial_summary': PERMISSIONS.VIEW_FINANCIAL,
      'strategic_overview': PERMISSIONS.VIEW_STRATEGIC,
      'audit_log_viewer': PERMISSIONS.VIEW_AUDIT_LOGS,
      'quantum_inference': PERMISSIONS.VIEW_QUANTUM_INFERENCE
    };

    // Get required permission for component type
    const requiredPermission = componentPermissions[componentType];

    // If no mapping exists, default to VIEW_SUMMARY
    if (!requiredPermission) {
      return this.rbac.hasPermission(userId, PERMISSIONS.VIEW_SUMMARY);
    }

    // Check permission
    return this.rbac.hasPermission(userId, requiredPermission);
  }

  /**
   * Check specific permission for component type
   * @param {string} userId - User ID
   * @param {string} componentType - Component type
   * @returns {boolean} - Whether user has permission
   * @private
   */
  _checkSpecificPermission(userId, componentType) {
    // Special case for quantum inference
    if (componentType === 'quantum_inference') {
      // Check if user has CISO or Security Analyst role
      if (this.rbac.hasRole(userId, 'CISO') || this.rbac.hasRole(userId, 'SECURITY_ANALYST')) {
        return true;
      }

      // Check for specific permissions
      return this.rbac.hasPermission(userId, 'view:quantum_inference') ||
             this.rbac.hasPermission(userId, 'view:all');
    }

    // Special case for trinity dashboard
    if (componentType === 'trinity_dashboard') {
      return this.rbac.hasPermission(userId, 'view:trinity_csde') ||
             this.rbac.hasPermission(userId, 'view:all');
    }

    // Special case for optimization
    if (componentType === 'optimization_chart') {
      return this.rbac.hasPermission(userId, 'view:optimization') ||
             this.rbac.hasPermission(userId, 'view:all');
    }

    // Special case for UUFT
    if (componentType === 'uuft_visualization') {
      return this.rbac.hasPermission(userId, 'view:uuft') ||
             this.rbac.hasPermission(userId, 'view:all');
    }

    // Default to false for unknown component types
    return false;
  }

  /**
   * Get restricted content for unauthorized access
   * @param {string} contentType - Content type
   * @returns {Object} - Restricted content
   * @private
   */
  _getRestrictedContent(contentType) {
    return {
      restricted: true,
      message: `Access to ${contentType} content is restricted. Please contact your administrator for access.`,
      contentType
    };
  }

  /**
   * Log security action
   * @param {string} action - Security action
   * @param {Object} data - Action data
   * @private
   */
  _logSecurityAction(action, data) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      action,
      ...data
    };

    this.securityLog.push(logEntry);

    // Limit log size
    if (this.securityLog.length > 1000) {
      this.securityLog.shift();
    }

    // Update metrics
    if (data.allowed === false) {
      this.metrics.accessDenied++;
      this.metrics.securityViolations++;
      this.metrics.lastViolationTimestamp = new Date().toISOString();
    } else if (data.allowed === true) {
      this.metrics.accessGranted++;
    }

    // Log to console
    console.log(`[SECURITY] ${action}: ${JSON.stringify(data)}`);
  }

  /**
   * Add entry to audit log
   * @param {Object} entry - Audit entry
   * @private
   */
  _addToAuditLog(entry) {
    if (!this.options.enableAuditLog) {
      return;
    }

    // Add to audit log
    this.auditLog.push(entry);

    // Limit log size
    if (this.auditLog.length > this.options.auditLogSize) {
      this.auditLog.shift();
    }

    // Log to console in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log(`[AUDIT] ${entry.operation} | User: ${entry.userId} | Status: ${entry.allowed ? 'ALLOWED' : 'DENIED'}`);
    }
  }

  /**
   * Get audit log
   * @param {Object} options - Filter options
   * @param {string} [options.userId] - Filter by user ID
   * @param {string} [options.operation] - Filter by operation
   * @param {boolean} [options.allowed] - Filter by allowed status
   * @param {string} [options.startDate] - Filter by start date
   * @param {string} [options.endDate] - Filter by end date
   * @returns {Array} - Filtered audit log
   */
  getAuditLog(options = {}) {
    const { userId, operation, allowed, startDate, endDate } = options;

    return this.auditLog.filter(entry => {
      if (userId && entry.userId !== userId) {
        return false;
      }

      if (operation && entry.operation !== operation) {
        return false;
      }

      if (allowed !== undefined && entry.allowed !== allowed) {
        return false;
      }

      if (startDate) {
        const entryDate = new Date(entry.timestamp);
        const filterDate = new Date(startDate);
        if (entryDate < filterDate) {
          return false;
        }
      }

      if (endDate) {
        const entryDate = new Date(entry.timestamp);
        const filterDate = new Date(endDate);
        if (entryDate > filterDate) {
          return false;
        }
      }

      return true;
    });
  }
}

module.exports = {
  NovaVisionSecurityManager,
  NovaVisionRBAC,
  NovaVisionNISTSecurity,
  ROLES,
  PERMISSIONS
};
