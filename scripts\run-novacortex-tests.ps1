# PowerShell script to run NovaCortex tests

# Colors for output
$GREEN = "Green"
$RED = "Red"

Write-Host "Starting NovaCortex test environment..." -ForegroundColor $GREEN

# Build and start the test environment
docker-compose -f docker-compose.novacortex-test.yml up -d --build

# Wait for services to be ready
Write-Host "Waiting for services to be ready..." -ForegroundColor $GREEN
Start-Sleep -Seconds 5

# Check if NovaCortex is healthy
$novaCortexHealthy = $false
$attempts = 0
$maxAttempts = 12

while (-not $novaCortexHealthy -and $attempts -lt $maxAttempts) {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:3010/health" -Method GET -ErrorAction Stop
        if ($response.status -eq "healthy") {
            $novaCortexHealthy = $true
            Write-Host "✅ NovaCortex is healthy!" -ForegroundColor $GREEN
        }
    } catch {
        Write-Host "Waiting for NovaCortex... (attempt $($attempts + 1)/$maxAttempts)" -ForegroundColor Yellow
        Start-Sleep -Seconds 5
        $attempts++
    }
}

if (-not $novaCortexHealthy) {
    Write-Host "❌ NovaCortex failed to become healthy" -ForegroundColor $RED
    docker-compose -f docker-compose.novacortex-test.yml logs novacortex
    exit 1
}

# Run tests
Write-Host "Running NovaCortex tests..." -ForegroundColor $GREEN
$testResult = docker-compose -f docker-compose.novacortex-test.yml run --rm test-runner

# Check test results
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ All tests passed!" -ForegroundColor $GREEN
    Write-Host ""
    Write-Host "Test Results Summary:" -ForegroundColor $GREEN
    Write-Host "--------------------" -ForegroundColor $GREEN
    Write-Host "- Coherence Initialization: ✅ PASSED"
    Write-Host "- CASTL Decision Making: ✅ PASSED"
    Write-Host "- π-Rhythm Synchronization: ✅ PASSED"
    Write-Host "- Metrics Endpoint: ✅ PASSED"
    Write-Host "- End-to-End Workflow: ✅ PASSED"
    Write-Host ""
    Write-Host "Monitoring URLs:" -ForegroundColor $GREEN
    Write-Host "- NovaCortex Health: http://localhost:3010/health"
    Write-Host "- NovaCortex Metrics: http://localhost:3010/metrics"
    Write-Host "- Prometheus: http://localhost:9090"
    Write-Host "- Grafana: http://localhost:3000 (admin/admin)"
    Write-Host ""
    Write-Host "✅ NovaCortex Docker testing completed successfully!" -ForegroundColor $GREEN
} else {
    Write-Host "❌ Some tests failed" -ForegroundColor $RED
    Write-Host ""
    Write-Host "Service logs:" -ForegroundColor $RED
    docker-compose -f docker-compose.novacortex-test.yml logs novacortex
    exit 1
}
