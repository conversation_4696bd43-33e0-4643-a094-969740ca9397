// React component testing setup
require('@testing-library/jest-dom');
const { configure } = require('@testing-library/react');

// Configure testing library
configure({ testIdAttribute: 'data-testid' });

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    reload: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn()
    }
  })
}));

// Mock framer-motion
jest.mock('framer-motion', () => {
  const React = require('react');
  const actual = jest.requireActual('framer-motion');
  return {
    ...actual,
    motion: {
      div: ({ children, ...props }) => React.createElement('div', props, children),
      p: ({ children, ...props }) => React.createElement('p', props, children),
      span: ({ children, ...props }) => React.createElement('span', props, children),
      button: ({ children, ...props }) => React.createElement('button', props, children),
      ul: ({ children, ...props }) => React.createElement('ul', props, children),
      li: ({ children, ...props }) => React.createElement('li', props, children),
      h1: ({ children, ...props }) => React.createElement('h1', props, children),
      h2: ({ children, ...props }) => React.createElement('h2', props, children),
      h3: ({ children, ...props }) => React.createElement('h3', props, children),
      h4: ({ children, ...props }) => React.createElement('h4', props, children),
      h5: ({ children, ...props }) => React.createElement('h5', props, children),
      h6: ({ children, ...props }) => React.createElement('h6', props, children),
      a: ({ children, ...props }) => React.createElement('a', props, children),
      img: (props) => React.createElement('img', props),
      svg: ({ children, ...props }) => React.createElement('svg', props, children),
      path: (props) => React.createElement('path', props)
    },
    AnimatePresence: ({ children }) => React.createElement(React.Fragment, null, children),
    useAnimation: () => ({
      start: jest.fn(),
      stop: jest.fn()
    }),
    useInView: () => [jest.fn(), true],
    useScroll: () => ({
      scrollYProgress: { onChange: jest.fn(), get: () => 0 }
    })
  };
});

// Mock intersection observer
global.IntersectionObserver = class IntersectionObserver {
  constructor(callback) {
    this.callback = callback;
  }
  observe() {
    this.callback([{ isIntersecting: true }]);
  }
  unobserve() {}
  disconnect() {}
};
