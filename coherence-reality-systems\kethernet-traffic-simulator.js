const axios = require('axios');

class KetherNetTrafficSimulator {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
    this.isRunning = false;
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      consciousnessValidations: 0,
      crownNodesCreated: 0,
      coheriumTransfers: 0
    };
  }

  // Generate random consciousness data
  generateConsciousnessData() {
    return {
      neural: Math.floor(Math.random() * 20) + 5,
      information: Math.floor(Math.random() * 25) + 10,
      coherence: Math.floor(Math.random() * 30) + 15
    };
  }

  // Generate random wallet addresses
  generateWalletAddress() {
    const chars = '0123456789abcdef';
    let address = '0x';
    for (let i = 0; i < 40; i++) {
      address += chars[Math.floor(Math.random() * chars.length)];
    }
    return address;
  }

  // Simulate consciousness validation requests
  async simulateConsciousnessValidation() {
    try {
      const consciousnessData = this.generateConsciousnessData();
      const response = await axios.post(`${this.baseUrl}/consciousness/validate`, consciousnessData);
      
      this.stats.totalRequests++;
      this.stats.consciousnessValidations++;
      
      if (response.data.isValid) {
        this.stats.crownNodesCreated++;
        console.log(`👑 NEW CROWN NODE: UUFT=${response.data.uuftScore} (threshold: ${response.data.threshold})`);
      } else {
        console.log(`🔍 Candidate Node: UUFT=${response.data.uuftScore} (below threshold)`);
      }
      
      this.stats.successfulRequests++;
      return response.data;
    } catch (error) {
      this.stats.failedRequests++;
      console.error('❌ Consciousness validation failed:', error.message);
    }
  }

  // Simulate blockchain stats requests
  async simulateStatsRequest() {
    try {
      const response = await axios.get(`${this.baseUrl}/stats`);
      this.stats.totalRequests++;
      this.stats.successfulRequests++;
      
      console.log(`📊 Block Height: ${response.data.blockchain.blockHeight} | Active Nodes: ${response.data.blockchain.activeNodes}`);
      return response.data;
    } catch (error) {
      this.stats.failedRequests++;
      console.error('❌ Stats request failed:', error.message);
    }
  }

  // Simulate Coherium balance checks
  async simulateCoheriumBalance() {
    try {
      const address = this.generateWalletAddress();
      const response = await axios.get(`${this.baseUrl}/coherium/balance/${address}`);
      this.stats.totalRequests++;
      this.stats.coheriumTransfers++;
      this.stats.successfulRequests++;
      
      console.log(`💰 Coherium Balance: ${response.data.balance} for ${address.substring(0, 10)}...`);
      return response.data;
    } catch (error) {
      this.stats.failedRequests++;
      console.error('❌ Coherium balance check failed:', error.message);
    }
  }

  // Simulate health checks
  async simulateHealthCheck() {
    try {
      const response = await axios.get(`${this.baseUrl}/health`);
      this.stats.totalRequests++;
      this.stats.successfulRequests++;
      
      console.log(`💚 Health: ${response.data.status} | Uptime: ${Math.floor(response.data.uptime)}s`);
      return response.data;
    } catch (error) {
      this.stats.failedRequests++;
      console.error('❌ Health check failed:', error.message);
    }
  }

  // Simulate block requests
  async simulateBlockRequest() {
    try {
      const response = await axios.get(`${this.baseUrl}/blocks`);
      this.stats.totalRequests++;
      this.stats.successfulRequests++;
      
      console.log(`🔗 Latest Block: #${response.data.blocks[0].height} | Consciousness Score: ${response.data.blocks[0].consciousnessScore}`);
      return response.data;
    } catch (error) {
      this.stats.failedRequests++;
      console.error('❌ Block request failed:', error.message);
    }
  }

  // Run mixed traffic simulation
  async runTrafficSimulation(duration = 60000, requestsPerSecond = 5) {
    console.log('🚀 Starting KetherNet Traffic Simulation...');
    console.log(`⏱️  Duration: ${duration/1000}s | Rate: ${requestsPerSecond} req/s`);
    console.log('🌐 Simulating real-world blockchain traffic...\n');

    this.isRunning = true;
    const interval = 1000 / requestsPerSecond;

    const trafficTimer = setInterval(async () => {
      if (!this.isRunning) {
        clearInterval(trafficTimer);
        return;
      }

      // Random traffic distribution
      const rand = Math.random();
      
      if (rand < 0.3) {
        await this.simulateConsciousnessValidation();
      } else if (rand < 0.5) {
        await this.simulateStatsRequest();
      } else if (rand < 0.7) {
        await this.simulateCoheriumBalance();
      } else if (rand < 0.85) {
        await this.simulateHealthCheck();
      } else {
        await this.simulateBlockRequest();
      }
    }, interval);

    // Stop after duration
    setTimeout(() => {
      this.isRunning = false;
      clearInterval(trafficTimer);
      this.printFinalStats();
    }, duration);

    // Print stats every 10 seconds
    const statsTimer = setInterval(() => {
      if (!this.isRunning) {
        clearInterval(statsTimer);
        return;
      }
      this.printStats();
    }, 10000);
  }

  printStats() {
    console.log('\n📈 TRAFFIC SIMULATION STATS:');
    console.log(`   Total Requests: ${this.stats.totalRequests}`);
    console.log(`   Successful: ${this.stats.successfulRequests}`);
    console.log(`   Failed: ${this.stats.failedRequests}`);
    console.log(`   Consciousness Validations: ${this.stats.consciousnessValidations}`);
    console.log(`   Crown Nodes Created: ${this.stats.crownNodesCreated}`);
    console.log(`   Coherium Operations: ${this.stats.coheriumTransfers}`);
    console.log(`   Success Rate: ${((this.stats.successfulRequests/this.stats.totalRequests)*100).toFixed(1)}%\n`);
  }

  printFinalStats() {
    console.log('\n🎯 FINAL SIMULATION RESULTS:');
    console.log('================================');
    this.printStats();
    console.log('🔥 KetherNet Traffic Simulation Complete!');
  }
}

// CLI interface
if (require.main === module) {
  const simulator = new KetherNetTrafficSimulator();
  
  const duration = process.argv[2] ? parseInt(process.argv[2]) * 1000 : 60000;
  const rps = process.argv[3] ? parseInt(process.argv[3]) : 5;
  
  simulator.runTrafficSimulation(duration, rps);
}

module.exports = KetherNetTrafficSimulator;
