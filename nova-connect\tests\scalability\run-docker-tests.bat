@echo off
REM Run NovaConnect UAC Scalability Tests in Docker

REM Set variables
set RESULTS_DIR=.\results
set REPORT_FILE=%RESULTS_DIR%\scalability-report.md

REM Create results directory
if not exist %RESULTS_DIR% mkdir %RESULTS_DIR%

REM Print banner
echo ==================================================
echo NovaConnect UAC Scalability Tests
echo ==================================================
echo.

REM Check if Docker is running
docker info > nul 2>&1
if %ERRORLEVEL% neq 0 (
  echo Error: Docker is not running. Please start Docker and try again.
  exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version > nul 2>&1
if %ERRORLEVEL% neq 0 (
  echo Error: Docker Compose is not installed. Please install Docker Compose and try again.
  exit /b 1
)

REM Start the test environment
echo Starting test environment...
docker-compose up -d nova-connect mongo redis
echo Waiting for services to start...
timeout /t 10 /nobreak > nul

REM Run API load test
echo Running API Load Test...
docker-compose run --rm k6 run /tests/api-load-test.js --out json=/results/api-load-test.json --summary-export=/results/api-load-test-summary.json

REM Run normalization load test
echo Running Normalization Load Test...
docker-compose run --rm k6 run /tests/normalization-load-test.js --out json=/results/normalization-load-test.json --summary-export=/results/normalization-load-test-summary.json

REM Run connector load test
echo Running Connector Load Test...
docker-compose run --rm k6 run /tests/connector-load-test.js --out json=/results/connector-load-test.json --summary-export=/results/connector-load-test-summary.json

REM Generate report
echo Generating report...
node generate-report.js

REM Stop the test environment
echo Stopping test environment...
docker-compose down

echo Scalability tests completed. Report generated: %REPORT_FILE%
