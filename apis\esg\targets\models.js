/**
 * @swagger
 * components:
 *   schemas:
 *     ESGTarget:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG target
 *         name:
 *           type: string
 *           description: Name of the ESG target
 *         description:
 *           type: string
 *           description: Description of the ESG target
 *         category:
 *           type: string
 *           enum: [environmental, social, governance]
 *           description: Category of the ESG target
 *         subcategory:
 *           type: string
 *           description: Subcategory of the ESG target
 *         metricId:
 *           type: string
 *           description: ID of the associated ESG metric
 *         targetValue:
 *           type: string
 *           description: Target value to achieve
 *         baselineValue:
 *           type: string
 *           description: Baseline value for comparison
 *         baselineDate:
 *           type: string
 *           format: date
 *           description: Date of the baseline measurement
 *         targetDate:
 *           type: string
 *           format: date
 *           description: Target date for achieving the target value
 *         unit:
 *           type: string
 *           description: Unit of measurement
 *         status:
 *           type: string
 *           enum: [planned, in-progress, achieved, missed, cancelled]
 *           description: Status of the target
 *         owner:
 *           type: string
 *           description: Owner of the target
 *         initiatives:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of associated initiatives
 *         milestones:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               targetValue:
 *                 type: string
 *               targetDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [planned, in-progress, achieved, missed, cancelled]
 *           description: Milestones for tracking progress
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the target was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the target was last updated
 *       required:
 *         - id
 *         - name
 *         - category
 *         - targetValue
 *         - targetDate
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     TargetProgress:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the progress record
 *         targetId:
 *           type: string
 *           description: ID of the associated ESG target
 *         date:
 *           type: string
 *           format: date
 *           description: Date of the progress measurement
 *         value:
 *           type: string
 *           description: Current value
 *         percentComplete:
 *           type: number
 *           description: Percentage of completion towards the target
 *         notes:
 *           type: string
 *           description: Additional notes about the progress
 *         createdBy:
 *           type: string
 *           description: User who created the progress record
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the progress record was created
 *       required:
 *         - id
 *         - targetId
 *         - date
 *         - value
 *         - createdBy
 *         - createdAt
 */

// Sample ESG targets
const esgTargets = [
  {
    id: 'tgt-001',
    name: 'Carbon Emissions Reduction',
    description: 'Reduce carbon emissions by 30% from 2020 baseline',
    category: 'environmental',
    subcategory: 'climate change',
    metricId: 'esg-m-001',
    targetValue: '4550',
    baselineValue: '6500',
    baselineDate: '2020-12-31',
    targetDate: '2025-12-31',
    unit: 'tCO2e',
    status: 'in-progress',
    owner: 'Sustainability Team',
    initiatives: ['esg-i-001'],
    milestones: [
      {
        id: 'mil-001',
        name: '10% Reduction',
        description: 'Achieve 10% reduction in carbon emissions',
        targetValue: '5850',
        targetDate: '2022-12-31',
        status: 'achieved'
      },
      {
        id: 'mil-002',
        name: '20% Reduction',
        description: 'Achieve 20% reduction in carbon emissions',
        targetValue: '5200',
        targetDate: '2024-06-30',
        status: 'in-progress'
      }
    ],
    createdAt: '2021-01-15T10:00:00Z',
    updatedAt: '2023-01-10T14:30:00Z'
  },
  {
    id: 'tgt-002',
    name: 'Renewable Energy Usage',
    description: 'Increase renewable energy usage to 50% of total energy consumption',
    category: 'environmental',
    subcategory: 'energy',
    metricId: 'esg-m-002',
    targetValue: '50',
    baselineValue: '15',
    baselineDate: '2020-12-31',
    targetDate: '2026-12-31',
    unit: '%',
    status: 'in-progress',
    owner: 'Facilities Management',
    initiatives: ['esg-i-001'],
    milestones: [
      {
        id: 'mil-003',
        name: '25% Renewable Energy',
        description: 'Achieve 25% renewable energy usage',
        targetValue: '25',
        targetDate: '2023-12-31',
        status: 'achieved'
      },
      {
        id: 'mil-004',
        name: '40% Renewable Energy',
        description: 'Achieve 40% renewable energy usage',
        targetValue: '40',
        targetDate: '2025-06-30',
        status: 'planned'
      }
    ],
    createdAt: '2021-01-15T10:30:00Z',
    updatedAt: '2023-01-10T14:45:00Z'
  },
  {
    id: 'tgt-003',
    name: 'Gender Diversity in Leadership',
    description: 'Achieve 40% women in leadership positions',
    category: 'social',
    subcategory: 'diversity and inclusion',
    metricId: 'esg-m-003',
    targetValue: '40',
    baselineValue: '25',
    baselineDate: '2020-12-31',
    targetDate: '2025-12-31',
    unit: '%',
    status: 'in-progress',
    owner: 'Human Resources',
    initiatives: ['esg-i-002'],
    milestones: [
      {
        id: 'mil-005',
        name: '30% Women in Leadership',
        description: 'Achieve 30% women in leadership positions',
        targetValue: '30',
        targetDate: '2023-06-30',
        status: 'achieved'
      },
      {
        id: 'mil-006',
        name: '35% Women in Leadership',
        description: 'Achieve 35% women in leadership positions',
        targetValue: '35',
        targetDate: '2024-12-31',
        status: 'planned'
      }
    ],
    createdAt: '2021-01-15T11:00:00Z',
    updatedAt: '2023-01-10T15:00:00Z'
  }
];

// Sample target progress records
const targetProgress = [
  {
    id: 'prg-001',
    targetId: 'tgt-001',
    date: '2021-12-31',
    value: '6175',
    percentComplete: 10,
    notes: 'Achieved 5% reduction through energy efficiency measures',
    createdBy: 'user-123',
    createdAt: '2022-01-15T09:00:00Z'
  },
  {
    id: 'prg-002',
    targetId: 'tgt-001',
    date: '2022-12-31',
    value: '5850',
    percentComplete: 20,
    notes: 'Achieved 10% reduction through renewable energy implementation',
    createdBy: 'user-123',
    createdAt: '2023-01-15T09:30:00Z'
  },
  {
    id: 'prg-003',
    targetId: 'tgt-002',
    date: '2022-12-31',
    value: '22',
    percentComplete: 20,
    notes: 'Installed solar panels at two major facilities',
    createdBy: 'user-456',
    createdAt: '2023-01-20T10:15:00Z'
  },
  {
    id: 'prg-004',
    targetId: 'tgt-002',
    date: '2023-12-31',
    value: '27',
    percentComplete: 34,
    notes: 'Signed renewable energy purchase agreements',
    createdBy: 'user-456',
    createdAt: '2024-01-10T11:45:00Z'
  },
  {
    id: 'prg-005',
    targetId: 'tgt-003',
    date: '2022-12-31',
    value: '28',
    percentComplete: 20,
    notes: 'Implemented leadership development program for women',
    createdBy: 'user-789',
    createdAt: '2023-01-25T14:30:00Z'
  },
  {
    id: 'prg-006',
    targetId: 'tgt-003',
    date: '2023-06-30',
    value: '31',
    percentComplete: 40,
    notes: 'Expanded recruitment efforts for diverse candidates',
    createdBy: 'user-789',
    createdAt: '2023-07-10T15:45:00Z'
  }
];

module.exports = {
  esgTargets,
  targetProgress
};
