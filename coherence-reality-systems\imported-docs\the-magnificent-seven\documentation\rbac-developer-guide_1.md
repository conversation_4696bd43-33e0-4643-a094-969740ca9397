# NovaConnect RBAC Developer Guide

This guide provides instructions for developers on how to integrate with the NovaConnect Role-Based Access Control (RBAC) system.

## Table of Contents

1. [Introduction](#introduction)
2. [Backend Integration](#backend-integration)
   - [Protecting API Routes](#protecting-api-routes)
   - [Checking Permissions in Controllers](#checking-permissions-in-controllers)
   - [Working with Roles and Permissions](#working-with-roles-and-permissions)
3. [Frontend Integration](#frontend-integration)
   - [Conditional Rendering](#conditional-rendering)
   - [Protected Routes](#protected-routes)
   - [Permission Hooks](#permission-hooks)
4. [Testing RBAC](#testing-rbac)
   - [Unit Testing](#unit-testing)
   - [Integration Testing](#integration-testing)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)

## Introduction

The NovaConnect RBAC system provides a flexible and powerful way to control access to resources and actions within the application. It is based on three main concepts:

- **Roles**: Collections of permissions that can be assigned to users
- **Permissions**: Definitions of what actions a user can perform on a specific resource
- **User Roles**: Associations between users and roles

## Backend Integration

### Protecting API Routes

To protect API routes with RBAC, use the `rbacMiddleware` in your route definitions:

```javascript
const express = require('express');
const router = express.Router();
const { authenticate } = require('../middleware/authMiddleware');
const rbacMiddleware = require('../middleware/rbacMiddleware');
const YourController = require('../controllers/YourController');

// All routes require authentication
router.use(authenticate);

// Protect route with permission check
router.get('/resources', rbacMiddleware.hasPermission('resource:view'), YourController.getAllResources);

// Protect route with role check
router.post('/resources', rbacMiddleware.hasRole('Administrator'), YourController.createResource);

// Protect route with multiple permissions (any one is sufficient)
router.put('/resources/:id', rbacMiddleware.hasPermission(['resource:edit', 'resource:admin']), YourController.updateResource);

// Protect route with multiple roles (any one is sufficient)
router.delete('/resources/:id', rbacMiddleware.hasRole(['Administrator', 'Manager']), YourController.deleteResource);

module.exports = router;
```

### Checking Permissions in Controllers

Sometimes you need to check permissions within a controller method, for example when a user can access a resource but only certain fields based on their permissions:

```javascript
const RBACService = require('../services/RBACService');
const rbacService = new RBACService();

// In your controller method
async function getResource(req, res, next) {
  try {
    const resource = await Resource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).json({ message: 'Resource not found' });
    }
    
    // Check if user has permission to view sensitive data
    const hasSensitivePermission = await rbacService.hasPermission(req.user.id, 'resource:view-sensitive');
    
    // Filter resource data based on permissions
    const resourceData = {
      id: resource._id,
      name: resource.name,
      description: resource.description
    };
    
    // Only include sensitive data if user has permission
    if (hasSensitivePermission) {
      resourceData.sensitiveField1 = resource.sensitiveField1;
      resourceData.sensitiveField2 = resource.sensitiveField2;
    }
    
    res.json(resourceData);
  } catch (error) {
    next(error);
  }
}
```

### Working with Roles and Permissions

To work with roles and permissions programmatically, use the `RBACService`:

```javascript
const RBACService = require('../services/RBACService');
const rbacService = new RBACService();

// Create a new role
const role = await rbacService.createRole({
  name: 'Custom Role',
  description: 'A custom role for specific use case',
  permissions: ['resource:view', 'resource:edit']
});

// Assign role to user
await rbacService.assignRoleToUser(userId, role._id);

// Check if user has permission
const hasPermission = await rbacService.hasPermission(userId, 'resource:view');

// Get user permissions
const permissions = await rbacService.getUserPermissions(userId);
```

## Frontend Integration

### Conditional Rendering

Use the `usePermission` hook to conditionally render UI elements based on user permissions:

```jsx
import React from 'react';
import { usePermission } from '../hooks/usePermission';

function ResourceList() {
  const canCreateResource = usePermission('resource:create');
  const canEditResource = usePermission('resource:edit');
  const canDeleteResource = usePermission('resource:delete');
  
  return (
    <div>
      <h1>Resources</h1>
      
      {canCreateResource && (
        <button onClick={handleCreateResource}>Create Resource</button>
      )}
      
      <ul>
        {resources.map(resource => (
          <li key={resource.id}>
            {resource.name}
            
            {canEditResource && (
              <button onClick={() => handleEditResource(resource.id)}>Edit</button>
            )}
            
            {canDeleteResource && (
              <button onClick={() => handleDeleteResource(resource.id)}>Delete</button>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}
```

### Protected Routes

Use the `ProtectedRoute` component to protect routes based on permissions:

```jsx
import React from 'react';
import { Route, Redirect } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

function ProtectedRoute({ component: Component, requiredPermission, ...rest }) {
  const { user, hasPermission } = useAuth();
  
  return (
    <Route
      {...rest}
      render={props => {
        // Check if user is authenticated
        if (!user) {
          return <Redirect to="/login" />;
        }
        
        // Check if user has required permission
        if (requiredPermission && !hasPermission(requiredPermission)) {
          return <Redirect to="/unauthorized" />;
        }
        
        // Render component if user has permission
        return <Component {...props} />;
      }}
    />
  );
}

// Usage in routes
<ProtectedRoute
  path="/resources/create"
  component={CreateResource}
  requiredPermission="resource:create"
/>
```

### Permission Hooks

Create custom hooks for working with permissions:

```jsx
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

// Hook for checking if user has a permission
export function usePermission(permission) {
  const { user } = useAuth();
  const [hasPermission, setHasPermission] = useState(false);
  
  useEffect(() => {
    if (!user) {
      setHasPermission(false);
      return;
    }
    
    async function checkPermission() {
      try {
        const response = await api.get(`/api/rbac/users/me/permissions/${permission}`);
        setHasPermission(response.data.hasPermission);
      } catch (error) {
        console.error('Error checking permission:', error);
        setHasPermission(false);
      }
    }
    
    checkPermission();
  }, [user, permission]);
  
  return hasPermission;
}

// Hook for getting user permissions
export function useUserPermissions() {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    if (!user) {
      setPermissions([]);
      setLoading(false);
      return;
    }
    
    async function fetchPermissions() {
      try {
        setLoading(true);
        const response = await api.get('/api/rbac/users/me/permissions');
        setPermissions(response.data);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching permissions:', error);
        setPermissions([]);
        setLoading(false);
      }
    }
    
    fetchPermissions();
  }, [user]);
  
  return { permissions, loading };
}
```

## Testing RBAC

### Unit Testing

Example of unit testing RBAC middleware:

```javascript
const { hasPermission, hasRole } = require('../middleware/rbacMiddleware');
const RBACService = require('../services/RBACService');

// Mock RBACService
jest.mock('../services/RBACService');

describe('RBAC Middleware', () => {
  let req, res, next;
  
  beforeEach(() => {
    req = {
      user: { id: 'user123' }
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    next = jest.fn();
    
    // Reset mocks
    RBACService.mockClear();
  });
  
  describe('hasPermission', () => {
    test('should call next if user has permission', async () => {
      // Mock hasPermission to return true
      RBACService.prototype.hasPermission = jest.fn().mockResolvedValue(true);
      
      const middleware = hasPermission('resource:view');
      await middleware(req, res, next);
      
      expect(RBACService.prototype.hasPermission).toHaveBeenCalledWith('user123', 'resource:view');
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });
    
    test('should return 403 if user does not have permission', async () => {
      // Mock hasPermission to return false
      RBACService.prototype.hasPermission = jest.fn().mockResolvedValue(false);
      
      const middleware = hasPermission('resource:view');
      await middleware(req, res, next);
      
      expect(RBACService.prototype.hasPermission).toHaveBeenCalledWith('user123', 'resource:view');
      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        error: 'Forbidden',
        message: expect.any(String)
      }));
    });
  });
});
```

### Integration Testing

Example of integration testing RBAC API:

```javascript
const request = require('supertest');
const app = require('../app');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const User = require('../models/User');
const Role = require('../models/Role');
const Permission = require('../models/Permission');
const { generateToken } = require('../utils/auth');

let mongoServer;
let adminToken;
let userToken;

beforeAll(async () => {
  // Set up in-memory MongoDB
  mongoServer = await MongoMemoryServer.create();
  await mongoose.connect(mongoServer.getUri());
  
  // Create test users and roles
  const adminUser = await User.create({
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Admin',
    lastName: 'User'
  });
  
  const regularUser = await User.create({
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Regular',
    lastName: 'User'
  });
  
  // Create tokens
  adminToken = generateToken(adminUser);
  userToken = generateToken(regularUser);
  
  // Set up RBAC data
  // ... (create roles, permissions, assign roles to users)
});

afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

describe('RBAC API', () => {
  describe('GET /api/rbac/roles', () => {
    test('should return roles for admin user', async () => {
      const response = await request(app)
        .get('/api/rbac/roles')
        .set('Authorization', `Bearer ${adminToken}`);
      
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
    });
    
    test('should return 403 for regular user', async () => {
      const response = await request(app)
        .get('/api/rbac/roles')
        .set('Authorization', `Bearer ${userToken}`);
      
      expect(response.status).toBe(403);
    });
  });
});
```

## Best Practices

1. **Define Clear Permission Naming Conventions**: Use consistent naming for permissions, e.g., `resource:action`.
2. **Use Role Inheritance**: Create a hierarchy of roles to simplify permission management.
3. **Principle of Least Privilege**: Assign the minimum permissions necessary for a user to perform their tasks.
4. **Cache Permission Checks**: Use caching to improve performance of permission checks.
5. **Audit RBAC Operations**: Log all RBAC-related operations for audit purposes.
6. **Regular Review**: Regularly review and audit role assignments and permissions.

## Troubleshooting

### Common Issues

1. **Permission Denied**: User does not have the required permission. Check user roles and permissions.
2. **Role Not Found**: Role does not exist or has been deleted. Check role ID.
3. **Permission Not Found**: Permission does not exist or has been deleted. Check permission format.
4. **Cache Inconsistency**: Cache may be out of sync with database. Try clearing the cache.

### Debugging

1. **Check User Roles**: Use the `/api/rbac/users/:userId/roles` endpoint to check user roles.
2. **Check User Permissions**: Use the `/api/rbac/users/:userId/permissions` endpoint to check user permissions.
3. **Check Role Permissions**: Use the `/api/rbac/roles/:id` endpoint to check role permissions.
4. **Check Audit Logs**: Check audit logs for RBAC-related operations.
5. **Enable Debug Logging**: Set `DEBUG=rbac:*` environment variable to enable debug logging for RBAC operations.
