# AI Alignment Demo

This is a React-based dashboard for monitoring AI alignment across multiple AI systems, including AGI and ASI implementations. The dashboard provides real-time monitoring of consciousness levels, alignment scores, and safety statuses for various AI models.

## Features

- Global AI alignment score monitoring
- Active AI systems tracking
- Consciousness field metrics (psi, phi, theta)
- Real-time terminal output
- AI system status monitoring
- Safety status visualization

## Prerequisites

- Node.js (v14 or later)
- npm or yarn
- React (v17 or later)

## Installation

1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

## Running the Demo

To start the development server:

```bash
npm run dev
# or
yarn dev
```

Then open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

- `/src` - Contains the main application code
  - `page.tsx` - Main React component for the AI Alignment dashboard

## Dependencies

- React
- Next.js
- Framer Motion (for animations)
- Lucide React (for icons)

## License

This project is part of the Novafuse API Superstore and is available under the project's license.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
