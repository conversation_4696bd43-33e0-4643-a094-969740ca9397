/**
 * NovaConnect Design System Variables
 * 
 * This file contains CSS variables for the NovaConnect design system.
 * These variables can be used to customize the appearance of NovaConnect components.
 */

:root {
  /* Primary Colors */
  --nova-primary: #0056b3;
  --nova-primary-light: #4d8ad6;
  --nova-primary-dark: #003b7a;
  
  /* Secondary Colors */
  --nova-secondary: #6c757d;
  --nova-secondary-light: #9da5ac;
  --nova-secondary-dark: #495057;
  
  /* Semantic Colors */
  --nova-success: #28a745;
  --nova-success-light: #48c664;
  --nova-success-dark: #1e7e34;
  
  --nova-warning: #ffc107;
  --nova-warning-light: #ffce3a;
  --nova-warning-dark: #d39e00;
  
  --nova-danger: #dc3545;
  --nova-danger-light: #e45c6a;
  --nova-danger-dark: #bd2130;
  
  --nova-info: #17a2b8;
  --nova-info-light: #3ab7cc;
  --nova-info-dark: #117a8b;
  
  /* Neutral Colors */
  --nova-white: #ffffff;
  --nova-gray-100: #f8f9fa;
  --nova-gray-200: #e9ecef;
  --nova-gray-300: #dee2e6;
  --nova-gray-400: #ced4da;
  --nova-gray-500: #adb5bd;
  --nova-gray-600: #6c757d;
  --nova-gray-700: #495057;
  --nova-gray-800: #343a40;
  --nova-gray-900: #212529;
  --nova-black: #000000;
  
  /* Typography */
  --nova-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --nova-font-family-monospace: 'Roboto Mono', SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  /* Font Sizes */
  --nova-font-size-display-1: 3rem;
  --nova-font-size-display-2: 2.5rem;
  --nova-font-size-h1: 2rem;
  --nova-font-size-h2: 1.75rem;
  --nova-font-size-h3: 1.5rem;
  --nova-font-size-h4: 1.25rem;
  --nova-font-size-h5: 1rem;
  --nova-font-size-h6: 0.875rem;
  --nova-font-size-body-1: 1rem;
  --nova-font-size-body-2: 0.875rem;
  --nova-font-size-caption: 0.75rem;
  --nova-font-size-button: 0.875rem;
  
  /* Line Heights */
  --nova-line-height-display: 1.2;
  --nova-line-height-heading: 1.25;
  --nova-line-height-body: 1.5;
  
  /* Font Weights */
  --nova-font-weight-light: 300;
  --nova-font-weight-regular: 400;
  --nova-font-weight-medium: 500;
  --nova-font-weight-semibold: 600;
  --nova-font-weight-bold: 700;
  
  /* Spacing */
  --nova-spacing-xs: 0.25rem;
  --nova-spacing-sm: 0.5rem;
  --nova-spacing-md: 1rem;
  --nova-spacing-lg: 1.5rem;
  --nova-spacing-xl: 2rem;
  --nova-spacing-xxl: 3rem;
  
  /* Border Radius */
  --nova-border-radius-none: 0;
  --nova-border-radius-sm: 0.125rem;
  --nova-border-radius-md: 0.25rem;
  --nova-border-radius-lg: 0.5rem;
  --nova-border-radius-xl: 1rem;
  --nova-border-radius-full: 9999px;
  
  /* Shadows */
  --nova-shadow-none: none;
  --nova-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --nova-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --nova-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --nova-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Z-Index */
  --nova-z-index-dropdown: 1000;
  --nova-z-index-sticky: 1020;
  --nova-z-index-fixed: 1030;
  --nova-z-index-modal-backdrop: 1040;
  --nova-z-index-modal: 1050;
  --nova-z-index-popover: 1060;
  --nova-z-index-tooltip: 1070;
  
  /* Transitions */
  --nova-transition-fast: 150ms;
  --nova-transition-normal: 300ms;
  --nova-transition-slow: 500ms;
  --nova-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Container Widths */
  --nova-container-sm: 540px;
  --nova-container-md: 720px;
  --nova-container-lg: 960px;
  --nova-container-xl: 1140px;
  --nova-container-xxl: 1320px;
  
  /* Grid */
  --nova-grid-columns: 12;
  --nova-grid-gutter-width: 1.5rem;
  
  /* Breakpoints */
  --nova-breakpoint-xs: 0;
  --nova-breakpoint-sm: 576px;
  --nova-breakpoint-md: 768px;
  --nova-breakpoint-lg: 992px;
  --nova-breakpoint-xl: 1200px;
  --nova-breakpoint-xxl: 1400px;
  
  /* Component Specific */
  --nova-border-width: 1px;
  --nova-border-color: var(--nova-gray-300);
  
  --nova-input-height: 2.5rem;
  --nova-input-padding-x: 0.75rem;
  --nova-input-padding-y: 0.375rem;
  --nova-input-border-width: var(--nova-border-width);
  --nova-input-border-color: var(--nova-gray-400);
  --nova-input-border-radius: var(--nova-border-radius-md);
  --nova-input-focus-border-color: var(--nova-primary-light);
  --nova-input-focus-box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  
  --nova-btn-padding-x: 1rem;
  --nova-btn-padding-y: 0.375rem;
  --nova-btn-border-width: var(--nova-border-width);
  --nova-btn-border-radius: var(--nova-border-radius-md);
  --nova-btn-font-weight: var(--nova-font-weight-medium);
  
  --nova-card-border-width: var(--nova-border-width);
  --nova-card-border-color: var(--nova-border-color);
  --nova-card-border-radius: var(--nova-border-radius-lg);
  --nova-card-box-shadow: var(--nova-shadow-md);
  --nova-card-padding: var(--nova-spacing-md);
  
  --nova-table-cell-padding-x: var(--nova-spacing-md);
  --nova-table-cell-padding-y: var(--nova-spacing-sm);
  --nova-table-border-width: var(--nova-border-width);
  --nova-table-border-color: var(--nova-border-color);
  
  /* Dark Mode Colors */
  --nova-dark-bg: #121212;
  --nova-dark-surface: #1e1e1e;
  --nova-dark-border: #333333;
  --nova-dark-text-primary: rgba(255, 255, 255, 0.87);
  --nova-dark-text-secondary: rgba(255, 255, 255, 0.6);
  --nova-dark-text-disabled: rgba(255, 255, 255, 0.38);
}

/* Dark Mode */
[data-theme="dark"] {
  /* Primary Colors */
  --nova-primary: #4d8ad6;
  --nova-primary-light: #7aa9e0;
  --nova-primary-dark: #0056b3;
  
  /* Secondary Colors */
  --nova-secondary: #9da5ac;
  --nova-secondary-light: #c1c7cc;
  --nova-secondary-dark: #6c757d;
  
  /* Neutral Colors */
  --nova-white: #121212;
  --nova-gray-100: #1e1e1e;
  --nova-gray-200: #2a2a2a;
  --nova-gray-300: #333333;
  --nova-gray-400: #444444;
  --nova-gray-500: #666666;
  --nova-gray-600: #888888;
  --nova-gray-700: #aaaaaa;
  --nova-gray-800: #cccccc;
  --nova-gray-900: #eeeeee;
  --nova-black: #ffffff;
  
  /* Component Specific */
  --nova-border-color: var(--nova-gray-700);
  --nova-input-border-color: var(--nova-gray-600);
  --nova-card-bg: var(--nova-gray-100);
  --nova-card-border-color: var(--nova-gray-300);
}
