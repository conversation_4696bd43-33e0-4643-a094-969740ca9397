/**
 * RBAC Routes
 *
 * This file contains routes for role-based access control operations.
 */

const express = require('express');
const router = express.Router();
const RBACController = require('../controllers/RBACController');
const { authenticate } = require('../middleware/authMiddleware');
const rbacMiddleware = require('../middleware/rbacMiddleware');

// All routes require authentication
router.use(authenticate);

// Role routes
router.get('/roles', rbacMiddleware.hasPermission('admin:system'), RBACController.getAllRoles);
router.get('/roles/with-permissions', rbacMiddleware.hasPermission('admin:system'), RBACController.getAllRolesWithPermissions);
router.get('/roles/:id', rbacMiddleware.hasPermission('admin:system'), RBACController.getRoleById);
router.get('/roles/:id/permissions', rbacMiddleware.hasPermission('admin:system'), RBACController.getRolePermissions);
router.get('/roles/:id/inheritance-tree', rbacMiddleware.hasPermission('admin:system'), RBACController.getRoleInheritanceTree);
router.post('/roles', rbacMiddleware.hasPermission('admin:system'), RBACController.createRole);
router.put('/roles/:id', rbacMiddleware.hasPermission('admin:system'), RBACController.updateRole);
router.delete('/roles/:id', rbacMiddleware.hasPermission('admin:system'), RBACController.deleteRole);

// Permission routes
router.get('/permissions', rbacMiddleware.hasPermission('admin:system'), RBACController.getAllPermissions);
router.get('/permissions/:id', rbacMiddleware.hasPermission('admin:system'), RBACController.getPermissionById);
router.post('/permissions', rbacMiddleware.hasPermission('admin:system'), RBACController.createPermission);
router.put('/permissions/:id', rbacMiddleware.hasPermission('admin:system'), RBACController.updatePermission);
router.delete('/permissions/:id', rbacMiddleware.hasPermission('admin:system'), RBACController.deletePermission);

// User role routes
router.get('/users/:userId/roles', rbacMiddleware.hasPermission(['admin:system', 'user:view']), RBACController.getUserRoles);
router.post('/users/:userId/roles', rbacMiddleware.hasPermission('admin:system'), RBACController.assignRoleToUser);
router.delete('/users/:userId/roles/:roleId', rbacMiddleware.hasPermission('admin:system'), RBACController.removeRoleFromUser);

// User permission routes
router.get('/users/:userId/permissions', rbacMiddleware.hasPermission(['admin:system', 'user:view']), RBACController.getUserPermissions);
router.get('/users/:userId/permissions/:permissionId', rbacMiddleware.hasPermission(['admin:system', 'user:view']), RBACController.checkUserPermission);

module.exports = router;
