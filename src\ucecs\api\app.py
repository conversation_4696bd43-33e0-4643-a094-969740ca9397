"""
REST API for the Universal Compliance Evidence Collection System.

This module provides a REST API for interacting with the UCECS system.
"""

import os
import json
import logging
import datetime
from typing import Dict, List, Any, Optional

from flask import Flask, request, jsonify, Response
from flask_cors import CORS
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt, create_access_token

from .auth import configure_jwt, authenticate_user, create_tokens, has_permission, has_role

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create the Flask application
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure JWT
configure_jwt(app)

# Global variables to store the managers
evidence_manager = None
query_manager = None
report_manager = None
notification_manager = None
schedule_manager = None

# API version
API_VERSION = "v1"

# Authentication routes
@app.route(f"/api/{API_VERSION}/auth/login", methods=["POST"])
def login() -> Response:
    """
    Authenticate a user and return access and refresh tokens.

    Returns:
        Access and refresh tokens
    """
    username = request.json.get("username", None)
    password = request.json.get("password", None)

    if not username or not password:
        return jsonify({"error": "Username and password are required"}), 400

    user = authenticate_user(username, password)

    if not user:
        return jsonify({"error": "Invalid username or password"}), 401

    access_token, refresh_token = create_tokens(username)

    return jsonify({
        "access_token": access_token,
        "refresh_token": refresh_token,
        "user": {
            "username": username,
            "roles": user.get("roles", []),
            "permissions": user.get("permissions", [])
        }
    })

@app.route(f"/api/{API_VERSION}/auth/refresh", methods=["POST"])
@jwt_required(refresh=True)
def refresh() -> Response:
    """
    Refresh the access token.

    Returns:
        A new access token
    """
    identity = get_jwt_identity()
    access_token = create_access_token(identity=identity)

    return jsonify({
        "access_token": access_token
    })

# API routes
@app.route(f"/api/{API_VERSION}/", methods=["GET"])
def api_info() -> Response:
    """
    Get API information.

    Returns:
        API information
    """
    return jsonify({
        "name": "Universal Compliance Evidence Collection System API",
        "version": API_VERSION,
        "description": "REST API for the UCECS system"
    })

# Evidence routes
@app.route(f"/api/{API_VERSION}/evidence", methods=["GET"])
@jwt_required()
def get_evidence() -> Response:
    """
    Get evidence based on query parameters.

    Returns:
        List of evidence matching the query
    """
    # Check if the user has permission to read evidence
    if not has_permission("read"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get query parameters
    page = int(request.args.get("page", 1))
    page_size = int(request.args.get("page_size", 10))
    query_json = request.args.get("query")

    # Parse the query if provided
    query = json.loads(query_json) if query_json else {}

    # Search for evidence
    results = evidence_manager.search_evidence(
        query=query,
        page=page,
        page_size=page_size
    )

    return jsonify(results)

@app.route(f"/api/{API_VERSION}/evidence/<evidence_id>", methods=["GET"])
@jwt_required()
def get_evidence_by_id(evidence_id: str) -> Response:
    """
    Get evidence by ID.

    Args:
        evidence_id: The ID of the evidence

    Returns:
        The evidence if found, 404 otherwise
    """
    # Check if the user has permission to read evidence
    if not has_permission("read"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get the evidence
    evidence = evidence_manager.get_evidence_by_id(evidence_id)

    if not evidence:
        return jsonify({"error": f"Evidence not found: {evidence_id}"}), 404

    return jsonify(evidence)

@app.route(f"/api/{API_VERSION}/evidence", methods=["POST"])
@jwt_required()
def create_evidence() -> Response:
    """
    Create new evidence.

    Returns:
        The created evidence
    """
    # Check if the user has permission to write evidence
    if not has_permission("write"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get the evidence from the request body
    evidence = request.json

    # Add the current user as the creator
    evidence["created_by"] = get_jwt_identity()

    # Register the evidence
    try:
        registered_evidence = evidence_manager.register_evidence(evidence)
        return jsonify(registered_evidence), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route(f"/api/{API_VERSION}/evidence/<evidence_id>", methods=["DELETE"])
@jwt_required()
def delete_evidence(evidence_id: str) -> Response:
    """
    Delete evidence.

    Args:
        evidence_id: The ID of the evidence

    Returns:
        Success message if deleted, 404 otherwise
    """
    # Check if the user has permission to delete evidence
    if not has_permission("delete"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Check if the evidence exists
    evidence = evidence_manager.get_evidence_by_id(evidence_id)

    if not evidence:
        return jsonify({"error": f"Evidence not found: {evidence_id}"}), 404

    # Get query parameters
    storage_id = request.args.get("storage_id", "file_system")
    permanent = request.args.get("permanent", "false").lower() == "true"

    # Delete the evidence
    try:
        evidence_manager.delete_evidence(
            evidence_id=evidence_id,
            storage_id=storage_id,
            permanent=permanent
        )
        return jsonify({"message": f"Evidence deleted: {evidence_id}"})
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route(f"/api/{API_VERSION}/evidence/<evidence_id>/validate", methods=["POST"])
@jwt_required()
def validate_evidence(evidence_id: str) -> Response:
    """
    Validate evidence.

    Args:
        evidence_id: The ID of the evidence

    Returns:
        The validated evidence
    """
    # Check if the user has permission to validate evidence
    if not has_permission("validate"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Check if the evidence exists
    evidence = evidence_manager.get_evidence_by_id(evidence_id)

    if not evidence:
        return jsonify({"error": f"Evidence not found: {evidence_id}"}), 404

    # Get the validator ID from the request body
    validator_id = request.json.get("validator_id")

    if not validator_id:
        return jsonify({"error": "Validator ID is required"}), 400

    # Validate the evidence
    try:
        validated_evidence = evidence_manager.validate_evidence(
            evidence=evidence,
            validator_id=validator_id
        )
        return jsonify(validated_evidence)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route(f"/api/{API_VERSION}/evidence/<evidence_id>/store", methods=["POST"])
@jwt_required()
def store_evidence(evidence_id: str) -> Response:
    """
    Store evidence.

    Args:
        evidence_id: The ID of the evidence

    Returns:
        The stored evidence
    """
    # Check if the user has permission to store evidence
    if not has_permission("store"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Check if the evidence exists
    evidence = evidence_manager.get_evidence_by_id(evidence_id)

    if not evidence:
        return jsonify({"error": f"Evidence not found: {evidence_id}"}), 404

    # Get the storage ID from the request body
    storage_id = request.json.get("storage_id")

    if not storage_id:
        return jsonify({"error": "Storage ID is required"}), 400

    # Get optional parameters
    encrypt = request.json.get("encrypt")
    create_version = request.json.get("create_version")
    version_comment = request.json.get("version_comment")

    # Store the evidence
    try:
        stored_evidence = evidence_manager.store_evidence(
            evidence=evidence,
            storage_id=storage_id,
            encrypt=encrypt,
            create_version=create_version,
            version_comment=version_comment
        )
        return jsonify(stored_evidence)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route(f"/api/{API_VERSION}/evidence/<evidence_id>/retrieve", methods=["GET"])
@jwt_required()
def retrieve_evidence(evidence_id: str) -> Response:
    """
    Retrieve evidence from storage.

    Args:
        evidence_id: The ID of the evidence

    Returns:
        The retrieved evidence
    """
    # Check if the user has permission to retrieve evidence
    if not has_permission("retrieve"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get query parameters
    storage_id = request.args.get("storage_id", "file_system")
    version_id = request.args.get("version_id")
    decrypt = request.args.get("decrypt", "true").lower() == "true"

    # Retrieve the evidence
    try:
        retrieved_evidence = evidence_manager.retrieve_evidence(
            evidence_id=evidence_id,
            storage_id=storage_id,
            version_id=version_id,
            decrypt=decrypt
        )
        return jsonify(retrieved_evidence)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Report routes
@app.route(f"/api/{API_VERSION}/reports/evidence", methods=["GET"])
@jwt_required()
def generate_evidence_report() -> Response:
    """
    Generate an evidence report.

    Returns:
        The report file path
    """
    # Check if the user has permission to generate reports
    if not has_permission("report"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get query parameters
    format = request.args.get("format", "json")
    query_json = request.args.get("query")

    # Parse the query if provided
    filters = json.loads(query_json) if query_json else None

    # Generate the report
    try:
        report_path = evidence_manager.generate_evidence_report(
            format=format,
            filters=filters
        )
        return jsonify({"report_path": report_path})
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route(f"/api/{API_VERSION}/reports/compliance", methods=["POST"])
@jwt_required()
def generate_compliance_report() -> Response:
    """
    Generate a compliance report.

    Returns:
        The report file path
    """
    # Check if the user has permission to generate reports
    if not has_permission("report"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get the requirements from the request body
    requirements = request.json.get("requirements")

    if not requirements:
        return jsonify({"error": "Requirements are required"}), 400

    # Get query parameters
    format = request.args.get("format", "json")

    # Generate the report
    try:
        report_path = evidence_manager.generate_compliance_report(
            requirements=requirements,
            format=format
        )
        return jsonify({"report_path": report_path})
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Search routes
@app.route(f"/api/{API_VERSION}/search/evidence", methods=["POST"])
@jwt_required()
def search_evidence() -> Response:
    """
    Search for evidence.

    Returns:
        Search results
    """
    # Check if the user has permission to search
    if not has_permission("search"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get the query from the request body
    query = request.json.get("query")

    if not query:
        return jsonify({"error": "Query is required"}), 400

    # Get pagination parameters
    page = request.json.get("page", 1)
    page_size = request.json.get("page_size", 10)

    # Search for evidence
    try:
        results = evidence_manager.search_evidence(
            query=query,
            page=page,
            page_size=page_size
        )
        return jsonify(results)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route(f"/api/{API_VERSION}/search/requirements", methods=["POST"])
@jwt_required()
def search_requirements() -> Response:
    """
    Search for requirements.

    Returns:
        Search results
    """
    # Check if the user has permission to search
    if not has_permission("search"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get the query and requirements from the request body
    query = request.json.get("query")
    requirements = request.json.get("requirements")

    if not query:
        return jsonify({"error": "Query is required"}), 400

    if not requirements:
        return jsonify({"error": "Requirements are required"}), 400

    # Get pagination parameters
    page = request.json.get("page", 1)
    page_size = request.json.get("page_size", 10)

    # Search for requirements
    try:
        results = evidence_manager.search_requirements(
            requirements=requirements,
            query=query,
            page=page,
            page_size=page_size
        )
        return jsonify(results)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Notification routes
@app.route(f"/api/{API_VERSION}/notifications", methods=["GET"])
@jwt_required()
def get_notifications() -> Response:
    """
    Get notifications.

    Returns:
        List of notifications
    """
    # Check if the user has permission to read
    if not has_permission("read"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get query parameters
    limit = int(request.args.get("limit", 100))
    notification_type = request.args.get("type")

    # Get notifications
    try:
        if notification_type:
            from src.ucecs.core.notification_manager import NotificationType
            notification_type_enum = NotificationType(notification_type)
            notifications = evidence_manager.notification_manager.get_notifications(
                limit=limit,
                notification_type=notification_type_enum
            )
        else:
            notifications = evidence_manager.notification_manager.get_notifications(
                limit=limit
            )
        return jsonify(notifications)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

# Schedule routes
@app.route(f"/api/{API_VERSION}/schedules", methods=["GET"])
@jwt_required()
def get_schedules() -> Response:
    """
    Get scheduled tasks.

    Returns:
        List of scheduled tasks
    """
    # Check if the user has permission to read
    if not has_permission("read"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get all scheduled tasks
    try:
        tasks = evidence_manager.schedule_manager.get_all_scheduled_tasks()
        return jsonify(tasks)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route(f"/api/{API_VERSION}/schedules", methods=["POST"])
@jwt_required()
def create_schedule() -> Response:
    """
    Create a scheduled task.

    Returns:
        The created task ID
    """
    # Check if the user has permission to write
    if not has_permission("write"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Get the task parameters from the request body
    name = request.json.get("name")
    task_type = request.json.get("type")
    interval = request.json.get("interval")
    parameters = request.json.get("parameters")

    if not name or not task_type or not interval or not parameters:
        return jsonify({"error": "Name, type, interval, and parameters are required"}), 400

    # Create the scheduled task
    try:
        from src.ucecs.core.schedule_manager import TaskType, ScheduleInterval
        task_id = evidence_manager.schedule_manager.create_scheduled_task(
            name=name,
            task_type=TaskType(task_type),
            interval=ScheduleInterval(interval),
            parameters=parameters,
            start_date=request.json.get("start_date"),
            end_date=request.json.get("end_date"),
            enabled=request.json.get("enabled", True)
        )
        return jsonify({"task_id": task_id}), 201
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route(f"/api/{API_VERSION}/schedules/<task_id>", methods=["DELETE"])
@jwt_required()
def delete_schedule(task_id: str) -> Response:
    """
    Delete a scheduled task.

    Args:
        task_id: The ID of the task

    Returns:
        Success message if deleted, 404 otherwise
    """
    # Check if the user has permission to delete
    if not has_permission("delete"):
        return jsonify({"error": "Insufficient permissions"}), 403

    # Delete the scheduled task
    try:
        evidence_manager.schedule_manager.delete_scheduled_task(task_id)
        return jsonify({"message": f"Task deleted: {task_id}"})
    except ValueError:
        return jsonify({"error": f"Task not found: {task_id}"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 400

def initialize_api(evidence_mgr):
    """
    Initialize the API with the necessary managers.

    Args:
        evidence_mgr: The Evidence Manager instance
    """
    global evidence_manager
    evidence_manager = evidence_mgr

def run_api(host: str = "0.0.0.0", port: int = 5000, debug: bool = False):
    """
    Run the API server.

    Args:
        host: The host to bind to
        port: The port to bind to
        debug: Whether to run in debug mode
    """
    app.run(host=host, port=port, debug=debug)

if __name__ == "__main__":
    # This is for development purposes only
    # In production, use a proper WSGI server like Gunicorn
    app.run(debug=True)
