/**
 * Control Service
 * 
 * This service provides functionality for control management.
 */

const { Control } = require('../models');
const logger = require('../utils/logger');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
const { Readable } = require('stream');
const ExcelJS = require('exceljs');

/**
 * Get all controls
 * @param {Object} filters - Filters
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @returns {Promise<Object>} - Controls with pagination
 */
async function getAllControls(filters = {}, page = 1, limit = 10) {
  try {
    // Build query
    const query = {};
    
    if (filters.framework) {
      query.framework = filters.framework;
    }
    
    if (filters.category) {
      query.category = filters.category;
    }
    
    if (filters.search) {
      query.$text = { $search: filters.search };
    }
    
    // Count total
    const total = await Control.countDocuments(query);
    
    // Get controls
    const controls = await Control.find(query)
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    return {
      controls,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Failed to get controls', error);
    throw error;
  }
}

/**
 * Get control by ID
 * @param {string} id - Control ID
 * @returns {Promise<Object>} - Control object
 */
async function getControlById(id) {
  try {
    const control = await Control.getById(id);
    
    if (!control) {
      throw new Error('Control not found');
    }
    
    return control;
  } catch (error) {
    logger.error(`Failed to get control ${id}`, error);
    throw error;
  }
}

/**
 * Create control
 * @param {Object} controlData - Control data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Created control
 */
async function createControl(controlData, userId) {
  try {
    // Create control
    const control = new Control({
      ...controlData,
      createdBy: userId,
      updatedBy: userId
    });
    
    // Save control
    await control.save();
    
    logger.info(`Control ${control._id} created by ${userId}`);
    
    return control;
  } catch (error) {
    logger.error('Failed to create control', error);
    throw error;
  }
}

/**
 * Update control
 * @param {string} id - Control ID
 * @param {Object} controlData - Control data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated control
 */
async function updateControl(id, controlData, userId) {
  try {
    // Get control
    const control = await Control.getById(id);
    
    if (!control) {
      throw new Error('Control not found');
    }
    
    // Update control
    Object.keys(controlData).forEach(key => {
      if (key !== '_id' && key !== 'createdBy' && key !== 'createdAt') {
        control[key] = controlData[key];
      }
    });
    
    control.updatedBy = userId;
    
    // Save control
    await control.save();
    
    logger.info(`Control ${id} updated by ${userId}`);
    
    return control;
  } catch (error) {
    logger.error(`Failed to update control ${id}`, error);
    throw error;
  }
}

/**
 * Delete control
 * @param {string} id - Control ID
 * @returns {Promise<Object>} - Deleted control
 */
async function deleteControl(id) {
  try {
    // Get control
    const control = await Control.getById(id);
    
    if (!control) {
      throw new Error('Control not found');
    }
    
    // Delete control
    await control.remove();
    
    logger.info(`Control ${id} deleted`);
    
    return control;
  } catch (error) {
    logger.error(`Failed to delete control ${id}`, error);
    throw error;
  }
}

/**
 * Import controls from CSV
 * @param {string} filePath - CSV file path
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Import result
 */
async function importControlsFromCsv(filePath, userId) {
  try {
    const results = [];
    const errors = [];
    
    // Read CSV file
    await new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', async (data) => {
          try {
            // Create control
            const control = new Control({
              name: data.name,
              description: data.description,
              framework: data.framework,
              category: data.category,
              requirements: data.requirements ? data.requirements.split(';') : [],
              testProcedures: data.testProcedures ? data.testProcedures.split(';') : [],
              references: data.references ? JSON.parse(data.references) : [],
              riskLevel: data.riskLevel || 'medium',
              implementationStatus: data.implementationStatus || 'not-implemented',
              owner: data.owner,
              tags: data.tags ? data.tags.split(';') : [],
              metadata: data.metadata ? JSON.parse(data.metadata) : {},
              createdBy: userId,
              updatedBy: userId
            });
            
            // Save control
            await control.save();
            
            results.push(control);
          } catch (error) {
            errors.push({
              row: data,
              error: error.message
            });
          }
        })
        .on('end', () => {
          resolve();
        })
        .on('error', (error) => {
          reject(error);
        });
    });
    
    logger.info(`Imported ${results.length} controls from CSV, ${errors.length} errors`);
    
    return {
      success: true,
      imported: results.length,
      errors: errors.length,
      details: errors
    };
  } catch (error) {
    logger.error('Failed to import controls from CSV', error);
    throw error;
  }
}

/**
 * Import controls from JSON
 * @param {string} filePath - JSON file path
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Import result
 */
async function importControlsFromJson(filePath, userId) {
  try {
    // Read JSON file
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    
    if (!Array.isArray(data)) {
      throw new Error('JSON file must contain an array of controls');
    }
    
    const results = [];
    const errors = [];
    
    // Process controls
    for (const controlData of data) {
      try {
        // Create control
        const control = new Control({
          ...controlData,
          createdBy: userId,
          updatedBy: userId
        });
        
        // Save control
        await control.save();
        
        results.push(control);
      } catch (error) {
        errors.push({
          control: controlData,
          error: error.message
        });
      }
    }
    
    logger.info(`Imported ${results.length} controls from JSON, ${errors.length} errors`);
    
    return {
      success: true,
      imported: results.length,
      errors: errors.length,
      details: errors
    };
  } catch (error) {
    logger.error('Failed to import controls from JSON', error);
    throw error;
  }
}

/**
 * Import controls from Excel
 * @param {string} filePath - Excel file path
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Import result
 */
async function importControlsFromExcel(filePath, userId) {
  try {
    const results = [];
    const errors = [];
    
    // Read Excel file
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    
    // Get first worksheet
    const worksheet = workbook.getWorksheet(1);
    
    // Get headers
    const headers = [];
    worksheet.getRow(1).eachCell((cell) => {
      headers.push(cell.value);
    });
    
    // Process rows
    worksheet.eachRow((row, rowNumber) => {
      // Skip header row
      if (rowNumber === 1) {
        return;
      }
      
      try {
        // Create control data
        const controlData = {};
        
        row.eachCell((cell, colNumber) => {
          const header = headers[colNumber - 1];
          controlData[header] = cell.value;
        });
        
        // Process special fields
        if (controlData.requirements && typeof controlData.requirements === 'string') {
          controlData.requirements = controlData.requirements.split(';');
        }
        
        if (controlData.testProcedures && typeof controlData.testProcedures === 'string') {
          controlData.testProcedures = controlData.testProcedures.split(';');
        }
        
        if (controlData.references && typeof controlData.references === 'string') {
          controlData.references = JSON.parse(controlData.references);
        }
        
        if (controlData.tags && typeof controlData.tags === 'string') {
          controlData.tags = controlData.tags.split(';');
        }
        
        if (controlData.metadata && typeof controlData.metadata === 'string') {
          controlData.metadata = JSON.parse(controlData.metadata);
        }
        
        // Create control
        const control = new Control({
          ...controlData,
          createdBy: userId,
          updatedBy: userId
        });
        
        // Save control
        await control.save();
        
        results.push(control);
      } catch (error) {
        errors.push({
          row: rowNumber,
          error: error.message
        });
      }
    });
    
    logger.info(`Imported ${results.length} controls from Excel, ${errors.length} errors`);
    
    return {
      success: true,
      imported: results.length,
      errors: errors.length,
      details: errors
    };
  } catch (error) {
    logger.error('Failed to import controls from Excel', error);
    throw error;
  }
}

/**
 * Import controls
 * @param {Object} file - File object
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Import result
 */
async function importControls(file, userId) {
  try {
    // Check file type
    const fileExt = path.extname(file.originalname).toLowerCase();
    
    switch (fileExt) {
      case '.csv':
        return importControlsFromCsv(file.path, userId);
      case '.json':
        return importControlsFromJson(file.path, userId);
      case '.xlsx':
      case '.xls':
        return importControlsFromExcel(file.path, userId);
      default:
        throw new Error('Unsupported file type. Supported types: CSV, JSON, XLSX, XLS');
    }
  } catch (error) {
    logger.error('Failed to import controls', error);
    throw error;
  } finally {
    // Delete file
    try {
      fs.unlinkSync(file.path);
    } catch (error) {
      logger.error('Failed to delete file', error);
    }
  }
}

/**
 * Export controls to CSV
 * @param {Object} filters - Filters
 * @returns {Promise<string>} - CSV content
 */
async function exportControlsToCsv(filters = {}) {
  try {
    // Build query
    const query = {};
    
    if (filters.framework) {
      query.framework = filters.framework;
    }
    
    if (filters.category) {
      query.category = filters.category;
    }
    
    // Get controls
    const controls = await Control.find(query).sort({ createdAt: -1 });
    
    // Create CSV content
    let csv = 'name,description,framework,category,requirements,testProcedures,references,riskLevel,implementationStatus,owner,tags,metadata\n';
    
    for (const control of controls) {
      csv += `"${control.name}",`;
      csv += `"${control.description}",`;
      csv += `"${control.framework}",`;
      csv += `"${control.category || ''}",`;
      csv += `"${control.requirements ? control.requirements.join(';') : ''}",`;
      csv += `"${control.testProcedures ? control.testProcedures.join(';') : ''}",`;
      csv += `"${control.references ? JSON.stringify(control.references).replace(/"/g, '""') : ''}",`;
      csv += `"${control.riskLevel}",`;
      csv += `"${control.implementationStatus}",`;
      csv += `"${control.owner || ''}",`;
      csv += `"${control.tags ? control.tags.join(';') : ''}",`;
      csv += `"${control.metadata ? JSON.stringify(control.metadata).replace(/"/g, '""') : ''}"\n`;
    }
    
    logger.info(`Exported ${controls.length} controls to CSV`);
    
    return csv;
  } catch (error) {
    logger.error('Failed to export controls to CSV', error);
    throw error;
  }
}

/**
 * Export controls to JSON
 * @param {Object} filters - Filters
 * @returns {Promise<string>} - JSON content
 */
async function exportControlsToJson(filters = {}) {
  try {
    // Build query
    const query = {};
    
    if (filters.framework) {
      query.framework = filters.framework;
    }
    
    if (filters.category) {
      query.category = filters.category;
    }
    
    // Get controls
    const controls = await Control.find(query).sort({ createdAt: -1 });
    
    // Create JSON content
    const json = JSON.stringify(controls, null, 2);
    
    logger.info(`Exported ${controls.length} controls to JSON`);
    
    return json;
  } catch (error) {
    logger.error('Failed to export controls to JSON', error);
    throw error;
  }
}

/**
 * Export controls to Excel
 * @param {Object} filters - Filters
 * @returns {Promise<Buffer>} - Excel buffer
 */
async function exportControlsToExcel(filters = {}) {
  try {
    // Build query
    const query = {};
    
    if (filters.framework) {
      query.framework = filters.framework;
    }
    
    if (filters.category) {
      query.category = filters.category;
    }
    
    // Get controls
    const controls = await Control.find(query).sort({ createdAt: -1 });
    
    // Create workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Controls');
    
    // Add headers
    worksheet.columns = [
      { header: 'name', key: 'name', width: 30 },
      { header: 'description', key: 'description', width: 50 },
      { header: 'framework', key: 'framework', width: 20 },
      { header: 'category', key: 'category', width: 20 },
      { header: 'requirements', key: 'requirements', width: 30 },
      { header: 'testProcedures', key: 'testProcedures', width: 30 },
      { header: 'references', key: 'references', width: 30 },
      { header: 'riskLevel', key: 'riskLevel', width: 15 },
      { header: 'implementationStatus', key: 'implementationStatus', width: 20 },
      { header: 'owner', key: 'owner', width: 20 },
      { header: 'tags', key: 'tags', width: 20 },
      { header: 'metadata', key: 'metadata', width: 30 }
    ];
    
    // Add rows
    for (const control of controls) {
      worksheet.addRow({
        name: control.name,
        description: control.description,
        framework: control.framework,
        category: control.category,
        requirements: control.requirements ? control.requirements.join(';') : '',
        testProcedures: control.testProcedures ? control.testProcedures.join(';') : '',
        references: control.references ? JSON.stringify(control.references) : '',
        riskLevel: control.riskLevel,
        implementationStatus: control.implementationStatus,
        owner: control.owner,
        tags: control.tags ? control.tags.join(';') : '',
        metadata: control.metadata ? JSON.stringify(control.metadata) : ''
      });
    }
    
    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    
    logger.info(`Exported ${controls.length} controls to Excel`);
    
    return buffer;
  } catch (error) {
    logger.error('Failed to export controls to Excel', error);
    throw error;
  }
}

/**
 * Export controls
 * @param {Object} filters - Filters
 * @param {string} format - Export format
 * @returns {Promise<Object>} - Export result
 */
async function exportControls(filters = {}, format = 'json') {
  try {
    switch (format) {
      case 'csv':
        return {
          content: await exportControlsToCsv(filters),
          contentType: 'text/csv',
          filename: `controls_${new Date().toISOString().split('T')[0]}.csv`
        };
      case 'json':
        return {
          content: await exportControlsToJson(filters),
          contentType: 'application/json',
          filename: `controls_${new Date().toISOString().split('T')[0]}.json`
        };
      case 'excel':
        return {
          content: await exportControlsToExcel(filters),
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          filename: `controls_${new Date().toISOString().split('T')[0]}.xlsx`
        };
      default:
        throw new Error('Unsupported format. Supported formats: csv, json, excel');
    }
  } catch (error) {
    logger.error('Failed to export controls', error);
    throw error;
  }
}

/**
 * Get control mapping
 * @param {string} sourceFramework - Source framework
 * @param {string} targetFramework - Target framework
 * @returns {Promise<Array>} - Control mapping
 */
async function getControlMapping(sourceFramework, targetFramework) {
  try {
    const mapping = await Control.getMapping(sourceFramework, targetFramework);
    
    logger.info(`Got control mapping from ${sourceFramework} to ${targetFramework}`);
    
    return mapping;
  } catch (error) {
    logger.error('Failed to get control mapping', error);
    throw error;
  }
}

module.exports = {
  getAllControls,
  getControlById,
  createControl,
  updateControl,
  deleteControl,
  importControls,
  exportControls,
  getControlMapping
};
