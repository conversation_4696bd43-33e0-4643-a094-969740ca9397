/**
 * Analytics Components
 *
 * This module exports analytics components for the Finite Universe
 * Principle defense system, enabling advanced analytics and insights.
 */

const { TrendAnalyzer, createTrendAnalyzer } = require('./trend-analyzer');
const { PatternDetector, createPatternDetector } = require('./pattern-detector');
const { AnomalyClassifier, createAnomalyClassifier } = require('./anomaly-classifier');

/**
 * Create all analytics components
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all analytics components
 */
function createAnalyticsComponents(options = {}) {
  // Create trend analyzer
  const trendAnalyzer = createTrendAnalyzer({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.trendAnalyzerOptions
  });
  
  // Create pattern detector
  const patternDetector = createPatternDetector({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.patternDetectorOptions
  });
  
  // Create anomaly classifier
  const anomalyClassifier = createAnomalyClassifier({
    enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
    ...options.anomalyClassifierOptions
  });
  
  return {
    trendAnalyzer,
    patternDetector,
    anomalyClassifier
  };
}

module.exports = {
  // Trend analyzer
  TrendAnalyzer,
  createTrendAnalyzer,
  
  // Pattern detector
  PatternDetector,
  createPatternDetector,
  
  // Anomaly classifier
  AnomalyClassifier,
  createAnomalyClassifier,
  
  // Factory function
  createAnalyticsComponents
};
