from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="comphyon-meter",
    version="0.1.0",
    author="ComphyonΨᶜ Framework Team",
    author_email="<EMAIL>",
    description="Quantifying Emergent Intelligence",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/Dartan1983/comphyon-meter",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=[
        "numpy>=1.20.0",
        "matplotlib>=3.4.0",
        "pandas>=1.3.0",
        "dash>=2.0.0",
        "plotly>=5.0.0",
    ],
)
