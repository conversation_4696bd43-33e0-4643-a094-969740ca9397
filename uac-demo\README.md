# NovaFuse Universal API Connector (UAC) Demo

This is a demonstration of the NovaFuse Universal API Connector (UAC), a powerful tool for connecting to various APIs and applying compliance rules in real-time.

## Features

- **Universal API Connector**: Connect to any API with a unified interface
- **Compliance Engine**: Apply compliance rules to API data in real-time
- **Authentication Manager**: Secure access to the UAC
- **Demo Scenarios**: HIPAA, GDPR, and PCI DSS compliance checks

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/novafuse/uac-demo.git
   cd uac-demo
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file (or use the provided one):
   ```
   PORT=3030
   NODE_ENV=development
   JWT_SECRET=your-secret-key
   JWT_EXPIRES_IN=1h
   LOG_LEVEL=info
   ```

4. Start the server:
   ```
   npm start
   ```

5. Open your browser and navigate to:
   ```
   http://localhost:3030
   ```

## API Documentation

The API documentation is available at:
```
http://localhost:3030/api-docs
```

## Demo Users

The following demo users are available:

- **Admin**: username: `admin`, password: `admin123`
- **Partner**: username: `partner`, password: `partner123`
- **User**: username: `user`, password: `user123`
- **Guest**: username: `guest`, password: `guest123`

## Demo Scenarios

### HIPAA Compliance

Test if patient data complies with HIPAA regulations.

### GDPR Compliance

Test if user data complies with GDPR regulations.

### PCI DSS Compliance

Test if payment data complies with PCI DSS regulations.

### Multi-API Demo

Demonstrate calling multiple APIs and applying compliance checks to the results.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- NovaFuse Team
- Open Source Community
