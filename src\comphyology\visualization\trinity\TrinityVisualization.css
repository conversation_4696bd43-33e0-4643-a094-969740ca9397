.trinity-visualization-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.trinity-visualization {
  width: 100%;
  height: 100%;
}

.trinity-visualization-overlay {
  position: absolute;
  top: 0;
  left: 0;
  padding: 20px;
  color: white;
  pointer-events: none;
  z-index: 10;
}

.trinity-visualization-overlay h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.trinity-legend {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.legend-color:hover {
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.legend-color.active {
  border-color: white;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
}

.legend-label {
  font-size: 14px;
}

.particle-tooltip {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  padding: 10px;
  max-width: 250px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 100;
}

.tooltip-header {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.tooltip-item {
  display: flex;
  justify-content: space-between;
}

.tooltip-label {
  font-size: 12px;
  opacity: 0.7;
}

.tooltip-value {
  font-size: 12px;
  font-weight: 500;
}

/* Animation for particles */
@keyframes pulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.3;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .trinity-visualization-overlay h2 {
    font-size: 18px;
  }

  .legend-label {
    font-size: 12px;
  }
}
