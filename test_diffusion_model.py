#!/usr/bin/env python3
"""
Test script for UUFT Diffusion Model
"""

import os
import logging
import numpy as np
import matplotlib.pyplot as plt
from uuft_social_analyzer import UUFTSocialNetwork
from uuft_diffusion_model import UUFTDiffusionModel

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_diffusion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('Test_Diffusion')

# Constants
RESULTS_DIR = "uuft_results/social"
os.makedirs(RESULTS_DIR, exist_ok=True)

def test_diffusion_model():
    """Test the diffusion model."""
    logger.info("Testing diffusion model")
    
    # Create a small-world network
    network = UUFTSocialNetwork(
        num_nodes=50,
        network_type="small_world",
        uuft_bias=0.5
    )
    
    # Create diffusion model
    diffusion_model = UUFTDiffusionModel(
        social_network=network,
        model_type="uuft_optimized",
        uuft_parameters={
            "pi_influence": 0.5,
            "pattern_1882_strength": 0.7,
            "temporal_stability": 0.8
        }
    )
    
    # Initialize adopters
    diffusion_model.initialize_adopters(
        method="1882_optimized",
        initial_fraction=0.1
    )
    
    # Run simulation
    logger.info("Running diffusion simulation")
    diffusion_model.run_simulation(max_steps=20)
    
    # Visualize diffusion
    diffusion_model.visualize_diffusion(
        time_points=[0, 5, 10, 20],
        save_path=os.path.join(RESULTS_DIR, "test_diffusion.png")
    )
    
    # Visualize adoption curve
    diffusion_model.visualize_adoption_curve(
        save_path=os.path.join(RESULTS_DIR, "test_adoption_curve.png")
    )
    
    logger.info("Diffusion model test completed successfully")

if __name__ == "__main__":
    test_diffusion_model()
