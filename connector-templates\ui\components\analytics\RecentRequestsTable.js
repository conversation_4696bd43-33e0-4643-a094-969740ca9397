/**
 * Recent Requests Table Component
 * 
 * This component displays a table of recent API requests or errors.
 */

import React from 'react';
import { 
  Box,
  Chip,
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper,
  Tooltip,
  Typography
} from '@mui/material';

const RecentRequestsTable = ({ requests, showErrorMessage = false }) => {
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Get status color
  const getStatusColor = (status) => {
    if (status >= 200 && status < 300) return 'success';
    if (status >= 300 && status < 400) return 'info';
    if (status >= 400 && status < 500) return 'warning';
    if (status >= 500) return 'error';
    return 'default';
  };
  
  return (
    <TableContainer component={Paper} variant="outlined">
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Timestamp</TableCell>
            <TableCell>Method</TableCell>
            <TableCell>Endpoint</TableCell>
            <TableCell>Status</TableCell>
            {!showErrorMessage && <TableCell align="right">Response Time</TableCell>}
            {showErrorMessage && <TableCell>Error Message</TableCell>}
          </TableRow>
        </TableHead>
        <TableBody>
          {requests.map((request, index) => (
            <TableRow key={index}>
              <TableCell>
                {formatDate(request.timestamp)}
              </TableCell>
              <TableCell>
                <Chip 
                  label={request.method} 
                  size="small" 
                  color={
                    request.method === 'GET' ? 'primary' :
                    request.method === 'POST' ? 'success' :
                    request.method === 'PUT' ? 'info' :
                    request.method === 'DELETE' ? 'error' :
                    'default'
                  }
                  variant="outlined"
                />
              </TableCell>
              <TableCell>
                <Box sx={{ 
                  maxWidth: 300, 
                  overflow: 'hidden', 
                  textOverflow: 'ellipsis', 
                  whiteSpace: 'nowrap' 
                }}>
                  {request.endpoint}
                </Box>
              </TableCell>
              <TableCell>
                <Chip 
                  label={request.responseStatus} 
                  size="small" 
                  color={getStatusColor(request.responseStatus)}
                />
              </TableCell>
              {!showErrorMessage && (
                <TableCell align="right">
                  {request.responseTime ? `${Math.round(request.responseTime)}ms` : '-'}
                </TableCell>
              )}
              {showErrorMessage && (
                <TableCell>
                  <Tooltip title={request.errorMessage || ''}>
                    <Typography variant="body2" noWrap sx={{ maxWidth: 300 }}>
                      {request.errorMessage || '-'}
                    </Typography>
                  </Tooltip>
                </TableCell>
              )}
            </TableRow>
          ))}
          {requests.length === 0 && (
            <TableRow>
              <TableCell colSpan={showErrorMessage ? 5 : 5} align="center">
                No data available
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default RecentRequestsTable;
