/**
 * CHAEONIX AUTOMATED TRADING TEST
 * Test script to verify CHAEONIX can execute automated trades
 */

const fetch = require('node-fetch');

async function testCHAEONIXTrading() {
  console.log('🚀 TESTING CHAEONIX AUTOMATED TRADING SYSTEM');
  console.log('='.repeat(60));

  try {
    // Step 1: Reset all metrics to zero
    console.log('\n🔄 Step 1: Resetting all metrics to zero...');
    const resetResponse = await fetch('http://localhost:3141/api/analytics/profit-tracker', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'RESET_METRICS' })
    });
    
    if (resetResponse.ok) {
      console.log('✅ All metrics reset to $0');
    } else {
      console.log('⚠️ Metrics reset failed, continuing...');
    }

    // Step 2: Check MT5 connection
    console.log('\n🔌 Step 2: Checking MT5 connection...');
    const mt5Response = await fetch('http://localhost:3141/api/mt5/status');
    const mt5Data = await mt5Response.json();
    
    console.log(`📡 MT5 Status: ${mt5Data.connection.status}`);
    console.log(`💰 Account Balance: $${mt5Data.account.balance.toLocaleString()}`);
    console.log(`🎯 Mode: ${mt5Data.connection.mode}`);

    // Step 3: Start CHAEONIX Live Trading Bot
    console.log('\n🤖 Step 3: Starting CHAEONIX Live Trading Bot...');
    const startResponse = await fetch('http://localhost:3141/api/trading/live-bot', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'START' })
    });

    const startResult = await startResponse.json();
    
    if (startResponse.ok) {
      console.log('✅ CHAEONIX Live Trading Bot STARTED');
      console.log(`🎯 Bot ID: ${startResult.bot_id}`);
      console.log(`📊 Markets: ${startResult.markets.join(', ')}`);
      console.log(`🔒 Risk Limits: ${startResult.risk_limits.per_trade * 100}%/trade, ${startResult.risk_limits.daily * 100}%/day`);
    } else {
      throw new Error(`Failed to start bot: ${startResult.error}`);
    }

    // Step 4: Monitor trading activity for 2 minutes
    console.log('\n📊 Step 4: Monitoring trading activity for 2 minutes...');
    console.log('⏰ Watching for automated trades...');
    
    let monitorCount = 0;
    const maxMonitorCycles = 12; // 2 minutes (10 seconds each)
    
    const monitorInterval = setInterval(async () => {
      try {
        // Check bot status
        const statusResponse = await fetch('http://localhost:3141/api/trading/live-bot');
        const statusData = await statusResponse.json();
        
        // Check MT5 for new trades
        const mt5StatusResponse = await fetch('http://localhost:3141/api/mt5/status');
        const mt5StatusData = await mt5StatusResponse.json();
        
        console.log(`\n📈 Monitor ${monitorCount + 1}/${maxMonitorCycles}:`);
        console.log(`   🤖 Bot Active: ${statusData.bot_status.active}`);
        console.log(`   📊 Total Trades: ${statusData.bot_status.total_trades || 0}`);
        console.log(`   💰 Daily P&L: $${(statusData.bot_status.daily_pnl || 0).toFixed(2)}`);
        console.log(`   🔄 Open Positions: ${statusData.bot_status.positions || 0}`);
        console.log(`   💎 MT5 Equity: $${mt5StatusData.account.equity.toFixed(2)}`);
        console.log(`   📈 MT5 Profit: $${mt5StatusData.account.profit.toFixed(2)}`);
        
        monitorCount++;
        
        if (monitorCount >= maxMonitorCycles) {
          clearInterval(monitorInterval);
          
          // Step 5: Final results
          console.log('\n🎯 Step 5: Final Results');
          console.log('='.repeat(40));
          
          if (statusData.bot_status.total_trades > 0) {
            console.log('✅ SUCCESS: CHAEONIX executed automated trades!');
            console.log(`📊 Total Trades: ${statusData.bot_status.total_trades}`);
            console.log(`💰 Total P&L: $${(statusData.bot_status.daily_pnl || 0).toFixed(2)}`);
            console.log(`🎯 Win Rate: ${((statusData.bot_status.total_trades - (statusData.bot_status.daily_pnl < 0 ? 1 : 0)) / statusData.bot_status.total_trades * 100).toFixed(1)}%`);
          } else {
            console.log('⚠️ NO TRADES: CHAEONIX did not execute any trades');
            console.log('   This could mean:');
            console.log('   - Engine confidence levels too low');
            console.log('   - Risk management preventing trades');
            console.log('   - MT5 connection issues');
          }
          
          // Step 6: Stop the bot
          console.log('\n🛑 Step 6: Stopping CHAEONIX Live Trading Bot...');
          const stopResponse = await fetch('http://localhost:3141/api/trading/live-bot', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'STOP' })
          });
          
          if (stopResponse.ok) {
            console.log('✅ CHAEONIX Live Trading Bot STOPPED');
          }
          
          console.log('\n🎉 CHAEONIX AUTOMATED TRADING TEST COMPLETE');
          console.log('='.repeat(60));
        }
        
      } catch (error) {
        console.error(`❌ Monitor error: ${error.message}`);
      }
    }, 10000); // Check every 10 seconds

  } catch (error) {
    console.error(`❌ Test failed: ${error.message}`);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure CHAEONIX dashboard is running on localhost:3141');
    console.log('2. Check that all APIs are responding');
    console.log('3. Verify MT5 connection is working');
  }
}

// Run the test
testCHAEONIXTrading();
