# NovaConnect UAC Feature Flags

This document provides information on the feature flag system used in NovaConnect Universal API Connector (UAC).

## Overview

NovaConnect UAC uses a feature flag system to enable or disable specific features based on the user's subscription tier. This allows for a tiered pricing model where different features are available in different tiers.

## Feature Flags

The following feature flags are available in NovaConnect UAC:

### Core Features

| Feature Flag | Description | Default |
| --- | --- | --- |
| `FEATURE_CONNECTOR_TEMPLATES` | Enable connector templates | `true` |
| `FEATURE_DATA_NORMALIZATION` | Enable data normalization | `true` |
| `FEATURE_WORKFLOWS` | Enable workflows | `true` |

### Advanced Features

| Feature Flag | Description | Default |
| --- | --- | --- |
| `FEATURE_MONITORING` | Enable monitoring | `true` |
| `FEATURE_ALERTING` | Enable alerting | `false` |
| `FEATURE_TRACING` | Enable tracing | `false` |
| `FEATURE_GOOGLE_CLOUD` | Enable Google Cloud integration | `false` |
| `FEATURE_CACHE` | Enable caching | `true` |
| `FEATURE_DB_OPTIMIZATION` | Enable database optimization | `false` |

### Tier-Specific Features

| Feature Flag | Description | Default |
| --- | --- | --- |
| `FEATURE_CORE_TIER` | Enable Core tier features | `true` |
| `FEATURE_SECURE_TIER` | Enable Secure tier features | `false` |
| `FEATURE_ENTERPRISE_TIER` | Enable Enterprise tier features | `false` |
| `FEATURE_AI_BOOST_TIER` | Enable AI Boost tier features | `false` |

## Subscription Tiers

NovaConnect UAC offers the following subscription tiers:

### Core Tier

The Core tier includes the following features:

- Connector Templates
- Data Normalization
- Workflows
- Monitoring
- Caching

### Secure Tier

The Secure tier includes all Core tier features, plus:

- Alerting

### Enterprise Tier

The Enterprise tier includes all Secure tier features, plus:

- Tracing
- Google Cloud Integration
- Database Optimization

### AI Boost Tier

The AI Boost tier includes all Enterprise tier features, plus:

- AI-powered features (coming soon)

## Using Feature Flags

### In Code

To use feature flags in your code, you can use the `requireFeature` middleware:

```javascript
const { requireFeature } = require('../middleware/featureFlagMiddleware');

// Require a specific feature
router.get('/api/connectors', requireFeature('CONNECTOR_TEMPLATES'), (req, res) => {
  // This route will only be accessible if the CONNECTOR_TEMPLATES feature is enabled
  // and the user has access to this feature
});
```

You can also check feature limits:

```javascript
const { checkFeatureLimit } = require('../middleware/featureFlagMiddleware');

// Check feature limit
router.post('/api/connectors', checkFeatureLimit('CONNECTOR_TEMPLATES', 10), (req, res) => {
  // This route will only be accessible if the user has not reached the limit
  // for the CONNECTOR_TEMPLATES feature
});
```

### In Configuration

You can configure feature flags using environment variables:

```bash
# Enable a feature
FEATURE_ALERTING=true

# Disable a feature
FEATURE_TRACING=false
```

## Feature Flag API

NovaConnect UAC provides an API for managing feature flags:

### Get User Subscription

```
GET /subscription
```

Returns the current user's subscription information, including the subscription tier and available features.

Example response:

```json
{
  "userId": "user123",
  "subscription": "ENTERPRISE",
  "tier": {
    "name": "Enterprise",
    "features": [
      "CONNECTOR_TEMPLATES",
      "DATA_NORMALIZATION",
      "WORKFLOWS",
      "MONITORING",
      "ALERTING",
      "TRACING",
      "GOOGLE_CLOUD",
      "CACHE",
      "DB_OPTIMIZATION",
      "CORE_TIER",
      "SECURE_TIER",
      "ENTERPRISE_TIER"
    ]
  },
  "features": [
    "CONNECTOR_TEMPLATES",
    "DATA_NORMALIZATION",
    "WORKFLOWS",
    "MONITORING",
    "ALERTING",
    "TRACING",
    "GOOGLE_CLOUD",
    "CACHE",
    "DB_OPTIMIZATION",
    "CORE_TIER",
    "SECURE_TIER",
    "ENTERPRISE_TIER"
  ]
}
```

### Get Available Features

```
GET /features
```

Returns a list of all available features and subscription tiers.

Example response:

```json
{
  "features": [
    "CONNECTOR_TEMPLATES",
    "DATA_NORMALIZATION",
    "WORKFLOWS",
    "MONITORING",
    "ALERTING",
    "CACHE",
    "CORE_TIER",
    "SECURE_TIER"
  ],
  "tiers": {
    "CORE": {
      "name": "Core",
      "features": [
        "CONNECTOR_TEMPLATES",
        "DATA_NORMALIZATION",
        "WORKFLOWS",
        "MONITORING",
        "CACHE",
        "CORE_TIER"
      ]
    },
    "SECURE": {
      "name": "Secure",
      "features": [
        "CONNECTOR_TEMPLATES",
        "DATA_NORMALIZATION",
        "WORKFLOWS",
        "MONITORING",
        "ALERTING",
        "CACHE",
        "CORE_TIER",
        "SECURE_TIER"
      ]
    },
    "ENTERPRISE": {
      "name": "Enterprise",
      "features": [
        "CONNECTOR_TEMPLATES",
        "DATA_NORMALIZATION",
        "WORKFLOWS",
        "MONITORING",
        "ALERTING",
        "TRACING",
        "GOOGLE_CLOUD",
        "CACHE",
        "DB_OPTIMIZATION",
        "CORE_TIER",
        "SECURE_TIER",
        "ENTERPRISE_TIER"
      ]
    },
    "AI_BOOST": {
      "name": "AI Boost",
      "features": [
        "CONNECTOR_TEMPLATES",
        "DATA_NORMALIZATION",
        "WORKFLOWS",
        "MONITORING",
        "ALERTING",
        "TRACING",
        "GOOGLE_CLOUD",
        "CACHE",
        "DB_OPTIMIZATION",
        "CORE_TIER",
        "SECURE_TIER",
        "ENTERPRISE_TIER",
        "AI_BOOST_TIER"
      ]
    }
  }
}
```

### Get Feature Usage

```
GET /features/usage
```

Returns the current user's feature usage.

Example response:

```json
{
  "userId": "user123",
  "usage": {
    "CONNECTOR_TEMPLATES": 5,
    "DATA_NORMALIZATION": 10,
    "WORKFLOWS": 2
  }
}
```

### Update User Subscription

```
PUT /subscription/{userId}
```

Updates a user's subscription.

Request body:

```json
{
  "subscription": "ENTERPRISE"
}
```

Example response:

```json
{
  "userId": "user123",
  "subscription": "ENTERPRISE",
  "tier": {
    "name": "Enterprise",
    "features": [
      "CONNECTOR_TEMPLATES",
      "DATA_NORMALIZATION",
      "WORKFLOWS",
      "MONITORING",
      "ALERTING",
      "TRACING",
      "GOOGLE_CLOUD",
      "CACHE",
      "DB_OPTIMIZATION",
      "CORE_TIER",
      "SECURE_TIER",
      "ENTERPRISE_TIER"
    ]
  },
  "features": [
    "CONNECTOR_TEMPLATES",
    "DATA_NORMALIZATION",
    "WORKFLOWS",
    "MONITORING",
    "ALERTING",
    "TRACING",
    "GOOGLE_CLOUD",
    "CACHE",
    "DB_OPTIMIZATION",
    "CORE_TIER",
    "SECURE_TIER",
    "ENTERPRISE_TIER"
  ]
}
```

## Error Handling

When a user tries to access a feature they don't have access to, they will receive a 403 Forbidden response:

```json
{
  "error": {
    "type": "feature_access_denied",
    "message": "You do not have access to feature ALERTING",
    "status": 403,
    "upgrade": {
      "tier": "SECURE",
      "name": "Secure",
      "features": [
        "CONNECTOR_TEMPLATES",
        "DATA_NORMALIZATION",
        "WORKFLOWS",
        "MONITORING",
        "ALERTING",
        "CACHE",
        "CORE_TIER",
        "SECURE_TIER"
      ]
    }
  }
}
```

When a user reaches a feature limit, they will receive a 429 Too Many Requests response:

```json
{
  "error": {
    "type": "feature_limit_reached",
    "message": "You have reached the limit for feature CONNECTOR_TEMPLATES",
    "status": 429,
    "usage": 10,
    "limit": 10,
    "upgrade": {
      "tier": "SECURE",
      "name": "Secure",
      "features": [
        "CONNECTOR_TEMPLATES",
        "DATA_NORMALIZATION",
        "WORKFLOWS",
        "MONITORING",
        "ALERTING",
        "CACHE",
        "CORE_TIER",
        "SECURE_TIER"
      ]
    }
  }
}
```

## Best Practices

### Use Feature Flags for New Features

When developing new features, use feature flags to control access to these features. This allows you to:

1. Gradually roll out new features to users
2. Test features with a subset of users before making them available to everyone
3. Offer different feature sets to different subscription tiers

### Use Feature Limits for Resource-Intensive Features

For features that consume significant resources, use feature limits to prevent abuse. This allows you to:

1. Limit the number of resources a user can create
2. Prevent users from consuming too many resources
3. Encourage users to upgrade to higher subscription tiers

### Document Feature Flags

Document all feature flags in this document to ensure that developers understand which features are available in which subscription tiers.

### Test Feature Flags

Test feature flags to ensure that they work correctly. This includes:

1. Testing that features are correctly enabled or disabled based on the feature flag
2. Testing that users can only access features they have access to
3. Testing that users cannot exceed feature limits
