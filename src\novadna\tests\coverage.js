/**
 * Test Coverage Script for NovaDNA
 * 
 * This script runs tests with coverage reporting.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Run tests with coverage
 */
function runTestsWithCoverage() {
  console.log('=== NovaDNA Test Coverage ===');
  console.log('Running tests with coverage reporting...\n');
  
  try {
    // Check if nyc is installed
    try {
      execSync('npx nyc --version', { stdio: 'ignore' });
    } catch (error) {
      console.log('Installing nyc...');
      execSync('npm install --save-dev nyc', { stdio: 'inherit' });
    }
    
    // Run tests with coverage
    console.log('Running tests with coverage...');
    
    try {
      // Run integration tests
      execSync('npx nyc --reporter=text --reporter=html node run.js', { 
        stdio: 'inherit',
        cwd: path.resolve(__dirname)
      });
      
      // Run end-to-end tests
      execSync('npx nyc --reporter=text --reporter=html node e2e/runE2ETests.js', { 
        stdio: 'inherit',
        cwd: path.resolve(__dirname)
      });
      
      console.log('\nCoverage report generated successfully!');
      console.log('HTML report is available in the coverage directory.');
      
      // Check if coverage meets the target
      const coverageSummary = fs.readFileSync(path.resolve(__dirname, 'coverage', 'coverage-summary.json'), 'utf8');
      const summary = JSON.parse(coverageSummary);
      
      const totalCoverage = summary.total.lines.pct;
      
      console.log(`\nTotal line coverage: ${totalCoverage.toFixed(2)}%`);
      
      if (totalCoverage >= 81) {
        console.log('✅ Coverage meets the target of 81%');
      } else {
        console.log(`❌ Coverage does not meet the target of 81%. Current coverage: ${totalCoverage.toFixed(2)}%`);
      }
    } catch (error) {
      console.error('Error running tests with coverage:', error.message);
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Run the tests with coverage
runTestsWithCoverage();
