#!/usr/bin/env python3
"""
Optimized CSDE Benchmark

This module implements benchmarks for the optimized CSDE implementation,
incorporating Orion's recommendations for improved performance.
"""

import time
import numpy as np
import tensorflow as tf
import os
import sys
import json
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import optimized CSDE implementation
from core_optimized import OptimizedCSDECore, Task

class CSDBenchmark:
    """
    Benchmark suite for CSDE performance testing
    """

    def __init__(self, use_gpu=None, output_dir="results"):
        """
        Initialize benchmark suite

        Args:
            use_gpu: Boolean to force GPU usage, or None to auto-detect
            output_dir: Directory to save results
        """
        self.csde = OptimizedCSDECore(use_gpu=use_gpu)
        self.output_dir = output_dir

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

    def run_latency_test(self, iterations=100, domain="cybersecurity"):
        """
        Measures the latency of CSDE processing
        Target: ≤0.07ms per event

        Args:
            iterations: Number of iterations to run
            domain: Domain for π10³ scaling

        Returns:
            Dictionary of latency test results
        """
        print(f"Running latency test with {iterations} iterations...")

        # Create synthetic data for testing
        compliance_data = np.random.normal(0, 1, (10, 5))
        infrastructure_data = np.random.normal(0, 1, (10, 5))
        threat_intelligence = np.random.normal(0, 1, (10, 5))

        # Warm-up run
        _ = self.csde.process(compliance_data, infrastructure_data, threat_intelligence, domain)

        # Measure latency
        latencies = []
        for _ in range(iterations):
            start_time = time.time()
            _ = self.csde.process(compliance_data, infrastructure_data, threat_intelligence, domain)
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000  # Convert to milliseconds
            latencies.append(latency_ms)

        # Calculate statistics
        avg_latency = sum(latencies) / len(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)

        # Prepare results
        results = {
            "average_latency_ms": avg_latency,
            "min_latency_ms": min_latency,
            "max_latency_ms": max_latency,
            "target_latency_ms": 0.07,
            "meets_target": avg_latency <= 0.07,
            "iterations": iterations,
            "domain": domain,
            "timestamp": datetime.now().isoformat(),
            "gpu_used": self.csde.use_gpu
        }

        print(f"Latency test complete. Average latency: {avg_latency:.6f} ms")

        # Save results
        self._save_results("latency", results)

        return results

    def run_throughput_test(self, batch_size=1000, num_batches=69, domain="cybersecurity"):
        """
        Measures the throughput of CSDE processing
        Target: 69,000 events/sec

        Args:
            batch_size: Size of each batch
            num_batches: Number of batches to process
            domain: Domain for π10³ scaling

        Returns:
            Dictionary of throughput test results
        """
        print(f"Running throughput test with batch size {batch_size} and {num_batches} batches...")

        # Create synthetic data for throughput testing
        compliance_data = np.random.normal(0, 1, (batch_size, 5))
        infrastructure_data = np.random.normal(0, 1, (batch_size, 5))
        threat_intelligence = np.random.normal(0, 1, (batch_size, 5))

        # Warm-up run
        _ = self.csde.process(compliance_data[0:1], infrastructure_data[0:1], threat_intelligence[0:1], domain)

        # Measure throughput
        start_time = time.time()

        for i in range(num_batches):
            _ = self.csde.process(compliance_data, infrastructure_data, threat_intelligence, domain)

        end_time = time.time()
        total_time = end_time - start_time
        total_events = batch_size * num_batches
        events_per_second = total_events / total_time

        # Prepare results
        results = {
            "events_per_second": events_per_second,
            "total_events_processed": total_events,
            "total_time_seconds": total_time,
            "target_throughput": 69000,
            "meets_target": events_per_second >= 69000,
            "batch_size": batch_size,
            "num_batches": num_batches,
            "domain": domain,
            "timestamp": datetime.now().isoformat(),
            "gpu_used": self.csde.use_gpu
        }

        print(f"Throughput test complete. Events per second: {events_per_second:.2f}")

        # Save results
        self._save_results("throughput", results)

        return results

    def run_resource_allocation_test(self, num_tasks=100):
        """
        Tests the 18/82 principle in resource allocation

        Args:
            num_tasks: Number of tasks to simulate

        Returns:
            Dictionary of resource allocation test results
        """
        print(f"Running resource allocation test with {num_tasks} tasks...")

        # Create tasks (18% critical, 82% standard)
        critical_count = int(num_tasks * 0.18)
        tasks = []

        for i in range(num_tasks):
            is_critical = i < critical_count
            tasks.append(Task(i, is_critical))

        # Total resources (arbitrary units)
        total_resources = num_tasks * 10

        # Test 1: Equal distribution (baseline)
        equal_tasks = tasks.copy()
        for task in equal_tasks:
            task.resources = total_resources / num_tasks
            task.calculate_completion()

        # Calculate equal distribution metrics
        equal_critical_completion = sum(t.completion for t in equal_tasks if t.is_critical) / critical_count
        equal_standard_completion = sum(t.completion for t in equal_tasks if not t.is_critical) / (num_tasks - critical_count)
        equal_overall_completion = sum(t.completion for t in equal_tasks) / num_tasks

        # Test 2: Optimized 18/82 distribution
        optimized_tasks = self.csde.allocate_resources(tasks.copy(), total_resources)
        for task in optimized_tasks:
            task.calculate_completion()

        # Calculate optimized distribution metrics
        optimized_critical_completion = sum(t.completion for t in optimized_tasks if t.is_critical) / critical_count
        optimized_standard_completion = sum(t.completion for t in optimized_tasks if not t.is_critical) / (num_tasks - critical_count)
        optimized_overall_completion = sum(t.completion for t in optimized_tasks) / num_tasks

        # Calculate improvement
        improvement_percentage = ((optimized_overall_completion / equal_overall_completion) - 1) * 100

        # Prepare results
        results = {
            "equal_distribution": {
                "critical_task_completion": equal_critical_completion,
                "standard_task_completion": equal_standard_completion,
                "overall_system_completion": equal_overall_completion
            },
            "optimized_distribution": {
                "critical_task_completion": optimized_critical_completion,
                "standard_task_completion": optimized_standard_completion,
                "overall_system_completion": optimized_overall_completion
            },
            "improvement_percentage": improvement_percentage,
            "meets_target": improvement_percentage > 0,
            "num_tasks": num_tasks,
            "critical_tasks": critical_count,
            "standard_tasks": num_tasks - critical_count,
            "timestamp": datetime.now().isoformat()
        }

        print(f"Resource allocation test complete. Improvement: {improvement_percentage:.2f}%")

        # Save results
        self._save_results("resource_allocation", results)

        return results

    def run_pattern_preservation_test(self, domain="cybersecurity"):
        """
        Tests pattern preservation across domains with Carl's skip connections

        Args:
            domain: Domain for π10³ scaling

        Returns:
            Dictionary of pattern preservation test results
        """
        print(f"Running pattern preservation test for {domain} domain...")

        # Create synthetic patterns
        pattern_A = np.random.normal(0, 1, (10, 5))
        pattern_B = np.random.normal(0, 1, (10, 5))

        # Create context data
        context = np.random.normal(0, 1, (10, 5))

        # Test 1: Process through CSDE without optimizations
        result_standard = self.csde.process(pattern_A, pattern_B, context, domain, optimize_speed=False)

        # Test 2: Process through CSDE with optimizations
        result_optimized = self.csde.process(pattern_A, pattern_B, context, domain, optimize_speed=True)

        # Measure pattern preservation for standard processing
        similarity_A_standard = self.csde.calculate_similarity(pattern_A, result_standard)
        similarity_B_standard = self.csde.calculate_similarity(pattern_B, result_standard)
        avg_standard = (similarity_A_standard + similarity_B_standard) / 2

        # Measure pattern preservation for optimized processing
        similarity_A_optimized = self.csde.calculate_similarity(pattern_A, result_optimized)
        similarity_B_optimized = self.csde.calculate_similarity(pattern_B, result_optimized)
        avg_optimized = (similarity_A_optimized + similarity_B_optimized) / 2

        # Calculate improvement from optimizations
        improvement = (avg_optimized / avg_standard - 1) * 100

        # Prepare results
        results = {
            "standard_processing": {
                "pattern_A_preservation": similarity_A_standard,
                "pattern_B_preservation": similarity_B_standard,
                "average_preservation": avg_standard
            },
            "optimized_processing": {
                "pattern_A_preservation": similarity_A_optimized,
                "pattern_B_preservation": similarity_B_optimized,
                "average_preservation": avg_optimized
            },
            "improvement_percentage": improvement,
            "target_preservation": 0.85,
            "meets_target": avg_optimized >= 0.85,
            "domain": domain,
            "timestamp": datetime.now().isoformat(),
            "gpu_used": self.csde.use_gpu
        }

        print(f"Pattern preservation test complete.")
        print(f"Standard processing: {avg_standard:.4f}")
        print(f"Optimized processing: {avg_optimized:.4f}")
        print(f"Improvement: {improvement:.2f}%")

        # Save results
        self._save_results("pattern_preservation", results)

        return results

    def run_speed_optimization_test(self, domain="cybersecurity"):
        """
        Tests speed optimizations based on Carl's suggestions

        Args:
            domain: Domain for π10³ scaling

        Returns:
            Dictionary of speed optimization test results
        """
        print(f"Running speed optimization test for {domain} domain...")

        # Create larger synthetic data for meaningful performance testing
        data_size = 100
        pattern_A = np.random.normal(0, 1, (data_size, data_size))
        pattern_B = np.random.normal(0, 1, (data_size, data_size))
        context = np.random.normal(0, 1, (data_size, data_size))

        # Warm-up run
        _ = self.csde.process(pattern_A[:10, :10], pattern_B[:10, :10], context[:10, :10], domain)

        # Test 1: Process without optimizations
        start_time = time.time()
        _ = self.csde.process(pattern_A, pattern_B, context, domain, optimize_speed=False)
        standard_time = time.time() - start_time

        # Test 2: Process with optimizations
        start_time = time.time()
        _ = self.csde.process(pattern_A, pattern_B, context, domain, optimize_speed=True)
        optimized_time = time.time() - start_time

        # Calculate speedup
        speedup = standard_time / optimized_time

        # Prepare results
        results = {
            "standard_processing_time": standard_time,
            "optimized_processing_time": optimized_time,
            "speedup_factor": speedup,
            "target_speedup": 2.0,  # Conservative target for this test
            "meets_target": speedup >= 2.0,
            "data_size": data_size,
            "domain": domain,
            "timestamp": datetime.now().isoformat(),
            "gpu_used": self.csde.use_gpu
        }

        print(f"Speed optimization test complete.")
        print(f"Standard processing time: {standard_time:.6f} seconds")
        print(f"Optimized processing time: {optimized_time:.6f} seconds")
        print(f"Speedup factor: {speedup:.2f}x")

        # Save results
        self._save_results("speed_optimization", results)

        return results

    def run_all_tests(self, domain="cybersecurity"):
        """
        Run all benchmark tests including Carl's optimizations

        Args:
            domain: Domain for π10³ scaling

        Returns:
            Dictionary of all test results
        """
        print("\n=== Running All CSDE Benchmark Tests with Carl's Optimizations ===\n")

        results = {
            "latency": self.run_latency_test(domain=domain),
            "throughput": self.run_throughput_test(domain=domain),
            "resource_allocation": self.run_resource_allocation_test(),
            "pattern_preservation": self.run_pattern_preservation_test(domain=domain),
            "speed_optimization": self.run_speed_optimization_test(domain=domain)
        }

        # Generate summary
        self._generate_summary(results)

        # Print overall conclusion
        tests_passed = sum(1 for result in results.values() if result.get("meets_target", False))
        total_tests = len(results)
        success_rate = (tests_passed / total_tests) * 100

        print("\n=== Overall Test Results ===")
        print(f"Tests Passed: {tests_passed}/{total_tests} ({success_rate:.1f}%)")

        if success_rate >= 80:
            print("\nCONCLUSION: CSDE PRINCIPLES VALIDATED WITH CARL'S OPTIMIZATIONS")
            print("The implementation successfully demonstrates the core claims with real-world performance.")
        else:
            print("\nCONCLUSION: FURTHER REFINEMENT NEEDED")
            print("The implementation shows promise but requires additional optimization to fully validate claims.")

        return results

    def _save_results(self, test_name, results):
        """
        Save test results to JSON file

        Args:
            test_name: Name of the test
            results: Dictionary of test results
        """
        output_file = os.path.join(self.output_dir, f"{test_name}_results.json")
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"Results saved to {output_file}")

    def _generate_summary(self, results):
        """
        Generate summary of all test results

        Args:
            results: Dictionary of all test results
        """
        # Count passed tests
        tests_passed = sum(1 for test, result in results.items() if result.get("meets_target", False))
        total_tests = len(results)

        # Create summary
        summary = {
            "timestamp": datetime.now().isoformat(),
            "tests_passed": tests_passed,
            "total_tests": total_tests,
            "success_rate": (tests_passed / total_tests) * 100,
            "test_results": {
                test: {
                    "passed": result.get("meets_target", False),
                    "details": result
                } for test, result in results.items()
            }
        }

        # Save summary
        output_file = os.path.join(self.output_dir, "summary_results.json")
        with open(output_file, 'w') as f:
            json.dump(summary, f, indent=2)

        # Create text summary
        text_summary = f"""
CSDE Benchmark Summary
=====================
Timestamp: {summary['timestamp']}

Overall Results:
- Tests Passed: {tests_passed}/{total_tests}
- Success Rate: {summary['success_rate']:.1f}%

Individual Test Results:
"""

        for test, result in results.items():
            status = "PASS" if result.get("meets_target", False) else "FAIL"
            text_summary += f"- {test}: {status}\n"

        if summary['success_rate'] >= 80:
            conclusion = "CONCLUSION: CSDE principles VALIDATED"
        else:
            conclusion = "CONCLUSION: Further refinement needed"

        text_summary += f"\n{conclusion}\n"

        # Save text summary
        text_file = os.path.join(self.output_dir, "summary_results.txt")
        with open(text_file, 'w') as f:
            f.write(text_summary)

        print(text_summary)

# Main function for standalone execution
def main():
    """
    Main function for running benchmarks
    """
    import argparse

    parser = argparse.ArgumentParser(description='Run CSDE benchmarks')
    parser.add_argument('--use-gpu', action='store_true', help='Force GPU usage')
    parser.add_argument('--output-dir', default='results', help='Output directory')
    parser.add_argument('--domain', default='cybersecurity', help='Domain for testing')
    parser.add_argument('--test', choices=['latency', 'throughput', 'resource', 'pattern', 'all'],
                        default='all', help='Test to run')

    args = parser.parse_args()

    # Create benchmark instance
    benchmark = CSDBenchmark(use_gpu=args.use_gpu, output_dir=args.output_dir)

    # Run selected test
    if args.test == 'latency':
        benchmark.run_latency_test(domain=args.domain)
    elif args.test == 'throughput':
        benchmark.run_throughput_test(domain=args.domain)
    elif args.test == 'resource':
        benchmark.run_resource_allocation_test()
    elif args.test == 'pattern':
        benchmark.run_pattern_preservation_test(domain=args.domain)
    else:
        benchmark.run_all_tests(domain=args.domain)

if __name__ == "__main__":
    main()
