import React, { useEffect, useState, useRef } from 'react';
import TrinityVisualization from './TrinityVisualization';
import TrinityDashboard from './TrinityDashboard';
import NovaVisionAdapter from './NovaVisionAdapter';
import './NovaVisionTrinityVisualization.css';

/**
 * NovaVision Trinity Visualization Component
 * 
 * This component integrates the Trinity Visualization with NovaVision,
 * providing real-time data flow and NovaFuse ecosystem integration.
 */
const NovaVisionTrinityVisualization = ({ 
  novaVision, 
  universalRippleStack,
  width = '100%',
  height = '600px',
  showDashboard = true,
  enableLogging = false,
  updateInterval = 1000,
  autoRotate = true,
  initialWheel = 'all'
}) => {
  const [adapter, setAdapter] = useState(null);
  const [visualizationData, setVisualizationData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);
  const [selectedParticle, setSelectedParticle] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  const containerRef = useRef(null);
  
  // Initialize NovaVision adapter
  useEffect(() => {
    if (!novaVision) {
      setError('NovaVision instance is required');
      return;
    }
    
    try {
      // Create adapter
      const novaVisionAdapter = new NovaVisionAdapter({
        novaVision,
        universalRippleStack,
        enableLogging,
        updateInterval,
        componentId: 'trinity-visualization',
        dataSourceId: 'universal-ripple-stack'
      });
      
      // Set adapter
      setAdapter(novaVisionAdapter);
      
      if (enableLogging) {
        console.log('NovaVision adapter created');
      }
    } catch (error) {
      setError(`Failed to create NovaVision adapter: ${error.message}`);
      
      if (enableLogging) {
        console.error('Failed to create NovaVision adapter:', error);
      }
    }
    
    // Cleanup
    return () => {
      // Adapter cleanup will be handled in the next effect
    };
  }, [novaVision, universalRippleStack, enableLogging, updateInterval]);
  
  // Connect to NovaVision
  useEffect(() => {
    if (!adapter) {
      return;
    }
    
    // Set up event listeners
    const handleUpdate = (event) => {
      setVisualizationData(event.data);
      
      if (enableLogging) {
        console.log('Visualization data updated:', event.data);
      }
    };
    
    const handleError = (event) => {
      setError(`NovaVision adapter error: ${event.error.message}`);
      
      if (enableLogging) {
        console.error('NovaVision adapter error:', event.error);
      }
    };
    
    const handleConnected = () => {
      setIsConnected(true);
      setError(null);
      
      if (enableLogging) {
        console.log('Connected to NovaVision');
      }
    };
    
    const handleDisconnected = () => {
      setIsConnected(false);
      
      if (enableLogging) {
        console.log('Disconnected from NovaVision');
      }
    };
    
    // Add event listeners
    adapter.on('update', handleUpdate);
    adapter.on('error', handleError);
    adapter.on('connected', handleConnected);
    adapter.on('disconnected', handleDisconnected);
    
    // Connect to NovaVision
    adapter.connect().catch((error) => {
      setError(`Failed to connect to NovaVision: ${error.message}`);
      
      if (enableLogging) {
        console.error('Failed to connect to NovaVision:', error);
      }
    });
    
    // Cleanup
    return () => {
      // Remove event listeners
      adapter.off('update', handleUpdate);
      adapter.off('error', handleError);
      adapter.off('connected', handleConnected);
      adapter.off('disconnected', handleDisconnected);
      
      // Disconnect from NovaVision
      adapter.disconnect().catch((error) => {
        if (enableLogging) {
          console.error('Failed to disconnect from NovaVision:', error);
        }
      });
    };
  }, [adapter, enableLogging]);
  
  // Handle particle selection
  const handleParticleSelect = (particle) => {
    setSelectedParticle(particle);
    
    if (enableLogging) {
      console.log('Particle selected:', particle);
    }
    
    // Publish event to NovaVision
    if (adapter && isConnected) {
      adapter.novaVision.publishComponentEvent('trinity-visualization', {
        type: 'particleSelected',
        data: particle,
        timestamp: new Date()
      }).catch((error) => {
        if (enableLogging) {
          console.error('Failed to publish particle selection event:', error);
        }
      });
    }
  };
  
  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!containerRef.current) {
      return;
    }
    
    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      } else if (containerRef.current.mozRequestFullScreen) {
        containerRef.current.mozRequestFullScreen();
      } else if (containerRef.current.webkitRequestFullscreen) {
        containerRef.current.webkitRequestFullscreen();
      } else if (containerRef.current.msRequestFullscreen) {
        containerRef.current.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  };
  
  // Handle fullscreen change
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(
        document.fullscreenElement ||
        document.mozFullScreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);
  
  // Render loading state
  if (!adapter) {
    return (
      <div className="nova-vision-trinity-visualization-loading">
        <div className="loading-spinner"></div>
        <p>Initializing NovaVision Trinity Visualization...</p>
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <div className="nova-vision-trinity-visualization-error">
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => adapter.connect()}>Retry Connection</button>
      </div>
    );
  }
  
  // Render visualization
  return (
    <div 
      ref={containerRef}
      className={`nova-vision-trinity-visualization ${isFullscreen ? 'fullscreen' : ''}`}
      style={{ width, height }}
    >
      {showDashboard ? (
        <TrinityDashboard 
          universalRippleStack={universalRippleStack}
          visualizationData={visualizationData}
          selectedParticle={selectedParticle}
        />
      ) : (
        <div className="visualization-container">
          <TrinityVisualization 
            data={visualizationData}
            width="100%"
            height="100%"
            onParticleSelect={handleParticleSelect}
            autoRotate={autoRotate}
            initialWheel={initialWheel}
          />
          
          {selectedParticle && (
            <div className="particle-details">
              <h3>Selected Particle</h3>
              <div className="particle-details-content">
                <div className="detail-item">
                  <span className="detail-label">Type:</span>
                  <span className="detail-value">{selectedParticle.type}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Aspect:</span>
                  <span className="detail-value">{selectedParticle.aspect}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Wheel:</span>
                  <span className="detail-value">{selectedParticle.wheel}</span>
                </div>
                <div className="detail-item">
                  <span className="detail-label">Value:</span>
                  <span className="detail-value">{selectedParticle.value.toFixed(2)}</span>
                </div>
              </div>
              <button onClick={() => setSelectedParticle(null)}>Close</button>
            </div>
          )}
        </div>
      )}
      
      <div className="visualization-controls">
        <button 
          className="fullscreen-button"
          onClick={toggleFullscreen}
          title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
        >
          {isFullscreen ? '⊠' : '⊞'}
        </button>
      </div>
      
      <div className="nova-vision-badge">
        <span>Powered by NovaVision</span>
      </div>
    </div>
  );
};

export default NovaVisionTrinityVisualization;
