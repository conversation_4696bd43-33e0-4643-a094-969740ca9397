FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    netcat-traditional \
    iputils-ping \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --no-cache-dir \
    aiohttp==3.8.6 \
    requests==2.31.0 \
    asyncio \
    json \
    datetime

# Create app directory
WORKDIR /app

# Copy simulation script
COPY kethernet-simulation-suite.py /app/

# Create results directory
RUN mkdir -p /app/results

# Set environment variables
ENV PYTHONPATH=/app
ENV SIMULATION_MODE=full

# Default command
CMD ["python", "/app/kethernet-simulation-suite.py"]
