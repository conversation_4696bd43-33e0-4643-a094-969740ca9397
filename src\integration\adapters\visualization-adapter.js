/**
 * Visualization Adapter
 * 
 * This module provides an adapter for the visualization system to integrate
 * with the unified integration system.
 */

const EventEmitter = require('events');

/**
 * VisualizationAdapter class
 */
class VisualizationAdapter extends EventEmitter {
  /**
   * Create a new VisualizationAdapter instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      autoConnect: true,
      ...options
    };
    
    // Store visualization instance
    this.visualization = options.visualization || null;
    
    // Initialize state
    this.state = {
      isConnected: false,
      lastUpdate: null,
      visualizationTypes: new Set(),
      activeVisualizations: new Map()
    };
    
    // Connect to visualization if provided and autoConnect is true
    if (this.visualization && this.options.autoConnect) {
      this.connect();
    }
    
    if (this.options.enableLogging) {
      console.log('VisualizationAdapter initialized');
    }
  }
  
  /**
   * Connect to the visualization system
   * @returns {boolean} - Success status
   */
  connect() {
    if (!this.visualization) {
      if (this.options.enableLogging) {
        console.log('VisualizationAdapter: No visualization instance provided');
      }
      return false;
    }
    
    if (this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationAdapter: Already connected');
      }
      return true;
    }
    
    // Set up event listeners
    this._setupEventListeners();
    
    // Discover available visualization types
    this._discoverVisualizationTypes();
    
    // Update state
    this.state.isConnected = true;
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('connected', {
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('VisualizationAdapter: Connected to visualization system');
    }
    
    return true;
  }
  
  /**
   * Disconnect from the visualization system
   * @returns {boolean} - Success status
   */
  disconnect() {
    if (!this.state.isConnected) {
      if (this.options.enableLogging) {
        console.log('VisualizationAdapter: Not connected');
      }
      return false;
    }
    
    // Remove event listeners
    this._removeEventListeners();
    
    // Update state
    this.state.isConnected = false;
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('disconnected', {
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log('VisualizationAdapter: Disconnected from visualization system');
    }
    
    return true;
  }
  
  /**
   * Set up event listeners
   * @private
   */
  _setupEventListeners() {
    if (!this.visualization) {
      return;
    }
    
    // Set up event listeners based on the visualization system
    if (this.visualization.on) {
      // If the visualization system has an event emitter interface
      this.visualization.on('visualization-created', this._handleVisualizationCreated.bind(this));
      this.visualization.on('visualization-updated', this._handleVisualizationUpdated.bind(this));
      this.visualization.on('visualization-deleted', this._handleVisualizationDeleted.bind(this));
    }
  }
  
  /**
   * Remove event listeners
   * @private
   */
  _removeEventListeners() {
    if (!this.visualization || !this.visualization.removeAllListeners) {
      return;
    }
    
    // Remove visualization event listeners
    this.visualization.removeAllListeners('visualization-created');
    this.visualization.removeAllListeners('visualization-updated');
    this.visualization.removeAllListeners('visualization-deleted');
  }
  
  /**
   * Discover available visualization types
   * @private
   */
  _discoverVisualizationTypes() {
    if (!this.visualization) {
      return;
    }
    
    // Check for common visualization type methods
    const potentialTypes = [
      'morphological_resonance_field',
      'quantum_phase_space_map',
      'ethical_tensor_projection',
      'trinity_integration_diagram',
      '3d_tensor_visualization',
      'resonance_spectrogram',
      'phase_space_visualization',
      'harmonic_pattern_explorer'
    ];
    
    // Check if the visualization system has methods for these types
    for (const type of potentialTypes) {
      const methodName = `generate${type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('')}`;
      
      if (typeof this.visualization[methodName] === 'function') {
        this.state.visualizationTypes.add(type);
      }
    }
    
    // Check for a getVisualizationTypes method
    if (typeof this.visualization.getVisualizationTypes === 'function') {
      const types = this.visualization.getVisualizationTypes();
      
      if (Array.isArray(types)) {
        for (const type of types) {
          this.state.visualizationTypes.add(type);
        }
      }
    }
    
    if (this.options.enableLogging) {
      console.log(`VisualizationAdapter: Discovered ${this.state.visualizationTypes.size} visualization types`);
    }
  }
  
  /**
   * Handle visualization created event
   * @param {Object} data - Event data
   * @private
   */
  _handleVisualizationCreated(data) {
    // Store active visualization
    this.state.activeVisualizations.set(data.id, {
      type: data.type,
      createdAt: Date.now()
    });
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('visualization-created', {
      id: data.id,
      type: data.type,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`VisualizationAdapter: Visualization created - ${data.id} (${data.type})`);
    }
  }
  
  /**
   * Handle visualization updated event
   * @param {Object} data - Event data
   * @private
   */
  _handleVisualizationUpdated(data) {
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('visualization-updated', {
      id: data.id,
      type: data.type,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`VisualizationAdapter: Visualization updated - ${data.id}`);
    }
  }
  
  /**
   * Handle visualization deleted event
   * @param {Object} data - Event data
   * @private
   */
  _handleVisualizationDeleted(data) {
    // Remove active visualization
    this.state.activeVisualizations.delete(data.id);
    
    // Update state
    this.state.lastUpdate = Date.now();
    
    // Emit event
    this.emit('visualization-deleted', {
      id: data.id,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`VisualizationAdapter: Visualization deleted - ${data.id}`);
    }
  }
  
  /**
   * Get available visualization types
   * @returns {Array} - Array of visualization types
   */
  getVisualizationTypes() {
    return Array.from(this.state.visualizationTypes);
  }
  
  /**
   * Create a visualization
   * @param {string} type - Visualization type
   * @param {Object} data - Visualization data
   * @param {Object} options - Visualization options
   * @returns {Object} - Created visualization
   */
  createVisualization(type, data, options = {}) {
    if (!this.visualization) {
      throw new Error('No visualization instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to visualization system');
    }
    
    // Check if the visualization type is available
    if (!this.state.visualizationTypes.has(type)) {
      throw new Error(`Visualization type not available: ${type}`);
    }
    
    // Create visualization
    const methodName = `generate${type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('')}`;
    
    if (typeof this.visualization[methodName] === 'function') {
      return this.visualization[methodName](data, options);
    }
    
    // Try a generic create method
    if (typeof this.visualization.createVisualization === 'function') {
      return this.visualization.createVisualization(type, data, options);
    }
    
    throw new Error(`Cannot create visualization of type: ${type}`);
  }
  
  /**
   * Update a visualization
   * @param {string} id - Visualization ID
   * @param {Object} data - Updated visualization data
   * @returns {Object} - Updated visualization
   */
  updateVisualization(id, data) {
    if (!this.visualization) {
      throw new Error('No visualization instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to visualization system');
    }
    
    // Check if the visualization exists
    if (!this.state.activeVisualizations.has(id)) {
      throw new Error(`Visualization not found: ${id}`);
    }
    
    // Update visualization
    if (typeof this.visualization.updateVisualization === 'function') {
      return this.visualization.updateVisualization(id, data);
    }
    
    throw new Error(`Cannot update visualization: ${id}`);
  }
  
  /**
   * Delete a visualization
   * @param {string} id - Visualization ID
   * @returns {boolean} - Success status
   */
  deleteVisualization(id) {
    if (!this.visualization) {
      throw new Error('No visualization instance provided');
    }
    
    if (!this.state.isConnected) {
      throw new Error('Not connected to visualization system');
    }
    
    // Check if the visualization exists
    if (!this.state.activeVisualizations.has(id)) {
      throw new Error(`Visualization not found: ${id}`);
    }
    
    // Delete visualization
    if (typeof this.visualization.deleteVisualization === 'function') {
      return this.visualization.deleteVisualization(id);
    }
    
    throw new Error(`Cannot delete visualization: ${id}`);
  }
}

module.exports = VisualizationAdapter;
