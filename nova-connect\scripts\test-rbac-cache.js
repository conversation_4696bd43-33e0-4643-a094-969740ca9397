/**
 * RBAC Cache Test Script
 *
 * This script tests the RBAC caching functionality.
 */

const mongoose = require('mongoose');
const Role = require('../api/models/Role');
const Permission = require('../api/models/Permission');
const UserRole = require('../api/models/UserRole');
const User = require('../api/models/User');
const logger = require('../config/logger');

// Override the CacheService module
const mockCacheService = require('../tests/mocks/MockCacheService');
const originalRequire = require;

// Create a custom require function that returns our mock for CacheService
require = function(id) {
  if (id === '../services/CacheService' || id === '../../api/services/CacheService') {
    return mockCacheService;
  }
  return originalRequire(id);
};

// Now import RBACService which will use the mocked CacheService
const RBACService = require('../api/services/RBACService');

// Restore original require
require = originalRequire;

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/novaconnect-test';

// Connect to MongoDB
async function setupDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing data to avoid duplicate key errors
    await Permission.deleteMany({});
    await Role.deleteMany({});
    await UserRole.deleteMany({});
    await User.deleteMany({ email: '<EMAIL>' });

    console.log('Cleared existing test data');

    return true;
  } catch (err) {
    console.error('Failed to connect to MongoDB', err);
    return false;
  }
}

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = process.hrtime.bigint();
  const result = await fn();
  const end = process.hrtime.bigint();
  const duration = Number(end - start) / 1000000; // Convert to milliseconds
  return { result, duration };
};

// Start tests
setupDatabase()
  .then(success => {
    if (success) {
      runTests();
    } else {
      process.exit(1);
    }
  });

// Run tests
async function runTests() {
  try {
    console.log('Starting RBAC cache tests...');

    // Create RBAC service
    const rbacService = new RBACService();

    // Initialize database with default roles and permissions
    await rbacService.initializeDatabase();
    console.log('Initialized database with default roles and permissions');

    // Test 1: Create a test user
    let testUser = await User.findOne({ email: '<EMAIL>' });

    if (!testUser) {
      testUser = new User({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        status: 'active'
      });

      await testUser.save();
    }

    console.log(`Test 1: Created test user with ID ${testUser._id}`);

    // Test 2: Create a custom role
    const customRoleName = 'Test Role ' + Date.now();
    let customRole;

    try {
      // Find permission IDs
      const viewPermission = await Permission.findOne({ resource: 'connector', action: 'view' });
      const usePermission = await Permission.findOne({ resource: 'connector', action: 'use' });

      if (!viewPermission || !usePermission) {
        console.error('Test 2: Could not find required permissions');
        console.log('Available permissions:');
        const allPerms = await Permission.find({});
        allPerms.forEach(p => console.log(`- ${p.resource}:${p.action} (${p._id})`));
      } else {
        customRole = await rbacService.createRole({
          name: customRoleName,
          description: 'Test role description',
          permissions: [viewPermission._id, usePermission._id]
        });

        console.log(`Test 2: Created custom role with ID ${customRole._id}`);
      }
    } catch (error) {
      console.error('Test 2 failed:', error.message);
    }

    // Test 3: Assign role to user
    if (customRole) {
      try {
        const result = await rbacService.assignRoleToUser(testUser._id, customRole._id);
        console.log(`Test 3: Assigned role to user - ${result.message}`);
      } catch (error) {
        console.error('Test 3 failed:', error.message);
      }
    }

    // Test 4: Test caching for hasPermission
    console.log('\nTest 4: Testing caching for hasPermission...');

    try {
      // First call (no cache)
      const { duration: firstDuration, result: firstResult } = await measureExecutionTime(() =>
        rbacService.hasPermission(testUser._id, 'connector:view')
      );

      // Second call (with cache)
      const { duration: secondDuration, result: secondResult } = await measureExecutionTime(() =>
        rbacService.hasPermission(testUser._id, 'connector:view')
      );

      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms, Result: ${firstResult}`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms, Result: ${secondResult}`);
      console.log(`Performance improvement: ${((firstDuration - secondDuration) / firstDuration * 100).toFixed(2)}%`);
    } catch (error) {
      console.error('Test 4 failed:', error.message);
    }

    // Test 5: Test caching for getUserPermissions
    console.log('\nTest 5: Testing caching for getUserPermissions...');

    try {
      // First call (no cache)
      const { duration: firstDuration, result: firstResult } = await measureExecutionTime(() =>
        rbacService.getUserPermissions(testUser._id)
      );

      // Second call (with cache)
      const { duration: secondDuration, result: secondResult } = await measureExecutionTime(() =>
        rbacService.getUserPermissions(testUser._id)
      );

      console.log(`First call (no cache): ${firstDuration.toFixed(2)}ms, Result: ${firstResult.length} permissions`);
      console.log(`Second call (with cache): ${secondDuration.toFixed(2)}ms, Result: ${secondResult.length} permissions`);
      console.log(`Performance improvement: ${((firstDuration - secondDuration) / firstDuration * 100).toFixed(2)}%`);
    } catch (error) {
      console.error('Test 5 failed:', error.message);
    }

    // Test 6: Test cache invalidation
    console.log('\nTest 6: Testing cache invalidation...');

    try {
      // First, populate the cache
      await rbacService.getUserPermissions(testUser._id);

      // Create a new role with additional permissions
      const newRole = await rbacService.createRole({
        name: 'Cache Test Role ' + Date.now(),
        description: 'Role for testing cache invalidation',
        permissions: ['connector:edit']
      });

      // Assign the new role to the user
      await rbacService.assignRoleToUser(testUser._id, newRole._id);

      // Get permissions again and verify they've changed
      const updatedPermissions = await rbacService.getUserPermissions(testUser._id);

      console.log(`After role assignment: User has ${updatedPermissions.length} permissions`);
      console.log(`Cache invalidation successful: ${updatedPermissions.includes('connector:edit')}`);
    } catch (error) {
      console.error('Test 6 failed:', error.message);
    }

    console.log('\nRBAC cache tests completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
}
