ae100ca90eac1c5739686a58225cc0a9
// Mock the PackageConfigRegistry
_getJestObj().mock('../../../api/services/PackageConfigRegistry');
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Feature Flag Service Tests
 */

const FeatureFlagService = require('../../../api/services/FeatureFlagService');
const PackageConfigRegistry = require('../../../api/services/PackageConfigRegistry');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
describe('FeatureFlagService', () => {
  let featureFlagService;
  let testDir;
  beforeEach(async () => {
    // Create a temporary directory for testing
    testDir = path.join(os.tmpdir(), `nova-connect-test-${Date.now()}`);
    await fs.mkdir(testDir, {
      recursive: true
    });

    // Mock PackageConfigRegistry methods
    PackageConfigRegistry.mockImplementation(() => ({
      hasTenantFeatureAccess: jest.fn().mockResolvedValue(true),
      getTenantFeatureLimit: jest.fn().mockResolvedValue(100),
      getTenantAvailableFeatures: jest.fn().mockResolvedValue(['feature1', 'feature2']),
      getTenantMapping: jest.fn().mockResolvedValue({
        tenantId: 'test-tenant',
        packageId: 'enterprise',
        customFeatures: ['custom.feature1'],
        customLimits: {
          connections: 200
        }
      }),
      getPackageById: jest.fn().mockResolvedValue({
        id: 'enterprise',
        name: 'Enterprise Package',
        features: ['feature1', 'feature2', 'feature3'],
        limits: {
          connections: 100
        }
      }),
      getAllPackages: jest.fn().mockResolvedValue([{
        id: 'core',
        name: 'Core Package'
      }, {
        id: 'enterprise',
        name: 'Enterprise Package'
      }]),
      setTenantMapping: jest.fn().mockResolvedValue({
        tenantId: 'test-tenant',
        packageId: 'enterprise',
        customFeatures: ['custom.feature1'],
        customLimits: {
          connections: 200
        }
      }),
      clearCache: jest.fn()
    }));

    // Initialize the feature flag service with the test directory
    featureFlagService = new FeatureFlagService(testDir);

    // Mock methods to avoid file system operations
    featureFlagService.loadData = jest.fn().mockResolvedValue([]);
    featureFlagService.saveData = jest.fn().mockResolvedValue();

    // Mock methods for user entitlements and subscription tiers
    featureFlagService.getUserEntitlement = jest.fn().mockResolvedValue({
      userId: 'test-user',
      tierId: 'professional',
      customFeatures: ['custom.user.feature'],
      customLimits: {}
    });
    featureFlagService.getSubscriptionTierById = jest.fn().mockResolvedValue({
      id: 'professional',
      name: 'Professional',
      features: ['feature1', 'feature2']
    });
    featureFlagService.getFeatureFlagById = jest.fn().mockResolvedValue({
      id: 'feature1',
      name: 'Feature 1',
      enabled: true,
      limits: {
        professional: {
          requests_per_day: 1000
        }
      }
    });
    featureFlagService.getAllFeatureFlags = jest.fn().mockResolvedValue([{
      id: 'feature1',
      name: 'Feature 1',
      enabled: true
    }, {
      id: 'feature2',
      name: 'Feature 2',
      enabled: true
    }, {
      id: 'feature3',
      name: 'Feature 3',
      enabled: false
    }]);
  });
  afterEach(async () => {
    // Clean up the test directory
    try {
      await fs.rm(testDir, {
        recursive: true,
        force: true
      });
    } catch (error) {
      console.error('Error cleaning up test directory:', error);
    }

    // Clear all mocks
    jest.clearAllMocks();
  });
  test('should check if user has feature access with tenant ID', async () => {
    // Test with tenant ID
    const hasAccessWithTenant = await featureFlagService.hasFeatureAccess('test-user', 'feature1', 'test-tenant');

    // Should check tenant access first
    expect(featureFlagService.packageRegistry.hasTenantFeatureAccess).toHaveBeenCalledWith('test-tenant', 'feature1');
    expect(hasAccessWithTenant).toBe(true);

    // Mock tenant access to return false
    featureFlagService.packageRegistry.hasTenantFeatureAccess.mockResolvedValueOnce(false);

    // Should fall back to user access
    const hasAccessWithTenantFallback = await featureFlagService.hasFeatureAccess('test-user', 'feature1', 'test-tenant');
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');
    expect(hasAccessWithTenantFallback).toBe(true);
  });
  test('should check if user has feature access without tenant ID', async () => {
    // Test without tenant ID
    const hasAccess = await featureFlagService.hasFeatureAccess('test-user', 'feature1');

    // Should not check tenant access
    expect(featureFlagService.packageRegistry.hasTenantFeatureAccess).not.toHaveBeenCalled();

    // Should check user access
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');
    expect(hasAccess).toBe(true);
  });
  test('should get feature limit with tenant ID', async () => {
    // Test with tenant ID
    const limitWithTenant = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'connections', 'test-tenant');

    // Should check tenant limit first
    expect(featureFlagService.packageRegistry.getTenantFeatureLimit).toHaveBeenCalledWith('test-tenant', 'connections');
    expect(limitWithTenant).toBe(100);

    // Mock tenant limit to return null
    featureFlagService.packageRegistry.getTenantFeatureLimit.mockResolvedValueOnce(null);

    // Should fall back to user limit
    const limitWithTenantFallback = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'requests_per_day', 'test-tenant');
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getFeatureFlagById).toHaveBeenCalledWith('feature1');
    expect(limitWithTenantFallback).toBe(1000);
  });
  test('should get feature limit without tenant ID', async () => {
    // Test without tenant ID
    const limit = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'requests_per_day');

    // Should not check tenant limit
    expect(featureFlagService.packageRegistry.getTenantFeatureLimit).not.toHaveBeenCalled();

    // Should check user limit
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getFeatureFlagById).toHaveBeenCalledWith('feature1');
    expect(limit).toBe(1000);
  });
  test('should get user available features with tenant ID', async () => {
    // Test with tenant ID
    const featuresWithTenant = await featureFlagService.getUserAvailableFeatures('test-user', 'test-tenant');

    // Should get tenant features
    expect(featureFlagService.packageRegistry.getTenantAvailableFeatures).toHaveBeenCalledWith('test-tenant');

    // Should also get user features
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');
    expect(featureFlagService.getAllFeatureFlags).toHaveBeenCalled();

    // Should return enabled features that are in tenant features, user features, or subscription features
    expect(featuresWithTenant).toHaveLength(2);
  });
  test('should get user available features without tenant ID', async () => {
    // Test without tenant ID
    const features = await featureFlagService.getUserAvailableFeatures('test-user');

    // Should not get tenant features
    expect(featureFlagService.packageRegistry.getTenantAvailableFeatures).not.toHaveBeenCalled();

    // Should get user features
    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');
    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');
    expect(featureFlagService.getAllFeatureFlags).toHaveBeenCalled();

    // Should return enabled features that are in user features or subscription features
    expect(features).toHaveLength(2);
  });
  test('should get tenant package', async () => {
    // Test getting tenant package
    const pkg = await featureFlagService.getTenantPackage('test-tenant');

    // Should get tenant mapping
    expect(featureFlagService.packageRegistry.getTenantMapping).toHaveBeenCalledWith('test-tenant');

    // Should get package details
    expect(featureFlagService.packageRegistry.getPackageById).toHaveBeenCalledWith('enterprise');

    // Should return package details
    expect(pkg.id).toBe('enterprise');
    expect(pkg.name).toBe('Enterprise Package');
  });
  test('should set tenant package', async () => {
    // Test setting tenant package
    const mapping = await featureFlagService.setTenantPackage('test-tenant', 'enterprise', ['custom.feature1'], {
      connections: 200
    });

    // Should set tenant mapping
    expect(featureFlagService.packageRegistry.setTenantMapping).toHaveBeenCalledWith('test-tenant', 'enterprise', ['custom.feature1'], {
      connections: 200
    });

    // Should return mapping
    expect(mapping.tenantId).toBe('test-tenant');
    expect(mapping.packageId).toBe('enterprise');
  });
  test('should get all packages', async () => {
    // Test getting all packages
    const packages = await featureFlagService.getAllPackages();

    // Should get all packages
    expect(featureFlagService.packageRegistry.getAllPackages).toHaveBeenCalled();

    // Should return packages
    expect(packages).toHaveLength(2);
    expect(packages[0].id).toBe('core');
    expect(packages[1].id).toBe('enterprise');
  });
  test('should get package by ID', async () => {
    // Test getting package by ID
    const pkg = await featureFlagService.getPackageById('enterprise');

    // Should get package by ID
    expect(featureFlagService.packageRegistry.getPackageById).toHaveBeenCalledWith('enterprise');

    // Should return package
    expect(pkg.id).toBe('enterprise');
    expect(pkg.name).toBe('Enterprise Package');
  });
  test('should clear cache', async () => {
    // Test clearing cache
    featureFlagService.clearCache();

    // Should clear package registry cache
    expect(featureFlagService.packageRegistry.clearCache).toHaveBeenCalled();
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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