"""
Vendor model for the Universal Vendor Risk Management System.

This module provides the Vendor class for representing vendor information.
"""

import uuid
import datetime
from typing import Dict, List, Any, Optional


class Vendor:
    """
    Vendor class for representing vendor information.

    This class represents a vendor in the Universal Vendor Risk Management System.
    """

    def __init__(self,
                name: str,
                industry: str,
                services: List[str],
                criticality: str,
                data_types: List[str],
                contact_info: Optional[Dict[str, Any]] = None,
                address: Optional[Dict[str, Any]] = None,
                metadata: Optional[Dict[str, Any]] = None,
                vendor_id: Optional[str] = None,
                created_at: Optional[str] = None,
                updated_at: Optional[str] = None):
        """
        Initialize a Vendor.

        Args:
            name: The name of the vendor
            industry: The industry of the vendor
            services: The services provided by the vendor
            criticality: The criticality of the vendor (e.g., 'High', 'Medium', 'Low')
            data_types: The types of data shared with the vendor (e.g., 'PII', 'PHI', 'PCI')
            contact_info: Contact information for the vendor
            address: Address information for the vendor
            metadata: Additional metadata about the vendor
            vendor_id: The ID of the vendor (generated if not provided)
            created_at: The creation timestamp (generated if not provided)
            updated_at: The last update timestamp (generated if not provided)
        """
        self.name = name
        self.industry = industry
        self.services = services
        self.criticality = criticality
        self.data_types = data_types
        self.contact_info = contact_info or {}
        self.address = address or {}
        self.metadata = metadata or {}
        self.vendor_id = vendor_id or str(uuid.uuid4())
        self.created_at = created_at or datetime.datetime.now().isoformat()
        self.updated_at = updated_at or self.created_at

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Vendor':
        """
        Create a Vendor from a dictionary.

        Args:
            data: Dictionary containing vendor data

        Returns:
            A Vendor instance
        """
        return cls(
            name=data.get('name', ''),
            industry=data.get('industry', ''),
            services=data.get('services', []),
            criticality=data.get('criticality', 'Low'),
            data_types=data.get('data_types', []),
            contact_info=data.get('contact_info', {}),
            address=data.get('address', {}),
            metadata=data.get('metadata', {}),
            vendor_id=data.get('vendor_id', None),
            created_at=data.get('created_at', None),
            updated_at=data.get('updated_at', None)
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Vendor to a dictionary.

        Returns:
            Dictionary representation of the Vendor
        """
        return {
            'vendor_id': self.vendor_id,
            'name': self.name,
            'industry': self.industry,
            'services': self.services,
            'criticality': self.criticality,
            'data_types': self.data_types,
            'contact_info': self.contact_info,
            'address': self.address,
            'metadata': self.metadata,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    def update(self, data: Dict[str, Any]) -> None:
        """
        Update the Vendor with new data.

        Args:
            data: Dictionary containing updated vendor data
        """
        if 'name' in data:
            self.name = data['name']
        if 'industry' in data:
            self.industry = data['industry']
        if 'services' in data:
            self.services = data['services']
        if 'criticality' in data:
            self.criticality = data['criticality']
        if 'data_types' in data:
            self.data_types = data['data_types']
        if 'contact_info' in data:
            self.contact_info = data['contact_info']
        if 'address' in data:
            self.address = data['address']
        if 'metadata' in data:
            self.metadata = data['metadata']

        self.updated_at = datetime.datetime.now().isoformat()

    def get_risk_factors(self) -> Dict[str, Any]:
        """
        Get risk factors for the vendor.

        Returns:
            Dictionary of risk factors
        """
        return {
            'criticality': self.criticality,
            'data_types': self.data_types,
            'industry': self.industry
        }

    def has_sensitive_data(self) -> bool:
        """
        Check if the vendor has access to sensitive data.

        Returns:
            True if the vendor has access to sensitive data, False otherwise
        """
        sensitive_data_types = ['PII', 'PHI', 'PCI', 'Financial', 'Intellectual Property']
        return any(data_type in sensitive_data_types for data_type in self.data_types)

    def is_critical(self) -> bool:
        """
        Check if the vendor is critical.

        Returns:
            True if the vendor is critical, False otherwise
        """
        return self.criticality.lower() == 'high'