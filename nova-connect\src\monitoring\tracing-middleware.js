/**
 * NovaFuse Universal API Connector - Tracing Middleware
 * 
 * This module provides middleware for distributed tracing.
 */

const tracingService = require('./tracing-service');
const { createLogger } = require('../utils/logger');
const { UAConnectorError } = require('../errors');

const logger = createLogger('tracing-middleware');

/**
 * Normalize the path by replacing path parameters with placeholders
 * 
 * @param {string} path - The request path
 * @returns {string} - The normalized path
 */
function normalizePath(path) {
  // Replace numeric IDs with :id
  return path
    .replace(/\/\d+/g, '/:id')
    .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:uuid');
}

/**
 * Tracing middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function tracingMiddleware(req, res, next) {
  // Skip tracing for health check and metrics endpoints
  if (req.path === '/health' || req.path === '/metrics') {
    return next();
  }
  
  // Parse trace headers
  const traceContext = tracingService.parseTraceHeaders(req.headers);
  
  // Start trace
  const normalizedPath = normalizePath(req.path);
  const spanName = `HTTP ${req.method} ${normalizedPath}`;
  
  const span = tracingService.startTrace(spanName, {
    ...traceContext,
    attributes: {
      'http.method': req.method,
      'http.url': req.originalUrl,
      'http.path': normalizedPath,
      'http.host': req.headers.host,
      'http.user_agent': req.headers['user-agent'],
      'http.request_id': req.headers['x-request-id'] || req.id,
      'service.name': 'nova-connect'
    }
  });
  
  // Store trace context in request
  req.traceContext = span;
  
  // Add trace headers to response
  const traceHeaders = tracingService.createTraceHeaders(span);
  for (const [key, value] of Object.entries(traceHeaders)) {
    res.setHeader(key, value);
  }
  
  // Capture response
  const originalEnd = res.end;
  res.end = function(...args) {
    // Add response attributes
    span.setAttribute('http.status_code', res.statusCode);
    span.setAttribute('http.response_content_length', parseInt(res.getHeader('content-length') || '0', 10));
    
    // End trace
    tracingService.endTrace(span);
    
    // Call original end
    return originalEnd.apply(res, args);
  };
  
  logger.debug(`Trace started for ${req.method} ${req.originalUrl}`);
  
  next();
}

/**
 * Tracing error middleware
 * 
 * @param {Error} err - The error
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function tracingErrorMiddleware(err, req, res, next) {
  // Get trace context from request
  const span = req.traceContext;
  
  if (span) {
    // Add error attributes
    span.setAttribute('error', true);
    span.setAttribute('error.message', err.message);
    
    if (err instanceof UAConnectorError) {
      span.setAttribute('error.type', err.name);
      span.setAttribute('error.code', err.code);
      span.setAttribute('error.severity', err.severity);
    } else {
      span.setAttribute('error.type', err.name || 'Error');
    }
    
    logger.debug(`Error recorded in trace for ${req.method} ${req.originalUrl}`);
  }
  
  next(err);
}

module.exports = {
  tracingMiddleware,
  tracingErrorMiddleware
};
