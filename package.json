{"name": "nova-marketplace", "version": "1.0.0", "description": "NovaFuse API Superstore: A comprehensive marketplace for GRC APIs featuring NovaConnect Universal API Connector and NovaMarketplace", "private": true, "scripts": {"start": "node server.js", "demo": "node examples/demo.js", "demo:mock": "node examples/demo-mock.js", "dev": "next dev", "test": "jest", "test:unit": "jest --testMatch='**/tests/unit/**/*.test.js'", "test:integration": "jest --testMatch='**/tests/integration/**/*.test.js'", "test:performance": "jest --testMatch='**/tests/performance/**/*.test.js'", "test:security": "jest --testMatch='**/tests/security/**/*.test.js'", "test:components": "jest --testMatch='**/tests/components/**/*.test.js'", "test:api": "jest --testMatch='**/tests/api/**/*.test.js'", "test:e2e": "cypress run", "test:all": "npm run test:unit && npm run test:components && npm run test:api && npm run test:e2e", "test:coverage": "jest --coverage --coverageThreshold='{\"global\":{\"branches\":81,\"functions\":81,\"lines\":81,\"statements\":81}}'", "test:coverage:novatrack": "jest --testMatch='**/tests/unit/novatrack/**/*.test.js' --coverage --coverageThreshold='{\"global\":{\"branches\":81,\"functions\":81,\"lines\":81,\"statements\":81}}'", "test:coverage:novacore": "jest --testMatch='**/tests/unit/novacore/**/*.test.js' --coverage --coverageThreshold='{\"global\":{\"branches\":81,\"functions\":81,\"lines\":81,\"statements\":81}}'", "test:coverage:novaproof": "jest --testMatch='**/tests/unit/novaproof/**/*.test.js' --coverage --coverageThreshold='{\"global\":{\"branches\":81,\"functions\":81,\"lines\":81,\"statements\":81}}'", "test:mutation": "stryker run", "test:mutation:compliance": "stryker run --mutate 'utils/compliance/**/*.js'", "test:novatrack": "jest --testMatch='**/tests/unit/novatrack/**/*.test.js'", "test:novatrack:simple": "jest tests/unit/novatrack/tracking-manager.simple.test.js --verbose", "test:novatrack:edge": "jest tests/unit/novatrack/tracking-manager.edge-cases.test.js --verbose", "test:novatrack:validation": "jest tests/unit/novatrack/tracking-manager.validation.test.js --verbose", "test:novatrack:perf": "jest tests/performance/novatrack/tracking-manager.perf.test.js --verbose", "test:novatrack:all": "jest --testMatch='**/tests/{unit,performance}/novatrack/**/*.test.js' --verbose", "test:novatrack:integration": "jest --testMatch='**/tests/integration/novatrack/**/*.test.js'", "test:novatrack:integration:simple": "jest tests/integration/novatrack/api.simple.test.js --verbose", "test:novatrack:integration:comprehensive": "jest tests/integration/novatrack/api.comprehensive.test.js --verbose", "test:novatrack:security": "jest tests/security/novatrack/api.security.test.js --verbose", "test:novatrack:docker": "docker-compose -f docker-compose.novatrack.yml up --build novatrack-unit-tests", "test:novatrack:integration:docker": "docker-compose -f docker-compose.novatrack.yml up --build novatrack-integration-tests", "test:novatrack:coverage:docker": "docker-compose -f docker-compose.novatrack.yml up --build novatrack-coverage-tests", "test:novatrack:docker:all": "docker-compose -f docker-compose.novatrack.yml up --build", "test:novacore": "jest --testMatch='**/tests/{unit,integration,performance,security}/novacore/**/*.test.js'", "test:novacore:unit": "jest --testMatch='**/tests/unit/novacore/**/*.test.js' --verbose", "test:novacore:integration": "jest --testMatch='**/tests/integration/novacore/**/*.test.js' --verbose", "test:novacore:performance": "jest --testMatch='**/tests/performance/novacore/**/*.test.js' --verbose", "test:novacore:security": "jest --testMatch='**/tests/security/novacore/**/*.test.js' --verbose", "test:novaproof": "jest --testMatch='**/tests/{unit,integration,performance,security}/novaproof/**/*.test.js'", "test:novaproof:unit": "jest --testMatch='**/tests/unit/novaproof/**/*.test.js' --verbose", "test:novaproof:integration": "jest --testMatch='**/tests/integration/novaproof/**/*.test.js' --verbose", "test:novaproof:performance": "jest --testMatch='**/tests/performance/novaproof/**/*.test.js' --verbose", "test:novaproof:security": "jest --testMatch='**/tests/security/novaproof/**/*.test.js' --verbose", "test:compliance": "jest --testMatch='**/tests/compliance/**/*.test.js'", "test:compliance:gdpr": "jest --testMatch='**/tests/compliance/gdpr/**/*.test.js'", "test:compliance:soc2": "jest --testMatch='**/tests/compliance/soc2/**/*.test.js'", "test:compliance:nist-csf": "jest --testMatch='**/tests/compliance/nist-csf/**/*.test.js'", "test:compliance:all": "node tests/compliance/run-all-compliance-tests.js", "test:market-readiness": "node test/market_readiness/comprehensive_verification.js", "test:novacortex": "python -m pytest tests/novacortex/test_integration.py -v", "test:novacortex:docker": "docker-compose -f docker-compose.novacortex-test.yml up --build test-runner", "test:market-readiness:docker": "docker-compose -f docker-compose.prod-test.yml up --build", "test:wait-and-verify": "node -e \"setTimeout(() => { require('./test/market_readiness/comprehensive_verification.js') }, 30000)\"", "test:comphyology": "node test/comphyology/test_comphyology.js", "test:comphyology:quantum": "node test/comphyology/test_enhanced_quantum_inference.js", "test:comphyology:performance": "node test/comphyology/test_visualization_performance.js", "test:comphyology:progressive": "node test/comphyology/test_progressive_loading.js", "test:comphyology:workers": "node test/comphyology/test_web_workers.js", "test:comphyology:integration": "node test/comphyology/test_integration.js", "test:comphyology:stress": "node test/comphyology/test_dashboard_stress.js", "test:unified": "node novafuse-unified-test-runner.js", "test:unified:html": "node novafuse-unified-test-runner.js --html", "test:unified:uuft": "node novafuse-unified-test-runner.js --category uuft", "test:unified:trinity": "node novafuse-unified-test-runner.js --category trinity", "test:unified:coherence": "node novafuse-unified-test-runner.js --category coherence", "test:ecosystem": "npm run test:unified && npm run test:all", "deploy": "node novafuse-deploy.js", "deploy:core": "node novafuse-deploy.js deploy --categories core", "deploy:coherence": "node novafuse-deploy.js deploy --categories coherence", "deploy:full": "node novafuse-deploy.js deploy --rebuild", "deploy:status": "node novafuse-deploy.js status", "deploy:rollback": "node novafuse-deploy.js rollback", "ecosystem:start": "docker-compose -f docker-compose.unified.yml up -d", "ecosystem:stop": "docker-compose -f docker-compose.unified.yml down", "ecosystem:logs": "docker-compose -f docker-compose.unified.yml logs -f", "test-api:start": "node novafuse-test-api-server.js", "test-api:dev": "node novafuse-test-api-server.js --port 3100", "demo:comphyology": "node src/comphyology/demo.js", "demo:comphyology:novavision": "node src/comphyology/examples/node-example.js", "demo:comphyology:real-time": "node src/comphyology/examples/real_time_dashboard_example.js", "demo:comphyology:quantum": "node src/comphyology/examples/quantum_inference_example.js", "demo:comphyology:ripple": "node src/comphyology/examples/universal_ripple_example.js", "demo:comphyology:trinity": "node src/comphyology/examples/trinity_visualization_example.js", "cypress:open": "cypress open", "cypress:run": "cypress run", "lint": "eslint .", "build": "next build", "start:next": "next start"}, "repository": {"type": "git", "url": "git+https://github.com/Dartan1983/nova-marketplace.git"}, "keywords": ["api", "marketplace", "grc", "governance", "risk", "compliance", "security", "privacy", "connector", "integration"], "author": "NovaGRC", "license": "MIT", "bugs": {"url": "https://github.com/Dartan1983/nova-marketplace/issues"}, "homepage": "https://github.com/Dartan1983/nova-marketplace#readme", "dependencies": {"@google-cloud/bigquery": "^8.1.0", "@google-cloud/billing": "^5.1.0", "@google-cloud/monitoring": "^5.3.0", "@google-cloud/service-usage": "^4.2.0", "ajv": "^8.12.0", "ajv-formats": "^2.1.1", "apollo-server-express": "^3.13.0", "axios": "^1.4.0", "bcrypt": "^5.1.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "busboy": "^1.6.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "express-busboy": "^10.1.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "framer-motion": "^12.7.4", "graphql": "^16.11.0", "graphql-subscriptions": "^3.0.0", "helmet": "^7.1.0", "ioredis": "^5.6.1", "ip-range-check": "^0.2.0", "joi": "^17.11.0", "jsonpath": "^1.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.503.0", "mongoose": "^7.4.1", "morgan": "^1.10.0", "multer": "^2.0.1", "next": "^13.4.12", "node-cache": "^5.1.2", "passport": "^0.7.0", "passport-oauth2": "^1.8.0", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "rate-limit-redis": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.10.1", "react-intersection-observer": "^9.16.0", "redis": "^5.6.0", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "sqlite3": "^5.1.7", "stream-throttle": "^0.1.3", "stripe": "^12.16.0", "subscriptions-transport-ws": "^0.11.0", "uuid": "^9.0.1", "winston": "^3.10.0", "ws": "^8.14.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/eslint-parser": "^7.27.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "autoprefixer": "^10.4.14", "axios-mock-adapter": "^1.22.0", "babel-jest": "^29.7.0", "chai": "^5.2.0", "chalk": "^4.1.2", "eslint": "^8.46.0", "eslint-config-next": "^13.4.12", "eslint-plugin-react": "^7.37.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "markdown-pdf": "^11.0.0", "nodemon": "^3.0.1", "nyc": "^17.1.0", "postcss": "^8.4.27", "sinon": "^20.0.0", "supertest": "^6.3.4", "tailwindcss": "^3.3.3"}, "engines": {"node": ">=14.0.0"}, "main": ".eslintrc.js", "directories": {"doc": "docs", "example": "examples", "test": "tests"}}