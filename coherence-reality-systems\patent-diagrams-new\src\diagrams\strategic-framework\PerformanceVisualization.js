import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const PerformanceVisualization = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel fontSize="18px">3,142x PERFORMANCE VISUALIZATION</ContainerLabel>
      </ContainerBox>

      {/* Data Normalization Chart */}
      <ContainerBox width="700px" height="200px" left="50px" top="70px">
        <ContainerLabel fontSize="16px">DATA NORMALIZATION SPEED (MS)</ContainerLabel>
      </ContainerBox>

      {/* Bar Chart for Data Normalization */}
      <svg width="700" height="200" style={{ position: 'absolute', top: 100, left: 50, zIndex: 1 }}>
        {/* NovaConnect Bar */}
        <g>
          <rect x="100" y="150" width="60" height="1" fill="#0A84FF" />
          <text x="130" y="170" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#0A84FF">0.07ms</text>
          <text x="130" y="190" textAnchor="middle" fontSize="12">NovaConnect</text>
        </g>

        {/* AWS Bar */}
        <g>
          <rect x="300" y="30" width="60" height="120" fill="#666" />
          <text x="330" y="20" textAnchor="middle" fontSize="12" fontWeight="bold">220ms</text>
          <text x="330" y="190" textAnchor="middle" fontSize="12">AWS</text>
        </g>

        {/* Azure Bar */}
        <g>
          <rect x="500" y="50" width="60" height="100" fill="#666" />
          <text x="530" y="40" textAnchor="middle" fontSize="12" fontWeight="bold">180ms</text>
          <text x="530" y="190" textAnchor="middle" fontSize="12">Azure</text>
        </g>

        {/* Highlight the difference */}
        <text x="130" y="100" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#0A84FF">3,142x Faster</text>

        {/* Component Numbers */}
        <g>
          <circle cx="100" cy="150" r="10" fill="#333" />
          <text x="100" y="154" textAnchor="middle" fontSize="10" fill="white">201</text>
        </g>
        <g>
          <circle cx="300" cy="30" r="10" fill="#333" />
          <text x="300" y="34" textAnchor="middle" fontSize="10" fill="white">202</text>
        </g>
        <g>
          <circle cx="500" cy="50" r="10" fill="#333" />
          <text x="500" y="54" textAnchor="middle" fontSize="10" fill="white">203</text>
        </g>
      </svg>

      {/* Events Processed Chart */}
      <ContainerBox width="700px" height="200px" left="50px" top="290px">
        <ContainerLabel fontSize="16px">EVENTS PROCESSED PER SECOND</ContainerLabel>
      </ContainerBox>

      {/* Bar Chart for Events Processed */}
      <svg width="700" height="200" style={{ position: 'absolute', top: 320, left: 50, zIndex: 1 }}>
        {/* NovaConnect Bar */}
        <g>
          <rect x="100" y="30" width="60" height="120" fill="#0A84FF" />
          <text x="130" y="20" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#0A84FF">69,000</text>
          <text x="130" y="190" textAnchor="middle" fontSize="12">NovaConnect</text>
        </g>

        {/* AWS Bar */}
        <g>
          <rect x="300" y="140" width="60" height="10" fill="#666" />
          <text x="330" y="130" textAnchor="middle" fontSize="12" fontWeight="bold">5,000</text>
          <text x="330" y="190" textAnchor="middle" fontSize="12">AWS</text>
        </g>

        {/* Azure Bar */}
        <g>
          <rect x="500" y="135" width="60" height="15" fill="#666" />
          <text x="530" y="125" textAnchor="middle" fontSize="12" fontWeight="bold">7,500</text>
          <text x="530" y="190" textAnchor="middle" fontSize="12">Azure</text>
        </g>

        {/* Highlight the difference */}
        <text x="350" y="100" textAnchor="middle" fontSize="16" fontWeight="bold" fill="#0A84FF">13.8x Higher Capacity</text>

        {/* Component Numbers */}
        <g>
          <circle cx="100" cy="30" r="10" fill="#333" />
          <text x="100" y="34" textAnchor="middle" fontSize="10" fill="white">204</text>
        </g>
        <g>
          <circle cx="300" cy="140" r="10" fill="#333" />
          <text x="300" y="144" textAnchor="middle" fontSize="10" fill="white">205</text>
        </g>
        <g>
          <circle cx="500" cy="135" r="10" fill="#333" />
          <text x="500" y="139" textAnchor="middle" fontSize="10" fill="white">206</text>
        </g>
      </svg>

      {/* Performance Summary */}
      <ComponentBox left="50px" top="510px" width="700px" height="60px">
        <ComponentNumber>207</ComponentNumber>
        <ComponentLabel fontSize="16px">Performance Summary</ComponentLabel>
        <div style={{ display: 'flex', justifyContent: 'space-around', width: '100%', marginTop: '5px' }}>
          <span style={{ fontWeight: 'bold', color: '#0A84FF' }}>Data Normalization: 3,142x faster</span>
          <span style={{ fontWeight: 'bold', color: '#0A84FF' }}>Event Processing: 13.8x higher capacity</span>
          <span style={{ fontWeight: 'bold', color: '#0A84FF' }}>Remediation: 4.75x faster</span>
        </div>
      </ComponentBox>

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#333" />
          <LegendText>NovaConnect Performance</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#666" />
          <LegendText>Competitor Performance</LegendText>
        </LegendItem>
      </DiagramLegend>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default PerformanceVisualization;
