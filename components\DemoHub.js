import React, { useState } from 'react';
import Link from 'next/link';

/**
 * Demo Hub Component
 * 
 * This component serves as a central hub for all NovaFuse demos.
 * 
 * @param {Object} props - Component props
 * @param {Array} props.demos - List of available demos
 * @returns {React.ReactNode} - Rendered component
 */
const DemoHub = ({ demos = [] }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  // Extract unique categories from demos
  const categories = ['all', ...new Set(demos.map(demo => demo.category))];

  // Filter demos based on search term and active category
  const filteredDemos = demos.filter(demo => {
    const matchesSearch = 
      demo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      demo.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      demo.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = activeCategory === 'all' || demo.category === activeCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="demo-hub bg-primary text-white">
      {/* Header Section */}
      <div className="accent-bg text-white rounded-lg p-8 mb-8">
        <h2 className="text-3xl md:text-4xl font-bold mb-4 text-center">NovaFuse Demo Hub</h2>
        <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
          Explore interactive demos of NovaFuse's revolutionary components and APIs.
        </p>
      </div>

      {/* Search and Filter */}
      <div className="bg-secondary rounded-lg p-6 mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          {/* Search */}
          <div className="w-full md:w-1/2 mb-4 md:mb-0">
            <div className="relative">
              <input
                type="text"
                placeholder="Search demos..."
                className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-4 pl-10 text-white"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div className="absolute left-3 top-2.5 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Category Filter */}
          <div className="w-full md:w-auto">
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <button
                  key={category}
                  className={`px-4 py-2 rounded-lg text-sm font-medium ${
                    activeCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                  }`}
                  onClick={() => setActiveCategory(category)}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Demo Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredDemos.length > 0 ? (
            filteredDemos.map(demo => (
              <div key={demo.id} className="border border-gray-700 rounded-lg overflow-hidden">
                {/* Demo Image */}
                <div className="h-40 bg-gray-800 relative">
                  {demo.image ? (
                    <img 
                      src={demo.image} 
                      alt={demo.title} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-900 to-purple-900">
                      <span className="text-4xl">{demo.icon || '🔌'}</span>
                    </div>
                  )}
                  
                  {/* Category Badge */}
                  <div className="absolute top-2 right-2 bg-blue-800 bg-opacity-90 px-2 py-1 rounded text-xs font-medium">
                    {demo.category}
                  </div>
                </div>
                
                {/* Demo Info */}
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">{demo.title}</h3>
                  <p className="text-gray-400 text-sm mb-4 line-clamp-2">{demo.description}</p>
                  
                  {/* Tags */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {demo.tags.map((tag, index) => (
                      <span key={index} className="bg-gray-800 text-gray-300 px-2 py-1 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  {/* Action Button */}
                  <Link href={demo.url} className="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg font-medium">
                    Launch Demo
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <div className="text-gray-400 mb-2">No demos found matching your criteria</div>
              <button 
                className="text-blue-500 hover:text-blue-400"
                onClick={() => {
                  setSearchTerm('');
                  setActiveCategory('all');
                }}
              >
                Clear filters
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Getting Started Section */}
      <div className="bg-secondary rounded-lg p-6">
        <h3 className="text-xl font-semibold mb-4">Getting Started with NovaFuse Demos</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="border border-gray-700 rounded-lg p-4">
            <h4 className="font-medium mb-2">How to Use the Demos</h4>
            <p className="text-sm text-gray-400">
              Each demo provides an interactive experience to showcase NovaFuse's capabilities.
              Select a demo scenario, run the demo, and explore the results to understand how
              NovaFuse can transform your compliance operations.
            </p>
          </div>
          <div className="border border-gray-700 rounded-lg p-4">
            <h4 className="font-medium mb-2">Request a Custom Demo</h4>
            <p className="text-sm text-gray-400 mb-3">
              Need a personalized demo for your specific use case? Our team can create
              a custom demonstration tailored to your organization's needs.
            </p>
            <Link href="/contact" className="text-blue-500 hover:text-blue-400 text-sm">
              Contact Us →
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DemoHub;
