const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const winston = require('winston');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Configure middleware
app.use(cors());
app.use(helmet());
app.use(express.json());
app.use(morgan('combined'));

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'novafuse-api' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  logger.info('Connected to MongoDB successfully');
})
.catch(err => {
  logger.error('MongoDB connection error:', err);
  process.exit(1);
});

// Feature flag middleware
const checkFeatureAccess = (featureFlag) => {
  return (req, res, next) => {
    // In a real implementation, this would check the user's product tier
    // For simulation, we'll check if the feature flag is enabled in the request
    const productTier = req.headers['x-product-tier'] || 'novaPrime';

    // For simulation, we'll allow all features for novaPrime
    if (productTier === 'novaPrime') {
      return next();
    }

    // For other tiers, we'd check the feature flag configuration
    // This is a simplified implementation for simulation purposes
    const featureFlags = {
      novaCore: ['privacy-management', 'security-assessment', 'regulatory-compliance'],
      novaShield: ['security-assessment', 'control-testing'],
      novaLearn: ['regulatory-compliance'],
      novaAssistAI: ['privacy-management', 'security-assessment', 'regulatory-compliance', 'control-testing', 'esg']
    };

    if (featureFlags[productTier] && featureFlags[productTier].includes(featureFlag)) {
      return next();
    }

    return res.status(403).json({
      error: 'Feature not available',
      message: `This feature is not available in your current plan (${productTier})`
    });
  };
};

// Import routes
const complianceRoutes = require('./routes/compliance');
const riskRoutes = require('./routes/risk');
const personasRoutes = require('./routes/personas');
const evidenceRoutes = require('./routes/evidence');

// Define routes
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Use enhanced routes
app.use('/api/compliance', complianceRoutes);
app.use('/api/risk', riskRoutes);
app.use('/api/personas', personasRoutes);
app.use('/api/evidence', evidenceRoutes);

// API documentation route
app.get('/api/docs', (req, res) => {
  res.json({
    message: 'NovaFuse API Documentation',
    endpoints: [
      {
        path: '/api/compliance/frameworks',
        method: 'GET',
        description: 'Get all compliance frameworks',
        parameters: ['category', 'region', 'minScore']
      },
      {
        path: '/api/compliance/frameworks/:id',
        method: 'GET',
        description: 'Get a specific compliance framework by ID',
        parameters: []
      },
      {
        path: '/api/compliance/frameworks/:id/controls',
        method: 'GET',
        description: 'Get controls for a specific compliance framework',
        parameters: ['status', 'category', 'minScore']
      },
      {
        path: '/api/compliance/frameworks/:frameworkId/controls/:controlId',
        method: 'GET',
        description: 'Get a specific control for a compliance framework',
        parameters: []
      },
      {
        path: '/api/compliance/summary',
        method: 'GET',
        description: 'Get a summary of compliance status across all frameworks',
        parameters: []
      },
      {
        path: '/api/risk/scenarios',
        method: 'GET',
        description: 'Get all risk scenarios',
        parameters: ['category', 'status', 'minRisk']
      },
      {
        path: '/api/risk/scenarios/:id',
        method: 'GET',
        description: 'Get a specific risk scenario by ID',
        parameters: []
      },
      {
        path: '/api/risk/heatmap',
        method: 'GET',
        description: 'Get risk heatmap data',
        parameters: []
      },
      {
        path: '/api/risk/summary',
        method: 'GET',
        description: 'Get a summary of risk status',
        parameters: []
      },
      {
        path: '/api/personas',
        method: 'GET',
        description: 'Get all user personas',
        parameters: ['role', 'department']
      },
      {
        path: '/api/personas/:id',
        method: 'GET',
        description: 'Get a specific user persona by ID',
        parameters: []
      },
      {
        path: '/api/personas/:id/dashboards',
        method: 'GET',
        description: 'Get dashboards for a specific user persona',
        parameters: []
      },
      {
        path: '/api/personas/:personaId/dashboards/:dashboardId',
        method: 'GET',
        description: 'Get a specific dashboard for a user persona',
        parameters: []
      },
      {
        path: '/api/evidence/records',
        method: 'GET',
        description: 'Get all evidence records',
        parameters: ['type', 'framework', 'controlId', 'resourceType', 'fromDate', 'toDate']
      },
      {
        path: '/api/evidence/records/:id',
        method: 'GET',
        description: 'Get a specific evidence record by ID',
        parameters: []
      },
      {
        path: '/api/evidence/types',
        method: 'GET',
        description: 'Get all evidence types',
        parameters: []
      },
      {
        path: '/api/evidence/binder',
        method: 'GET',
        description: 'Get evidence binder configuration',
        parameters: []
      },
      {
        path: '/api/evidence/summary',
        method: 'GET',
        description: 'Get a summary of evidence records',
        parameters: []
      }
    ],
    headers: {
      'x-product-tier': 'Product tier for feature flag control (novaPrime, novaCore, novaShield, novaLearn, novaAssistAI)'
    }
  });
});

// Privacy Management API routes
app.get('/privacy/management/processing-activities', checkFeatureAccess('privacy-management'), (req, res) => {
  res.json({
    data: [
      { id: '1', name: 'Customer Data Processing', purpose: 'Service Delivery', dataCategories: ['Personal Data', 'Contact Information'] },
      { id: '2', name: 'Marketing Analytics', purpose: 'Marketing', dataCategories: ['Usage Data', 'Preferences'] },
      { id: '3', name: 'Employee Records', purpose: 'HR Management', dataCategories: ['Personal Data', 'Financial Information'] }
    ]
  });
});

app.get('/privacy/management/subject-requests', checkFeatureAccess('privacy-management'), (req, res) => {
  res.json({
    data: [
      { id: '1', type: 'Access', status: 'Pending', requestDate: '2023-08-01' },
      { id: '2', type: 'Deletion', status: 'Completed', requestDate: '2023-07-15' },
      { id: '3', type: 'Correction', status: 'In Progress', requestDate: '2023-07-28' }
    ]
  });
});

// Security Assessment API routes
app.get('/security/assessment/assessments', checkFeatureAccess('security-assessment'), (req, res) => {
  res.json({
    data: [
      { id: '1', name: 'Annual Security Review', status: 'Completed', completionDate: '2023-06-30' },
      { id: '2', name: 'Vendor Security Assessment', status: 'In Progress', startDate: '2023-07-15' },
      { id: '3', name: 'Cloud Infrastructure Audit', status: 'Planned', scheduledDate: '2023-09-01' }
    ]
  });
});

// Regulatory Compliance API routes
app.get('/compliance/regulatory/frameworks', checkFeatureAccess('regulatory-compliance'), (req, res) => {
  res.json({
    data: [
      { id: '1', name: 'GDPR', description: 'General Data Protection Regulation', region: 'EU' },
      { id: '2', name: 'HIPAA', description: 'Health Insurance Portability and Accountability Act', region: 'US' },
      { id: '3', name: 'CCPA', description: 'California Consumer Privacy Act', region: 'US-CA' }
    ]
  });
});

// Control Testing API routes
app.get('/control/testing/tests', checkFeatureAccess('control-testing'), (req, res) => {
  res.json({
    data: [
      { id: '1', name: 'Access Control Test', status: 'Passed', executionDate: '2023-07-20' },
      { id: '2', name: 'Encryption Validation', status: 'Failed', executionDate: '2023-07-22' },
      { id: '3', name: 'Backup Recovery Test', status: 'Scheduled', scheduledDate: '2023-08-15' }
    ]
  });
});

// ESG API routes
app.get('/esg/metrics', checkFeatureAccess('esg'), (req, res) => {
  res.json({
    data: [
      { id: '1', category: 'Environmental', name: 'Carbon Emissions', value: '120 tons', reportingPeriod: 'Q2 2023' },
      { id: '2', category: 'Social', name: 'Diversity Index', value: '0.78', reportingPeriod: 'Q2 2023' },
      { id: '3', category: 'Governance', name: 'Board Independence', value: '85%', reportingPeriod: 'Q2 2023' }
    ]
  });
});

// Google Cloud Integration routes
app.get('/integrations/gcp/scc/findings', (req, res) => {
  // This would integrate with Security Command Center in a real implementation
  res.json({
    data: [
      { id: '1', category: 'Vulnerability', severity: 'High', resource: 'vm-instance-1', description: 'Unpatched vulnerability detected' },
      { id: '2', category: 'Misconfiguration', severity: 'Medium', resource: 'storage-bucket-1', description: 'Public access enabled' },
      { id: '3', category: 'Threat', severity: 'Low', resource: 'network-1', description: 'Unusual traffic pattern detected' }
    ]
  });
});

app.post('/integrations/gcp/scc/findings', (req, res) => {
  // This would create a finding in Security Command Center in a real implementation
  logger.info('Creating SCC finding', req.body);
  res.status(201).json({
    id: '4',
    category: req.body.category,
    severity: req.body.severity,
    resource: req.body.resource,
    description: req.body.description,
    createdAt: new Date().toISOString()
  });
});

// Start the server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
  console.log(`NovaFuse API running on http://localhost:${PORT}`);
});

module.exports = app;
