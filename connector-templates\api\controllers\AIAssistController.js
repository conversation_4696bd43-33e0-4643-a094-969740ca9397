/**
 * AI Assistant Controller
 * 
 * This controller handles API requests related to AI-assisted features.
 */

const AIAssistService = require('../services/AIAssistService');
const { ValidationError } = require('../utils/errors');

class AIAssistController {
  constructor() {
    this.aiAssistService = new AIAssistService();
  }

  /**
   * Generate connector configuration from API documentation
   */
  async generateConnectorConfig(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Request data is required');
      }
      
      const connectorConfig = await this.aiAssistService.generateConnectorConfig(data);
      res.json(connectorConfig);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate connector configuration from natural language description
   */
  async generateFromDescription(req, res, next) {
    try {
      const data = req.body;
      
      if (!data || !data.description) {
        throw new ValidationError('Description is required');
      }
      
      const connectorConfig = await this.aiAssistService.generateFromDescription(data);
      res.json(connectorConfig);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Suggest fixes for API errors
   */
  async suggestErrorFixes(req, res, next) {
    try {
      const data = req.body;
      
      if (!data || !data.error || !data.context) {
        throw new ValidationError('Error information and context are required');
      }
      
      const suggestions = await this.aiAssistService.suggestErrorFixes(data);
      res.json(suggestions);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Optimize workflow
   */
  async optimizeWorkflow(req, res, next) {
    try {
      const data = req.body;
      
      if (!data || !data.workflow) {
        throw new ValidationError('Workflow configuration is required');
      }
      
      const optimizations = await this.aiAssistService.optimizeWorkflow(data);
      res.json(optimizations);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AIAssistController();
