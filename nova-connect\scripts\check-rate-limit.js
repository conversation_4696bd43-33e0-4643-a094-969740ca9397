/**
 * Rate Limiting Configuration Check
 * 
 * This script checks for proper rate limiting configuration using express-rate-limit.
 * It analyzes the server.js and other relevant files to ensure that rate limiting is properly configured.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Files to check
const filesToCheck = [
  path.join(__dirname, '..', 'server.js'),
  path.join(__dirname, '..', 'app.js'),
  path.join(__dirname, '..', 'index.js'),
  path.join(__dirname, '..', 'api', 'connector-api.js'),
  path.join(__dirname, '..', 'middleware', 'rate-limit.js')
];

// Results
const results = {
  rateLimitFound: false,
  rateLimitImportFound: false,
  rateLimitConfigured: false,
  rateLimitApplied: false,
  issues: []
};

// Check if express-rate-limit is installed
try {
  execSync('npm list express-rate-limit', { stdio: 'pipe' });
  results.rateLimitFound = true;
} catch (error) {
  results.issues.push('express-rate-limit is not installed. Run: npm install express-rate-limit --save');
}

// Check files for rate limiting configuration
for (const file of filesToCheck) {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    
    // Check for rate limit import
    if (content.includes('require(\'express-rate-limit\')') || 
        content.includes('require("express-rate-limit")') || 
        content.includes('from \'express-rate-limit\'') || 
        content.includes('from "express-rate-limit"')) {
      results.rateLimitImportFound = true;
    }
    
    // Check for rate limit configuration
    if (content.includes('new RateLimit') || 
        content.includes('rateLimit(') || 
        content.includes('= rateLimit({') || 
        content.includes('= new RateLimit({')) {
      results.rateLimitConfigured = true;
    }
    
    // Check if rate limit is applied to routes
    if (content.includes('app.use(') && 
        (content.includes('limiter') || content.includes('rateLimiter') || content.includes('rateLimit'))) {
      results.rateLimitApplied = true;
    }
  }
}

// Generate report
let report = '# Rate Limiting Configuration Check\n\n';

if (!results.rateLimitFound) {
  report += '❌ express-rate-limit is not installed\n';
} else {
  report += '✅ express-rate-limit is installed\n';
}

if (!results.rateLimitImportFound) {
  report += '❌ express-rate-limit import not found in any checked files\n';
} else {
  report += '✅ express-rate-limit import found\n';
}

if (!results.rateLimitConfigured) {
  report += '❌ Rate limiting is not configured\n';
} else {
  report += '✅ Rate limiting is configured\n';
}

if (!results.rateLimitApplied) {
  report += '❌ Rate limiting is not applied to routes\n';
} else {
  report += '✅ Rate limiting is applied to routes\n';
}

if (results.issues.length > 0) {
  report += '\n## Issues\n\n';
  for (const issue of results.issues) {
    report += `- ${issue}\n`;
  }
}

// Recommended configuration
report += '\n## Recommended Rate Limiting Configuration\n\n';
report += '```javascript\nconst rateLimit = require(\'express-rate-limit\');\n\n';
report += '// Basic rate limiter for all routes\n';
report += 'const globalLimiter = rateLimit({\n';
report += '  windowMs: 15 * 60 * 1000, // 15 minutes\n';
report += '  max: 100, // limit each IP to 100 requests per windowMs\n';
report += '  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers\n';
report += '  legacyHeaders: false, // Disable the `X-RateLimit-*` headers\n';
report += '  message: \'Too many requests from this IP, please try again after 15 minutes\'\n';
report += '});\n\n';
report += '// More strict limiter for authentication routes\n';
report += 'const authLimiter = rateLimit({\n';
report += '  windowMs: 60 * 60 * 1000, // 1 hour\n';
report += '  max: 5, // limit each IP to 5 requests per windowMs\n';
report += '  standardHeaders: true,\n';
report += '  legacyHeaders: false,\n';
report += '  message: \'Too many login attempts from this IP, please try again after an hour\'\n';
report += '});\n\n';
report += '// Apply rate limiters\n';
report += 'app.use(globalLimiter); // Apply to all routes\n';
report += 'app.use(\'/api/auth\', authLimiter); // Apply to authentication routes\n```\n';

// Additional recommendations
report += '\n## Additional Recommendations\n\n';
report += '1. **Use Redis Store**: For production environments with multiple server instances, use a Redis store to share rate limit data across instances.\n';
report += '```javascript\nconst RedisStore = require(\'rate-limit-redis\');\n';
report += 'const redis = require(\'redis\');\n\n';
report += 'const redisClient = redis.createClient({\n';
report += '  host: process.env.REDIS_HOST,\n';
report += '  port: process.env.REDIS_PORT,\n';
report += '  password: process.env.REDIS_PASSWORD\n';
report += '});\n\n';
report += 'const limiter = rateLimit({\n';
report += '  store: new RedisStore({\n';
report += '    client: redisClient,\n';
report += '    prefix: \'rate-limit:\'\n';
report += '  }),\n';
report += '  windowMs: 15 * 60 * 1000,\n';
report += '  max: 100\n';
report += '});\n```\n\n';
report += '2. **Different Limits for Different Routes**: Apply different rate limits to different routes based on their sensitivity and expected usage patterns.\n\n';
report += '3. **Whitelist Trusted IPs**: Consider whitelisting trusted IPs or providing higher limits for authenticated users.\n\n';
report += '4. **Monitor Rate Limiting**: Log rate limiting events and set up alerts for unusual patterns that might indicate abuse.\n';

// Output report
console.log(report);

// Exit with appropriate code
if (!results.rateLimitFound || !results.rateLimitConfigured || !results.rateLimitApplied) {
  process.exit(1);
} else {
  process.exit(0);
}
