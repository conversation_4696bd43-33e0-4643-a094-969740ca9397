{"displayName": "NovaConnect UAC - Tenant ${TENANT_ID}", "gridLayout": {"columns": 2, "widgets": [{"title": "API Request Rate", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/http_requests_total\" resource.type=\"k8s_container\" resource.label.\"namespace_name\"=\"tenant-${TENANT_ID}\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"method\"", "metric.label.\"status\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Requests/second", "scale": "LINEAR"}}}, {"title": "CPU Usage", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"kubernetes.io/container/cpu/utilization\" resource.type=\"k8s_container\" resource.label.\"namespace_name\"=\"tenant-${TENANT_ID}\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["resource.label.\"pod_name\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "CPU Utilization", "scale": "LINEAR"}}}, {"title": "Memory Usage", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"kubernetes.io/container/memory/used_bytes\" resource.type=\"k8s_container\" resource.label.\"namespace_name\"=\"tenant-${TENANT_ID}\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["resource.label.\"pod_name\""]}}, "unitOverride": "By"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Memory Usage (bytes)", "scale": "LINEAR"}}}, {"title": "API Latency", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/http_request_duration_seconds\" resource.type=\"k8s_container\" resource.label.\"namespace_name\"=\"tenant-${TENANT_ID}\"", "aggregation": {"perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.\"path\""]}}, "unitOverride": "s"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Latency (seconds)", "scale": "LINEAR"}}}, {"title": "Error Rate", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/http_errors_total\" resource.type=\"k8s_container\" resource.label.\"namespace_name\"=\"tenant-${TENANT_ID}\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"status\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Errors/second", "scale": "LINEAR"}}}, {"title": "GRC Checks", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/grc_checks_total\" resource.type=\"k8s_container\" resource.label.\"namespace_name\"=\"tenant-${TENANT_ID}\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"framework\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Checks/second", "scale": "LINEAR"}}}, {"title": "Connector Executions", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/connector_executions_total\" resource.type=\"k8s_container\" resource.label.\"namespace_name\"=\"tenant-${TENANT_ID}\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"connector_type\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "Executions/second", "scale": "LINEAR"}}}, {"title": "Resource Usage Summary", "pieChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"kubernetes.io/container/cpu/request_utilization\" resource.type=\"k8s_container\" resource.label.\"namespace_name\"=\"tenant-${TENANT_ID}\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["resource.label.\"pod_name\""]}}, "unitOverride": "1"}}]}}]}}