const controllers = require('../../../../apis/esg/targets/controllers');
const models = require('../../../../apis/esg/targets/models');

// Mock the models
jest.mock('../../../../apis/esg/targets/models', () => ({
  esgTargets: [
    {
      id: 'esg-t-12345678',
      name: 'Carbon Neutrality',
      description: 'Achieve carbon neutrality across all operations',
      category: 'environmental',
      type: 'reduction',
      metric: 'carbon-emissions',
      unit: 'metric-tons',
      baseline: {
        value: 10000,
        year: 2020
      },
      target: {
        value: 0,
        year: 2030
      },
      milestones: [
        {
          id: 'milestone-1',
          name: '50% Reduction',
          description: 'Achieve 50% reduction in carbon emissions',
          value: 5000,
          year: 2025,
          status: 'in-progress'
        }
      ],
      status: 'in-progress',
      progress: 0.2,
      owner: 'Sustainability Team',
      contributors: ['Operations', 'Facilities'],
      relatedFrameworks: ['GRI', 'SASB'],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'esg-t-87654321',
      name: 'Diversity in Leadership',
      description: 'Increase diversity in leadership positions',
      category: 'social',
      type: 'improvement',
      metric: 'diversity-percentage',
      unit: 'percentage',
      baseline: {
        value: 25,
        year: 2022
      },
      target: {
        value: 50,
        year: 2025
      },
      milestones: [],
      status: 'not-started',
      progress: 0,
      owner: 'HR Team',
      contributors: ['Executive Leadership'],
      relatedFrameworks: ['GRI'],
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-01T00:00:00Z'
    }
  ],
  targetGroups: [
    {
      id: 'group-12345',
      name: 'Environmental Targets 2030',
      description: 'Group of environmental targets to be achieved by 2030',
      category: 'environmental',
      targets: ['esg-t-12345678'],
      owner: 'Sustainability Team',
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-01-15T00:00:00Z'
    }
  ]
}));

// Mock Express request and response
const mockRequest = (params = {}, query = {}, body = {}) => ({
  params,
  query,
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('ESG Targets Controllers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getTargets', () => {
    it('should return all targets with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getTargets(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgTargets,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter targets by category', () => {
      const req = mockRequest({}, { category: 'environmental' });
      const res = mockResponse();

      controllers.getTargets(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.esgTargets[0]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter targets by status', () => {
      const req = mockRequest({}, { status: 'not-started' });
      const res = mockResponse();

      controllers.getTargets(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.esgTargets[1]],
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should handle errors', () => {
      const req = mockRequest();
      const res = mockResponse();

      // Force an error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      jest.spyOn(Array.prototype, 'filter').mockImplementation(() => {
        throw new Error('Test error');
      });

      controllers.getTargets(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Internal Server Error',
        message: 'Test error'
      });

      // Restore console.error
      console.error.mockRestore();
    });
  });

  describe('getTargetById', () => {
    it('should return a specific target by ID', () => {
      const req = mockRequest({ id: 'esg-t-12345678' });
      const res = mockResponse();

      controllers.getTargetById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgTargets[0]
      });
    });

    it('should return 404 if target not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getTargetById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG target with ID non-existent-id not found'
      });
    });
  });

  describe('createTarget', () => {
    it('should create a new target', () => {
      const newTarget = {
        name: 'Water Conservation',
        description: 'Reduce water usage across all facilities',
        category: 'environmental',
        type: 'reduction',
        metric: 'water-usage',
        unit: 'gallons',
        baseline: {
          value: 1000000,
          year: 2022
        },
        target: {
          value: 750000,
          year: 2025
        },
        status: 'not-started',
        owner: 'Facilities Team'
      };

      const req = mockRequest({}, {}, newTarget);
      const res = mockResponse();

      // Mock Date and UUID
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => 1577836800000); // 2020-01-01T00:00:00Z
      global.Date = jest.fn(() => ({
        toISOString: () => '2020-01-01T00:00:00Z'
      }));
      jest.mock('uuid', () => ({
        v4: jest.fn(() => '00000000-0000-0000-0000-000000000000')
      }));

      controllers.createTarget(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Water Conservation',
          category: 'environmental'
        }),
        message: 'ESG target created successfully'
      }));

      // Restore Date
      Date.now = originalDateNow;
    });
  });

  describe('updateTarget', () => {
    it('should update an existing target', () => {
      const updatedTarget = {
        name: 'Updated Target Name',
        progress: 0.3
      };

      const req = mockRequest({ id: 'esg-t-12345678' }, {}, updatedTarget);
      const res = mockResponse();

      controllers.updateTarget(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'esg-t-12345678',
          name: 'Updated Target Name',
          progress: 0.3
        }),
        message: 'ESG target updated successfully'
      }));
    });

    it('should return 404 if target not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateTarget(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG target with ID non-existent-id not found'
      });
    });
  });

  describe('deleteTarget', () => {
    it('should delete an existing target', () => {
      const req = mockRequest({ id: 'esg-t-12345678' });
      const res = mockResponse();

      controllers.deleteTarget(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'ESG target deleted successfully'
      });
    });

    it('should return 404 if target not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteTarget(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG target with ID non-existent-id not found'
      });
    });
  });

  describe('getTargetMilestones', () => {
    it('should return milestones for a specific target', () => {
      const req = mockRequest({ id: 'esg-t-12345678' });
      const res = mockResponse();

      controllers.getTargetMilestones(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgTargets[0].milestones
      });
    });

    it('should return 404 if target not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getTargetMilestones(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG target with ID non-existent-id not found'
      });
    });
  });

  describe('getTargetMilestoneById', () => {
    it('should return a specific milestone by ID', () => {
      const req = mockRequest({ id: 'esg-t-12345678', milestoneId: 'milestone-1' });
      const res = mockResponse();

      controllers.getTargetMilestoneById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgTargets[0].milestones[0]
      });
    });

    it('should return 404 if milestone not found', () => {
      const req = mockRequest({ id: 'esg-t-12345678', milestoneId: 'non-existent-id' });
      const res = mockResponse();

      controllers.getTargetMilestoneById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Target milestone with ID non-existent-id not found'
      });
    });
  });

  describe('addTargetMilestone', () => {
    it('should add a new milestone to a target', () => {
      const newMilestone = {
        name: 'New Milestone',
        description: 'Description for new milestone',
        value: 2500,
        year: 2027,
        status: 'not-started'
      };

      const req = mockRequest({ id: 'esg-t-12345678' }, {}, newMilestone);
      const res = mockResponse();

      controllers.addTargetMilestone(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'New Milestone',
          value: 2500
        }),
        message: 'Target milestone added successfully'
      }));
    });

    it('should return 404 if target not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'New Milestone' });
      const res = mockResponse();

      controllers.addTargetMilestone(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG target with ID non-existent-id not found'
      });
    });
  });

  describe('updateTargetMilestone', () => {
    it('should update an existing milestone', () => {
      const updatedMilestone = {
        name: 'Updated Milestone Name',
        status: 'completed'
      };

      const req = mockRequest({ id: 'esg-t-12345678', milestoneId: 'milestone-1' }, {}, updatedMilestone);
      const res = mockResponse();

      controllers.updateTargetMilestone(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'milestone-1',
          name: 'Updated Milestone Name',
          status: 'completed'
        }),
        message: 'Target milestone updated successfully'
      }));
    });

    it('should return 404 if milestone not found', () => {
      const req = mockRequest({ id: 'esg-t-12345678', milestoneId: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateTargetMilestone(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Target milestone with ID non-existent-id not found'
      });
    });
  });

  describe('removeTargetMilestone', () => {
    it('should remove a milestone from a target', () => {
      const req = mockRequest({ id: 'esg-t-12345678', milestoneId: 'milestone-1' });
      const res = mockResponse();

      controllers.removeTargetMilestone(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Target milestone removed successfully'
      });
    });

    it('should return 404 if milestone not found', () => {
      const req = mockRequest({ id: 'esg-t-12345678', milestoneId: 'non-existent-id' });
      const res = mockResponse();

      controllers.removeTargetMilestone(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Target milestone with ID non-existent-id not found'
      });
    });
  });

  describe('getTargetGroups', () => {
    it('should return all target groups', () => {
      const req = mockRequest();
      const res = mockResponse();

      controllers.getTargetGroups(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.targetGroups
      });
    });
  });

  describe('getTargetGroupById', () => {
    it('should return a specific target group by ID', () => {
      const req = mockRequest({ id: 'group-12345' });
      const res = mockResponse();

      controllers.getTargetGroupById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.targetGroups[0]
      });
    });

    it('should return 404 if target group not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getTargetGroupById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Target group with ID non-existent-id not found'
      });
    });
  });

  describe('createTargetGroup', () => {
    it('should create a new target group', () => {
      const newGroup = {
        name: 'Social Targets 2025',
        description: 'Group of social targets to be achieved by 2025',
        category: 'social',
        targets: ['esg-t-87654321'],
        owner: 'HR Team'
      };

      const req = mockRequest({}, {}, newGroup);
      const res = mockResponse();

      controllers.createTargetGroup(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Social Targets 2025',
          category: 'social'
        }),
        message: 'Target group created successfully'
      }));
    });
  });

  describe('updateTargetGroup', () => {
    it('should update an existing target group', () => {
      const updatedGroup = {
        name: 'Updated Group Name',
        targets: ['esg-t-12345678', 'esg-t-87654321']
      };

      const req = mockRequest({ id: 'group-12345' }, {}, updatedGroup);
      const res = mockResponse();

      controllers.updateTargetGroup(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'group-12345',
          name: 'Updated Group Name',
          targets: ['esg-t-12345678', 'esg-t-87654321']
        }),
        message: 'Target group updated successfully'
      }));
    });

    it('should return 404 if target group not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateTargetGroup(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Target group with ID non-existent-id not found'
      });
    });
  });

  describe('deleteTargetGroup', () => {
    it('should delete an existing target group', () => {
      const req = mockRequest({ id: 'group-12345' });
      const res = mockResponse();

      controllers.deleteTargetGroup(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Target group deleted successfully'
      });
    });

    it('should return 404 if target group not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteTargetGroup(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Target group with ID non-existent-id not found'
      });
    });
  });
});
