#!/usr/bin/env python3
"""
Nova Component Health Monitor
Tracks and reports on the health of Nova components
"""

import os
import json
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import re


@dataclass
class HealthMetric:
    """Individual health metric"""
    name: str
    value: float
    max_value: float
    status: str  # "healthy", "warning", "critical"
    description: str


@dataclass
class ComponentHealth:
    """Complete health assessment for a component"""
    component: str
    overall_score: float
    status: str
    metrics: List[HealthMetric]
    last_updated: datetime
    recommendations: List[str]


class NovaHealthMonitor:
    """Monitor health of Nova components"""
    
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = Path(workspace_path)
        self.health_data: List[ComponentHealth] = []
    
    def assess_all_components(self) -> Dict[str, Any]:
        """Assess health of all Nova components"""
        print("🏥 Assessing Nova component health...")
        
        components = self._discover_components()
        
        for component in components:
            print(f"   📊 Analyzing {component['name']}...")
            health = self._assess_component_health(component)
            self.health_data.append(health)
        
        return self._generate_health_report()
    
    def _discover_components(self) -> List[Dict[str, Any]]:
        """Discover Nova components in workspace"""
        components = []
        
        # Check src/ directory
        src_path = self.workspace_path / "src"
        if src_path.exists():
            for item in src_path.iterdir():
                if item.is_dir() and item.name.lower().startswith("nova"):
                    components.append({
                        "name": item.name,
                        "path": item,
                        "type": self._detect_component_type(item),
                        "language": self._detect_language(item)
                    })
        
        # Check root level Nova components
        for pattern in ["nova*", "Nova*"]:
            for item in self.workspace_path.glob(pattern):
                if item.is_dir() and item.name not in [c["name"] for c in components]:
                    components.append({
                        "name": item.name,
                        "path": item,
                        "type": self._detect_component_type(item),
                        "language": self._detect_language(item)
                    })
        
        return components
    
    def _detect_component_type(self, path: Path) -> str:
        """Detect component type from directory structure"""
        if "auth" in path.name.lower() or "shield" in path.name.lower():
            return "Security"
        elif "pulse" in path.name.lower() or "monitor" in path.name.lower():
            return "Telemetry"
        elif "core" in path.name.lower():
            return "Infrastructure"
        elif "vision" in path.name.lower() or "ui" in path.name.lower():
            return "UI/Dashboard"
        elif "ai" in path.name.lower() or "sentient" in path.name.lower():
            return "AI/ML"
        else:
            return "Unknown"
    
    def _detect_language(self, path: Path) -> str:
        """Detect primary language from files"""
        if (path / "package.json").exists() or any(path.glob("*.js")):
            return "javascript"
        elif (path / "requirements.txt").exists() or any(path.glob("*.py")):
            return "python"
        elif (path / "go.mod").exists() or any(path.glob("*.go")):
            return "go"
        elif (path / "Cargo.toml").exists() or any(path.glob("*.rs")):
            return "rust"
        else:
            return "unknown"
    
    def _assess_component_health(self, component: Dict[str, Any]) -> ComponentHealth:
        """Assess health of a single component"""
        path = component["path"]
        metrics = []
        recommendations = []
        
        # 1. Code Quality Metrics
        code_quality = self._assess_code_quality(path)
        metrics.append(code_quality)
        
        # 2. Documentation Coverage
        doc_coverage = self._assess_documentation(path)
        metrics.append(doc_coverage)
        
        # 3. Test Coverage
        test_coverage = self._assess_test_coverage(path)
        metrics.append(test_coverage)
        
        # 4. Dependency Health
        dependency_health = self._assess_dependencies(path, component["language"])
        metrics.append(dependency_health)
        
        # 5. Security Score
        security_score = self._assess_security(path)
        metrics.append(security_score)
        
        # 6. Performance Indicators
        performance_score = self._assess_performance(path)
        metrics.append(performance_score)
        
        # Calculate overall score
        overall_score = sum(m.value for m in metrics) / len(metrics)
        
        # Determine status
        if overall_score >= 0.8:
            status = "healthy"
        elif overall_score >= 0.6:
            status = "warning"
        else:
            status = "critical"
        
        # Generate recommendations
        for metric in metrics:
            if metric.status in ["warning", "critical"]:
                recommendations.append(f"Improve {metric.name}: {metric.description}")
        
        return ComponentHealth(
            component=component["name"],
            overall_score=overall_score,
            status=status,
            metrics=metrics,
            last_updated=datetime.now(),
            recommendations=recommendations
        )
    
    def _assess_code_quality(self, path: Path) -> HealthMetric:
        """Assess code quality metrics"""
        
        # Count lines of code
        code_files = list(path.rglob("*.py")) + list(path.rglob("*.js")) + list(path.rglob("*.ts"))
        
        if not code_files:
            return HealthMetric("Code Quality", 0.0, 1.0, "critical", "No code files found")
        
        total_lines = 0
        comment_lines = 0
        
        for file_path in code_files:
            try:
                content = file_path.read_text()
                lines = content.split('\n')
                total_lines += len([l for l in lines if l.strip()])
                comment_lines += len([l for l in lines if l.strip().startswith('#') or l.strip().startswith('//')])
            except:
                pass
        
        # Calculate comment ratio
        comment_ratio = comment_lines / total_lines if total_lines > 0 else 0
        
        # Score based on comment ratio and code size
        if comment_ratio >= 0.2 and total_lines >= 100:
            score = 0.9
            status = "healthy"
            desc = f"{total_lines} lines, {comment_ratio:.1%} comments"
        elif comment_ratio >= 0.1 and total_lines >= 50:
            score = 0.7
            status = "warning"
            desc = f"{total_lines} lines, {comment_ratio:.1%} comments - needs more documentation"
        else:
            score = 0.4
            status = "critical"
            desc = f"{total_lines} lines, {comment_ratio:.1%} comments - insufficient code/docs"
        
        return HealthMetric("Code Quality", score, 1.0, status, desc)
    
    def _assess_documentation(self, path: Path) -> HealthMetric:
        """Assess documentation coverage"""
        
        readme_exists = (path / "README.md").exists()
        api_docs = len(list(path.rglob("*api*"))) > 0
        inline_docs = len(list(path.rglob("*.md"))) > 1  # More than just README
        
        score = 0.0
        if readme_exists:
            score += 0.5
        if api_docs:
            score += 0.3
        if inline_docs:
            score += 0.2
        
        if score >= 0.8:
            status = "healthy"
            desc = "Comprehensive documentation"
        elif score >= 0.5:
            status = "warning"
            desc = "Basic documentation present"
        else:
            status = "critical"
            desc = "Insufficient documentation"
        
        return HealthMetric("Documentation", score, 1.0, status, desc)
    
    def _assess_test_coverage(self, path: Path) -> HealthMetric:
        """Assess test coverage"""
        
        test_files = list(path.rglob("*test*")) + list(path.rglob("*spec*"))
        code_files = list(path.rglob("*.py")) + list(path.rglob("*.js")) + list(path.rglob("*.ts"))
        
        if not code_files:
            return HealthMetric("Test Coverage", 0.0, 1.0, "critical", "No code files")
        
        test_ratio = len(test_files) / len(code_files) if code_files else 0
        
        if test_ratio >= 0.3:
            score = 0.9
            status = "healthy"
            desc = f"{len(test_files)} test files for {len(code_files)} code files"
        elif test_ratio >= 0.1:
            score = 0.6
            status = "warning"
            desc = f"{len(test_files)} test files - needs more tests"
        else:
            score = 0.3
            status = "critical"
            desc = f"{len(test_files)} test files - insufficient testing"
        
        return HealthMetric("Test Coverage", score, 1.0, status, desc)
    
    def _assess_dependencies(self, path: Path, language: str) -> HealthMetric:
        """Assess dependency health"""
        
        if language == "python":
            req_file = path / "requirements.txt"
            if req_file.exists():
                score = 0.8
                status = "healthy"
                desc = "Python dependencies managed"
            else:
                score = 0.5
                status = "warning"
                desc = "No requirements.txt found"
        elif language == "javascript":
            pkg_file = path / "package.json"
            if pkg_file.exists():
                score = 0.8
                status = "healthy"
                desc = "Node.js dependencies managed"
            else:
                score = 0.5
                status = "warning"
                desc = "No package.json found"
        else:
            score = 0.6
            status = "warning"
            desc = f"Unknown dependency management for {language}"
        
        return HealthMetric("Dependencies", score, 1.0, status, desc)
    
    def _assess_security(self, path: Path) -> HealthMetric:
        """Assess security indicators"""
        
        # Look for security-related files and patterns
        security_files = list(path.rglob("*security*")) + list(path.rglob("*auth*"))
        
        # Check for common security patterns in code
        security_patterns = 0
        code_files = list(path.rglob("*.py")) + list(path.rglob("*.js"))
        
        for file_path in code_files[:5]:  # Sample first 5 files
            try:
                content = file_path.read_text().lower()
                if any(pattern in content for pattern in ["jwt", "auth", "security", "encrypt", "hash"]):
                    security_patterns += 1
            except:
                pass
        
        if security_files or security_patterns >= 2:
            score = 0.8
            status = "healthy"
            desc = "Security measures implemented"
        elif security_patterns >= 1:
            score = 0.6
            status = "warning"
            desc = "Basic security measures"
        else:
            score = 0.4
            status = "critical"
            desc = "No obvious security measures"
        
        return HealthMetric("Security", score, 1.0, status, desc)
    
    def _assess_performance(self, path: Path) -> HealthMetric:
        """Assess performance indicators"""
        
        # Look for performance-related patterns
        perf_indicators = 0
        code_files = list(path.rglob("*.py")) + list(path.rglob("*.js"))
        
        for file_path in code_files[:5]:  # Sample first 5 files
            try:
                content = file_path.read_text().lower()
                if any(pattern in content for pattern in ["cache", "optimize", "performance", "async", "parallel"]):
                    perf_indicators += 1
            except:
                pass
        
        if perf_indicators >= 3:
            score = 0.9
            status = "healthy"
            desc = "Performance optimizations present"
        elif perf_indicators >= 1:
            score = 0.7
            status = "warning"
            desc = "Some performance considerations"
        else:
            score = 0.5
            status = "warning"
            desc = "Limited performance optimization"
        
        return HealthMetric("Performance", score, 1.0, status, desc)
    
    def _generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report"""
        
        if not self.health_data:
            return {"error": "No health data available"}
        
        # Overall statistics
        total_components = len(self.health_data)
        healthy_components = sum(1 for h in self.health_data if h.status == "healthy")
        warning_components = sum(1 for h in self.health_data if h.status == "warning")
        critical_components = sum(1 for h in self.health_data if h.status == "critical")
        
        avg_score = sum(h.overall_score for h in self.health_data) / total_components
        
        # Top issues
        all_recommendations = []
        for health in self.health_data:
            all_recommendations.extend(health.recommendations)
        
        return {
            "summary": {
                "total_components": total_components,
                "healthy_components": healthy_components,
                "warning_components": warning_components,
                "critical_components": critical_components,
                "average_health_score": avg_score,
                "health_percentage": healthy_components / total_components * 100
            },
            "components": [
                {
                    "name": h.component,
                    "score": h.overall_score,
                    "status": h.status,
                    "metrics": [
                        {
                            "name": m.name,
                            "value": m.value,
                            "status": m.status,
                            "description": m.description
                        }
                        for m in h.metrics
                    ],
                    "recommendations": h.recommendations,
                    "last_updated": h.last_updated.isoformat()
                }
                for h in self.health_data
            ],
            "top_recommendations": list(set(all_recommendations))[:10]
        }


def main():
    import sys
    
    workspace = sys.argv[1] if len(sys.argv) > 1 else "."
    
    monitor = NovaHealthMonitor(workspace)
    report = monitor.assess_all_components()
    
    print("\n" + "="*60)
    print("NOVA COMPONENT HEALTH REPORT")
    print("="*60)
    print(json.dumps(report, indent=2))
    
    # Summary
    summary = report["summary"]
    print(f"\n📊 Health Summary:")
    print(f"   🟢 Healthy: {summary['healthy_components']}")
    print(f"   🟡 Warning: {summary['warning_components']}")
    print(f"   🔴 Critical: {summary['critical_components']}")
    print(f"   📈 Average Score: {summary['average_health_score']:.2f}")
    print(f"   💚 Health Rate: {summary['health_percentage']:.1f}%")


if __name__ == "__main__":
    main()
