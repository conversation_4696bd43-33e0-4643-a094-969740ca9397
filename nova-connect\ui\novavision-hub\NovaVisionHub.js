/**
 * NovaVision Integration Hub
 * 
 * This module serves as a central integration point for connecting NovaVision with all Nova components.
 * It provides adapters for each Nova component and orchestrates the flow of data and UI schemas between them.
 */

import NovaVision from '@novafuse/uui-core';
import { NovaVisionIntegration } from '../novavision-integration';

// Import Nova component adapters
import NovaConnectAdapter from './adapters/NovaConnectAdapter';
import NovaCoreAdapter from './adapters/NovaCoreAdapter';
import NovaShieldAdapter from './adapters/NovaShieldAdapter';
import NovaTrackAdapter from './adapters/NovaTrackAdapter';
import NovaGraphAdapter from './adapters/NovaGraphAdapter';
import NovaDNAAdapter from './adapters/NovaDNAAdapter';
import NovaPulseAdapter from './adapters/NovaPulseAdapter';
import NovaThinkAdapter from './adapters/NovaThinkAdapter';
import NovaFlowXAdapter from './adapters/NovaFlowXAdapter';
import NovaProofAdapter from './adapters/NovaProofAdapter';
import NovaStoreAdapter from './adapters/NovaStoreAdapter';

/**
 * NovaVision Hub class
 * 
 * This class orchestrates the integration between NovaVision and all Nova components.
 */
class NovaVisionHub {
  /**
   * Constructor
   * 
   * @param {Object} options - Hub options
   * @param {Object} options.novaVision - NovaVision instance
   * @param {Object} options.novaConnect - NovaConnect instance
   * @param {Object} options.novaCore - NovaCore instance
   * @param {Object} options.novaShield - NovaShield instance
   * @param {Object} options.novaTrack - NovaTrack instance
   * @param {Object} options.novaGraph - NovaGraph instance
   * @param {Object} options.novaDNA - NovaDNA instance
   * @param {Object} options.novaPulse - NovaPulse instance
   * @param {Object} options.novaThink - NovaThink instance
   * @param {Object} options.novaFlowX - NovaFlowX instance
   * @param {Object} options.novaProof - NovaProof instance
   * @param {Object} options.novaStore - NovaStore instance
   * @param {boolean} [options.enableLogging=false] - Whether to enable logging
   * @param {boolean} [options.useWorkers=false] - Whether to use web workers
   * @param {Array} [options.subscribeTopics=[]] - Topics to subscribe to
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: false,
      useWorkers: false,
      subscribeTopics: [
        'novaShield.threatDetected',
        'novaShield.threatAnalyzed',
        'novaTrack.complianceChanged',
        'novaTrack.regulationUpdated',
        'novaCore.decisionMade',
        'novaCore.policyApplied',
        'novaPulse.alertTriggered',
        'novaThink.insightGenerated',
        'novaFlowX.workflowCompleted',
        'novaProof.evidenceCollected',
        'novaDNA.identityVerified'
      ],
      ...options
    };
    
    this.novaVision = options.novaVision || new NovaVision({
      theme: 'default',
      responsive: true,
      accessibilityLevel: 'AA',
      regulationAware: true,
      aiOptimization: true,
      consistencyEnforcement: true
    });
    
    this.logger = options.logger || console;
    
    // Initialize NovaVision integration
    this.novaVisionIntegration = new NovaVisionIntegration({
      novaVision: this.novaVision,
      enableLogging: this.options.enableLogging,
      useWorkers: this.options.useWorkers
    });
    
    // Initialize adapters for each Nova component
    this.adapters = {};
    
    if (options.novaConnect) {
      this.adapters.novaConnect = new NovaConnectAdapter({
        novaConnect: options.novaConnect,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaCore) {
      this.adapters.novaCore = new NovaCoreAdapter({
        novaCore: options.novaCore,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaShield) {
      this.adapters.novaShield = new NovaShieldAdapter({
        novaShield: options.novaShield,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaTrack) {
      this.adapters.novaTrack = new NovaTrackAdapter({
        novaTrack: options.novaTrack,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaGraph) {
      this.adapters.novaGraph = new NovaGraphAdapter({
        novaGraph: options.novaGraph,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaDNA) {
      this.adapters.novaDNA = new NovaDNAAdapter({
        novaDNA: options.novaDNA,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaPulse) {
      this.adapters.novaPulse = new NovaPulseAdapter({
        novaPulse: options.novaPulse,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaThink) {
      this.adapters.novaThink = new NovaThinkAdapter({
        novaThink: options.novaThink,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaFlowX) {
      this.adapters.novaFlowX = new NovaFlowXAdapter({
        novaFlowX: options.novaFlowX,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaProof) {
      this.adapters.novaProof = new NovaProofAdapter({
        novaProof: options.novaProof,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    if (options.novaStore) {
      this.adapters.novaStore = new NovaStoreAdapter({
        novaStore: options.novaStore,
        novaVision: this.novaVision,
        enableLogging: this.options.enableLogging,
        subscribeTopics: this.options.subscribeTopics
      });
    }
    
    this.logger.info('NovaVision Hub initialized');
  }
  
  /**
   * Initialize the hub
   * 
   * @returns {Promise} - Promise that resolves when initialization is complete
   */
  async initialize() {
    if (this.options.enableLogging) {
      this.logger.info('Initializing NovaVision Hub...');
    }
    
    try {
      // Initialize NovaVision integration
      await this.novaVisionIntegration.initialize();
      
      // Initialize all adapters
      const adapterPromises = Object.values(this.adapters).map(adapter => adapter.initialize());
      await Promise.all(adapterPromises);
      
      if (this.options.enableLogging) {
        this.logger.info('NovaVision Hub initialized successfully');
      }
      
      return true;
    } catch (error) {
      this.logger.error('Error initializing NovaVision Hub', error);
      throw error;
    }
  }
  
  /**
   * Get UI schema for a specific Nova component
   * 
   * @param {string} component - Nova component name
   * @param {string} schemaType - Schema type
   * @param {Object} options - Schema options
   * @returns {Promise<Object>} - UI schema
   */
  async getUISchema(component, schemaType, options = {}) {
    if (this.options.enableLogging) {
      this.logger.info(`Getting UI schema for ${component}.${schemaType}...`);
    }
    
    try {
      const adapter = this.adapters[component];
      
      if (!adapter) {
        throw new Error(`Adapter for ${component} not found`);
      }
      
      const schema = await adapter.getUISchema(schemaType, options);
      
      return schema;
    } catch (error) {
      this.logger.error(`Error getting UI schema for ${component}.${schemaType}`, error);
      throw error;
    }
  }
  
  /**
   * Get integrated dashboard UI schema
   * 
   * @param {Object} options - Dashboard options
   * @returns {Promise<Object>} - Dashboard UI schema
   */
  async getIntegratedDashboardSchema(options = {}) {
    if (this.options.enableLogging) {
      this.logger.info('Getting integrated dashboard schema...');
    }
    
    try {
      // Get UI schemas from all available adapters
      const schemaPromises = Object.entries(this.adapters).map(async ([component, adapter]) => {
        try {
          const schema = await adapter.getDashboardSchema(options);
          return { component, schema };
        } catch (error) {
          this.logger.warn(`Error getting dashboard schema for ${component}`, error);
          return null;
        }
      });
      
      const schemas = (await Promise.all(schemaPromises)).filter(Boolean);
      
      // Create integrated dashboard schema
      const dashboardSchema = {
        type: 'dashboard',
        title: 'NovaFuse Integrated Dashboard',
        description: 'Integrated dashboard for all Nova components',
        layout: {
          type: 'grid',
          columns: 3,
          rows: Math.ceil(schemas.length / 3)
        },
        components: schemas.map(({ component, schema }, index) => ({
          type: 'card',
          gridArea: `${Math.floor(index / 3) + 1} / ${(index % 3) + 1}`,
          header: schema.title || component,
          content: schema
        }))
      };
      
      return dashboardSchema;
    } catch (error) {
      this.logger.error('Error getting integrated dashboard schema', error);
      throw error;
    }
  }
  
  /**
   * Handle action from NovaVision
   * 
   * @param {string} action - Action name
   * @param {Object} data - Action data
   * @returns {Promise<Object>} - Action result
   */
  async handleAction(action, data) {
    if (this.options.enableLogging) {
      this.logger.info(`Handling action ${action}...`, data);
    }
    
    try {
      // Parse action name to determine component and action
      const [component, actionName] = action.split('.');
      
      const adapter = this.adapters[component];
      
      if (!adapter) {
        throw new Error(`Adapter for ${component} not found`);
      }
      
      const result = await adapter.handleAction(actionName, data);
      
      return result;
    } catch (error) {
      this.logger.error(`Error handling action ${action}`, error);
      throw error;
    }
  }
}

export default NovaVisionHub;
