{"id": "business-intelligence-workflow", "name": "Business Intelligence & Workflow Connector", "description": "Connector for business intelligence platforms and workflow automation systems", "version": "1.0.0", "category": "bi-workflow", "icon": "bi-workflow-icon.svg", "author": "NovaFuse", "website": "https://novafuse.io", "documentation": "https://docs.novafuse.io/connectors/bi-workflow", "supportEmail": "<EMAIL>", "authentication": {"type": "oauth2", "oauth2": {"authorizationUrl": "https://auth.example.com/oauth2/authorize", "tokenUrl": "https://auth.example.com/oauth2/token", "scopes": ["read:dashboards", "write:dashboards", "read:reports", "write:reports", "read:workflows", "write:workflows"], "refreshTokenUrl": "https://auth.example.com/oauth2/token"}, "fields": {"clientId": {"type": "string", "label": "Client ID", "required": true, "sensitive": false, "description": "OAuth 2.0 Client ID"}, "clientSecret": {"type": "string", "label": "Client Secret", "required": true, "sensitive": true, "description": "OAuth 2.0 Client Secret"}, "redirectUri": {"type": "string", "label": "Redirect URI", "required": true, "sensitive": false, "description": "OAuth 2.0 Redirect URI"}}}, "endpoints": [{"id": "listDashboards", "name": "List Dashboards", "description": "List all dashboards", "method": "GET", "url": "https://api.example.com/dashboards", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "folder": {"type": "string", "label": "Folder", "required": false, "description": "Filter by folder"}, "owner": {"type": "string", "label": "Owner", "required": false, "description": "Filter by owner"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "folder": {"type": "string", "description": "Filter by folder"}, "owner": {"type": "string", "description": "Filter by owner"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Dashboard ID"}, "name": {"type": "string", "description": "Dashboard name"}, "description": {"type": "string", "description": "Dashboard description"}, "folder": {"type": "string", "description": "Dashboard folder"}, "owner": {"type": "string", "description": "Dashboard owner"}, "url": {"type": "string", "description": "Dashboard URL"}, "createdAt": {"type": "string", "format": "date-time", "description": "Dashboard creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Dashboard last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getDashboard", "name": "Get Dashboard", "description": "Get a specific dashboard", "method": "GET", "url": "https://api.example.com/dashboards/{dashboardId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"dashboardId": {"type": "string", "label": "Dashboard ID", "required": true, "description": "ID of the dashboard to retrieve"}}, "inputSchema": {"type": "object", "properties": {"dashboardId": {"type": "string", "description": "ID of the dashboard to retrieve"}}, "required": ["dashboardId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Dashboard ID"}, "name": {"type": "string", "description": "Dashboard name"}, "description": {"type": "string", "description": "Dashboard description"}, "folder": {"type": "string", "description": "Dashboard folder"}, "owner": {"type": "string", "description": "Dashboard owner"}, "url": {"type": "string", "description": "Dashboard URL"}, "layout": {"type": "object", "description": "Dashboard layout configuration"}, "widgets": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Widget ID"}, "type": {"type": "string", "description": "Widget type"}, "title": {"type": "string", "description": "Widget title"}, "dataSource": {"type": "string", "description": "Widget data source"}, "config": {"type": "object", "description": "Widget configuration"}}}}, "filters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Filter ID"}, "field": {"type": "string", "description": "Filter field"}, "operator": {"type": "string", "description": "Filter operator"}, "value": {"type": "string", "description": "Filter value"}}}}, "permissions": {"type": "array", "items": {"type": "object", "properties": {"userId": {"type": "string", "description": "User ID"}, "role": {"type": "string", "description": "User role"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Dashboard creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Dashboard last update date"}}}}, {"id": "listReports", "name": "List Reports", "description": "List all reports", "method": "GET", "url": "https://api.example.com/reports", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "folder": {"type": "string", "label": "Folder", "required": false, "description": "Filter by folder"}, "owner": {"type": "string", "label": "Owner", "required": false, "description": "Filter by owner"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "folder": {"type": "string", "description": "Filter by folder"}, "owner": {"type": "string", "description": "Filter by owner"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Report ID"}, "name": {"type": "string", "description": "Report name"}, "description": {"type": "string", "description": "Report description"}, "folder": {"type": "string", "description": "Report folder"}, "owner": {"type": "string", "description": "Report owner"}, "url": {"type": "string", "description": "Report URL"}, "schedule": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Whether the report is scheduled"}, "frequency": {"type": "string", "description": "Report schedule frequency"}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Report creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Report last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getReport", "name": "Get Report", "description": "Get a specific report", "method": "GET", "url": "https://api.example.com/reports/{reportId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"reportId": {"type": "string", "label": "Report ID", "required": true, "description": "ID of the report to retrieve"}}, "inputSchema": {"type": "object", "properties": {"reportId": {"type": "string", "description": "ID of the report to retrieve"}}, "required": ["reportId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Report ID"}, "name": {"type": "string", "description": "Report name"}, "description": {"type": "string", "description": "Report description"}, "folder": {"type": "string", "description": "Report folder"}, "owner": {"type": "string", "description": "Report owner"}, "url": {"type": "string", "description": "Report URL"}, "query": {"type": "string", "description": "Report query"}, "parameters": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Parameter name"}, "type": {"type": "string", "description": "Parameter type"}, "defaultValue": {"type": "string", "description": "Parameter default value"}}}}, "schedule": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Whether the report is scheduled"}, "frequency": {"type": "string", "description": "Report schedule frequency"}, "recipients": {"type": "array", "items": {"type": "string", "description": "Report recipient email"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Report creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Report last update date"}}}}, {"id": "executeReport", "name": "Execute Report", "description": "Execute a report", "method": "POST", "url": "https://api.example.com/reports/{reportId}/execute", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"reportId": {"type": "string", "label": "Report ID", "required": true, "description": "ID of the report to execute"}}, "bodyParameters": {"parameters": {"type": "object", "label": "Parameters", "required": false, "description": "Report parameters"}, "format": {"type": "string", "label": "Format", "required": false, "default": "json", "enum": ["json", "csv", "excel", "pdf"], "description": "Output format"}}, "inputSchema": {"type": "object", "properties": {"reportId": {"type": "string", "description": "ID of the report to execute"}, "parameters": {"type": "object", "description": "Report parameters"}, "format": {"type": "string", "description": "Output format", "enum": ["json", "csv", "excel", "pdf"]}}, "required": ["reportId"]}, "outputSchema": {"type": "object", "properties": {"executionId": {"type": "string", "description": "Execution ID"}, "status": {"type": "string", "description": "Execution status", "enum": ["success", "failure", "in_progress"]}, "data": {"type": "array", "description": "Report data (if format is json)"}, "url": {"type": "string", "description": "URL to download the report (if format is not json)"}, "executedAt": {"type": "string", "format": "date-time", "description": "Execution time"}}}}, {"id": "listWorkflows", "name": "List Workflows", "description": "List all workflows", "method": "GET", "url": "https://api.example.com/workflows", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["active", "inactive", "draft"], "description": "Filter by workflow status"}, "category": {"type": "string", "label": "Category", "required": false, "description": "Filter by workflow category"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "status": {"type": "string", "description": "Filter by workflow status", "enum": ["active", "inactive", "draft"]}, "category": {"type": "string", "description": "Filter by workflow category"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Workflow ID"}, "name": {"type": "string", "description": "Workflow name"}, "description": {"type": "string", "description": "Workflow description"}, "status": {"type": "string", "description": "Workflow status", "enum": ["active", "inactive", "draft"]}, "category": {"type": "string", "description": "Workflow category"}, "trigger": {"type": "object", "properties": {"type": {"type": "string", "description": "Trigger type"}, "config": {"type": "object", "description": "Trigger configuration"}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Workflow creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Workflow last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getWorkflow", "name": "Get Workflow", "description": "Get a specific workflow", "method": "GET", "url": "https://api.example.com/workflows/{workflowId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"workflowId": {"type": "string", "label": "Workflow ID", "required": true, "description": "ID of the workflow to retrieve"}}, "inputSchema": {"type": "object", "properties": {"workflowId": {"type": "string", "description": "ID of the workflow to retrieve"}}, "required": ["workflowId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Workflow ID"}, "name": {"type": "string", "description": "Workflow name"}, "description": {"type": "string", "description": "Workflow description"}, "status": {"type": "string", "description": "Workflow status", "enum": ["active", "inactive", "draft"]}, "category": {"type": "string", "description": "Workflow category"}, "trigger": {"type": "object", "properties": {"type": {"type": "string", "description": "Trigger type"}, "config": {"type": "object", "description": "Trigger configuration"}}}, "steps": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Step ID"}, "name": {"type": "string", "description": "Step name"}, "type": {"type": "string", "description": "Step type"}, "config": {"type": "object", "description": "Step configuration"}, "nextSteps": {"type": "array", "items": {"type": "object", "properties": {"stepId": {"type": "string", "description": "Next step ID"}, "condition": {"type": "string", "description": "Condition for next step"}}}}}}}, "variables": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Variable name"}, "type": {"type": "string", "description": "Variable type"}, "defaultValue": {"type": "string", "description": "Variable default value"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Workflow creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Workflow last update date"}}}}, {"id": "executeWorkflow", "name": "Execute Workflow", "description": "Execute a workflow", "method": "POST", "url": "https://api.example.com/workflows/{workflowId}/execute", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"workflowId": {"type": "string", "label": "Workflow ID", "required": true, "description": "ID of the workflow to execute"}}, "bodyParameters": {"input": {"type": "object", "label": "Input", "required": false, "description": "Workflow input data"}, "async": {"type": "boolean", "label": "Async", "required": false, "default": true, "description": "Execute asynchronously"}}, "inputSchema": {"type": "object", "properties": {"workflowId": {"type": "string", "description": "ID of the workflow to execute"}, "input": {"type": "object", "description": "Workflow input data"}, "async": {"type": "boolean", "description": "Execute asynchronously"}}, "required": ["workflowId"]}, "outputSchema": {"type": "object", "properties": {"executionId": {"type": "string", "description": "Execution ID"}, "status": {"type": "string", "description": "Execution status", "enum": ["queued", "in_progress", "completed", "failed"]}, "startTime": {"type": "string", "format": "date-time", "description": "Execution start time"}, "output": {"type": "object", "description": "Workflow output (if async is false and execution completed)"}, "statusUrl": {"type": "string", "description": "URL to check execution status (if async is true)"}}}}]}