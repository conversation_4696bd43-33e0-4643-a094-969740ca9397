# Google Secret Manager integration
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: {{ .Release.Name }}-novafuse-uac-secrets-provider
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
spec:
  provider: gcp
  parameters:
    secrets: |
      - resourceName: projects/{{ .Values.gcpProjectId }}/secrets/novafuse-api-key/versions/latest
        path: api-key
      - resourceName: projects/{{ .Values.gcpProjectId }}/secrets/novafuse-jwt-secret/versions/latest
        path: jwt-secret
      - resourceName: projects/{{ .Values.gcpProjectId }}/secrets/novafuse-csrf-secret/versions/latest
        path: csrf-secret
---
# Fallback Secret for environments without Secret Manager
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-novafuse-uac-secrets
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
type: Opaque
data:
  api-key: {{ .Values.apiKey | b64enc | quote }}
  jwt-secret: {{ .Values.jwtSecret | b64enc | quote }}
  csrf-secret: {{ .Values.csrfSecret | b64enc | quote }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-novafuse-uac-mongodb
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
type: Opaque
data:
  uri: {{ .Values.mongodb.uri | b64enc | quote }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Release.Name }}-novafuse-uac-redis
  labels:
    app: novafuse-uac
    component: api
    release: {{ .Release.Name }}
    app.kubernetes.io/name: novafuse-uac
    app.kubernetes.io/component: api
    app.kubernetes.io/managed-by: helm
type: Opaque
data:
  uri: {{ .Values.redis.uri | b64enc | quote }}
