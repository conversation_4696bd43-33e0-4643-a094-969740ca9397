/**
 * Unified Integration Example
 * 
 * This example demonstrates how to use the unified integration module to connect
 * the Self-Healing Tensor, 3D Tensor Visualization, Analytics Dashboard, and
 * Real-Time Data Flow components.
 */

// Import the unified integration module
const { createUnifiedIntegration } = require('../index');

// Import the Self-Healing Tensor
const SelfHealingTensor = require('../../quantum/self-healing-tensor');

// Import visualization components (mock for this example)
const VisualizationSystem = {
  on: (event, callback) => {
    // Mock event listener
    console.log(`Registered event listener for: ${event}`);
  },
  
  getVisualizationTypes: () => {
    return [
      '3d_tensor_visualization',
      'resonance_spectrogram',
      'phase_space_visualization',
      'harmonic_pattern_explorer'
    ];
  },
  
  create3dTensorVisualization: (data, options) => {
    console.log('Creating 3D Tensor Visualization', { data, options });
    return {
      id: `viz-${Date.now()}`,
      type: '3d_tensor_visualization',
      data
    };
  },
  
  createResonanceSpectrogram: (data, options) => {
    console.log('Creating Resonance Spectrogram', { data, options });
    return {
      id: `viz-${Date.now()}`,
      type: 'resonance_spectrogram',
      data
    };
  }
};

// Import analytics components (mock for this example)
const AnalyticsDashboard = {
  on: (event, callback) => {
    // Mock event listener
    console.log(`Registered event listener for: ${event}`);
  },
  
  getMetrics: () => {
    return {
      'tensor.health': 0.95,
      'tensor.entropyContainment': 0.02,
      'tensor.healingCycles': 3,
      'visualization.updateRate': 30,
      'system.performance': 0.87
    };
  },
  
  getDashboards: () => {
    return [
      {
        id: 'tensor-health-dashboard',
        name: 'Tensor Health Dashboard',
        metrics: ['tensor.health', 'tensor.entropyContainment', 'tensor.healingCycles']
      },
      {
        id: 'system-performance-dashboard',
        name: 'System Performance Dashboard',
        metrics: ['visualization.updateRate', 'system.performance']
      }
    ];
  },
  
  executeQuery: (query, params) => {
    console.log('Executing query', { query, params });
    return Promise.resolve({
      id: `query-${Date.now()}`,
      results: [
        { metric: 'tensor.health', value: 0.95, timestamp: Date.now() },
        { metric: 'tensor.entropyContainment', value: 0.02, timestamp: Date.now() }
      ]
    });
  }
};

// Run the example
async function runExample() {
  try {
    console.log('Starting Unified Integration Example...');
    
    // Create Self-Healing Tensor instance
    const selfHealingTensor = new SelfHealingTensor({
      healingThreshold: 0.6,
      healingFactor: 0.6,
      maxHealingCycles: 6,
      autoHeal: true
    });
    
    // Create unified integration
    const unifiedIntegration = createUnifiedIntegration({
      enableLogging: true,
      enableMetrics: true,
      autoConnect: true,
      updateInterval: 1000,
      tensor: selfHealingTensor,
      visualization: VisualizationSystem,
      analytics: AnalyticsDashboard
    });
    
    console.log('\nUnified Integration created successfully!');
    console.log(`- Components registered: ${unifiedIntegration.registry.getCount()}`);
    console.log(`- Tensor components: ${unifiedIntegration.registry.getTensors().length}`);
    console.log(`- Visualization components: ${unifiedIntegration.registry.getVisualizations().length}`);
    console.log(`- Analytics components: ${unifiedIntegration.registry.getAnalytics().length}`);
    
    // Register a tensor
    console.log('\nRegistering a tensor...');
    const tensorId = 'example-tensor';
    const tensor = {
      values: [0.5, 0.6, 0.7, 0.8, 0.9]
    };
    
    const registeredTensor = unifiedIntegration.adapters.tensor.registerTensor(tensorId, tensor, 'universal');
    console.log(`Tensor registered: ${tensorId}`);
    console.log(`- Health: ${registeredTensor.health}`);
    console.log(`- Entropy Containment: ${registeredTensor.entropyContainment}`);
    
    // Create a visualization
    console.log('\nCreating a visualization...');
    const visualizationType = '3d_tensor_visualization';
    const visualizationData = {
      tensor: registeredTensor,
      dimensions: [5, 1, 1]
    };
    
    const visualization = unifiedIntegration.adapters.visualization.createVisualization(
      visualizationType,
      visualizationData,
      { renderMode: 'high-quality' }
    );
    
    console.log(`Visualization created: ${visualization.id}`);
    console.log(`- Type: ${visualization.type}`);
    
    // Create data flows
    console.log('\nCreating data flows...');
    
    // Flow from tensor to visualization
    const tensorToVisualizationFlow = unifiedIntegration.dataFlowManager.createFlow(
      'tensor-adapter',
      'visualization-adapter',
      {
        dataType: 'tensor',
        direction: 'forward',
        transformations: [
          (data) => {
            // Transform tensor data for visualization
            return {
              ...data,
              dimensions: [data.values.length, 1, 1],
              transformed: true
            };
          }
        ],
        filters: [
          (data) => {
            // Only process data with health below threshold
            return data.health < 0.9;
          }
        ]
      }
    );
    
    console.log(`Data flow created: ${tensorToVisualizationFlow}`);
    
    // Flow from tensor to analytics
    const tensorToAnalyticsFlow = unifiedIntegration.dataFlowManager.createFlow(
      'tensor-adapter',
      'analytics-adapter',
      {
        dataType: 'analytics',
        direction: 'forward',
        transformations: [
          (data) => {
            // Transform tensor data for analytics
            return {
              'tensor.health': data.health,
              'tensor.entropyContainment': data.entropyContainment,
              'tensor.healingCycles': data.healingCycles || 0,
              timestamp: Date.now()
            };
          }
        ]
      }
    );
    
    console.log(`Data flow created: ${tensorToAnalyticsFlow}`);
    
    // Simulate tensor damage and healing
    console.log('\nSimulating tensor damage and healing...');
    
    // Damage the tensor
    const damagedTensor = unifiedIntegration.adapters.tensor.tensor.damageTensor(tensorId, 0.5);
    console.log(`Tensor damaged: ${tensorId}`);
    console.log(`- Health: ${damagedTensor.health}`);
    console.log(`- Entropy Containment: ${damagedTensor.entropyContainment}`);
    
    // Execute data flows
    console.log('\nExecuting data flows...');
    
    // Execute tensor to visualization flow
    const visualizationData1 = unifiedIntegration.dataFlowManager.executeFlow(
      tensorToVisualizationFlow,
      damagedTensor
    );
    
    console.log('Tensor to Visualization flow executed');
    console.log(`- Transformed: ${visualizationData1.transformed}`);
    console.log(`- Dimensions: ${visualizationData1.dimensions}`);
    
    // Execute tensor to analytics flow
    const analyticsData = unifiedIntegration.dataFlowManager.executeFlow(
      tensorToAnalyticsFlow,
      damagedTensor
    );
    
    console.log('Tensor to Analytics flow executed');
    console.log(`- Health Metric: ${analyticsData['tensor.health']}`);
    console.log(`- Entropy Containment Metric: ${analyticsData['tensor.entropyContainment']}`);
    
    // Heal the tensor
    console.log('\nHealing the tensor...');
    const healingResult = unifiedIntegration.adapters.tensor.tensor.healTensor(tensorId);
    
    console.log(`Tensor healed: ${tensorId}`);
    console.log(`- Health Before: ${healingResult.diagnostics.healthBefore}`);
    console.log(`- Health After: ${healingResult.diagnostics.healthAfter}`);
    console.log(`- Entropy Before: ${healingResult.diagnostics.entropyBefore}`);
    console.log(`- Entropy After: ${healingResult.diagnostics.entropyAfter}`);
    
    // Execute data flows again with healed tensor
    console.log('\nExecuting data flows with healed tensor...');
    
    // Execute tensor to visualization flow
    const visualizationData2 = unifiedIntegration.dataFlowManager.executeFlow(
      tensorToVisualizationFlow,
      healingResult.tensor
    );
    
    if (visualizationData2) {
      console.log('Tensor to Visualization flow executed');
      console.log(`- Transformed: ${visualizationData2.transformed}`);
      console.log(`- Dimensions: ${visualizationData2.dimensions}`);
    } else {
      console.log('Tensor to Visualization flow filtered out (health above threshold)');
    }
    
    // Execute tensor to analytics flow
    const analyticsData2 = unifiedIntegration.dataFlowManager.executeFlow(
      tensorToAnalyticsFlow,
      healingResult.tensor
    );
    
    console.log('Tensor to Analytics flow executed');
    console.log(`- Health Metric: ${analyticsData2['tensor.health']}`);
    console.log(`- Entropy Containment Metric: ${analyticsData2['tensor.entropyContainment']}`);
    
    // Get metrics
    console.log('\nGetting metrics...');
    const metrics = unifiedIntegration.integration.getMetrics();
    
    console.log('Integration Metrics:');
    console.log(`- Components Registered: ${metrics.componentsRegistered}`);
    console.log(`- Connections Established: ${metrics.connectionsEstablished}`);
    console.log(`- Data Flows Processed: ${metrics.dataFlowsProcessed}`);
    console.log(`- Visualization Updates: ${metrics.visualizationUpdates}`);
    console.log(`- Analytics Updates: ${metrics.analyticsUpdates}`);
    console.log(`- Tensor Operations: ${metrics.tensorOperations}`);
    console.log(`- Processing Time: ${metrics.processingTimeMs.toFixed(2)} ms`);
    
    console.log('\nUnified Integration Example completed successfully!');
  } catch (error) {
    console.error('Error running Unified Integration Example:', error);
  }
}

// Run the example
runExample();
