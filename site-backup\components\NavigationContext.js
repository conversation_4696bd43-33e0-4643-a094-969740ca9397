import React, { createContext, useContext, useState } from 'react';

const NavigationContext = createContext();

export function NavigationProvider({ children }) {
  const [isNavigationRendered, setIsNavigationRendered] = useState(false);

  return (
    <NavigationContext.Provider value={{ isNavigationRendered, setIsNavigationRendered }}>
      {children}
    </NavigationContext.Provider>
  );
}

export function useNavigation() {
  return useContext(NavigationContext);
}
