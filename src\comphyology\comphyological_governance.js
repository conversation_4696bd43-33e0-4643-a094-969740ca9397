/**
 * Comphyological Governance System
 * 
 * This module implements the First Law of Comphyological Governance:
 * 
 * "A system shall neither externalize non-resonant states (Harmonic Enforcement) 
 * nor propagate unmeasured energy transitions (Ψₑ Dynamics)."
 * 
 * It integrates the dual-axis operational framework:
 * 1. Harmonic Enforcement (3-6-9-12-13) - What states are allowed
 * 2. Ψₑ Dynamics (Velocity + Acceleration) - How states evolve
 */

const EventEmitter = require('events');
const ResonanceValidator = require('./resonance_validator');
const ResonantSlopeMonitor = require('./resonant_slope_monitor');

/**
 * Comphyological Governance class
 */
class ComphyologicalGovernance extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      // Harmonic Enforcement options
      resonanceLock: true, // Whether to enforce resonance constraints
      strictMode: false, // Whether to reject non-resonant values (true) or harmonize them (false)
      logValidation: true, // Whether to log validation results
      
      // Ψₑ Dynamics options
      slopeMonitoring: true, // Whether to monitor resonant slope
      slopeThreshold: 0.13, // Threshold for slope warning
      criticalSlopeThreshold: 0.3, // Threshold for critical slope warning
      velocityThreshold: 0.6, // Threshold for velocity warning
      accelerationThreshold: 0.09, // Threshold for acceleration warning
      samplingRate: 0, // ms between samples (0 = manual sampling)
      historyLength: 10, // Number of samples to keep
      resonancePatternEnforcement: true, // Whether to enforce 3-6-9-12-13 pattern on slopes
      autoModeSwitch: true, // Whether to automatically switch modes based on slope
      
      // Governance options
      autoHarmonization: true, // Whether to automatically harmonize non-resonant values
      preemptiveHarmonization: true, // Whether to preemptively harmonize based on slope warnings
      crossDomainEnforcement: true, // Whether to enforce resonance across domain boundaries
      
      ...options
    };
    
    // Initialize components
    this.resonanceValidator = new ResonanceValidator({
      strictMode: this.options.strictMode,
      logValidation: this.options.logValidation,
      resonanceLock: this.options.resonanceLock
    });
    
    this.slopeMonitor = new ResonantSlopeMonitor({
      slopeThreshold: this.options.slopeThreshold,
      criticalSlopeThreshold: this.options.criticalSlopeThreshold,
      velocityThreshold: this.options.velocityThreshold,
      accelerationThreshold: this.options.accelerationThreshold,
      samplingRate: this.options.samplingRate,
      historyLength: this.options.historyLength,
      resonancePatternEnforcement: this.options.resonancePatternEnforcement,
      autoModeSwitch: this.options.autoModeSwitch
    });
    
    // Initialize governance metrics
    this.metrics = {
      validations: 0,
      harmonizations: 0,
      rejections: 0,
      preemptiveHarmonizations: 0,
      slopeWarnings: 0,
      criticalSlopeWarnings: 0,
      modeChanges: 0,
      crossDomainEnforcements: 0,
      totalOperations: 0
    };
    
    // Initialize current mode
    this.currentMode = "Standard";
    
    // Forward events from components
    this.slopeMonitor.on('slope-warning', (warning) => {
      this.emit('slope-warning', warning);
      
      // Update metrics
      if (warning.critical) {
        this.metrics.criticalSlopeWarnings++;
      } else {
        this.metrics.slopeWarnings++;
      }
      
      // Perform preemptive harmonization if enabled
      if (this.options.preemptiveHarmonization) {
        this.preemptiveHarmonize(warning);
      }
    });
    
    this.slopeMonitor.on('mode-change', (data) => {
      this.currentMode = data.newMode;
      this.metrics.modeChanges++;
      this.emit('mode-change', data);
    });
  }
  
  /**
   * Validate a value against the First Law of Comphyological Governance
   * @param {number} value - Value to validate
   * @param {string} type - Type of value ('cycle', 'threshold', 'decay', 'factor')
   * @param {Object} context - Additional context information
   * @returns {Object} - Validation result
   */
  validate(value, type = 'generic', context = {}) {
    this.metrics.totalOperations++;
    this.metrics.validations++;
    
    // Get current mode
    const mode = context.mode || this.currentMode;
    
    // Apply different validation strategies based on mode
    let validationResult;
    
    switch (mode) {
      case "Strict Enforcement":
        // In strict mode, reject non-resonant values
        const strictValidator = new ResonanceValidator({
          strictMode: true,
          logValidation: this.options.logValidation,
          resonanceLock: true
        });
        validationResult = strictValidator.validate(value, type);
        break;
        
      case "Harmonizing Filter":
        // In harmonizing mode, always harmonize values
        validationResult = this.resonanceValidator.validate(value, type);
        if (!validationResult.isResonant && validationResult.isValid) {
          validationResult.wasHarmonized = true;
          validationResult.harmonizedValue = this.resonanceValidator.harmonize(value, type);
          validationResult.resonanceDrift = Math.abs(value - validationResult.harmonizedValue);
        }
        break;
        
      case "Velocity Control":
      case "Acceleration Control":
        // In velocity/acceleration control mode, apply stricter harmonization
        validationResult = this.resonanceValidator.validate(value, type);
        if (!validationResult.isResonant) {
          // Find the nearest resonant value with lower absolute value
          const resonantValues = [0.03, 0.06, 0.09, 0.3, 0.6, 0.9];
          const absValue = Math.abs(value);
          let nearestLower = 0;
          
          for (let i = 0; i < resonantValues.length; i++) {
            if (resonantValues[i] <= absValue) {
              nearestLower = resonantValues[i];
            } else {
              break;
            }
          }
          
          // Apply sign of original value
          validationResult.harmonizedValue = nearestLower * Math.sign(value);
          validationResult.wasHarmonized = true;
          validationResult.resonanceDrift = Math.abs(value - validationResult.harmonizedValue);
        }
        break;
        
      default:
        // Standard mode - use normal validation
        validationResult = this.resonanceValidator.validate(value, type);
    }
    
    // Update metrics
    if (!validationResult.isValid) {
      this.metrics.rejections++;
    } else if (validationResult.wasHarmonized) {
      this.metrics.harmonizations++;
    }
    
    // Add governance information to result
    validationResult.governance = {
      mode,
      timestamp: Date.now(),
      context
    };
    
    // Monitor slope if slope monitoring is enabled
    if (this.options.slopeMonitoring) {
      // Use the harmonized value if available, otherwise use the original value
      const psiE = validationResult.harmonizedValue || value;
      const slopeWarning = this.slopeMonitor.addSample(psiE);
      
      // Add slope information to result
      validationResult.slope = {
        warning: slopeWarning.warning,
        value: this.slopeMonitor.currentSlope,
        velocity: this.slopeMonitor.currentVelocity,
        acceleration: this.slopeMonitor.currentAcceleration
      };
    }
    
    // Emit validation event
    this.emit('validation', {
      result: validationResult,
      value,
      type,
      context
    });
    
    return validationResult;
  }
  
  /**
   * Validate a tensor against the First Law of Comphyological Governance
   * @param {Object} tensor - Tensor to validate
   * @param {Object} context - Additional context information
   * @returns {Object} - Validation result
   */
  validateTensor(tensor, context = {}) {
    this.metrics.totalOperations++;
    
    // Validate tensor using resonance validator
    const validationResult = this.resonanceValidator.validateTensor(tensor);
    
    // Update metrics
    if (!validationResult.isValid) {
      this.metrics.rejections++;
    } else if (validationResult.wasHarmonized) {
      this.metrics.harmonizations++;
    }
    
    // Add governance information to result
    validationResult.governance = {
      mode: this.currentMode,
      timestamp: Date.now(),
      context
    };
    
    // Monitor slope if slope monitoring is enabled
    if (this.options.slopeMonitoring) {
      // Use the average of harmonized values as psiE
      const values = validationResult.harmonizedTensor ? 
        validationResult.harmonizedTensor.values : 
        tensor.values;
      
      const psiE = values.reduce((sum, v) => sum + v, 0) / values.length;
      const slopeWarning = this.slopeMonitor.addSample(psiE);
      
      // Add slope information to result
      validationResult.slope = {
        warning: slopeWarning.warning,
        value: this.slopeMonitor.currentSlope,
        velocity: this.slopeMonitor.currentVelocity,
        acceleration: this.slopeMonitor.currentAcceleration
      };
    }
    
    // Emit validation event
    this.emit('tensor-validation', {
      result: validationResult,
      tensor,
      context
    });
    
    return validationResult;
  }
  
  /**
   * Preemptively harmonize based on slope warning
   * @param {Object} warning - Slope warning
   * @private
   */
  preemptiveHarmonize(warning) {
    this.metrics.preemptiveHarmonizations++;
    
    // Switch to recommended mode
    this.switchMode(warning.recommendedMode);
    
    // Emit preemptive harmonization event
    this.emit('preemptive-harmonization', {
      warning,
      mode: this.currentMode,
      timestamp: Date.now()
    });
  }
  
  /**
   * Switch the current mode
   * @param {string} mode - New mode
   */
  switchMode(mode) {
    if (this.currentMode !== mode) {
      const previousMode = this.currentMode;
      this.currentMode = mode;
      this.metrics.modeChanges++;
      
      // Update resonance validator strictMode based on mode
      if (mode === "Strict Enforcement") {
        this.resonanceValidator.options.strictMode = true;
      } else {
        this.resonanceValidator.options.strictMode = this.options.strictMode;
      }
      
      // Emit mode change event
      this.emit('mode-change', {
        previousMode,
        newMode: mode,
        timestamp: Date.now(),
        metrics: this.getMetrics()
      });
    }
  }
  
  /**
   * Get the current mode
   * @returns {string} - Current mode
   */
  getCurrentMode() {
    return this.currentMode;
  }
  
  /**
   * Get current metrics
   * @returns {Object} - Current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      resonanceMetrics: this.resonanceValidator ? {
        resonanceLockEnabled: this.resonanceValidator.options.resonanceLock,
        strictModeEnabled: this.resonanceValidator.options.strictMode
      } : {},
      slopeMetrics: this.slopeMonitor ? this.slopeMonitor.getMetrics() : {},
      currentMode: this.currentMode
    };
  }
  
  /**
   * Reset the governance system
   */
  reset() {
    // Reset components
    if (this.slopeMonitor) {
      this.slopeMonitor.reset();
    }
    
    // Reset metrics
    this.metrics = {
      validations: 0,
      harmonizations: 0,
      rejections: 0,
      preemptiveHarmonizations: 0,
      slopeWarnings: 0,
      criticalSlopeWarnings: 0,
      modeChanges: 0,
      crossDomainEnforcements: 0,
      totalOperations: 0
    };
    
    // Reset mode
    this.currentMode = "Standard";
    
    // Emit reset event
    this.emit('reset', {
      timestamp: Date.now()
    });
  }
}

module.exports = ComphyologicalGovernance;
