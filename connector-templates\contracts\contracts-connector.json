{"extends": "base-connector", "name": "novafuse-contracts-connector", "version": "1.0.0", "description": "Contracts & Policy Lifecycle connector template for NovaFuse API Superstore", "category": "contracts", "base_url": "http://localhost:8000/contracts", "endpoints": [{"name": "contracts", "path": "/list", "method": "GET", "description": "Get a list of contracts", "parameters": [{"name": "status", "in": "query", "required": false, "description": "Filter by status (active, expired, pending, draft)"}, {"name": "type", "in": "query", "required": false, "description": "Filter by contract type"}, {"name": "party", "in": "query", "required": false, "description": "Filter by contracting party"}]}, {"name": "contract_details", "path": "/details/{id}", "method": "GET", "description": "Get details of a specific contract", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Contract ID"}]}, {"name": "policies", "path": "/policies", "method": "GET", "description": "Get a list of policies", "parameters": [{"name": "status", "in": "query", "required": false, "description": "Filter by status (active, archived, draft)"}, {"name": "category", "in": "query", "required": false, "description": "Filter by policy category"}]}, {"name": "policy_details", "path": "/policies/{id}", "method": "GET", "description": "Get details of a specific policy", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Policy ID"}]}, {"name": "contract_renewals", "path": "/renewals", "method": "GET", "description": "Get a list of upcoming contract renewals", "parameters": [{"name": "days", "in": "query", "required": false, "description": "Number of days to look ahead"}]}, {"name": "contract_analysis", "path": "/analysis", "method": "POST", "description": "Analyze a contract document", "parameters": []}, {"name": "policy_reviews", "path": "/policies/reviews", "method": "GET", "description": "Get a list of policy reviews", "parameters": [{"name": "status", "in": "query", "required": false, "description": "Filter by review status (pending, completed, overdue)"}, {"name": "policy_id", "in": "query", "required": false, "description": "Filter by policy ID"}]}, {"name": "contract_templates", "path": "/templates", "method": "GET", "description": "Get a list of contract templates", "parameters": [{"name": "category", "in": "query", "required": false, "description": "Filter by template category"}]}]}