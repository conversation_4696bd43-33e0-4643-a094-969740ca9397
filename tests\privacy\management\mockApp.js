const express = require('express');
const bodyParser = require('body-parser');

// Import routes
const privacyManagementRoutes = require('../../../apis/privacy/management/routes');
const authRoutes = require('../../../apis/privacy/management/auth-routes');

// Create Express app
const app = express();

// Apply middleware
app.use(bodyParser.json()); // Parse JSON request bodies
app.use(bodyParser.urlencoded({ extended: true })); // Parse URL-encoded request bodies

// Mock authentication and authorization middleware for testing
const mockAuthenticate = (req, res, next) => {
  // Add a mock user to the request
  req.user = {
    id: 'user-001',
    username: 'admin',
    role: 'admin'
  };
  next();
};

const mockAuthorize = (action) => {
  return (req, res, next) => {
    // Skip authorization checks in tests
    next();
  };
};

// Mock the auth module
jest.mock('../../../apis/privacy/management/auth', () => ({
  authenticate: mockAuthenticate,
  authorize: mockAuthorize,
  generateToken: jest.fn().mockReturnValue('mock-token'),
  verifyToken: jest.fn().mockReturnValue({ id: 'user-001', username: 'admin', role: 'admin' }),
  authenticateUser: jest.fn().mockResolvedValue({ success: true, token: 'mock-token', user: { id: 'user-001', username: 'admin', role: 'admin' } }),
  hasPermission: jest.fn().mockReturnValue(true)
}));

// Mock the cache module
jest.mock('../../../apis/privacy/management/cache', () => ({
  cacheMiddleware: () => (req, res, next) => next(),
  set: jest.fn(),
  get: jest.fn(),
  del: jest.fn(),
  clear: jest.fn(),
  getStats: jest.fn().mockReturnValue({ totalEntries: 0, activeEntries: 0, expiredEntries: 0 }),
  cleanExpired: jest.fn().mockReturnValue(0)
}));

// API routes
app.use('/privacy/management/auth', authRoutes);
app.use('/privacy/management', privacyManagementRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: err.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.url} not found`
  });
});

module.exports = app;
