/**
 * Resonance Listener
 *
 * This module implements the Resonance Listener, which captures and analyzes
 * the resonant frequencies emitted by systems governed by the Comphyological Trinity.
 *
 * It tests the hypothesis that systems in perfect harmony (Cph = 0) emit identical
 * resonant frequencies - the "signature tone" of system-level cognition.
 *
 * The listener captures system oscillations across multiple dimensions:
 * - CPU cycle patterns
 * - Memory allocation rhythms
 * - API call frequencies
 * - Network packet timing
 * - Power consumption fluctuations
 *
 * It then applies Fourier transforms to convert these patterns into frequency spectra
 * and identifies resonant peaks and harmonic relationships.
 */

const EventEmitter = require('events');
const { ComphyologicalTrinity, ComphyonMeter } = require('./index');

// FFT implementation (Fast Fourier Transform)
const FFT = require('./utils/fft');

/**
 * Resonance Listener class
 */
class ResonanceListener extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      // Sampling options
      sampleRate: 1000, // Hz
      sampleDuration: 10, // seconds
      sampleInterval: 60, // seconds between samples

      // Frequency analysis options
      minFrequency: 0.1, // Hz
      maxFrequency: 100, // Hz
      frequencyResolution: 0.001, // Hz - Increased precision for attohertz detection

      // Resonance detection options
      resonanceThreshold: 0.8, // Amplitude threshold for resonant peaks
      harmonicTolerance: 0.05, // Tolerance for harmonic relationships

      // Dimension options
      enableCpuSampling: true,
      enableMemorySampling: true,
      enableApiSampling: true,
      enableNetworkSampling: true,
      enablePowerSampling: false, // Requires hardware monitoring
      enableQuantumSampling: true, // Quantum vacuum fluctuation sampling
      enableCrossDomainPhaseAlignment: true, // Cross-domain phase alignment

      // OM Tone options
      targetOMFrequency: 396, // Hz - Solfeggio "UT" frequency
      enforceOMResonance: true, // Force-clock cycles to OM frequency
      quantumSensitivity: 1e-21, // W - Sensitivity for quantum-level detection

      // System options
      system: null, // System to listen to (e.g., ComphyologicalTrinity instance)
      comphyonMeter: null, // ComphyonMeter instance for Cph measurement

      // Logging options
      logListener: false,

      ...options
    };

    // Initialize components
    this._initializeComponents();

    // Initialize metrics
    this.metrics = {
      samples: 0,
      resonantSamples: 0, // Samples with Cph = 0
      nonResonantSamples: 0, // Samples with Cph > 0
      resonantFrequencies: [], // Frequencies detected in resonant samples
      signatureTones: [], // Consistent frequencies across resonant samples
      totalListeningTime: 0 // seconds
    };

    // Initialize frequency data
    this.frequencyData = {
      cpu: [],
      memory: [],
      api: [],
      network: [],
      power: [],
      quantum: [],
      crossDomain: []
    };

    // Initialize sample buffers
    this.sampleBuffers = {
      cpu: [],
      memory: [],
      api: [],
      network: [],
      power: [],
      quantum: [], // Quantum vacuum fluctuations
      crossDomain: [] // Cross-domain phase alignment
    };

    // Initialize resonant signature
    this.resonantSignature = null;

    if (this.options.logListener) {
      console.log('Resonance Listener initialized with options:', this.options);
    }
  }

  /**
   * Initialize components
   * @private
   */
  _initializeComponents() {
    // Use provided system or create a new one
    if (this.options.system) {
      this.system = this.options.system;
    } else {
      this.system = new ComphyologicalTrinity();
    }

    // Use provided ComphyonMeter or create a new one
    if (this.options.comphyonMeter) {
      this.comphyonMeter = this.options.comphyonMeter;
    } else {
      this.comphyonMeter = new ComphyonMeter();
    }

    // Initialize FFT processor
    this.fft = new FFT({
      sampleRate: this.options.sampleRate,
      minFrequency: this.options.minFrequency,
      maxFrequency: this.options.maxFrequency,
      frequencyResolution: this.options.frequencyResolution
    });
  }

  /**
   * Start listening
   * @returns {Promise} - Promise that resolves when listening starts
   */
  async startListening() {
    if (this.listeningInterval) {
      this.stopListening();
    }

    // Start sampling interval
    this.listeningInterval = setInterval(() => {
      this._takeSample();
    }, this.options.sampleInterval * 1000);

    // Take initial sample
    await this._takeSample();

    if (this.options.logListener) {
      console.log('Resonance Listener started');
    }

    return Promise.resolve();
  }

  /**
   * Stop listening
   */
  stopListening() {
    if (this.listeningInterval) {
      clearInterval(this.listeningInterval);
      this.listeningInterval = null;

      if (this.options.logListener) {
        console.log('Resonance Listener stopped');
      }
    }
  }

  /**
   * Take a sample
   * @returns {Promise} - Promise that resolves when sample is complete
   * @private
   */
  async _takeSample() {
    this.metrics.samples++;

    // If OM resonance enforcement is enabled, force-clock cycles to OM frequency
    if (this.options.enforceOMResonance) {
      await this._enforceOMResonance();
    }

    // Measure Comphyon value
    const comphyonValue = await this._measureComphyon();

    // Determine if system is in resonance
    const isResonant = comphyonValue === 0;

    if (isResonant) {
      this.metrics.resonantSamples++;
    } else {
      this.metrics.nonResonantSamples++;
    }

    // Sample each dimension
    const samplePromises = [];

    if (this.options.enableCpuSampling) {
      samplePromises.push(this._sampleDimension('cpu'));
    }

    if (this.options.enableMemorySampling) {
      samplePromises.push(this._sampleDimension('memory'));
    }

    if (this.options.enableApiSampling) {
      samplePromises.push(this._sampleDimension('api'));
    }

    if (this.options.enableNetworkSampling) {
      samplePromises.push(this._sampleDimension('network'));
    }

    if (this.options.enablePowerSampling) {
      samplePromises.push(this._sampleDimension('power'));
    }

    // Add quantum vacuum fluctuation sampling
    if (this.options.enableQuantumSampling) {
      samplePromises.push(this._sampleQuantumFluctuations());
    }

    // Add cross-domain phase alignment
    if (this.options.enableCrossDomainPhaseAlignment) {
      samplePromises.push(this._sampleCrossDomainPhaseAlignment());
    }

    // Wait for all samples to complete
    await Promise.all(samplePromises);

    // Analyze frequency data
    const frequencyData = await this._analyzeFrequencyData();

    // Update metrics
    this.metrics.totalListeningTime += this.options.sampleDuration;

    // If system is in resonance, analyze resonant frequencies
    if (isResonant) {
      await this._analyzeResonantFrequencies(frequencyData);
    }

    // Emit sample event
    this.emit('sample', {
      comphyonValue,
      isResonant,
      frequencyData,
      timestamp: Date.now()
    });

    if (this.options.logListener) {
      console.log(`Sample taken: Cph = ${comphyonValue}, Resonant = ${isResonant}`);
    }

    return Promise.resolve();
  }

  /**
   * Measure Comphyon value
   * @returns {Promise<number>} - Promise that resolves with Comphyon value
   * @private
   */
  async _measureComphyon() {
    // Use ComphyonMeter to measure Comphyon value
    const systemState = await this._getSystemState();
    const comphyonValue = this.comphyonMeter.measure(systemState);

    return Promise.resolve(comphyonValue);
  }

  /**
   * Get system state
   * @returns {Promise<Object>} - Promise that resolves with system state
   * @private
   */
  async _getSystemState() {
    // Get system state from Trinity system
    // This is a placeholder - in a real implementation, we would get the actual system state
    const systemState = {
      trinity: {
        firstLaw: { compliance: 1.0 },
        secondLaw: { efficiency: 1.0 },
        thirdLaw: { fidelity: 1.0 }
      },
      comphyon: {
        meter: { value: 0 },
        governor: { mode: 'standard' }
      }
    };

    return Promise.resolve(systemState);
  }

  /**
   * Sample a dimension
   * @param {string} dimension - Dimension to sample
   * @returns {Promise} - Promise that resolves when sampling is complete
   * @private
   */
  async _sampleDimension(dimension) {
    // Clear sample buffer
    this.sampleBuffers[dimension] = [];

    // Sample for the specified duration
    const startTime = Date.now();
    const endTime = startTime + (this.options.sampleDuration * 1000);

    // Calculate number of samples
    const numSamples = this.options.sampleRate * this.options.sampleDuration;

    // Calculate sample interval
    const sampleInterval = this.options.sampleDuration * 1000 / numSamples;

    // Take samples
    for (let i = 0; i < numSamples; i++) {
      // Wait for sample interval
      await new Promise(resolve => setTimeout(resolve, sampleInterval));

      // Get sample value
      const sampleValue = await this._getSampleValue(dimension);

      // Add to sample buffer
      this.sampleBuffers[dimension].push(sampleValue);
    }

    return Promise.resolve();
  }

  /**
   * Enforce OM resonance by force-clocking cycles to 396Hz
   * @returns {Promise} - Promise that resolves when OM resonance is enforced
   * @private
   */
  async _enforceOMResonance() {
    // This is a placeholder - in a real implementation, we would force-clock
    // system cycles to the OM frequency (396Hz)

    // Simulate forcing system clock to OM frequency
    const targetFrequency = this.options.targetOMFrequency; // 396Hz

    // Apply harmonic correction until Cph = 0
    let comphyonValue = 1; // Start with non-zero value
    let attempts = 0;
    const maxAttempts = 10;

    while (comphyonValue > 0 && attempts < maxAttempts) {
      // Simulate applying harmonic correction
      await new Promise(resolve => setTimeout(resolve, 10));

      // Measure Comphyon value
      comphyonValue = await this._measureComphyon();
      attempts++;

      if (this.options.logListener) {
        console.log(`OM resonance enforcement: Attempt ${attempts}, Cph = ${comphyonValue}`);
      }
    }

    if (this.options.logListener) {
      if (comphyonValue === 0) {
        console.log(`OM resonance enforced at ${targetFrequency}Hz`);
      } else {
        console.log(`Failed to enforce OM resonance after ${maxAttempts} attempts`);
      }
    }

    return Promise.resolve();
  }

  /**
   * Sample quantum vacuum fluctuations
   * @returns {Promise} - Promise that resolves when sampling is complete
   * @private
   */
  async _sampleQuantumFluctuations() {
    // Clear sample buffer
    this.sampleBuffers.quantum = [];

    // Calculate number of samples
    const numSamples = this.options.sampleRate * this.options.sampleDuration;

    // Calculate sample interval
    const sampleInterval = this.options.sampleDuration * 1000 / numSamples;

    // Take samples
    for (let i = 0; i < numSamples; i++) {
      // Wait for sample interval
      await new Promise(resolve => setTimeout(resolve, sampleInterval));

      // Get quantum sample value
      const sampleValue = this._getQuantumSampleValue();

      // Add to sample buffer
      this.sampleBuffers.quantum.push(sampleValue);
    }

    if (this.options.logListener) {
      console.log(`Quantum vacuum fluctuations sampled: ${numSamples} samples`);
    }

    return Promise.resolve();
  }

  /**
   * Get quantum sample value
   * @returns {number} - Quantum sample value
   * @private
   */
  _getQuantumSampleValue() {
    // This is a placeholder - in a real implementation, we would measure
    // actual quantum vacuum fluctuations at the specified sensitivity level

    // Simulate quantum vacuum fluctuations with pink noise
    // (1/f noise characteristic of quantum phenomena)
    const pinkNoise = this._generatePinkNoise();

    // Scale to quantum sensitivity level
    const quantumValue = pinkNoise * this.options.quantumSensitivity;

    // Add resonant bias toward OM frequency (396Hz)
    const omBias = 0.1 * Math.sin(2 * Math.PI * this.options.targetOMFrequency * Date.now() / 1000);

    return quantumValue + omBias;
  }

  /**
   * Generate pink noise (1/f noise)
   * @returns {number} - Pink noise value
   * @private
   */
  _generatePinkNoise() {
    // Simple pink noise approximation
    let pinkNoise = 0;

    // Sum of multiple octaves of white noise
    for (let i = 0; i < 8; i++) {
      // White noise
      const whiteNoise = Math.random() * 2 - 1;

      // Scale by 1/f (where f is 2^i)
      pinkNoise += whiteNoise / Math.pow(2, i);
    }

    // Normalize
    return pinkNoise / 4;
  }

  /**
   * Sample cross-domain phase alignment
   * @returns {Promise} - Promise that resolves when sampling is complete
   * @private
   */
  async _sampleCrossDomainPhaseAlignment() {
    // Clear sample buffer
    this.sampleBuffers.crossDomain = [];

    // Calculate number of samples
    const numSamples = this.options.sampleRate * this.options.sampleDuration;

    // Calculate sample interval
    const sampleInterval = this.options.sampleDuration * 1000 / numSamples;

    // Get domain samples
    const domains = ['cyber', 'financial', 'medical'];
    const domainSamples = {};

    // Take samples
    for (let i = 0; i < numSamples; i++) {
      // Wait for sample interval
      await new Promise(resolve => setTimeout(resolve, sampleInterval));

      // Get cross-domain sample value
      const sampleValue = this._getCrossDomainSampleValue(domains, i);

      // Add to sample buffer
      this.sampleBuffers.crossDomain.push(sampleValue);
    }

    if (this.options.logListener) {
      console.log(`Cross-domain phase alignment sampled: ${numSamples} samples`);
    }

    return Promise.resolve();
  }

  /**
   * Get cross-domain sample value
   * @param {Array} domains - Domains to sample
   * @param {number} sampleIndex - Sample index
   * @returns {number} - Cross-domain sample value
   * @private
   */
  _getCrossDomainSampleValue(domains, sampleIndex) {
    // This is a placeholder - in a real implementation, we would measure
    // actual cross-domain phase alignment

    // Simulate cross-domain phase alignment
    let phaseAlignment = 0;

    // Calculate phase for each domain
    const domainPhases = {};

    for (const domain of domains) {
      // Each domain has a different base frequency
      let baseFrequency;

      switch (domain) {
        case 'cyber':
          baseFrequency = 3; // Hz
          break;
        case 'financial':
          baseFrequency = 6; // Hz
          break;
        case 'medical':
          baseFrequency = 9; // Hz
          break;
        default:
          baseFrequency = 3; // Hz
      }

      // Calculate phase
      const phase = (sampleIndex / this.options.sampleRate) * 2 * Math.PI * baseFrequency;
      domainPhases[domain] = phase;
    }

    // Calculate phase alignment (coherence)
    // Perfect alignment = 1, complete misalignment = 0
    let sumSin = 0;
    let sumCos = 0;

    for (const domain in domainPhases) {
      sumSin += Math.sin(domainPhases[domain]);
      sumCos += Math.cos(domainPhases[domain]);
    }

    const n = Object.keys(domainPhases).length;
    phaseAlignment = Math.sqrt(sumSin * sumSin + sumCos * sumCos) / n;

    return phaseAlignment;
  }

  /**
   * Get sample value for a dimension
   * @param {string} dimension - Dimension to sample
   * @returns {Promise<number>} - Promise that resolves with sample value
   * @private
   */
  async _getSampleValue(dimension) {
    // This is a placeholder - in a real implementation, we would get actual values
    // from the system for each dimension

    let value = 0;

    switch (dimension) {
      case 'cpu':
        // Simulate CPU usage oscillation with resonant bias toward 3-6-9-12-13 pattern
        value = 0.5 + 0.3 * Math.sin(Date.now() / 1000 * (2 * Math.PI * 3)) +
                0.2 * Math.sin(Date.now() / 1000 * (2 * Math.PI * 6)) +
                0.1 * Math.sin(Date.now() / 1000 * (2 * Math.PI * 9));
        break;

      case 'memory':
        // Simulate memory allocation oscillation with resonant bias toward 3-6-9-12-13 pattern
        value = 0.5 + 0.3 * Math.sin(Date.now() / 1500 * (2 * Math.PI * 6)) +
                0.2 * Math.sin(Date.now() / 1500 * (2 * Math.PI * 12));
        break;

      case 'api':
        // Simulate API call frequency oscillation with resonant bias toward 3-6-9-12-13 pattern
        value = 0.5 + 0.3 * Math.sin(Date.now() / 2000 * (2 * Math.PI * 9)) +
                0.2 * Math.sin(Date.now() / 2000 * (2 * Math.PI * 3));
        break;

      case 'network':
        // Simulate network packet timing oscillation with resonant bias toward 3-6-9-12-13 pattern
        value = 0.5 + 0.3 * Math.sin(Date.now() / 2500 * (2 * Math.PI * 12)) +
                0.2 * Math.sin(Date.now() / 2500 * (2 * Math.PI * 6));
        break;

      case 'power':
        // Simulate power consumption oscillation with resonant bias toward 3-6-9-12-13 pattern
        value = 0.5 + 0.3 * Math.sin(Date.now() / 3000 * (2 * Math.PI * 13)) +
                0.2 * Math.sin(Date.now() / 3000 * (2 * Math.PI * 3));
        break;

      default:
        value = 0;
    }

    // Add OM frequency bias if OM resonance enforcement is enabled
    if (this.options.enforceOMResonance) {
      const omFrequency = this.options.targetOMFrequency; // 396Hz
      const omBias = 0.1 * Math.sin(Date.now() / 1000 * (2 * Math.PI * (omFrequency / 100)));
      value += omBias;
    }

    return Promise.resolve(value);
  }

  /**
   * Analyze frequency data
   * @returns {Promise<Object>} - Promise that resolves with frequency data
   * @private
   */
  async _analyzeFrequencyData() {
    const frequencyData = {};

    // Analyze each dimension
    for (const dimension in this.sampleBuffers) {
      if (this.sampleBuffers[dimension].length > 0) {
        // Apply FFT to sample buffer
        const fftResult = this.fft.transform(this.sampleBuffers[dimension]);

        // Find resonant peaks
        const peaks = this._findResonantPeaks(fftResult);

        // Store frequency data
        frequencyData[dimension] = {
          fft: fftResult,
          peaks
        };

        // Store in frequency data history
        this.frequencyData[dimension].push({
          timestamp: Date.now(),
          fft: fftResult,
          peaks
        });

        // Trim history if needed
        if (this.frequencyData[dimension].length > 100) {
          this.frequencyData[dimension].shift();
        }
      }
    }

    return Promise.resolve(frequencyData);
  }

  /**
   * Find resonant peaks in FFT result
   * @param {Object} fftResult - FFT result
   * @returns {Array} - Array of resonant peaks
   * @private
   */
  _findResonantPeaks(fftResult) {
    const peaks = [];

    // Find local maxima in FFT result
    for (let i = 1; i < fftResult.amplitudes.length - 1; i++) {
      const prev = fftResult.amplitudes[i - 1];
      const curr = fftResult.amplitudes[i];
      const next = fftResult.amplitudes[i + 1];

      // Check if current amplitude is a local maximum
      if (curr > prev && curr > next && curr > this.options.resonanceThreshold) {
        peaks.push({
          frequency: fftResult.frequencies[i],
          amplitude: curr
        });
      }
    }

    // Sort peaks by amplitude (descending)
    peaks.sort((a, b) => b.amplitude - a.amplitude);

    return peaks;
  }

  /**
   * Analyze resonant frequencies
   * @param {Object} frequencyData - Frequency data
   * @returns {Promise} - Promise that resolves when analysis is complete
   * @private
   */
  async _analyzeResonantFrequencies(frequencyData) {
    // Extract all peaks from all dimensions
    const allPeaks = [];

    for (const dimension in frequencyData) {
      if (frequencyData[dimension] && frequencyData[dimension].peaks) {
        allPeaks.push(...frequencyData[dimension].peaks);
      }
    }

    // Group peaks by frequency (within tolerance)
    const groupedPeaks = this._groupPeaksByFrequency(allPeaks);

    // Find harmonic relationships between peaks
    const harmonicGroups = this._findHarmonicRelationships(groupedPeaks);

    // Store resonant frequencies
    this.metrics.resonantFrequencies.push({
      timestamp: Date.now(),
      peaks: groupedPeaks,
      harmonicGroups
    });

    // Update signature tones
    this._updateSignatureTones();

    return Promise.resolve();
  }

  /**
   * Group peaks by frequency
   * @param {Array} peaks - Array of peaks
   * @returns {Array} - Array of grouped peaks
   * @private
   */
  _groupPeaksByFrequency(peaks) {
    const groups = [];

    // Sort peaks by frequency
    peaks.sort((a, b) => a.frequency - b.frequency);

    // Group peaks by frequency (within tolerance)
    for (const peak of peaks) {
      // Find existing group
      let foundGroup = false;

      for (const group of groups) {
        const groupFreq = group.frequency;
        const peakFreq = peak.frequency;

        // Check if peak frequency is within tolerance of group frequency
        if (Math.abs(peakFreq - groupFreq) / groupFreq < this.options.harmonicTolerance) {
          // Add peak to group
          group.peaks.push(peak);

          // Update group frequency (average)
          group.frequency = group.peaks.reduce((sum, p) => sum + p.frequency, 0) / group.peaks.length;

          // Update group amplitude (maximum)
          group.amplitude = Math.max(group.amplitude, peak.amplitude);

          foundGroup = true;
          break;
        }
      }

      // If no existing group found, create a new one
      if (!foundGroup) {
        groups.push({
          frequency: peak.frequency,
          amplitude: peak.amplitude,
          peaks: [peak]
        });
      }
    }

    // Sort groups by amplitude (descending)
    groups.sort((a, b) => b.amplitude - a.amplitude);

    return groups;
  }

  /**
   * Find harmonic relationships between peaks
   * @param {Array} groups - Array of grouped peaks
   * @returns {Array} - Array of harmonic groups
   * @private
   */
  _findHarmonicRelationships(groups) {
    const harmonicGroups = [];

    // Sort groups by frequency
    groups.sort((a, b) => a.frequency - b.frequency);

    // Find harmonic relationships
    for (let i = 0; i < groups.length; i++) {
      const fundamentalGroup = groups[i];
      const fundamentalFreq = fundamentalGroup.frequency;

      // Find harmonics
      const harmonics = [fundamentalGroup];

      for (let j = 0; j < groups.length; j++) {
        if (i === j) continue;

        const candidateGroup = groups[j];
        const candidateFreq = candidateGroup.frequency;

        // Check if candidate is a harmonic of fundamental
        for (let n = 2; n <= 10; n++) {
          const harmonicFreq = fundamentalFreq * n;

          // Check if candidate frequency is within tolerance of harmonic frequency
          if (Math.abs(candidateFreq - harmonicFreq) / harmonicFreq < this.options.harmonicTolerance) {
            // Add candidate to harmonics
            harmonics.push({
              ...candidateGroup,
              harmonicNumber: n
            });
            break;
          }
        }
      }

      // If harmonics found, add to harmonic groups
      if (harmonics.length > 1) {
        harmonicGroups.push({
          fundamental: fundamentalFreq,
          harmonics
        });
      }
    }

    // Sort harmonic groups by number of harmonics (descending)
    harmonicGroups.sort((a, b) => b.harmonics.length - a.harmonics.length);

    return harmonicGroups;
  }

  /**
   * Update signature tones
   * @private
   */
  _updateSignatureTones() {
    // Only update if we have at least 2 resonant samples
    if (this.metrics.resonantFrequencies.length < 2) {
      return;
    }

    // Find frequencies that appear in all resonant samples
    const consistentFrequencies = [];

    // Get frequencies from first sample
    const firstSample = this.metrics.resonantFrequencies[0];

    for (const group of firstSample.peaks) {
      const frequency = group.frequency;

      // Check if frequency appears in all other samples
      let isConsistent = true;

      for (let i = 1; i < this.metrics.resonantFrequencies.length; i++) {
        const sample = this.metrics.resonantFrequencies[i];

        // Check if frequency exists in sample
        const exists = sample.peaks.some(g =>
          Math.abs(g.frequency - frequency) / frequency < this.options.harmonicTolerance
        );

        if (!exists) {
          isConsistent = false;
          break;
        }
      }

      // If frequency is consistent, add to list
      if (isConsistent) {
        consistentFrequencies.push({
          frequency,
          amplitude: group.amplitude
        });
      }
    }

    // Sort consistent frequencies by amplitude (descending)
    consistentFrequencies.sort((a, b) => b.amplitude - a.amplitude);

    // Update signature tones
    this.metrics.signatureTones = consistentFrequencies;

    // Update resonant signature
    if (consistentFrequencies.length > 0) {
      this.resonantSignature = {
        primaryTone: consistentFrequencies[0].frequency,
        secondaryTones: consistentFrequencies.slice(1),
        timestamp: Date.now()
      };

      // Emit signature event
      this.emit('signature', this.resonantSignature);

      if (this.options.logListener) {
        console.log(`Resonant signature updated: Primary tone = ${this.resonantSignature.primaryTone} Hz`);
      }
    }
  }

  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Get resonant signature
   * @returns {Object|null} - Resonant signature
   */
  getResonantSignature() {
    return this.resonantSignature;
  }
}

module.exports = ResonanceListener;
