# Applications Inventory
*Generated on 2025-07-03*

## Overview
This document lists all applications found in the codebase, organized by their creation date.

## Applications

### 1. Comphyology Demo
- **Type**: Web Application
- **Location**: `/comphyology_demo`
- **Description**: Interactive dashboard for the Comphyology project
- **Technologies**: React, Next.js, WebGL
- **Created**: [Date from git history]
- **Last Modified**: [Date from git history]
- **Key Files**:
  - `comphyology_demo/pages/index.js`
  - `comphyology_demo/components/`

### 2. Nova Agent Dashboard
- **Type**: Web Application
- **Location**: `/nova-agent-dashboard`
- **Description**: Dashboard for monitoring and managing Nova agents
- **Technologies**: React, Next.js
- **Created**: [Date from git history]
- **Last Modified**: [Date from git history]
- **Key Files**:
  - `nova-agent-dashboard/pages/_app.js`
  - `nova-agent-dashboard/components/`

### 3. KetherNet Server
- **Type**: Backend Service
- **Location**: `/kethernet-server.js`
- **Description**: Server for KetherNet communication
- **Technologies**: Node.js
- **Created**: [Date from git history]
- **Last Modified**: [Date from git history]

### 4. Novabrowser
- **Type**: Web Browser
- **Location**: `/novabrowser`
- **Description**: Custom web browser implementation
- **Technologies**: Electron, React
- **Created**: [Date from git history]
- **Last Modified**: [Date from git history]

### 5. Nova Agent
- **Type**: Desktop Application
- **Location**: `/nova-agent.exe`
- **Description**: Desktop agent for Nova platform
- **Technologies**: Go
- **Created**: [Date from git history]
- **Last Modified**: [Date from git history]

## Utility Scripts

### 1. Start Scripts
- `start-ai-alignment.bat`
- `start-kethernet.ps1`
- `start-nova-platform.bat`

### 2. Build Scripts
- `build-nova-agent.bat`

### 3. Test Scripts
- `test-aetherium.js`
- `test-chaeonix-trading.js`
- `test-coherium.js`
- `test-crown-consensus.js`

## Documentation

### 1. Main Documentation
- `CHAEONIX-DOCUMENTATION.md`
- `COMPLETE-MIGRATION-INDEX.md`
- `LATEST-DEVELOPMENTS-INDEX.md`
- `NOVAALIGN-DEPLOYMENT-SUMMARY.md`
- `QUICK-START-GUIDE.md`

### 2. Technical Documentation
- `ALPHA-CORE-MANUAL.md`
- `COHERENCE-IMPLEMENTATION-GUIDE.md`
- `DEPLOYMENT-GUIDE.md`
- `NHETX-CASTL-OMEGA-DOCUMENTATION.md`

## Notes
- This is a manual inventory based on the visible file structure.
- For accurate creation dates, please check git history.
- Some applications may have additional dependencies or components not listed here.

## Next Steps
1. Verify the creation dates from git history
2. Update the descriptions with more detailed information
3. Add any missing applications or components
4. Include version numbers and dependencies for each application
