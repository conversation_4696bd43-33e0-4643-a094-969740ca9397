
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #0A84FF;
      color: white;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .summary-item {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      flex: 1;
      margin-right: 10px;
      text-align: center;
    }
    .summary-item:last-child {
      margin-right: 0;
    }
    .summary-item.passed {
      background-color: #d4edda;
      color: #155724;
    }
    .summary-item.failed {
      background-color: #f8d7da;
      color: #721c24;
    }
    .summary-item.pending {
      background-color: #fff3cd;
      color: #856404;
    }
    .summary-item.total {
      background-color: #e2e3e5;
      color: #383d41;
    }
    .summary-number {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .test-suite {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .test-suite-header {
      padding: 10px 15px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .test-suite-title {
      margin: 0;
      font-size: 18px;
    }
    .test-suite-status {
      font-weight: bold;
    }
    .test-suite-status.passed {
      color: #28a745;
    }
    .test-suite-status.failed {
      color: #dc3545;
    }
    .test-suite-body {
      padding: 15px;
    }
    .test-case {
      padding: 8px 15px;
      border-bottom: 1px solid #eee;
    }
    .test-case:last-child {
      border-bottom: none;
    }
    .test-case.passed {
      border-left: 4px solid #28a745;
    }
    .test-case.failed {
      border-left: 4px solid #dc3545;
      background-color: #f8f9fa;
    }
    .test-case.pending {
      border-left: 4px solid #ffc107;
      color: #6c757d;
    }
    .test-case-title {
      margin: 0;
      font-size: 16px;
    }
    .test-case-error {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8d7da;
      border-radius: 3px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    .test-duration {
      color: #6c757d;
      font-size: 14px;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6c757d;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <header>
    <h1>NovaFuse Test Report</h1>
    <p>Generated on 5/23/2025, 1:37:58 AM</p>
  </header>
  
  <div class="summary">
    <div class="summary-item passed">
      <h3>Passed</h3>
      <div class="summary-number">13</div>
      <div>100%</div>
    </div>
    <div class="summary-item failed">
      <h3>Failed</h3>
      <div class="summary-number">0</div>
      <div>0%</div>
    </div>
    <div class="summary-item pending">
      <h3>Pending</h3>
      <div class="summary-number">0</div>
      <div>0%</div>
    </div>
    <div class="summary-item total">
      <h3>Total</h3>
      <div class="summary-number">13</div>
      <div>100%</div>
    </div>
  </div>
  
  <h2>Test Suites</h2>
  
  
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">blockchain-verification.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should verify evidence correctly</h4>
        <div class="test-duration">Duration: 27ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle evidence with attachments</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate proof correctly</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify proof correctly</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify actual Merkle proofs correctly</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify evidence efficiently</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">evidence-verification.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/evidence should return a list of evidence items</h4>
        <div class="test-duration">Duration: 39ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">POST /api/v1/evidence should create a new evidence item</h4>
        <div class="test-duration">Duration: 22ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/evidence/:id should return a specific evidence item</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">POST /api/v1/verification should verify an evidence item</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/verification should return a list of verifications</h4>
        <div class="test-duration">Duration: 9ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/verification/:id should return a specific verification</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/verification/:id/proof should return the proof for a verification</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
    </div>
  </div>
    
  
  <footer>
    <p>NovaFuse Universal Platform &copy; 2025</p>
  </footer>
</body>
</html>
  