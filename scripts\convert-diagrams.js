const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Source and output directories
const sourceDir = path.join(__dirname, '..', 'patent_drawings', 'mermaid_diagrams');
const outputDir = path.join(__dirname, '..', 'patent_drawings', 'generated');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Get all .mmd files in the source directory
fs.readdir(sourceDir, (err, files) => {
  if (err) {
    console.error('Error reading source directory:', err);
    return;
  }

  const mmdFiles = files.filter(file => file.endsWith('.mmd'));
  
  if (mmdFiles.length === 0) {
    console.log('No Mermaid diagram files (.mmd) found.');
    return;
  }

  console.log(`Found ${mmdFiles.length} Mermaid diagram(s) to convert.`);

  // Convert each .mmd file to .png and .svg
  mmdFiles.forEach(file => {
    const baseName = path.basename(file, '.mmd');
    const inputFile = path.join(sourceDir, file);
    
    // Generate PNG
    const pngOutput = path.join(outputDir, `${baseName}.png`);
    const pngCommand = `npx mmdc -i "${inputFile}" -o "${pngOutput}" -t neutral -b transparent`;
    
    // Generate SVG
    const svgOutput = path.join(outputDir, `${baseName}.svg`);
    const svgCommand = `npx mmdc -i "${inputFile}" -o "${svgOutput}" -t neutral -b transparent`;
    
    console.log(`Converting ${file} to PNG and SVG...`);
    
    // Execute conversion commands
    exec(pngCommand, (pngError) => {
      if (pngError) {
        console.error(`Error converting ${file} to PNG:`, pngError);
      } else {
        console.log(`Successfully created ${path.basename(pngOutput)}`);
      }
    });
    
    exec(svgCommand, (svgError) => {
      if (svgError) {
        console.error(`Error converting ${file} to SVG:`, svgError);
      } else {
        console.log(`Successfully created ${path.basename(svgOutput)}`);
      }
    });
  });
});
