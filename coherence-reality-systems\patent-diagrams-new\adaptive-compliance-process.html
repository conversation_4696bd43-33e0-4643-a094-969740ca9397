<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adaptive Compliance Process Diagram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 650px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
            z-index: 1;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #555555; /* Grey color for patent compliance */
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .arrow {
            position: absolute;
            background-color: transparent;
            z-index: 0;
        }
        .arrow-line {
            position: absolute;
            background-color: #333;
            z-index: 0;
        }
        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
        }
        .formula-box {
            position: absolute;
            font-size: 12px;
            font-style: italic;
            color: #555555;
            background-color: #f9f9f9;
            border: 1px dashed #ccc;
            border-radius: 4px;
            padding: 2px 5px;
            z-index: 1;
        }
        .cycle-arrow {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #333;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
            z-index: 1;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            z-index: 10;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 10px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 9: Adaptive Compliance Process Diagram</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">ADAPTIVE COMPLIANCE PROCESS: CYBER-SAFETY CONTEXT</div>
        </div>

        <!-- Continuous Cycle Indicator -->
        <div class="cycle-arrow" style="left: 355px; top: 300px;">↻</div>

        <!-- Regulatory Monitoring -->
        <div class="component-box" style="left: 300px; top: 80px; width: 200px; height: 80px;">
            <div class="component-number">1101</div>
            <div class="component-label">Regulatory Monitoring</div>
            <div style="font-size: 12px; text-align: center;">
                NovaTrack<br>
                Real-time Compliance Updates
            </div>
        </div>
        <div class="formula-box" style="left: 510px; top: 100px;">Data Purity Score</div>

        <!-- Compliance Assessment -->
        <div class="component-box" style="left: 500px; top: 180px; width: 200px; height: 80px;">
            <div class="component-number">1102</div>
            <div class="component-label">Compliance Assessment</div>
            <div style="font-size: 12px; text-align: center;">
                NovaCore<br>
                Gap Analysis & Risk Evaluation
            </div>
        </div>
        <div class="formula-box" style="left: 500px; top: 270px;">(A⊗B⊕C)×π10³</div>

        <!-- Policy Generation -->
        <div class="component-box" style="left: 500px; top: 380px; width: 200px; height: 80px;">
            <div class="component-number">1103</div>
            <div class="component-label">Policy Generation</div>
            <div style="font-size: 12px; text-align: center;">
                NovaThink<br>
                Automated Policy Creation
            </div>
        </div>
        <div class="formula-box" style="left: 500px; top: 470px;">Trinity Equation</div>

        <!-- Implementation -->
        <div class="component-box" style="left: 300px; top: 480px; width: 200px; height: 80px;">
            <div class="component-number">1104</div>
            <div class="component-label">Implementation</div>
            <div style="font-size: 12px; text-align: center;">
                NovaFlowX<br>
                Automated Control Deployment
            </div>
        </div>
        <div class="formula-box" style="left: 200px; top: 500px;">18/82 Principle</div>

        <!-- Verification -->
        <div class="component-box" style="left: 100px; top: 380px; width: 200px; height: 80px;">
            <div class="component-number">1105</div>
            <div class="component-label">Verification</div>
            <div style="font-size: 12px; text-align: center;">
                NovaProof<br>
                Control Effectiveness Testing
            </div>
        </div>
        <div class="formula-box" style="left: 100px; top: 470px;">Field Coherence</div>

        <!-- Continuous Learning -->
        <div class="component-box" style="left: 100px; top: 180px; width: 200px; height: 80px;">
            <div class="component-number">1106</div>
            <div class="component-label">Continuous Learning</div>
            <div style="font-size: 12px; text-align: center;">
                NovaLearn<br>
                Adaptive Improvement
            </div>
        </div>
        <div class="formula-box" style="left: 100px; top: 270px;">Adaptive Coherence</div>

        <!-- Compliance Domains -->
        <div class="container-box" style="width: 650px; height: 80px; left: 75px; top: 580px;">
            <div class="container-label" style="font-size: 14px;">COMPLIANCE DOMAINS</div>
        </div>

        <div style="position: absolute; left: 100px; top: 610px; display: flex; justify-content: space-between; width: 600px;">
            <div style="font-size: 12px; text-align: center; width: 80px;">GDPR</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">HIPAA</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">PCI DSS</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">SOX</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">NIST 800-53</div>
            <div style="font-size: 12px; text-align: center; width: 80px;">ISO 27001</div>
        </div>

        <!-- Arrows -->
        <div class="arrow-line" style="left: 400px; top: 160px; width: 100px; height: 2px; transform: translate(0, 0) rotate(45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 500px; top: 180px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <div class="arrow-line" style="left: 600px; top: 260px; width: 2px; height: 120px;"></div>
        <div class="arrow-head" style="left: 596px; top: 380px; border-width: 8px 4px 0 4px; border-color: #333 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 500px; top: 420px; width: 100px; height: 2px; transform: translate(0, 0) rotate(-45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 400px; top: 480px; border-width: 4px 8px 4px 0; border-color: transparent #333 transparent transparent;"></div>

        <div class="arrow-line" style="left: 300px; top: 520px; width: 100px; height: 2px; transform: translate(0, 0) rotate(-45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 200px; top: 460px; border-width: 4px 8px 4px 0; border-color: transparent #333 transparent transparent;"></div>

        <div class="arrow-line" style="left: 200px; top: 380px; width: 2px; height: 120px;"></div>
        <div class="arrow-head" style="left: 196px; top: 260px; border-width: 0 4px 8px 4px; border-color: transparent transparent #333 transparent;"></div>

        <div class="arrow-line" style="left: 200px; top: 180px; width: 100px; height: 2px; transform: translate(0, 0) rotate(-45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 300px; top: 120px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Process Steps</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f9f9f9;"></div>
                <div>Mathematical Formulas</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 10px;">↻</div>
                <div>Continuous Cycle</div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
