/**
 * useNovaAssistAI Hook
 *
 * A custom hook for interacting with the NovaAssistAI chatbot.
 * It provides methods for sending messages and managing conversation state.
 */

import { useState, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

export interface FileAttachment {
  id: string;
  url: string;
  name: string;
  type: string;
  size: number;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  attachments?: FileAttachment[];
}

export interface Suggestion {
  id: string;
  text: string;
  action?: string;
  params?: Record<string, any>;
}

export interface NovaAssistAIState {
  messages: Message[];
  isLoading: boolean;
  error: Error | null;
  suggestions: Suggestion[];
  pendingAttachments: FileAttachment[];
}

/**
 * Hook for interacting with NovaAssistAI
 * @returns Methods and state for NovaAssistAI interactions
 */
function useNovaAssistAI() {
  const { isAuthenticated, user } = useAuth();
  const [state, setState] = useState<NovaAssistAIState>({
    messages: [],
    isLoading: false,
    error: null,
    suggestions: [],
    pendingAttachments: []
  });

  /**
   * Send a message to NovaAssistAI
   * @param content Message content
   * @param context Optional context information
   */
  const sendMessage = useCallback(async (
    content: string,
    context?: {
      currentPage?: string;
      selectedItems?: any[];
      [key: string]: any;
    }
  ) => {
    if (!isAuthenticated) {
      setState(prev => ({
        ...prev,
        error: new Error('User is not authenticated')
      }));
      return;
    }

    // Add user message to state with any pending attachments
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content,
      timestamp: new Date(),
      attachments: state.pendingAttachments.length > 0 ? [...state.pendingAttachments] : undefined
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isLoading: true,
      error: null,
      pendingAttachments: [] // Clear pending attachments after adding to message
    }));

    try {
      // Call NovaAssistAI API
      const response = await axios.post('/api/v1/nova-assist', {
        message: content,
        context: {
          ...context,
          userId: user?.id,
          userEmail: user?.email,
          userRole: user?.role
        },
        history: state.messages.slice(-10), // Send last 10 messages for context
        attachments: userMessage.attachments // Include attachments in the request
      });

      // Add assistant response to state
      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: response.data.message,
        timestamp: new Date(),
        attachments: response.data.attachments // Include any attachments from the response
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, assistantMessage],
        isLoading: false,
        suggestions: response.data.suggestions || []
      }));

      return assistantMessage;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error('Failed to get response from NovaAssistAI')
      }));
    }
  }, [isAuthenticated, user, state.messages, state.pendingAttachments]);

  /**
   * Add file attachments to be sent with the next message
   * @param files Array of file attachments
   */
  const addAttachments = useCallback((files: FileAttachment[]) => {
    setState(prev => ({
      ...prev,
      pendingAttachments: [...prev.pendingAttachments, ...files]
    }));
  }, []);

  /**
   * Remove a pending file attachment
   * @param fileId ID of the file to remove
   */
  const removeAttachment = useCallback((fileId: string) => {
    setState(prev => ({
      ...prev,
      pendingAttachments: prev.pendingAttachments.filter(file => file.id !== fileId)
    }));
  }, []);

  /**
   * Clear all pending attachments
   */
  const clearAttachments = useCallback(() => {
    setState(prev => ({
      ...prev,
      pendingAttachments: []
    }));
  }, []);

  /**
   * Clear the conversation history
   */
  const clearConversation = useCallback(() => {
    setState({
      messages: [],
      isLoading: false,
      error: null,
      suggestions: [],
      pendingAttachments: []
    });
  }, []);

  /**
   * Execute a suggested action
   * @param suggestion The suggestion to execute
   */
  const executeSuggestion = useCallback(async (suggestion: Suggestion) => {
    if (!suggestion.action) {
      // If no action, treat as a regular message
      return sendMessage(suggestion.text);
    }

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    try {
      // Call action API
      const response = await axios.post('/api/v1/nova-assist/actions', {
        action: suggestion.action,
        params: suggestion.params
      });

      // Add system message about the action
      const systemMessage: Message = {
        id: `system-${Date.now()}`,
        role: 'system',
        content: `Executed action: ${suggestion.action}`,
        timestamp: new Date()
      };

      // Add assistant response if provided
      const messages = [...state.messages, systemMessage];
      if (response.data.message) {
        const assistantMessage: Message = {
          id: `assistant-${Date.now()}`,
          role: 'assistant',
          content: response.data.message,
          timestamp: new Date()
        };
        messages.push(assistantMessage);
      }

      setState(prev => ({
        ...prev,
        messages,
        isLoading: false,
        suggestions: response.data.suggestions || []
      }));

      return response.data;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error(`Failed to execute action: ${suggestion.action}`)
      }));
    }
  }, [sendMessage, state.messages]);

  /**
   * Load a previous conversation
   * @param conversationId The ID of the conversation to load
   */
  const loadConversation = useCallback(async (conversationId: string) => {
    if (!isAuthenticated) {
      setState(prev => ({
        ...prev,
        error: new Error('User is not authenticated')
      }));
      return;
    }

    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }));

    try {
      // Call API to load conversation
      const response = await axios.get(`/api/v1/nova-assist/conversations/${conversationId}`);

      setState(prev => ({
        ...prev,
        messages: response.data.messages,
        isLoading: false,
        suggestions: response.data.suggestions || []
      }));

      return response.data;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error : new Error(`Failed to load conversation: ${conversationId}`)
      }));
    }
  }, [isAuthenticated]);

  return {
    ...state,
    sendMessage,
    clearConversation,
    executeSuggestion,
    loadConversation,
    addAttachments,
    removeAttachment,
    clearAttachments
  };
}

export default useNovaAssistAI;
