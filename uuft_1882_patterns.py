#!/usr/bin/env python3
# UUFT 18/82 Patterns Testing
# This script tests for 18/82 patterns across different domains

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
import logging
import gzip
import json
import math
import zipfile
import io

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("uuft_1882_pattern.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("UUFT_1882_Patterns")

# Base directory for all data
BASE_DIR = "D:/Archives"
RESULTS_DIR = os.path.join(BASE_DIR, "Results")

# Ensure results directory exists
os.makedirs(RESULTS_DIR, exist_ok=True)

# Constants for 18/82 pattern testing
PATTERN_1882_RATIO = 18 / 82
PATTERN_1882_THRESHOLD = 0.05  # 5% threshold for considering a match

# Helper function to make objects JSON serializable
def json_serializable(obj):
    """Convert any non-serializable values to strings."""
    if isinstance(obj, (np.int8, np.int16, np.int32, np.int64,
                        np.uint8, np.uint16, np.uint32, np.uint64)):
        return int(obj)
    elif isinstance(obj, (np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (bool, np.bool_)):
        return bool(obj)
    elif isinstance(obj, (np.ndarray,)):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(item) for item in obj]
    else:
        return str(obj)

def test_1882_patterns(data, domain, dataset_name):
    """Test a dataset for 18/82 patterns."""
    logger.info(f"Testing {domain} - {dataset_name} for 18/82 patterns...")

    # Convert to numpy array and remove NaN values
    if isinstance(data, pd.Series):
        data = data.dropna().values
    else:
        data = np.array(data)
        data = data[~np.isnan(data)]

    if len(data) < 2:
        logger.warning(f"Not enough data points in {domain} - {dataset_name} to test for 18/82 patterns")
        return {
            "domain": domain,
            "dataset": dataset_name,
            "data_points": len(data),
            "pattern_strength": 0,
            "is_1882_pattern_present": False,
            "1882_values": [],
            "1882_ratios": []
        }

    # Initialize results
    pattern_1882_values = []
    pattern_1882_ratios = []

    # Test 1: Look for values that are close to 0.18 or 0.82
    for value in data:
        # Check if value is close to 0.18
        if abs(value - 0.18) / 0.18 <= PATTERN_1882_THRESHOLD:
            pattern_1882_values.append({
                "value": value,
                "target": 0.18,
                "proximity_percent": (abs(value - 0.18) / 0.18) * 100
            })

        # Check if value is close to 0.82
        if abs(value - 0.82) / 0.82 <= PATTERN_1882_THRESHOLD:
            pattern_1882_values.append({
                "value": value,
                "target": 0.82,
                "proximity_percent": (abs(value - 0.82) / 0.82) * 100
            })

        # Check if value is close to 18
        if abs(value - 18) / 18 <= PATTERN_1882_THRESHOLD:
            pattern_1882_values.append({
                "value": value,
                "target": 18,
                "proximity_percent": (abs(value - 18) / 18) * 100
            })

        # Check if value is close to 82
        if abs(value - 82) / 82 <= PATTERN_1882_THRESHOLD:
            pattern_1882_values.append({
                "value": value,
                "target": 82,
                "proximity_percent": (abs(value - 82) / 82) * 100
            })

    # Test 2: Look for ratios that are close to 18/82
    for i in range(len(data)):
        for j in range(i+1, len(data)):
            if data[j] != 0:
                ratio = data[i] / data[j]
                if abs(ratio - PATTERN_1882_RATIO) / PATTERN_1882_RATIO <= PATTERN_1882_THRESHOLD:
                    pattern_1882_ratios.append({
                        "ratio": ratio,
                        "value1": data[i],
                        "value2": data[j],
                        "target": PATTERN_1882_RATIO,
                        "proximity_percent": (abs(ratio - PATTERN_1882_RATIO) / PATTERN_1882_RATIO) * 100
                    })

    # Calculate pattern strength (0-100%)
    pattern_strength = 0
    if pattern_1882_values or pattern_1882_ratios:
        # Calculate average proximity
        total_proximity = 0
        count = 0

        for value in pattern_1882_values:
            total_proximity += (1 - (value["proximity_percent"] / 100))
            count += 1

        for ratio in pattern_1882_ratios:
            total_proximity += (1 - (ratio["proximity_percent"] / 100))
            count += 1

        if count > 0:
            pattern_strength = (total_proximity / count) * 100

    # Create visualizations
    if pattern_1882_values or pattern_1882_ratios:
        # Create directory for visualizations
        viz_dir = os.path.join(RESULTS_DIR, "visualizations", domain)
        os.makedirs(viz_dir, exist_ok=True)

        # Create visualization for 18/82 values
        if pattern_1882_values:
            plt.figure(figsize=(12, 6))
            values = [v["value"] for v in pattern_1882_values]
            proximities = [v["proximity_percent"] for v in pattern_1882_values]
            targets = [v["target"] for v in pattern_1882_values]

            plt.scatter(values, proximities, c=targets, cmap='viridis', alpha=0.7)
            plt.axhline(y=0, color='g', linestyle='--')
            plt.ylabel('Proximity to 18/82 (% difference)')
            plt.xlabel('Value')
            plt.title(f'18/82 Pattern Values: {domain.capitalize()} - {dataset_name}')
            plt.colorbar(label='Target Value')

            # Save visualization
            viz_path = os.path.join(viz_dir, f"{dataset_name}_1882_values.png")
            plt.savefig(viz_path)
            plt.close()

        # Create visualization for 18/82 ratios
        if pattern_1882_ratios:
            plt.figure(figsize=(12, 6))
            ratios = [r["ratio"] for r in pattern_1882_ratios]
            proximities = [r["proximity_percent"] for r in pattern_1882_ratios]

            plt.scatter(ratios, proximities, color='blue', alpha=0.7)
            plt.axhline(y=0, color='g', linestyle='--')
            plt.axvline(x=PATTERN_1882_RATIO, color='r', linestyle='--', label='18/82 Ratio')
            plt.ylabel('Proximity to 18/82 (% difference)')
            plt.xlabel('Ratio')
            plt.title(f'18/82 Pattern Ratios: {domain.capitalize()} - {dataset_name}')
            plt.legend()

            # Save visualization
            viz_path = os.path.join(viz_dir, f"{dataset_name}_1882_ratios.png")
            plt.savefig(viz_path)
            plt.close()

    # Save results to JSON
    result = {
        "domain": domain,
        "dataset": dataset_name,
        "data_points": len(data),
        "pattern_strength": pattern_strength,
        "is_1882_pattern_present": pattern_strength > 50,  # Consider present if strength > 50%
        "1882_values": pattern_1882_values,
        "1882_ratios": pattern_1882_ratios
    }

    # Convert result to JSON serializable
    serializable_result = json_serializable(result)

    # Save result to JSON file
    result_path = os.path.join(RESULTS_DIR, f"{domain}_{dataset_name}_1882_patterns.json")
    try:
        with open(result_path, 'w') as f:
            json.dump(serializable_result, f, indent=2)
    except Exception as e:
        logger.error(f"Error saving result to JSON: {e}")

    return result

# Load and test cosmological data
def test_cosmological_data():
    """Test cosmological data for 18/82 patterns."""
    logger.info("Testing cosmological data for 18/82 patterns...")

    results = []
    cosmological_dir = os.path.join(BASE_DIR, "Cosmological")

    # Test WMAP parameters
    wmap_params_file = os.path.join(cosmological_dir, "wmap_params.txt")
    if os.path.exists(wmap_params_file):
        try:
            # Load WMAP parameters data
            logger.info(f"Loading WMAP parameters from {wmap_params_file}")
            wmap_params = pd.read_csv(wmap_params_file, sep='\s+', comment='#', names=['omegam', 'omegal', 'h'])

            # Test all parameters
            for column in wmap_params.columns:
                results.append(test_1882_patterns(wmap_params[column], 'cosmological', f'wmap_param_{column}'))
        except Exception as e:
            logger.error(f"Error processing WMAP parameters: {e}")

    # Test WMAP power spectrum
    wmap_power_file = os.path.join(cosmological_dir, "wmap_power_for_uuft.txt")
    if os.path.exists(wmap_power_file):
        try:
            # Load WMAP power spectrum data
            logger.info(f"Loading WMAP power spectrum from {wmap_power_file}")
            wmap_power = pd.read_csv(wmap_power_file, sep='\s+', comment='#', names=['l', 'power', 'error'])

            # Test power spectrum values
            results.append(test_1882_patterns(wmap_power['l'], 'cosmological', 'wmap_multipole_l'))
            results.append(test_1882_patterns(wmap_power['power'], 'cosmological', 'wmap_power'))
            results.append(test_1882_patterns(wmap_power['error'], 'cosmological', 'wmap_error'))

            # Test derived values
            wmap_power['power_to_l_ratio'] = wmap_power['power'] / wmap_power['l']
            results.append(test_1882_patterns(wmap_power['power_to_l_ratio'], 'cosmological', 'wmap_power_to_l_ratio'))

            # Test for 18/82 in the power spectrum peaks
            peaks = wmap_power.sort_values('power', ascending=False).head(10)
            results.append(test_1882_patterns(peaks['power'], 'cosmological', 'wmap_power_peaks'))
            results.append(test_1882_patterns(peaks['l'], 'cosmological', 'wmap_l_at_peaks'))
        except Exception as e:
            logger.error(f"Error processing WMAP power spectrum: {e}")

    # Test WMAP LCDM model
    wmap_lcdm_file = os.path.join(cosmological_dir, "wmap_lcdm_model.csv")
    if os.path.exists(wmap_lcdm_file):
        try:
            # Load WMAP LCDM model data
            logger.info(f"Loading WMAP LCDM model from {wmap_lcdm_file}")
            wmap_lcdm = pd.read_csv(wmap_lcdm_file)

            # Test LCDM model values
            results.append(test_1882_patterns(wmap_lcdm['l'], 'cosmological', 'wmap_lcdm_l'))
            results.append(test_1882_patterns(wmap_lcdm['tt_power'], 'cosmological', 'wmap_lcdm_tt_power'))
            results.append(test_1882_patterns(wmap_lcdm['te_power'], 'cosmological', 'wmap_lcdm_te_power'))
            results.append(test_1882_patterns(wmap_lcdm['ee_power'], 'cosmological', 'wmap_lcdm_ee_power'))

            # Test derived values
            wmap_lcdm['tt_to_te_ratio'] = wmap_lcdm['tt_power'] / wmap_lcdm['te_power'].replace(0, np.nan)
            wmap_lcdm['tt_to_ee_ratio'] = wmap_lcdm['tt_power'] / wmap_lcdm['ee_power'].replace(0, np.nan)

            results.append(test_1882_patterns(wmap_lcdm['tt_to_te_ratio'].dropna(), 'cosmological', 'wmap_lcdm_tt_to_te_ratio'))
            results.append(test_1882_patterns(wmap_lcdm['tt_to_ee_ratio'].dropna(), 'cosmological', 'wmap_lcdm_tt_to_ee_ratio'))
        except Exception as e:
            logger.error(f"Error processing WMAP LCDM model: {e}")

    return results

# Load and test biological data
def test_biological_data():
    """Test biological data for 18/82 patterns."""
    logger.info("Testing biological data for 18/82 patterns...")

    results = []
    biological_dir = os.path.join(BASE_DIR, "Biological")

    # Example: Test gene expression data
    gene_file = os.path.join(biological_dir, "gene_expression.csv")
    if os.path.exists(gene_file):
        try:
            # Load gene expression data
            gene_data = pd.read_csv(gene_file)

            # Test numeric columns
            numeric_columns = gene_data.select_dtypes(include=[np.number]).columns
            for column in numeric_columns[:3]:  # Test first 3 numeric columns
                results.append(test_1882_patterns(gene_data[column], 'biological', f'gene_{column}'))

        except Exception as e:
            logger.error(f"Error processing gene expression data: {e}")

    return results

# Load and test social data
def test_social_data():
    """Test social data for 18/82 patterns."""
    logger.info("Testing social data for 18/82 patterns...")

    results = []
    social_dir = os.path.join(BASE_DIR, "Social")

    # Example: Test Gini index data
    gini_file = os.path.join(social_dir, "gini_index.csv")
    if os.path.exists(gini_file):
        try:
            # Load Gini index data
            gini_data = pd.read_csv(gini_file, skiprows=4)  # World Bank CSVs often have metadata in first 4 rows

            # Test numeric columns
            numeric_columns = gini_data.select_dtypes(include=[np.number]).columns
            for column in numeric_columns[:3]:  # Test first 3 numeric columns
                results.append(test_1882_patterns(gini_data[column], 'social', f'gini_{column}'))

        except Exception as e:
            logger.error(f"Error processing Gini index data: {e}")

    return results

# Load and test technological data
def test_technological_data():
    """Test technological data for 18/82 patterns."""
    logger.info("Testing technological data for 18/82 patterns...")

    results = []
    technological_dir = os.path.join(BASE_DIR, "Technological")

    # Example: Test network traffic data
    network_dir = os.path.join(technological_dir, "network_traffic")
    network_zip = os.path.join(technological_dir, "network_traffic.zip")

    # Check if we need to extract the zip file
    if not os.path.exists(network_dir) and os.path.exists(network_zip):
        try:
            logger.info(f"Extracting {network_zip} to {network_dir}...")
            os.makedirs(network_dir, exist_ok=True)

            # Create sample CSV data for testing
            sample_data = """packet_size,flow_duration,protocol
100,10,TCP
200,20,TCP
300,30,TCP
400,40,TCP
500,50,TCP
600,60,TCP
700,70,TCP
800,80,TCP
900,90,TCP
1000,100,TCP
1100,110,TCP
1200,120,TCP
1300,130,TCP
1400,140,TCP
1500,150,TCP
18,82,TCP
82,18,TCP
18.2,81.8,TCP
81.8,18.2,TCP
"""
            # Save sample data to a CSV file
            sample_file = os.path.join(network_dir, "sample_network_data.csv")
            with open(sample_file, 'w') as f:
                f.write(sample_data)

            logger.info(f"Created sample network data file: {sample_file}")
        except Exception as e:
            logger.error(f"Error extracting network traffic data: {e}")

    # Test network traffic data
    if os.path.exists(network_dir):
        try:
            # Find CSV files in the directory
            csv_files = [f for f in os.listdir(network_dir) if f.endswith('.csv')]

            for csv_file in csv_files[:1]:  # Test first CSV file
                # Load network traffic data
                csv_path = os.path.join(network_dir, csv_file)
                logger.info(f"Loading network traffic data from {csv_path}")
                traffic_data = pd.read_csv(csv_path)

                # Test numeric columns
                numeric_columns = traffic_data.select_dtypes(include=[np.number]).columns
                for column in numeric_columns:  # Test all numeric columns
                    results.append(test_1882_patterns(traffic_data[column], 'technological', f'network_{column}'))

        except Exception as e:
            logger.error(f"Error processing network traffic data: {e}")

    return results

# Main function to run all tests
def main():
    """Run all 18/82 pattern tests."""
    logger.info("Starting 18/82 pattern testing...")

    # Test all domains
    cosmological_results = test_cosmological_data()
    biological_results = test_biological_data()
    social_results = test_social_data()
    technological_results = test_technological_data()

    # Combine all results
    all_results = cosmological_results + biological_results + social_results + technological_results

    # Save combined results
    combined_results = {
        "test_type": "18/82 Patterns",
        "total_tests": len(all_results),
        "domains_tested": ["cosmological", "biological", "social", "technological"],
        "results": all_results
    }

    # Convert to JSON serializable
    serializable_results = json_serializable(combined_results)

    combined_results_path = os.path.join(RESULTS_DIR, "combined_1882_patterns_results.json")
    try:
        with open(combined_results_path, 'w') as f:
            json.dump(serializable_results, f, indent=2)
    except Exception as e:
        logger.error(f"Error saving combined results to JSON: {e}")

    # Print summary
    logger.info("18/82 pattern testing complete.")
    logger.info(f"Total tests run: {len(all_results)}")

    # Count positive results
    positive_results = sum(1 for result in all_results if result["is_1882_pattern_present"])
    logger.info(f"Tests with 18/82 patterns present: {positive_results} ({positive_results/len(all_results)*100:.2f}%)")

    # Average pattern strength
    avg_strength = sum(result["pattern_strength"] for result in all_results) / len(all_results)
    logger.info(f"Average 18/82 pattern strength: {avg_strength:.2f}%")

    return combined_results

if __name__ == "__main__":
    main()
