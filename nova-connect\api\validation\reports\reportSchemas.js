/**
 * Report Validation Schemas
 * 
 * This file contains validation schemas for report-related endpoints.
 */

const Joi = require('joi');
const { commonSchemas } = require('../common/commonSchemas');

/**
 * Generate compliance report schema
 */
const generateComplianceReportSchema = {
  body: Joi.object({
    framework: Joi.string().required(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    includeControls: Joi.boolean().default(true),
    includeFindings: Joi.boolean().default(true),
    includeRemediation: Joi.boolean().default(true),
    format: commonSchemas.reportFormat.default('pdf')
  })
};

/**
 * Generate performance report schema
 */
const generatePerformanceReportSchema = {
  body: Joi.object({
    metrics: Joi.array().items(Joi.string()).default(['api_calls', 'response_time', 'error_rate']),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    interval: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    connectorIds: commonSchemas.idArray.optional(),
    format: commonSchemas.reportFormat.default('pdf')
  })
};

/**
 * Generate security report schema
 */
const generateSecurityReportSchema = {
  body: Joi.object({
    securityDomains: Joi.array().items(Joi.string()).default(['authentication', 'authorization', 'data_protection', 'network_security']),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    includeIncidents: Joi.boolean().default(true),
    includeVulnerabilities: Joi.boolean().default(true),
    includeMitigations: Joi.boolean().default(true),
    format: commonSchemas.reportFormat.default('pdf')
  })
};

/**
 * Generate custom report schema
 */
const generateCustomReportSchema = {
  body: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().allow('').optional(),
    dataSources: Joi.array().items(Joi.string()).min(1).required(),
    filters: Joi.object().optional(),
    groupBy: Joi.array().items(Joi.string()).optional(),
    metrics: Joi.array().items(Joi.string()).min(1).required(),
    visualizations: Joi.array().items(Joi.object({
      type: Joi.string().valid('bar_chart', 'line_chart', 'pie_chart', 'table', 'metric').required(),
      title: Joi.string().required(),
      xAxis: Joi.string().when('type', {
        is: Joi.valid('bar_chart', 'line_chart'),
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      yAxis: Joi.string().when('type', {
        is: Joi.valid('bar_chart', 'line_chart'),
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      metric: Joi.string().when('type', {
        is: 'metric',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      columns: Joi.array().items(Joi.string()).when('type', {
        is: 'table',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    })).optional(),
    format: commonSchemas.reportFormat.default('pdf')
  })
};

/**
 * Get report status schema
 */
const getReportStatusSchema = {
  params: Joi.object({
    reportId: commonSchemas.id.required()
  })
};

/**
 * Download report schema
 */
const downloadReportSchema = {
  params: Joi.object({
    reportId: commonSchemas.id.required()
  }),
  query: Joi.object({
    format: commonSchemas.reportFormat.optional()
  })
};

/**
 * Schedule report schema
 */
const scheduleReportSchema = {
  params: Joi.object({
    reportId: commonSchemas.id.required()
  }),
  body: Joi.object({
    schedule: Joi.object({
      type: commonSchemas.scheduleType.required(),
      dayOfWeek: Joi.number().min(0).max(6).when('type', {
        is: 'weekly',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      dayOfMonth: Joi.number().min(1).max(31).when('type', {
        is: 'monthly',
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      hour: Joi.number().min(0).max(23).default(0),
      minute: Joi.number().min(0).max(59).default(0),
      cronExpression: Joi.string().when('type', {
        is: 'custom',
        then: Joi.required(),
        otherwise: Joi.optional()
      })
    }).required(),
    recipients: Joi.array().items(commonSchemas.email).min(1).required(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    timezone: Joi.string().default('UTC'),
    format: commonSchemas.reportFormat.default('pdf')
  })
};

/**
 * Get all scheduled reports schema
 */
const getAllScheduledReportsSchema = {
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('created', 'updated', 'nextRunAt').default('created'),
    sortOrder: commonSchemas.sortOrder,
    reportType: commonSchemas.reportType.optional(),
    active: Joi.boolean().optional()
  })
};

/**
 * Get my scheduled reports schema
 */
const getMyScheduledReportsSchema = {
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('created', 'updated', 'nextRunAt').default('created'),
    sortOrder: commonSchemas.sortOrder,
    reportType: commonSchemas.reportType.optional(),
    active: Joi.boolean().optional()
  })
};

/**
 * Get scheduled report by ID schema
 */
const getScheduledReportByIdSchema = {
  params: Joi.object({
    id: commonSchemas.id.required()
  })
};

/**
 * Update scheduled report schema
 */
const updateScheduledReportSchema = {
  params: Joi.object({
    id: commonSchemas.id.required()
  }),
  body: Joi.object({
    schedule: Joi.object({
      type: commonSchemas.scheduleType.optional(),
      dayOfWeek: Joi.number().min(0).max(6).optional(),
      dayOfMonth: Joi.number().min(1).max(31).optional(),
      hour: Joi.number().min(0).max(23).optional(),
      minute: Joi.number().min(0).max(59).optional(),
      cronExpression: Joi.string().optional()
    }).optional(),
    recipients: Joi.array().items(commonSchemas.email).min(1).optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    timezone: Joi.string().optional(),
    format: commonSchemas.reportFormat.optional(),
    active: Joi.boolean().optional()
  }).min(1)
};

/**
 * Delete scheduled report schema
 */
const deleteScheduledReportSchema = {
  params: Joi.object({
    id: commonSchemas.id.required()
  })
};

/**
 * Run scheduled report schema
 */
const runScheduledReportSchema = {
  params: Joi.object({
    id: commonSchemas.id.required()
  })
};

/**
 * Get all report templates schema
 */
const getAllReportTemplatesSchema = {
  query: Joi.object({
    page: commonSchemas.page,
    limit: commonSchemas.limit,
    sortBy: Joi.string().valid('name', 'type', 'created', 'updated').default('created'),
    sortOrder: commonSchemas.sortOrder,
    type: commonSchemas.reportType.optional(),
    search: Joi.string().allow('').optional()
  })
};

/**
 * Get report template by ID schema
 */
const getReportTemplateByIdSchema = {
  params: Joi.object({
    id: commonSchemas.id.required()
  })
};

/**
 * Create report template schema
 */
const createReportTemplateSchema = {
  body: Joi.object({
    name: Joi.string().required(),
    description: Joi.string().allow('').optional(),
    type: commonSchemas.reportType.required(),
    parameters: Joi.object().required(),
    isPublic: Joi.boolean().default(false)
  })
};

/**
 * Update report template schema
 */
const updateReportTemplateSchema = {
  params: Joi.object({
    id: commonSchemas.id.required()
  }),
  body: Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().allow('').optional(),
    parameters: Joi.object().optional(),
    isPublic: Joi.boolean().optional()
  }).min(1)
};

/**
 * Delete report template schema
 */
const deleteReportTemplateSchema = {
  params: Joi.object({
    id: commonSchemas.id.required()
  })
};

/**
 * Clone report template schema
 */
const cloneReportTemplateSchema = {
  params: Joi.object({
    id: commonSchemas.id.required()
  }),
  body: Joi.object({
    name: Joi.string().optional(),
    description: Joi.string().allow('').optional()
  })
};

module.exports = {
  generateComplianceReportSchema,
  generatePerformanceReportSchema,
  generateSecurityReportSchema,
  generateCustomReportSchema,
  getReportStatusSchema,
  downloadReportSchema,
  scheduleReportSchema,
  getAllScheduledReportsSchema,
  getMyScheduledReportsSchema,
  getScheduledReportByIdSchema,
  updateScheduledReportSchema,
  deleteScheduledReportSchema,
  runScheduledReportSchema,
  getAllReportTemplatesSchema,
  getReportTemplateByIdSchema,
  createReportTemplateSchema,
  updateReportTemplateSchema,
  deleteReportTemplateSchema,
  cloneReportTemplateSchema
};
