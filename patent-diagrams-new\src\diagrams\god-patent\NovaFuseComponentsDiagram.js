import React from 'react';
import {
  Diagram<PERSON>rame,
  ContainerBox,
  ContainerLabel,
  InventorLabel
} from '../../components/DiagramComponents';

const NovaFuseComponentsDiagram = () => {
  // Define the 13 Universal NovaFuse Components
  const components = [
    { id: 1, name: 'NovaCore', desc: 'Universal Compliance Testing Framework' },
    { id: 2, name: 'NovaShield', desc: 'Universal Vendor Risk Management' },
    { id: 3, name: 'NovaTrack', desc: 'Universal Compliance Tracking' },
    { id: 4, name: 'NovaLearn', desc: 'Universal Adaptive Learning' },
    { id: 5, name: 'NovaView', desc: 'Universal Visualization' },
    { id: 6, name: 'NovaFlowX', desc: 'Universal Workflow Automation' },
    { id: 7, name: 'NovaPulse+', desc: 'Universal Regulatory Change Management' },
    { id: 8, name: 'NovaProof', desc: 'Universal Compliance Evidence' },
    { id: 9, name: 'NovaThink', desc: 'Universal Compliance Intelligence' },
    { id: 10, name: 'NovaConnect', desc: 'Universal API Connector' },
    { id: 11, name: 'NovaVision', desc: 'Universal UI Framework' },
    { id: 12, name: 'NovaDNA', desc: 'Universal Identity Graph' },
    { id: 13, name: 'NovaStore', desc: 'Universal API Marketplace' }
  ];

  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel>NOVAFUSE 13 UNIVERSAL COMPONENTS - COMPHYOLOGY (Ψᶜ) FRAMEWORK</ContainerLabel>
      </ContainerBox>

      <div style={{
        position: 'absolute',
        top: '100px',
        left: '50px',
        width: '700px',
        padding: '20px',
        boxSizing: 'border-box'
      }}>
        <h2 style={{
          textAlign: 'center',
          fontSize: '18px',
          marginBottom: '30px',
          color: '#555' // Grey color for heading
        }}>
          The Immutable Backbone of Intelligent Compliance
        </h2>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '20px',
          fontSize: '14px',
          lineHeight: '1.5'
        }}>
          {components.map((component, idx) => (
            <div key={idx} style={{
              padding: '10px',
              border: '2px solid #555', // Grey border instead of dark
              borderRadius: '6px',
              backgroundColor: 'white'
            }}>
              <strong style={{ color: '#444' }}>{component.id}. {component.name}</strong> {/* Grey text for component names */}
              <div style={{ color: '#666' }}>{component.desc}</div> {/* Lighter grey for descriptions */}
            </div>
          ))}
        </div>

        <div style={{
          marginTop: '30px',
          textAlign: 'center',
          fontWeight: 'bold',
          fontSize: '14px',
          color: '#444' // Grey color for UUFT equation
        }}>
          Universal Unified Field Theory (UUFT): (A⊗B⊕C)×π10³
        </div>

        <div style={{
          marginTop: '20px',
          textAlign: 'center',
          fontSize: '14px',
          color: '#555' // Grey color for footer text
        }}>
          These components unify GRC, AI, and User Empowerment into a cohesive system.
        </div>
      </div>

      <InventorLabel style={{ color: '#666' }}>Inventor: David Nigel Irvin</InventorLabel> {/* Grey color for inventor label */}
    </DiagramFrame>
  );
};

export default NovaFuseComponentsDiagram;
