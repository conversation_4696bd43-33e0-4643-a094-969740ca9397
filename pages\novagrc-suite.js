import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../components/PageWithSidebar';

const NovaGRCSuite = () => {
  // SEO metadata
  const pageProps = {
    title: 'NovaGRC Suite - Comprehensive API-first GRC Solution - NovaFuse',
    description: 'NovaGRC Suite: Comprehensive API-first governance, risk, and compliance solution powered by the Universal API Connector. Manage privacy, regulatory compliance, security, and controls.',
    keywords: 'NovaGRC Suite, API-first GRC, governance, risk, compliance, privacy management, regulatory compliance, security assessment, control testing, NovaFuse',
    canonical: 'https://novafuse.io/novagrc-suite',
    ogImage: '/images/novagrc-suite-og-image.png'
  };

  const sidebarItems = [
    { label: 'Suite Overview', href: '#suite-overview' },
    { label: 'API Categories', href: '#api-categories' },
    { label: 'Key Features', href: '#key-features' },
    { label: 'Compliance Frameworks', href: '#compliance-frameworks' },
    { label: 'Integration Capabilities', href: '#integration-capabilities' },
    { label: 'Documentation', href: '/api-docs' },
  ];

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-2/3 mb-6 md:mb-0 md:pr-8">
              <h1 className="text-3xl font-bold mb-4">NovaGRC Suite</h1>
              <p className="text-xl mb-6">
                Comprehensive API-first governance, risk, and compliance solution powered by the Universal API Connector.
              </p>
              <div className="flex flex-wrap gap-4">
                <Link href="/api-docs" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                  View API Documentation
                </Link>
                <Link href="/partner-empowerment" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                  Partner With Us
                </Link>
              </div>
            </div>
            <div className="md:w-1/3">
              <div className="bg-blue-800 p-6 rounded-lg shadow-inner">
                <h3 className="text-xl font-bold mb-3">API Categories</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Privacy Management</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Regulatory Compliance</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Security Assessment</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text font-bold">✓</span>
                    <span>Control Testing</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Suite Overview Section */}
        <div id="suite-overview" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Suite Overview</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              The NovaGRC Suite is a comprehensive collection of API-first governance, risk, and compliance solutions designed to help organizations manage their compliance posture across multiple frameworks and regulations.
            </p>
            <p className="mb-4">
              Powered by the Universal API Connector (UAC), the NovaGRC Suite provides seamless integration with your existing technology stack, enabling real-time compliance monitoring, automated evidence collection, and bidirectional control enforcement.
            </p>
            <p className="mb-4">
              Unlike traditional GRC platforms that operate in isolation, the NovaGRC Suite acts as a compliance nervous system, connecting and orchestrating compliance activities across your entire organization.
            </p>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">API-First Architecture</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>100% API coverage for all features</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>RESTful and GraphQL interfaces</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Webhook support for real-time events</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Comprehensive SDK support</span>
                  </li>
                </ul>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Modular Design</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Use only the APIs you need</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Flexible deployment options</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Seamless integration with existing tools</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Custom workflow support</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* API Categories Section */}
        <div id="api-categories" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">API Categories</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
              <h3 className="text-xl font-bold mb-3">Privacy Management API</h3>
              <p className="text-sm text-blue-100 mb-4">
                Comprehensive APIs for managing privacy compliance, data subject requests, consent management, and privacy impact assessments.
              </p>
              <div className="bg-blue-800 bg-opacity-50 p-3 rounded-lg">
                <h4 className="font-semibold mb-2">Key Endpoints:</h4>
                <ul className="text-sm space-y-1">
                  <li>• /data-subjects</li>
                  <li>• /consent-records</li>
                  <li>• /data-processing-activities</li>
                  <li>• /privacy-impact-assessments</li>
                </ul>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
              <h3 className="text-xl font-bold mb-3">Regulatory Compliance API</h3>
              <p className="text-sm text-blue-100 mb-4">
                APIs for managing compliance with various regulatory frameworks, including control mapping, evidence collection, and compliance reporting.
              </p>
              <div className="bg-blue-800 bg-opacity-50 p-3 rounded-lg">
                <h4 className="font-semibold mb-2">Key Endpoints:</h4>
                <ul className="text-sm space-y-1">
                  <li>• /frameworks</li>
                  <li>• /controls</li>
                  <li>• /evidence</li>
                  <li>• /compliance-reports</li>
                </ul>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
              <h3 className="text-xl font-bold mb-3">Security Assessment API</h3>
              <p className="text-sm text-blue-100 mb-4">
                APIs for security risk assessments, vulnerability management, threat modeling, and security control implementation.
              </p>
              <div className="bg-blue-800 bg-opacity-50 p-3 rounded-lg">
                <h4 className="font-semibold mb-2">Key Endpoints:</h4>
                <ul className="text-sm space-y-1">
                  <li>• /risk-assessments</li>
                  <li>• /vulnerabilities</li>
                  <li>• /threat-models</li>
                  <li>• /security-controls</li>
                </ul>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
              <h3 className="text-xl font-bold mb-3">Control Testing API</h3>
              <p className="text-sm text-blue-100 mb-4">
                APIs for automated control testing, test case management, test results tracking, and remediation workflows.
              </p>
              <div className="bg-blue-800 bg-opacity-50 p-3 rounded-lg">
                <h4 className="font-semibold mb-2">Key Endpoints:</h4>
                <ul className="text-sm space-y-1">
                  <li>• /test-cases</li>
                  <li>• /test-runs</li>
                  <li>• /test-results</li>
                  <li>• /remediation-plans</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Key Features Section */}
        <div id="key-features" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Key Features</h2>

          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">Automated Evidence Collection</h3>
                <p className="text-sm">
                  Automatically collect and validate compliance evidence from connected systems, eliminating manual evidence gathering and reducing audit preparation time by up to 90%.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">Cross-Framework Mapping</h3>
                <p className="text-sm">
                  Map controls across multiple compliance frameworks, allowing you to achieve compliance with multiple standards simultaneously with minimal duplication of effort.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">Real-Time Compliance Monitoring</h3>
                <p className="text-sm">
                  Continuously monitor your compliance posture in real-time, with instant alerts for control failures or policy violations across your connected systems.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">Bidirectional Control Enforcement</h3>
                <p className="text-sm">
                  Not just monitor but enforce compliance controls across your technology stack, ensuring that policy changes are automatically implemented in connected systems.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">AI-Powered Risk Assessment</h3>
                <p className="text-sm">
                  Leverage AI to identify, assess, and prioritize risks across your organization, with predictive analytics to anticipate potential compliance issues before they occur.
                </p>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">Customizable Workflows</h3>
                <p className="text-sm">
                  Create and automate custom compliance workflows that match your organization's specific processes, with flexible approval chains and escalation paths.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Compliance Frameworks Section */}
        <div id="compliance-frameworks" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Compliance Frameworks</h2>

          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-6">
              The NovaGRC Suite supports a wide range of compliance frameworks and regulations out of the box, with new frameworks added regularly.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-2">Security</h3>
                <ul className="text-sm space-y-1">
                  <li>• SOC 2</li>
                  <li>• ISO 27001</li>
                  <li>• NIST CSF</li>
                  <li>• CIS Controls</li>
                  <li>• PCI DSS</li>
                </ul>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-2">Privacy</h3>
                <ul className="text-sm space-y-1">
                  <li>• GDPR</li>
                  <li>• CCPA/CPRA</li>
                  <li>• LGPD</li>
                  <li>• PIPEDA</li>
                  <li>• APPI</li>
                </ul>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-2">Industry-Specific</h3>
                <ul className="text-sm space-y-1">
                  <li>• HIPAA</li>
                  <li>• GLBA</li>
                  <li>• FERPA</li>
                  <li>• FISMA</li>
                  <li>• NERC CIP</li>
                </ul>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-2">Government</h3>
                <ul className="text-sm space-y-1">
                  <li>• FedRAMP</li>
                  <li>• CMMC</li>
                  <li>• StateRAMP</li>
                  <li>• IRAP</li>
                  <li>• IL2/4/5</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Integration Capabilities Section */}
        <div id="integration-capabilities" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Integration Capabilities</h2>

          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-6">
              Powered by the Universal API Connector, the NovaGRC Suite integrates seamlessly with your existing technology stack, enabling comprehensive compliance management across your entire organization.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">Cloud Platforms</h3>
                <ul className="text-sm space-y-1">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>AWS</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Google Cloud</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Microsoft Azure</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Oracle Cloud</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>IBM Cloud</span>
                  </li>
                </ul>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">Security Tools</h3>
                <ul className="text-sm space-y-1">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Identity Providers (Okta, Auth0)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>SIEM Solutions (Splunk, ELK)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Vulnerability Scanners</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>EDR/XDR Platforms</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>CASB Solutions</span>
                  </li>
                </ul>
              </div>

              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-lg font-bold mb-3">Business Systems</h3>
                <ul className="text-sm space-y-1">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>HRIS (Workday, BambooHR)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>CRM (Salesforce, HubSpot)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>ITSM (ServiceNow, Jira)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>ERP Systems</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Collaboration Tools</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Ready to Transform Your Compliance Program?</h2>
            <p className="mb-6">
              Explore our API documentation or partner with us to bring the power of the NovaGRC Suite to your customers.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/api-docs" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                View API Documentation
              </Link>
              <Link href="/partner-empowerment" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Partner With Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default NovaGRCSuite;
