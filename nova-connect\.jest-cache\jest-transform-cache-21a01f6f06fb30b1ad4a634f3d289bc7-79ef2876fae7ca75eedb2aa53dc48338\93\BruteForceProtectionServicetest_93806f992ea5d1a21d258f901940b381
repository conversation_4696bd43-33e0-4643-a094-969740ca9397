7160e686ef052f866cb8eab217edade3
// Mock fs.promises
_getJestObj().mock('fs', () => ({
  promises: {
    mkdir: jest.fn().mockResolvedValue(undefined),
    readFile: jest.fn(),
    writeFile: jest.fn().mockResolvedValue(undefined)
  }
}));

// Mock Date.now to control time
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Brute Force Protection Service Tests
 */

const BruteForceProtectionService = require('../../../api/services/BruteForceProtectionService');
const {
  BruteForceError
} = require('../../../api/utils/errors');
const fs = require('fs').promises;
const path = require('path');
const mockDateNow = jest.spyOn(Date, 'now');
describe('BruteForceProtectionService', () => {
  let bruteForceService;
  const testDataDir = path.join(__dirname, 'test-data');
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Reset Date.now mock
    mockDateNow.mockReset();
    mockDateNow.mockImplementation(() => 1000); // Mock current time

    // Create a new instance for each test
    bruteForceService = new BruteForceProtectionService(testDataDir);

    // Override the config for testing
    bruteForceService.config = {
      maxAttempts: 3,
      windowMs: 15 * 60 * 1000,
      // 15 minutes
      blockDuration: 30 * 60 * 1000 // 30 minutes
    };
  });
  describe('constructor', () => {
    it('should initialize with the correct data directory', () => {
      expect(bruteForceService.dataDir).toBe(testDataDir);
      expect(bruteForceService.attemptsFile).toBe(path.join(testDataDir, 'login_attempts.json'));
    });
    it('should call ensureDataDir', () => {
      expect(fs.mkdir).toHaveBeenCalledWith(testDataDir, {
        recursive: true
      });
    });
  });
  describe('loadAttempts', () => {
    it('should load attempts from file', async () => {
      const mockAttempts = {
        'test-user': {
          count: 1,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockAttempts));
      const attempts = await bruteForceService.loadAttempts();
      expect(fs.readFile).toHaveBeenCalledWith(bruteForceService.attemptsFile, 'utf8');
      expect(attempts).toEqual(mockAttempts);
    });
    it('should return empty object if file does not exist', async () => {
      const error = new Error('File not found');
      error.code = 'ENOENT';
      fs.readFile.mockRejectedValueOnce(error);
      const attempts = await bruteForceService.loadAttempts();
      expect(attempts).toEqual({});
    });
    it('should throw error if file read fails for other reasons', async () => {
      const error = new Error('Permission denied');
      fs.readFile.mockRejectedValueOnce(error);
      await expect(bruteForceService.loadAttempts()).rejects.toThrow('Permission denied');
    });
  });
  describe('saveAttempts', () => {
    it('should save attempts to file', async () => {
      const attempts = {
        'test-user': {
          count: 1,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      await bruteForceService.saveAttempts(attempts);
      expect(fs.writeFile).toHaveBeenCalledWith(bruteForceService.attemptsFile, JSON.stringify(attempts, null, 2));
    });
    it('should throw error if file write fails', async () => {
      const error = new Error('Permission denied');
      fs.writeFile.mockRejectedValueOnce(error);
      await expect(bruteForceService.saveAttempts({})).rejects.toThrow('Permission denied');
    });
  });
  describe('recordFailedAttempt', () => {
    it('should initialize attempts for new identifier', async () => {
      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));
      const result = await bruteForceService.recordFailedAttempt('test-user');
      expect(result).toEqual({
        count: 1,
        firstAttempt: 1000,
        lastAttempt: 1000,
        blocked: false,
        blockedUntil: null
      });
      expect(fs.writeFile).toHaveBeenCalled();
    });
    it('should increment count for existing identifier', async () => {
      const existingAttempts = {
        'test-user': {
          count: 1,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      const result = await bruteForceService.recordFailedAttempt('test-user');
      expect(result).toEqual({
        count: 2,
        firstAttempt: 500,
        lastAttempt: 1000,
        blocked: false,
        blockedUntil: null
      });
      expect(fs.writeFile).toHaveBeenCalled();
    });
    it('should block identifier after max attempts', async () => {
      const existingAttempts = {
        'test-user': {
          count: 2,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      const result = await bruteForceService.recordFailedAttempt('test-user');
      expect(result).toEqual({
        count: 3,
        firstAttempt: 500,
        lastAttempt: 1000,
        blocked: true,
        blockedUntil: 1000 + bruteForceService.config.blockDuration
      });
      expect(fs.writeFile).toHaveBeenCalled();
    });
  });
  describe('isBlocked', () => {
    it('should return false for unknown identifier', async () => {
      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));
      const result = await bruteForceService.isBlocked('test-user');
      expect(result).toBe(false);
    });
    it('should return false for non-blocked identifier', async () => {
      const existingAttempts = {
        'test-user': {
          count: 1,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: false,
          blockedUntil: null
        }
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      const result = await bruteForceService.isBlocked('test-user');
      expect(result).toBe(false);
    });
    it('should return block info for blocked identifier', async () => {
      const blockedUntil = 1000 + 60000; // 1 minute from now
      const existingAttempts = {
        'test-user': {
          count: 3,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: true,
          blockedUntil
        }
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      const result = await bruteForceService.isBlocked('test-user');
      expect(result).toEqual({
        blocked: true,
        remainingTime: 60,
        attemptsCount: 3,
        maxAttempts: 3
      });
    });
    it('should reset block if block duration has expired', async () => {
      const blockedUntil = 500; // Already expired
      const existingAttempts = {
        'test-user': {
          count: 3,
          firstAttempt: 100,
          lastAttempt: 100,
          blocked: true,
          blockedUntil
        }
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      const result = await bruteForceService.isBlocked('test-user');
      expect(result).toBe(false);
      expect(fs.writeFile).toHaveBeenCalled();
    });
  });
  describe('checkLoginAttempt', () => {
    it('should throw BruteForceError if identifier is blocked', async () => {
      const blockedUntil = 1000 + 60000; // 1 minute from now
      const existingAttempts = {
        'test-user': {
          count: 3,
          firstAttempt: 500,
          lastAttempt: 500,
          blocked: true,
          blockedUntil
        }
      };
      fs.readFile.mockResolvedValueOnce(JSON.stringify(existingAttempts));
      await expect(bruteForceService.checkLoginAttempt('test-user')).rejects.toThrow(BruteForceError);
    });
    it('should return true if identifier is not blocked', async () => {
      fs.readFile.mockResolvedValueOnce(JSON.stringify({}));
      const result = await bruteForceService.checkLoginAttempt('test-user');
      expect(result).toBe(true);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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