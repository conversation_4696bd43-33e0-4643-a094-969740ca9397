/**
 * NovaConnect DAST (Dynamic Application Security Testing) Scan
 * 
 * This script runs dynamic security tests against a running instance
 * of the NovaConnect API to identify security vulnerabilities.
 * 
 * Usage:
 *   npm run security:dast
 */

const axios = require('axios');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  outputDir: path.join(__dirname, '../../security-reports/dast'),
  targetUrl: process.env.DAST_TARGET_URL || 'http://localhost:3000',
  apiKey: process.env.DAST_API_KEY || 'test-api-key',
  zapPath: process.env.ZAP_PATH || 'zap.sh',
  skipZap: process.env.SKIP_ZAP === 'true'
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

/**
 * Run basic HTTP security tests
 */
async function runHttpSecurityTests() {
  console.log('Running HTTP security tests...');
  
  const results = {
    missingHeaders: [],
    insecureHeaders: [],
    corsIssues: [],
    csrfIssues: []
  };
  
  try {
    // Test for security headers
    const response = await axios.get(`${config.targetUrl}/health`);
    
    // Check for missing security headers
    const requiredHeaders = [
      'strict-transport-security',
      'x-content-type-options',
      'x-frame-options',
      'content-security-policy',
      'x-xss-protection'
    ];
    
    for (const header of requiredHeaders) {
      if (!response.headers[header]) {
        results.missingHeaders.push(header);
      }
    }
    
    // Check for insecure headers
    if (response.headers['server']) {
      results.insecureHeaders.push('server');
    }
    
    if (response.headers['x-powered-by']) {
      results.insecureHeaders.push('x-powered-by');
    }
    
    // Test CORS configuration
    try {
      const corsResponse = await axios.get(`${config.targetUrl}/health`, {
        headers: {
          'Origin': 'https://malicious-site.com'
        }
      });
      
      if (corsResponse.headers['access-control-allow-origin'] === 'https://malicious-site.com') {
        results.corsIssues.push('Allows requests from arbitrary origins');
      }
    } catch (error) {
      // Expected behavior - should reject cross-origin requests
    }
    
    // Test CSRF protection
    try {
      const csrfResponse = await axios.post(`${config.targetUrl}/api/auth/login`, {
        username: 'test',
        password: 'test'
      }, {
        headers: {
          'Origin': 'https://malicious-site.com',
          'Referer': 'https://malicious-site.com'
        }
      });
      
      if (csrfResponse.status === 200 || csrfResponse.status === 401) {
        results.csrfIssues.push('No CSRF protection on login endpoint');
      }
    } catch (error) {
      // Check if the error is due to CSRF protection
      if (error.response && error.response.status === 403) {
        // CSRF protection working
      } else {
        // Other error
        results.csrfIssues.push(`Error testing CSRF: ${error.message}`);
      }
    }
    
    // Write results to file
    const outputFile = path.join(config.outputDir, 'http-security.json');
    fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
    
    console.log(`HTTP security tests completed. Results saved to ${outputFile}`);
    return results;
  } catch (error) {
    console.error('HTTP security tests failed:', error.message);
    return results;
  }
}

/**
 * Run API security tests
 */
async function runApiSecurityTests() {
  console.log('Running API security tests...');
  
  const results = {
    authenticationIssues: [],
    authorizationIssues: [],
    inputValidationIssues: [],
    rateLimit: {
      implemented: false,
      details: null
    }
  };
  
  try {
    // Test authentication
    try {
      const noAuthResponse = await axios.get(`${config.targetUrl}/api/connectors`);
      
      if (noAuthResponse.status === 200) {
        results.authenticationIssues.push('Endpoint accessible without authentication');
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior - authentication required
      } else {
        results.authenticationIssues.push(`Error testing authentication: ${error.message}`);
      }
    }
    
    // Test with invalid API key
    try {
      const invalidAuthResponse = await axios.get(`${config.targetUrl}/api/connectors`, {
        headers: {
          'Authorization': 'Bearer invalid-api-key'
        }
      });
      
      if (invalidAuthResponse.status === 200) {
        results.authenticationIssues.push('Accepts invalid API key');
      }
    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Expected behavior - invalid authentication rejected
      } else {
        results.authenticationIssues.push(`Error testing invalid authentication: ${error.message}`);
      }
    }
    
    // Test input validation with SQL injection attempt
    try {
      const sqlInjectionResponse = await axios.post(`${config.targetUrl}/api/connectors/search`, {
        query: "' OR 1=1; --"
      }, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        }
      });
      
      // Check if the response contains too many results, indicating possible SQL injection
      if (sqlInjectionResponse.data && sqlInjectionResponse.data.length > 10) {
        results.inputValidationIssues.push('Possible SQL injection vulnerability');
      }
    } catch (error) {
      if (error.response && error.response.status === 400) {
        // Expected behavior - input validation rejected the malicious input
      } else {
        results.inputValidationIssues.push(`Error testing SQL injection: ${error.message}`);
      }
    }
    
    // Test rate limiting
    const rateLimitResults = await testRateLimiting();
    results.rateLimit = rateLimitResults;
    
    // Write results to file
    const outputFile = path.join(config.outputDir, 'api-security.json');
    fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
    
    console.log(`API security tests completed. Results saved to ${outputFile}`);
    return results;
  } catch (error) {
    console.error('API security tests failed:', error.message);
    return results;
  }
}

/**
 * Test rate limiting
 */
async function testRateLimiting() {
  console.log('Testing rate limiting...');
  
  const result = {
    implemented: false,
    details: null
  };
  
  try {
    // Make multiple requests in quick succession
    const requests = [];
    for (let i = 0; i < 50; i++) {
      requests.push(axios.get(`${config.targetUrl}/health`));
    }
    
    const responses = await Promise.all(requests);
    
    // Check if any responses have rate limiting headers
    const rateLimitHeaders = [
      'x-ratelimit-limit',
      'x-ratelimit-remaining',
      'x-ratelimit-reset',
      'retry-after'
    ];
    
    let hasRateLimitHeaders = false;
    let rateLimited = false;
    
    for (const response of responses) {
      const headers = response.headers;
      
      for (const header of rateLimitHeaders) {
        if (headers[header]) {
          hasRateLimitHeaders = true;
          break;
        }
      }
      
      if (hasRateLimitHeaders) {
        break;
      }
    }
    
    // Check if any requests were rate limited
    for (const response of responses) {
      if (response.status === 429) {
        rateLimited = true;
        break;
      }
    }
    
    result.implemented = hasRateLimitHeaders || rateLimited;
    result.details = {
      hasRateLimitHeaders,
      rateLimited,
      requestCount: responses.length
    };
    
    return result;
  } catch (error) {
    if (error.response && error.response.status === 429) {
      // Rate limiting is implemented
      result.implemented = true;
      result.details = {
        hasRateLimitHeaders: true,
        rateLimited: true,
        error: error.message
      };
    } else {
      result.details = {
        error: error.message
      };
    }
    
    return result;
  }
}

/**
 * Run OWASP ZAP scan
 */
async function runZapScan() {
  if (config.skipZap) {
    console.log('Skipping OWASP ZAP scan (SKIP_ZAP=true)');
    return true;
  }
  
  console.log('Running OWASP ZAP scan...');
  
  try {
    // Check if ZAP is installed
    try {
      execSync(`${config.zapPath} -version`, { stdio: 'ignore' });
    } catch (error) {
      console.error('OWASP ZAP not found. Please install it or set the correct path in ZAP_PATH environment variable.');
      return false;
    }
    
    const outputFile = path.join(config.outputDir, 'zap-report.html');
    
    // Run ZAP scan
    const command = `${config.zapPath} -cmd -quickurl ${config.targetUrl} -quickout ${outputFile}`;
    
    execSync(command, { stdio: 'inherit' });
    
    console.log(`OWASP ZAP scan completed. Results saved to ${outputFile}`);
    return true;
  } catch (error) {
    console.error('OWASP ZAP scan failed:', error.message);
    return false;
  }
}

/**
 * Generate security report summary
 */
function generateSummary(httpResults, apiResults) {
  console.log('Generating DAST report summary...');
  
  const summaryFile = path.join(config.outputDir, 'dast-summary.md');
  
  let summary = `# NovaConnect DAST Scan Summary\n\n`;
  summary += `Generated on: ${new Date().toISOString()}\n\n`;
  summary += `Target URL: ${config.targetUrl}\n\n`;
  
  // Add HTTP security results
  summary += `## HTTP Security Tests\n\n`;
  
  if (httpResults.missingHeaders.length > 0) {
    summary += `### Missing Security Headers\n\n`;
    for (const header of httpResults.missingHeaders) {
      summary += `- ${header}\n`;
    }
    summary += `\n`;
  } else {
    summary += `All required security headers are present.\n\n`;
  }
  
  if (httpResults.insecureHeaders.length > 0) {
    summary += `### Insecure Headers\n\n`;
    for (const header of httpResults.insecureHeaders) {
      summary += `- ${header}\n`;
    }
    summary += `\n`;
  }
  
  if (httpResults.corsIssues.length > 0) {
    summary += `### CORS Issues\n\n`;
    for (const issue of httpResults.corsIssues) {
      summary += `- ${issue}\n`;
    }
    summary += `\n`;
  }
  
  if (httpResults.csrfIssues.length > 0) {
    summary += `### CSRF Issues\n\n`;
    for (const issue of httpResults.csrfIssues) {
      summary += `- ${issue}\n`;
    }
    summary += `\n`;
  }
  
  // Add API security results
  summary += `## API Security Tests\n\n`;
  
  if (apiResults.authenticationIssues.length > 0) {
    summary += `### Authentication Issues\n\n`;
    for (const issue of apiResults.authenticationIssues) {
      summary += `- ${issue}\n`;
    }
    summary += `\n`;
  } else {
    summary += `No authentication issues found.\n\n`;
  }
  
  if (apiResults.authorizationIssues.length > 0) {
    summary += `### Authorization Issues\n\n`;
    for (const issue of apiResults.authorizationIssues) {
      summary += `- ${issue}\n`;
    }
    summary += `\n`;
  } else {
    summary += `No authorization issues found.\n\n`;
  }
  
  if (apiResults.inputValidationIssues.length > 0) {
    summary += `### Input Validation Issues\n\n`;
    for (const issue of apiResults.inputValidationIssues) {
      summary += `- ${issue}\n`;
    }
    summary += `\n`;
  } else {
    summary += `No input validation issues found.\n\n`;
  }
  
  summary += `### Rate Limiting\n\n`;
  if (apiResults.rateLimit.implemented) {
    summary += `Rate limiting is implemented.\n\n`;
  } else {
    summary += `Rate limiting is not implemented or could not be detected.\n\n`;
  }
  
  // Add ZAP scan results
  if (!config.skipZap) {
    summary += `## OWASP ZAP Scan\n\n`;
    summary += `OWASP ZAP scan results are available in the HTML report.\n\n`;
  }
  
  // Write summary to file
  fs.writeFileSync(summaryFile, summary);
  
  console.log(`DAST report summary generated: ${summaryFile}`);
}

/**
 * Main function
 */
async function main() {
  console.log('Starting NovaConnect DAST scan...');
  console.log(`Target URL: ${config.targetUrl}`);
  
  // Run HTTP security tests
  const httpResults = await runHttpSecurityTests();
  
  // Run API security tests
  const apiResults = await runApiSecurityTests();
  
  // Run OWASP ZAP scan
  const zapResult = await runZapScan();
  
  // Generate summary
  generateSummary(httpResults, apiResults);
  
  console.log('\nDAST scan completed.');
  console.log(`Results saved to ${config.outputDir}`);
}

// Run the main function
main().catch(error => {
  console.error('DAST scan failed:', error);
  process.exit(1);
});
