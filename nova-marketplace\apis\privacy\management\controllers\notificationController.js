/**
 * Notification Controller
 * 
 * This controller handles operations related to notifications.
 */

const { Notification } = require('../models');

// Get all notifications with pagination and filtering
const getAllNotifications = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.recipient) {
      filter.recipient = req.query.recipient;
    }
    
    if (req.query.type) {
      filter.type = req.query.type;
    }
    
    if (req.query.priority) {
      filter.priority = req.query.priority;
    }
    
    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }
    
    // Date range filtering
    if (req.query.startDate || req.query.endDate) {
      filter.createdAt = {};
      
      if (req.query.startDate) {
        filter.createdAt.$gte = new Date(req.query.startDate);
      }
      
      if (req.query.endDate) {
        filter.createdAt.$lte = new Date(req.query.endDate);
      }
    }
    
    // Build sort object
    const sort = {};
    
    if (req.query.sortBy) {
      sort[req.query.sortBy] = req.query.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by creation date descending
    }
    
    // Execute query with pagination
    const notifications = await Notification
      .find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await Notification.countDocuments(filter);
    
    res.json({
      data: notifications,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get a specific notification by ID
const getNotificationById = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id);
    
    if (!notification) {
      const error = new Error('Notification not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    res.json({
      data: notification
    });
  } catch (error) {
    next(error);
  }
};

// Create a new notification
const createNotification = async (req, res, next) => {
  try {
    const notification = new Notification(req.body);
    await notification.save();
    
    res.status(201).json({
      data: notification,
      message: 'Notification created successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Mark a notification as read
const markNotificationAsRead = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id);
    
    if (!notification) {
      const error = new Error('Notification not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    notification.status = 'read';
    notification.readAt = new Date();
    
    await notification.save();
    
    res.json({
      data: notification,
      message: 'Notification marked as read'
    });
  } catch (error) {
    next(error);
  }
};

// Send a notification
const sendNotification = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id);
    
    if (!notification) {
      const error = new Error('Notification not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    if (notification.status !== 'pending') {
      const error = new Error(`Notification cannot be sent because it is in '${notification.status}' status`);
      error.name = 'ValidationError';
      throw error;
    }
    
    // In a real implementation, this would send the notification through the appropriate channel
    // For now, we'll just update the status
    
    notification.status = 'sent';
    notification.sentAt = new Date();
    
    await notification.save();
    
    res.json({
      data: notification,
      message: 'Notification sent successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Generate notifications based on criteria
const generateNotifications = async (req, res, next) => {
  try {
    const { notificationType, filter } = req.body;
    
    if (!notificationType) {
      const error = new Error('Notification type is required');
      error.name = 'ValidationError';
      throw error;
    }
    
    // In a real implementation, this would query the appropriate entities based on the filter
    // and generate notifications for each entity
    // For now, we'll just return a mock result
    
    const generatedCount = Math.floor(Math.random() * 5) + 1; // Random number between 1 and 5
    
    res.json({
      data: {
        generatedCount,
        notificationType,
        filter
      },
      message: `${generatedCount} notifications generated successfully`
    });
  } catch (error) {
    next(error);
  }
};

// Send all pending notifications
const sendAllPendingNotifications = async (req, res, next) => {
  try {
    // In a real implementation, this would find all pending notifications and send them
    // For now, we'll just return a mock result
    
    const pendingCount = Math.floor(Math.random() * 10) + 1; // Random number between 1 and 10
    const sentCount = Math.floor(pendingCount * 0.8); // 80% success rate
    const failedCount = pendingCount - sentCount;
    
    res.json({
      data: {
        pendingCount,
        sentCount,
        failedCount
      },
      message: `${sentCount} notifications sent successfully, ${failedCount} failed`
    });
  } catch (error) {
    next(error);
  }
};

// Get notifications by recipient
const getNotificationsByRecipient = async (req, res, next) => {
  try {
    const recipient = req.params.recipient;
    
    const notifications = await Notification.find({ recipient }).sort({ createdAt: -1 });
    
    res.json({
      data: notifications
    });
  } catch (error) {
    next(error);
  }
};

// Get notifications by related entity
const getNotificationsByRelatedEntity = async (req, res, next) => {
  try {
    const { entityType, entityId } = req.params;
    
    const notifications = await Notification.find({
      relatedEntityType: entityType,
      relatedEntityId: entityId
    }).sort({ createdAt: -1 });
    
    res.json({
      data: notifications
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllNotifications,
  getNotificationById,
  createNotification,
  markNotificationAsRead,
  sendNotification,
  generateNotifications,
  sendAllPendingNotifications,
  getNotificationsByRecipient,
  getNotificationsByRelatedEntity
};
