/**
 * NovaFuse Universal API Connector - Health Service
 * 
 * This module provides a service for health checks.
 */

const os = require('os');
const { createLogger } = require('../utils/logger');
const metricsService = require('./metrics-service');

const logger = createLogger('health-service');

// Initialize metrics
const healthCheckTotal = metricsService.registerCounter(
  'health_check_total',
  'Total number of health checks',
  ['status']
);

const healthCheckDuration = metricsService.registerHistogram(
  'health_check_duration_seconds',
  'Health check duration in seconds',
  ['check_name', 'status']
);

/**
 * Health Service class for performing health checks
 */
class HealthService {
  constructor() {
    // Initialize health checks
    this.checks = new Map();
    
    // Add default system checks
    this.addCheck('system.memory', this.checkMemory.bind(this));
    this.addCheck('system.cpu', this.checkCpu.bind(this));
    this.addCheck('system.disk', this.checkDisk.bind(this));
    
    logger.info('Health service initialized');
  }

  /**
   * Add a health check
   * 
   * @param {string} name - The check name
   * @param {Function} checkFn - The check function
   * @param {Object} options - Check options
   * @param {number} options.timeout - Check timeout in milliseconds
   * @param {boolean} options.critical - Whether the check is critical
   */
  addCheck(name, checkFn, options = {}) {
    this.checks.set(name, {
      name,
      check: checkFn,
      timeout: options.timeout || 5000,
      critical: options.critical !== undefined ? options.critical : true
    });
    
    logger.debug(`Added health check: ${name}`);
  }

  /**
   * Remove a health check
   * 
   * @param {string} name - The check name
   */
  removeCheck(name) {
    this.checks.delete(name);
    logger.debug(`Removed health check: ${name}`);
  }

  /**
   * Run all health checks
   * 
   * @returns {Promise<Object>} - The health check results
   */
  async runChecks() {
    const startTime = process.hrtime();
    
    const results = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: process.env.SERVICE_VERSION || '1.0.0',
      checks: {}
    };
    
    const checkPromises = [];
    
    // Run all checks
    for (const [name, check] of this.checks.entries()) {
      checkPromises.push(this._runCheck(name, check));
    }
    
    // Wait for all checks to complete
    const checkResults = await Promise.all(checkPromises);
    
    // Process results
    for (const result of checkResults) {
      results.checks[result.name] = result;
      
      // Update overall status
      if (result.status === 'error' && result.critical) {
        results.status = 'error';
      } else if (result.status === 'warn' && results.status !== 'error') {
        results.status = 'warn';
      }
    }
    
    // Calculate duration
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;
    results.duration = duration.toFixed(3);
    
    // Record metrics
    healthCheckTotal.inc(1, { status: results.status });
    
    logger.debug(`Health check completed with status: ${results.status}`);
    
    return results;
  }

  /**
   * Run a single health check
   * 
   * @param {string} name - The check name
   * @param {Object} check - The check configuration
   * @returns {Promise<Object>} - The check result
   */
  async _runCheck(name, check) {
    const startTime = process.hrtime();
    
    const result = {
      name,
      status: 'ok',
      timestamp: new Date().toISOString(),
      critical: check.critical
    };
    
    try {
      // Run check with timeout
      const checkPromise = check.check();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Check timed out after ${check.timeout}ms`)), check.timeout);
      });
      
      const checkResult = await Promise.race([checkPromise, timeoutPromise]);
      
      // Process check result
      if (checkResult && typeof checkResult === 'object') {
        Object.assign(result, checkResult);
      }
    } catch (error) {
      result.status = 'error';
      result.message = error.message;
      result.error = error.toString();
      
      logger.error(`Health check '${name}' failed:`, { error });
    }
    
    // Calculate duration
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;
    result.duration = duration.toFixed(3);
    
    // Record metrics
    healthCheckDuration.observe(duration, { check_name: name, status: result.status });
    
    return result;
  }

  /**
   * Check system memory
   * 
   * @returns {Object} - The check result
   */
  async checkMemory() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsage = usedMemory / totalMemory;
    
    const result = {
      metrics: {
        total_memory_bytes: totalMemory,
        free_memory_bytes: freeMemory,
        used_memory_bytes: usedMemory,
        memory_usage: memoryUsage.toFixed(2)
      }
    };
    
    // Set status based on memory usage
    if (memoryUsage > 0.9) {
      result.status = 'error';
      result.message = 'Memory usage is critical';
    } else if (memoryUsage > 0.8) {
      result.status = 'warn';
      result.message = 'Memory usage is high';
    }
    
    return result;
  }

  /**
   * Check CPU usage
   * 
   * @returns {Object} - The check result
   */
  async checkCpu() {
    const cpus = os.cpus();
    const cpuCount = cpus.length;
    
    let totalIdle = 0;
    let totalTick = 0;
    
    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }
    
    const cpuUsage = 1 - (totalIdle / totalTick);
    
    const result = {
      metrics: {
        cpu_count: cpuCount,
        cpu_usage: cpuUsage.toFixed(2)
      }
    };
    
    // Set status based on CPU usage
    if (cpuUsage > 0.9) {
      result.status = 'error';
      result.message = 'CPU usage is critical';
    } else if (cpuUsage > 0.8) {
      result.status = 'warn';
      result.message = 'CPU usage is high';
    }
    
    return result;
  }

  /**
   * Check disk usage
   * 
   * @returns {Object} - The check result
   */
  async checkDisk() {
    // This is a simplified check since we don't have direct disk info in Node.js
    // In a real implementation, we would use a library like diskusage
    
    return {
      status: 'ok',
      message: 'Disk check not implemented',
      metrics: {
        disk_check_implemented: false
      }
    };
  }
}

// Create singleton instance
const healthService = new HealthService();

module.exports = healthService;
