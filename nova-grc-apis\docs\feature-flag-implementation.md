# NovaFuse Feature Flag Implementation

This document outlines the implementation of feature flags for controlling API access and UI component visibility based on product tiers.

## Overview

Feature flags are used to control access to features based on the user's product tier. This allows us to:

1. Offer different feature sets for different product tiers
2. Gradually roll out new features
3. A/B test features with specific user segments
4. Provide a clear upgrade path for users

## Product Tiers

NovaFuse offers the following product tiers:

1. **NovaPrime**: Premium, full-featured offering with all capabilities
2. **NovaCore**: Mid-tier offering with essential functionality
3. **NovaShield**: Security-focused offering
4. **NovaLearn**: Training and education-focused offering

## Feature Flag Architecture

### Backend Implementation

The feature flag system is implemented at the API level using middleware:

```javascript
// middleware/featureFlags.js

const { getFeatureFlags } = require('../config/featureFlags');

/**
 * Middleware to check if a feature is enabled for the user's product tier
 * @param {string} feature - The feature to check
 * @returns {function} Express middleware function
 */
const checkFeatureAccess = (feature) => {
  return (req, res, next) => {
    const { user } = req;
    
    // Get user's product tier
    const productTier = user.productTier.toLowerCase(); // e.g., 'novaprime', 'novacore', etc.
    
    // Get feature flags configuration
    const featureFlags = getFeatureFlags();
    
    // Check if the feature is available for the user's product tier
    const hasAccess = featureFlags[feature] && featureFlags[feature][productTier];
    
    if (!hasAccess) {
      return res.status(403).json({
        error: 'Feature not available',
        message: `The requested feature is not available in your current plan.`,
        upgradeInfo: {
          feature,
          currentTier: productTier,
          availableIn: Object.keys(featureFlags[feature] || {}).filter(tier => featureFlags[feature][tier]),
          upgradeUrl: '/upgrade'
        }
      });
    }
    
    next();
  };
};

module.exports = { checkFeatureAccess };
```

### Frontend Implementation

The feature flag system is implemented at the UI level using a React hook:

```javascript
// hooks/useFeatureFlag.js

import { useContext } from 'react';
import { UserContext } from '../contexts/UserContext';
import { featureFlags } from '../config/featureFlags';

/**
 * Hook to check if a feature is enabled for the current user
 * @param {string} feature - The feature to check
 * @returns {object} Object containing access status and upgrade info
 */
const useFeatureFlag = (feature) => {
  const { user } = useContext(UserContext);
  
  // Get user's product tier
  const productTier = user.productTier.toLowerCase();
  
  // Check if the feature is available for the user's product tier
  const hasAccess = featureFlags[feature] && featureFlags[feature][productTier];
  
  // Return access status and upgrade info if needed
  return {
    hasAccess,
    upgradeInfo: !hasAccess ? {
      feature,
      currentTier: productTier,
      availableIn: Object.keys(featureFlags[feature] || {}).filter(tier => featureFlags[feature][tier]),
      upgradeUrl: '/upgrade'
    } : null
  };
};

export default useFeatureFlag;
```

## Feature Flag Configuration

The feature flags are defined in a configuration file:

```javascript
// config/featureFlags.js

/**
 * Feature flag configuration
 * Each feature has a map of product tiers and whether the feature is enabled for that tier
 */
const featureFlags = {
  // Privacy Management API
  'privacy-management-processing-activities': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: false
  },
  'privacy-management-subject-requests': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: false
  },
  'privacy-management-consent-records': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: false
  },
  'privacy-management-privacy-notices': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: false
  },
  'privacy-management-data-breaches': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: false
  },
  'privacy-management-impact-assessment': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  
  // Regulatory Compliance API
  'compliance-regulatory-frameworks': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: true
  },
  'compliance-regulatory-requirements': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: true
  },
  'compliance-regulatory-jurisdictions': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: true
  },
  'compliance-regulatory-changes': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: true
  },
  'compliance-regulatory-reports': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: true
  },
  'compliance-regulatory-gap-analysis': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  
  // Security Assessment API
  'security-assessment-vulnerabilities': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  'security-assessment-controls': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  'security-assessment-threats': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  'security-assessment-risk-assessments': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: false
  },
  'security-assessment-incidents': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  
  // Control Testing API
  'control-testing-controls': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: false
  },
  'control-testing-test-plans': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: false
  },
  'control-testing-test-cases': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: false
  },
  'control-testing-test-executions': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  'control-testing-evidence': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  
  // ESG API
  'esg-metrics': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: false
  },
  'esg-frameworks': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: false
  },
  'esg-disclosures': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  'esg-reports': {
    novaprime: true,
    novacore: true,
    novashield: false,
    novalearn: false
  },
  'esg-targets': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  
  // Compliance Automation API
  'compliance-automation-workflows': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  'compliance-automation-tasks': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: false
  },
  'compliance-automation-schedules': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  'compliance-automation-integrations': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  'compliance-automation-reports': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: false
  },
  
  // AI Capabilities
  'ai-basic-qa': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: true
  },
  'ai-regulatory-guidance': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: true
  },
  'ai-data-analysis': {
    novaprime: true,
    novacore: true,
    novashield: true,
    novalearn: true
  },
  'ai-predictive-analytics': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  'ai-custom-model-training': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  'ai-advanced-nlp': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  'ai-automated-reporting': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: false
  },
  'ai-security-analysis': {
    novaprime: true,
    novacore: false,
    novashield: true,
    novalearn: false
  },
  'ai-training-generation': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: true
  },
  'ai-interactive-learning': {
    novaprime: true,
    novacore: false,
    novashield: false,
    novalearn: true
  }
};

/**
 * Get the feature flags configuration
 * @returns {object} Feature flags configuration
 */
const getFeatureFlags = () => {
  return featureFlags;
};

module.exports = { getFeatureFlags };
```

## API Implementation

Feature flags are applied to API routes using the middleware:

```javascript
// routes/privacyManagement.js

const express = require('express');
const router = express.Router();
const { checkFeatureAccess } = require('../middleware/featureFlags');
const privacyController = require('../controllers/privacyManagement');

// Processing Activities
router.get('/processing-activities', 
  checkFeatureAccess('privacy-management-processing-activities'),
  privacyController.getProcessingActivities
);

router.post('/processing-activities', 
  checkFeatureAccess('privacy-management-processing-activities'),
  privacyController.createProcessingActivity
);

// Impact Assessment
router.get('/impact-assessment/:id', 
  checkFeatureAccess('privacy-management-impact-assessment'),
  privacyController.getImpactAssessment
);

router.post('/impact-assessment', 
  checkFeatureAccess('privacy-management-impact-assessment'),
  privacyController.createImpactAssessment
);

module.exports = router;
```

## UI Implementation

Feature flags are applied to UI components using the hook:

```jsx
// components/PrivacyDashboard.jsx

import React from 'react';
import useFeatureFlag from '../hooks/useFeatureFlag';
import UpgradePrompt from './UpgradePrompt';
import ProcessingActivities from './ProcessingActivities';
import ImpactAssessment from './ImpactAssessment';

const PrivacyDashboard = () => {
  const processingActivities = useFeatureFlag('privacy-management-processing-activities');
  const impactAssessment = useFeatureFlag('privacy-management-impact-assessment');
  
  return (
    <div className="privacy-dashboard">
      <h1>Privacy Management Dashboard</h1>
      
      {processingActivities.hasAccess ? (
        <ProcessingActivities />
      ) : (
        <UpgradePrompt info={processingActivities.upgradeInfo} />
      )}
      
      {impactAssessment.hasAccess ? (
        <ImpactAssessment />
      ) : (
        <UpgradePrompt info={impactAssessment.upgradeInfo} />
      )}
    </div>
  );
};

export default PrivacyDashboard;
```

## Upgrade Prompt Component

The upgrade prompt component is used to display information about upgrading when a feature is not available:

```jsx
// components/UpgradePrompt.jsx

import React from 'react';
import { Link } from 'react-router-dom';

const UpgradePrompt = ({ info }) => {
  if (!info) return null;
  
  const { feature, currentTier, availableIn, upgradeUrl } = info;
  
  // Format the feature name for display
  const featureName = feature
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
  
  // Get the lowest tier that has access to this feature
  const lowestTier = availableIn.length > 0 ? availableIn[0] : null;
  
  return (
    <div className="upgrade-prompt">
      <h3>Upgrade to Access {featureName}</h3>
      <p>
        This feature is not available in your current {currentTier.charAt(0).toUpperCase() + currentTier.slice(1)} plan.
      </p>
      {lowestTier && (
        <p>
          Upgrade to {lowestTier.charAt(0).toUpperCase() + lowestTier.slice(1)} or higher to access this feature.
        </p>
      )}
      <Link to={upgradeUrl} className="upgrade-button">
        Upgrade Now
      </Link>
    </div>
  );
};

export default UpgradePrompt;
```

## Feature Flag Management

### Adding New Features

To add a new feature flag:

1. Add the feature to the `featureFlags` object in `config/featureFlags.js`
2. Apply the feature flag to the relevant API routes using the `checkFeatureAccess` middleware
3. Apply the feature flag to the relevant UI components using the `useFeatureFlag` hook

### Updating Feature Access

To update which product tiers have access to a feature:

1. Update the feature in the `featureFlags` object in `config/featureFlags.js`
2. No changes are needed to the API routes or UI components

### Feature Flag Administration

A feature flag administration UI can be created to allow administrators to update feature flags without code changes:

1. Create a database table to store feature flags
2. Create an admin UI to manage feature flags
3. Update the `getFeatureFlags` function to retrieve flags from the database
4. Implement caching to improve performance

## Testing

### Unit Testing

```javascript
// tests/middleware/featureFlags.test.js

const { checkFeatureAccess } = require('../../middleware/featureFlags');
const { getFeatureFlags } = require('../../config/featureFlags');

// Mock the getFeatureFlags function
jest.mock('../../config/featureFlags');

describe('Feature Flag Middleware', () => {
  let req, res, next;
  
  beforeEach(() => {
    req = {
      user: {
        productTier: 'novacore'
      }
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    next = jest.fn();
    
    // Mock the feature flags
    getFeatureFlags.mockReturnValue({
      'test-feature': {
        novaprime: true,
        novacore: true,
        novashield: false,
        novalearn: false
      },
      'premium-feature': {
        novaprime: true,
        novacore: false,
        novashield: false,
        novalearn: false
      }
    });
  });
  
  test('should allow access to features available in the user\'s tier', () => {
    const middleware = checkFeatureAccess('test-feature');
    middleware(req, res, next);
    
    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
    expect(res.json).not.toHaveBeenCalled();
  });
  
  test('should deny access to features not available in the user\'s tier', () => {
    const middleware = checkFeatureAccess('premium-feature');
    middleware(req, res, next);
    
    expect(next).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'Feature not available'
    }));
  });
  
  test('should handle unknown features', () => {
    const middleware = checkFeatureAccess('unknown-feature');
    middleware(req, res, next);
    
    expect(next).not.toHaveBeenCalled();
    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      error: 'Feature not available'
    }));
  });
});
```

### Integration Testing

```javascript
// tests/integration/privacyManagement.test.js

const request = require('supertest');
const app = require('../../app');
const { getFeatureFlags } = require('../../config/featureFlags');

// Mock the getFeatureFlags function
jest.mock('../../config/featureFlags');

describe('Privacy Management API', () => {
  beforeEach(() => {
    // Mock the feature flags
    getFeatureFlags.mockReturnValue({
      'privacy-management-processing-activities': {
        novaprime: true,
        novacore: true,
        novashield: false,
        novalearn: false
      },
      'privacy-management-impact-assessment': {
        novaprime: true,
        novacore: false,
        novashield: false,
        novalearn: false
      }
    });
    
    // Mock the authentication middleware
    app.use((req, res, next) => {
      req.user = {
        productTier: 'novacore'
      };
      next();
    });
  });
  
  test('should allow access to processing activities for NovaCore users', async () => {
    const response = await request(app)
      .get('/api/privacy/management/processing-activities');
    
    expect(response.status).toBe(200);
  });
  
  test('should deny access to impact assessment for NovaCore users', async () => {
    const response = await request(app)
      .get('/api/privacy/management/impact-assessment/1');
    
    expect(response.status).toBe(403);
    expect(response.body).toHaveProperty('error', 'Feature not available');
  });
});
```

## Conclusion

The feature flag system provides a flexible way to control access to features based on product tiers. It allows us to:

1. Offer different feature sets for different product tiers
2. Provide a clear upgrade path for users
3. Gradually roll out new features
4. A/B test features with specific user segments

By implementing feature flags at both the API and UI levels, we ensure consistent access control throughout the application.
