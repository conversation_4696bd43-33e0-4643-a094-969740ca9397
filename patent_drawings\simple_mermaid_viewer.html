<!DOCTYPE html>
<html>
<head>
    <title>Mermaid Diagram Viewer</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .editor, .preview {
            flex: 1;
        }
        textarea {
            width: 100%;
            height: 500px;
            font-family: monospace;
            padding: 10px;
        }
        button {
            padding: 10px 20px;
            margin: 10px 0;
            cursor: pointer;
        }
        #diagram {
            border: 1px solid #ccc;
            min-height: 500px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <h1>Mermaid Diagram Viewer</h1>
    <button onclick="updateDiagram()">Update Diagram</button>
    <button onclick="downloadSVG()">Download SVG</button>
    
    <div class="container">
        <div class="editor">
            <h3>Mermaid Code:</h3>
            <textarea id="mermaid-code" spellcheck="false">graph TD
    %% Your Mermaid code here
    A[Start] --> B[Process]
    B --> C[End]</textarea>
        </div>
        <div class="preview">
            <h3>Preview:</h3>
            <div id="diagram" class="mermaid">
                <!-- Diagram will be rendered here -->
            </div>
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif'
        });

        // Update diagram when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateDiagram();
        });

        // Update the diagram preview
        function updateDiagram() {
            const code = document.getElementById('mermaid-code').value;
            const diagramDiv = document.getElementById('diagram');
            
            // Clear previous diagram
            diagramDiv.innerHTML = '';
            
            // Add the new diagram
            diagramDiv.textContent = code;
            
            // Re-render with Mermaid
            mermaid.init(undefined, diagramDiv);
        }

        // Download the diagram as SVG
        function downloadSVG() {
            const svg = document.querySelector('#diagram svg');
            if (!svg) {
                alert('No diagram to download. Please render a diagram first.');
                return;
            }
            
            // Serialize the SVG
            const serializer = new XMLSerializer();
            let source = serializer.serializeToString(svg);
            
            // Add XML declaration
            if(!source.match(/^<svg[^>]+xmlns="http\:\/\/www\.w3\.org\/2000\/svg"/)){
                source = source.replace(/^<svg/, '<svg xmlns="http://www.w3.org/2000/svg"');
            }
            if(!source.match(/^<svg[^>]+"http\:\/\/www\.w3\.org\/1999\/xlink"/)){
                source = source.replace(/^<svg/, '<svg xmlns:xlink="http://www.w3.org/1999/xlink"');
            }
            
            // Add XML declaration
            source = '<?xml version="1.0" standalone="no"?>\r\n' + source;
            
            // Create a download link
            const url = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);
            const link = document.createElement('a');
            link.download = 'diagram.svg';
            link.href = url;
            link.click();
        }
    </script>
</body>
</html>
