{"metadata": {"name": "Okta", "version": "1.0.0", "category": "Identity & Access Management", "description": "Connect to Okta for identity governance and access management", "author": "NovaGRC", "tags": ["identity", "access", "authentication", "sso"], "created": "2025-01-01T00:00:00Z", "updated": "2025-01-01T00:00:00Z", "icon": "https://www.okta.com/sites/default/files/Okta_Logo_BrightBlue_Medium.png"}, "authentication": {"type": "API_KEY", "fields": {"apiToken": {"type": "string", "description": "Okta API Token", "required": true, "sensitive": true}, "domain": {"type": "string", "description": "Okta Domain (e.g., company.okta.com)", "required": true}}, "testConnection": {"endpoint": "/api/v1/users?limit=1", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "https://{{domain}}", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "SSWS {{apiToken}}"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getUsers", "name": "Get Users", "description": "Get users from Okta", "path": "/api/v1/users", "method": "GET", "parameters": {"query": {"limit": 200, "filter": "status eq \"ACTIVE\""}}, "pagination": {"type": "cursor", "parameters": {"after": "after"}}, "response": {"successCode": 200}}, {"id": "getUserGroups", "name": "Get User Groups", "description": "Get groups for a specific user", "path": "/api/v1/users/{{userId}}/groups", "method": "GET", "parameters": {"path": {"userId": "string"}}, "response": {"successCode": 200}}, {"id": "getApplications", "name": "Get Applications", "description": "Get applications from Okta", "path": "/api/v1/apps", "method": "GET", "parameters": {"query": {"limit": 200, "filter": "status eq \"ACTIVE\""}}, "pagination": {"type": "cursor", "parameters": {"after": "after"}}, "response": {"successCode": 200}}, {"id": "getUserAssignedApplications", "name": "Get User Assigned Applications", "description": "Get applications assigned to a specific user", "path": "/api/v1/apps?filter=user.id eq \"{{userId}}\"", "method": "GET", "parameters": {"path": {"userId": "string"}}, "response": {"successCode": 200}}, {"id": "getSystemLog", "name": "Get System Log", "description": "Get system log events from Okta", "path": "/api/v1/logs", "method": "GET", "parameters": {"query": {"limit": 100, "since": "{{since}}", "until": "{{until}}", "filter": "{{filter}}"}}, "pagination": {"type": "cursor", "parameters": {"after": "after"}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "getUsers", "targetSystem": "NovaGRC", "targetEntity": "Users", "transformations": [{"source": "$[*].id", "target": "userId", "transform": "identity"}, {"source": "$[*].profile.firstName", "target": "firstName", "transform": "identity"}, {"source": "$[*].profile.lastName", "target": "lastName", "transform": "identity"}, {"source": "$[*].profile.email", "target": "email", "transform": "identity"}, {"source": "$[*].status", "target": "status", "transform": "identity"}, {"source": "$[*].created", "target": "createdAt", "transform": "formatDate"}, {"source": "$[*].lastLogin", "target": "lastLoginAt", "transform": "formatDate"}]}, {"sourceEndpoint": "getUserGroups", "targetSystem": "NovaGRC", "targetEntity": "UserGroups", "transformations": [{"source": "$[*].id", "target": "groupId", "transform": "identity"}, {"source": "$[*].profile.name", "target": "groupName", "transform": "identity"}, {"source": "$[*].profile.description", "target": "description", "transform": "identity"}, {"source": "$[*].type", "target": "groupType", "transform": "identity"}]}, {"sourceEndpoint": "getSystemLog", "targetSystem": "NovaGRC", "targetEntity": "SecurityEvents", "transformations": [{"source": "$[*].uuid", "target": "eventId", "transform": "identity"}, {"source": "$[*].eventType", "target": "eventType", "transform": "identity"}, {"source": "$[*].severity", "target": "severity", "transform": "identity"}, {"source": "$[*].actor.displayName", "target": "actor", "transform": "identity"}, {"source": "$[*].target[0].displayName", "target": "target", "transform": "identity"}, {"source": "$[*].published", "target": "timestamp", "transform": "formatDate"}, {"source": "$[*].client.ipAddress", "target": "ip<PERSON><PERSON><PERSON>", "transform": "identity"}]}], "events": {"polling": [{"endpoint": "getSystemLog", "interval": "5m", "condition": "hasNewEvents"}]}}