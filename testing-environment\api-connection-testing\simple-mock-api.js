/**
 * Simple Mock API Server for NovaConnect API Connection Testing
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');

// Create Express app
const app = express();
const port = process.env.PORT || 3005;

// Configure middleware
app.use(cors());
app.use(bodyParser.json());

// Track request history for assertions
const requestHistory = [];

// Clear request history
app.post('/clear-history', (req, res) => {
  requestHistory.length = 0;
  res.status(200).json({ success: true, message: 'Request history cleared' });
});

// Get request history
app.get('/history', (req, res) => {
  res.status(200).json(requestHistory);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy' });
});

// Record all requests
app.use((req, res, next) => {
  const requestData = {
    method: req.method,
    path: req.path,
    query: req.query,
    headers: req.headers,
    body: req.body,
    timestamp: new Date().toISOString()
  };

  requestHistory.push(requestData);
  console.log(`[${req.method}] ${req.path}`);
  next();
});

// API Key Authentication endpoint
app.get('/api-key/resource', (req, res) => {
  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    return res.status(401).json({ error: 'API key is required' });
  }

  if (apiKey !== 'valid-api-key') {
    return res.status(403).json({ error: 'Invalid API key' });
  }

  res.status(200).json({ success: true, data: { id: 'resource-1', name: 'Test Resource' } });
});

// Complex data endpoint for transformation testing
app.get('/api-key/findings', (req, res) => {
  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    return res.status(401).json({ error: 'API key is required' });
  }

  if (apiKey !== 'valid-api-key') {
    return res.status(403).json({ error: 'Invalid API key' });
  }

  res.status(200).json({
    findings: [
      {
        id: 'finding-1',
        title: 'Security Group allows unrestricted access',
        severity: 'HIGH',
        status: 'ACTIVE',
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-02T00:00:00Z',
        compliance: {
          status: 'FAILED',
          requirements: ['PCI DSS 1.2.1', 'CIS 4.1']
        },
        resources: [
          {
            id: 'sg-12345',
            type: 'SecurityGroup',
            region: 'us-east-1'
          }
        ]
      },
      {
        id: 'finding-2',
        title: 'S3 bucket allows public access',
        severity: 'CRITICAL',
        status: 'ACTIVE',
        created_at: '2023-01-03T00:00:00Z',
        updated_at: '2023-01-04T00:00:00Z',
        compliance: {
          status: 'FAILED',
          requirements: ['PCI DSS 1.3.1', 'CIS 3.1']
        },
        resources: [
          {
            id: 'bucket-12345',
            type: 'S3Bucket',
            region: 'us-east-1'
          }
        ]
      }
    ]
  });
});

// Start the server
const server = app.listen(port, () => {
  console.log(`Simple Mock API server listening on port ${port}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  server.close(() => {
    console.log('Server shut down');
    process.exit(0);
  });
});

module.exports = { app, server };
