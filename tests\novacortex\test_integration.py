"""
NovaCortex Integration Tests

Integration tests for NovaCortex functionality via NovaConnect API.
"""

import asyncio
import pytest
from typing import Dict, Any
from .test_client import NovaCortexTestClient, NovaCortexTestConfig
from datetime import datetime

@pytest.fixture
def test_config():
    """Fixture providing test configuration."""
    return NovaCortexTestConfig(
        base_url="http://novacortex:3010",
        api_key="test-api-key"  # Should be set via environment variable in CI/CD
    )

@pytest.mark.asyncio
async def test_coherence_initialization(test_config):
    """Test that NovaCortex initializes with proper coherence."""
    async with NovaCortexTestClient(test_config) as client:
        result = await client.test_coherence()
        assert "coherence_level" in result
        assert 0.95 <= result["coherence_level"] <= 1.0, "Coherence level out of expected range"

@pytest.mark.asyncio
async def test_castl_decision_making(test_config):
    """Test CASTL-compliant decision making."""
    scenario = {
        "type": "trolley",
        "options": ["do_nothing", "pull_lever"],
        "consequences": {
            "do_nothing": ["5_fatalities"],
            "pull_lever": ["1_fatality"]
        }
    }
    
    async with NovaCortexTestClient(test_config) as client:
        result = await client.test_castl_decision(scenario)
        assert "decision" in result
        assert "reasoning" in result
        assert "principles_applied" in result
        assert len(result["principles_applied"]) > 0, "No CASTL principles were applied"

@pytest.mark.asyncio
async def test_pi_rhythm_synchronization(test_config):
    """Test π-Rhythm synchronization."""
    async with NovaCortexTestClient(test_config) as client:
        result = await client.test_pi_rhythm(duration=3.0)
        assert "deviation" in result
        assert "frequency" in result
        assert "phase" in result
        assert result["deviation"] < 0.1, "π-Rhythm deviation too high"

@pytest.mark.asyncio
async def test_metrics_endpoint(test_config):
    """Test metrics endpoint returns expected data."""
    async with NovaCortexTestClient(test_config) as client:
        metrics = await client.get_metrics()
        assert "timestamp" in metrics
        assert "coherence" in metrics
        assert "castl_violations" in metrics
        assert "pi_rhythm" in metrics
        assert "system_health" in metrics

@pytest.mark.asyncio
async def test_end_to_end_workflow(test_config):
    """Test a complete workflow through the NovaCortex system."""
    async with NovaCortexTestClient(test_config) as client:
        # 1. Check initial coherence
        coherence = await client.test_coherence()
        assert coherence["coherence_level"] > 0.95
        
        # 2. Make a CASTL decision
        scenario = {
            "type": "resource_allocation",
            "options": ["distribute_evenly", "prioritize_critical"],
            "constraints": {
                "total_resources": 100,
                "critical_needs": 60,
                "non_critical_needs": 50
            }
        }
        decision = await client.test_castl_decision(scenario)
        assert decision["decision"] in ["distribute_evenly", "prioritize_critical"]
        
        # 3. Verify π-Rhythm is maintained
        rhythm = await client.test_pi_rhythm(duration=1.0)
        assert rhythm["deviation"] < 0.1
        
        # 4. Check final metrics
        metrics = await client.get_metrics()
        assert metrics["system_health"] == "optimal"

if __name__ == "__main__":
    import sys
    import asyncio
    
    async def run_tests():
        config = NovaCortexTestConfig(
            base_url="http://localhost:3010",
            api_key="test-api-key"
        )
        
        # Run tests programmatically
        test_functions = [
            test_coherence_initialization,
            test_castl_decision_making,
            test_pi_rhythm_synchronization,
            test_metrics_endpoint,
            test_end_to_end_workflow
        ]
        
        for test_func in test_functions:
            try:
                print(f"Running {test_func.__name__}...")
                await test_func(config)
                print(f"✅ {test_func.__name__} passed")
            except Exception as e:
                print(f"❌ {test_func.__name__} failed: {str(e)}")
                sys.exit(1)
    
    asyncio.run(run_tests())
