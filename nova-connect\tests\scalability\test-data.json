{"connectors": [{"name": "AWS Security Hub", "type": "aws", "description": "AWS Security Hub connector for security findings", "config": {"region": "us-east-1", "service": "securityhub", "action": "getFindings", "authentication": {"type": "iam_role", "role_arn": "arn:aws:iam::************:role/NovaConnectRole"}}, "schema": {"input": {"type": "object", "properties": {"Filters": {"type": "object"}, "MaxResults": {"type": "number"}}}, "output": {"type": "object", "properties": {"Findings": {"type": "array"}}}}}, {"name": "Azure Security Center", "type": "azure", "description": "Azure Security Center connector for security alerts", "config": {"resource": "Microsoft.Security/alerts", "api_version": "2020-01-01", "authentication": {"type": "service_principal", "tenant_id": "tenant-id", "client_id": "client-id", "client_secret_ref": "azure-client-secret"}}, "schema": {"input": {"type": "object", "properties": {"subscription_id": {"type": "string"}, "filter": {"type": "string"}}}, "output": {"type": "object", "properties": {"value": {"type": "array"}}}}}, {"name": "GCP Security Command Center", "type": "gcp", "description": "GCP Security Command Center connector for security findings", "config": {"service": "securitycenter", "version": "v1", "method": "organizations.sources.findings.list", "authentication": {"type": "service_account", "project_id": "project-id", "private_key_ref": "gcp-private-key"}}, "schema": {"input": {"type": "object", "properties": {"parent": {"type": "string"}, "filter": {"type": "string"}, "pageSize": {"type": "number"}}}, "output": {"type": "object", "properties": {"findings": {"type": "array"}}}}}, {"name": "HTTP API", "type": "http", "description": "Generic HTTP API connector", "config": {"base_url": "https://httpbin.org", "default_headers": {"Content-Type": "application/json", "Accept": "application/json"}, "authentication": {"type": "bearer_token", "token_ref": "http-api-token"}}, "schema": {"input": {"type": "object", "properties": {"method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE"]}, "path": {"type": "string"}, "query": {"type": "object"}, "headers": {"type": "object"}, "body": {"type": "object"}}, "required": ["method", "path"]}, "output": {"type": "object"}}}, {"name": "MongoDB Database", "type": "database", "description": "MongoDB database connector", "config": {"type": "mongodb", "connection_string_ref": "mongodb-connection-string", "database": "test_database"}, "schema": {"input": {"type": "object", "properties": {"collection": {"type": "string"}, "operation": {"type": "string", "enum": ["find", "findOne", "insertOne", "updateOne", "deleteOne"]}, "filter": {"type": "object"}, "data": {"type": "object"}, "options": {"type": "object"}}, "required": ["collection", "operation"]}, "output": {"type": "object"}}}], "testRequests": [{"Filters": {"SeverityLabel": [{"Value": "HIGH", "Comparison": "EQUALS"}], "CreatedAt": [{"DateRange": {"Value": 30, "Unit": "DAYS"}}]}, "MaxResults": 100}, {"subscription_id": "subscription-id", "filter": "properties/status eq 'Active'"}, {"parent": "organizations/123/sources/456", "filter": "state=\"ACTIVE\"", "pageSize": 50}, {"method": "POST", "path": "/anything", "body": {"test": true, "data": {"key1": "value1", "key2": "value2"}}}, {"collection": "test_collection", "operation": "find", "filter": {"status": "active"}, "options": {"limit": 100, "sort": {"createdAt": -1}}}], "normalizationRequests": [{"source": "aws", "type": "finding", "data": {"Id": "arn:aws:securityhub:us-east-1:************:finding/********-1234-1234-1234-************", "ProductArn": "arn:aws:securityhub:us-east-1::product/aws/securityhub", "ProductName": "Security Hub", "CompanyName": "AWS", "Region": "us-east-1", "GeneratorId": "aws-security-hub", "AwsAccountId": "************", "Types": ["Software and Configuration Checks/Vulnerabilities/CVE"], "FirstObservedAt": "2023-01-01T00:00:00Z", "LastObservedAt": "2023-01-01T00:00:00Z", "CreatedAt": "2023-01-01T00:00:00Z", "UpdatedAt": "2023-01-01T00:00:00Z", "Severity": {"Label": "HIGH", "Normalized": 70}, "Title": "CVE-2023-12345 - Critical vulnerability in package", "Description": "A critical vulnerability was found in package that could allow remote code execution.", "Resources": [{"Type": "AwsEc2Instance", "Id": "i-************34567", "Partition": "aws", "Region": "us-east-1", "Details": {"AwsEc2Instance": {"Type": "t2.micro", "ImageId": "ami-********", "VpcId": "vpc-********"}}}]}}, {"source": "azure", "type": "alert", "data": {"id": "/subscriptions/subscription-id/providers/Microsoft.Security/alerts/alert-id", "name": "alert-id", "type": "Microsoft.Security/alerts", "properties": {"alertDisplayName": "Suspicious process execution detected", "alertType": "Process_Execution", "compromisedEntity": "vm-name", "description": "A suspicious process execution was detected on the virtual machine.", "detectionTime": "2023-01-01T00:00:00Z", "reportedTimeUtc": "2023-01-01T00:00:00Z", "severity": "High", "status": "Active", "resourceIdentifiers": {"resourceId": "/subscriptions/subscription-id/resourceGroups/resource-group/providers/Microsoft.Compute/virtualMachines/vm-name", "resourceType": "Virtual Machine"}}}}, {"source": "gcp", "type": "finding", "data": {"name": "organizations/123/sources/456/findings/789", "parent": "organizations/123/sources/456", "resourceName": "//compute.googleapis.com/projects/project-id/zones/us-central1-a/instances/instance-id", "state": "ACTIVE", "category": "VULNERABILITY", "externalUri": "https://console.cloud.google.com/security/command-center/findings?organizationId=123", "sourceProperties": {"ScannerName": "Web Security Scanner", "Finding Category": "XSS", "Finding Class": "VULNERABILITY"}, "securityMarks": {}, "eventTime": "2023-01-01T00:00:00Z", "createTime": "2023-01-01T00:00:00Z", "severity": "HIGH"}}, {"source": "http", "type": "generic", "data": {"id": "12345", "timestamp": "2023-01-01T00:00:00Z", "type": "security_event", "severity": "high", "description": "Suspicious login attempt detected", "source_ip": "***********", "user": "admin", "resource": "login_portal", "action": "login_attempt", "result": "failed", "details": {"attempt_count": 5, "location": "Unknown", "user_agent": "Mozilla/5.0"}}}, {"source": "database", "type": "record", "data": {"_id": "12345", "type": "compliance_finding", "status": "open", "severity": "high", "title": "PCI DSS Compliance Issue", "description": "Unencrypted credit card data found in database", "resource": "database_server_1", "created_at": "2023-01-01T00:00:00Z", "updated_at": "2023-01-01T00:00:00Z", "assigned_to": "security_team", "remediation_steps": ["Identify all instances of unencrypted data", "Implement encryption for sensitive data", "Update data handling procedures"], "compliance_frameworks": ["PCI DSS", "GDPR"], "tags": ["high_priority", "security", "compliance"]}}, {"source": "aws", "type": "finding", "nested": {"depth": 5, "data": {"level1": {"level2": {"level3": {"level4": {"level5": {"id": "deep-nested-id", "value": "deep-nested-value"}}}}}}}, "data": {"Id": "arn:aws:securityhub:us-east-1:************:finding/complex-finding", "ProductArn": "arn:aws:securityhub:us-east-1::product/aws/securityhub", "ProductName": "Security Hub", "CompanyName": "AWS", "Region": "us-east-1", "GeneratorId": "aws-security-hub", "AwsAccountId": "************", "Types": ["Software and Configuration Checks/Vulnerabilities/CVE", "Effects/Data Exposure"], "FirstObservedAt": "2023-01-01T00:00:00Z", "LastObservedAt": "2023-01-02T00:00:00Z", "CreatedAt": "2023-01-01T00:00:00Z", "UpdatedAt": "2023-01-02T00:00:00Z", "Severity": {"Label": "CRITICAL", "Normalized": 90, "Original": "10.0"}, "Title": "Complex finding with nested data", "Description": "This is a complex finding with deeply nested data structures for testing normalization performance.", "Resources": [{"Type": "AwsEc2Instance", "Id": "i-************34567", "Partition": "aws", "Region": "us-east-1", "Details": {"AwsEc2Instance": {"Type": "t2.micro", "ImageId": "ami-********", "VpcId": "vpc-********", "SubnetId": "subnet-********", "LaunchedAt": "2023-01-01T00:00:00Z", "NetworkInterfaces": [{"NetworkInterfaceId": "eni-********", "PrivateIpAddress": "********", "PrivateDnsName": "ip-10-0-0-1.ec2.internal", "PublicIp": "***********", "PublicDnsName": "ec2-203-0-113-1.compute-1.amazonaws.com", "SecurityGroups": [{"GroupId": "sg-********", "GroupName": "default"}]}], "Tags": [{"Key": "Name", "Value": "Test Instance"}, {"Key": "Environment", "Value": "Production"}]}}}, {"Type": "AwsS3Bucket", "Id": "arn:aws:s3:::example-bucket", "Partition": "aws", "Region": "us-east-1", "Details": {"AwsS3Bucket": {"OwnerId": "************", "CreatedAt": "2023-01-01T00:00:00Z", "ServerSideEncryptionConfiguration": {"Rules": [{"ApplyServerSideEncryptionByDefault": {"SSEAlgorithm": "AES256"}}]}, "PublicAccessBlockConfiguration": {"BlockPublicAcls": false, "BlockPublicPolicy": false, "IgnorePublicAcls": false, "RestrictPublicBuckets": false}}}}], "Compliance": {"Status": "FAILED", "RelatedRequirements": ["PCI DSS v3.2.1/1.2.1", "CIS AWS Foundations Benchmark v1.2.0/2.9"], "SecurityControlId": "EC2.1", "AssociatedStandards": [{"StandardsId": "standards/pci-dss/v3.2.1"}, {"StandardsId": "standards/cis-aws-foundations-benchmark/v1.2.0"}]}, "Remediation": {"Recommendation": {"Text": "Apply security group rules that restrict access to only authorized sources.", "Url": "https://docs.aws.amazon.com/securityhub/latest/userguide/securityhub-standards-fsbp-controls.html#ec2-1"}}, "FindingProviderFields": {"Severity": {"Label": "CRITICAL"}, "Types": ["Software and Configuration Checks/Vulnerabilities/CVE"]}, "ProcessedAt": "2023-01-02T00:00:00Z", "WorkflowState": "NEW", "RecordState": "ACTIVE"}}]}