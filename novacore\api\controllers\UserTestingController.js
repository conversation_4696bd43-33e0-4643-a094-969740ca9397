/**
 * User Testing Controller
 * 
 * This controller handles user testing sessions and results.
 */

const logger = require('../../config/logger');
const { v4: uuidv4 } = require('uuid');
const { getRedisClient } = require('../../config/redis');

// Redis client for user testing data
const redisClient = getRedisClient();

// Redis keys
const TEST_SESSIONS_KEY = 'user_testing:sessions';
const TEST_RESULTS_KEY = 'user_testing:results';

/**
 * Get all test sessions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getTestSessions = async (req, res, next) => {
  try {
    logger.debug('Getting all test sessions');
    
    // Get all test sessions from Redis
    const sessions = await getAllTestSessions();
    
    // Return sessions
    res.status(200).json(sessions);
  } catch (error) {
    logger.error('Error getting test sessions:', error);
    next(error);
  }
};

/**
 * Get a test session by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getTestSessionById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    logger.debug(`Getting test session with ID: ${id}`);
    
    // Get test session from Redis
    const session = await getTestSession(id);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Test session with ID ${id} not found`,
          code: 'TEST_SESSION_NOT_FOUND'
        }
      });
    }
    
    // Return session
    res.status(200).json(session);
  } catch (error) {
    logger.error(`Error getting test session with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Create a new test session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createTestSession = async (req, res, next) => {
  try {
    const { visualizationType, testType, description } = req.body;
    
    logger.debug(`Creating new test session for ${visualizationType}`);
    
    // Validate required fields
    if (!visualizationType || !testType) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Visualization type and test type are required',
          code: 'INVALID_REQUEST'
        }
      });
    }
    
    // Create new test session
    const session = {
      id: uuidv4(),
      visualizationType,
      testType,
      description: description || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      active: true,
      completedCount: 0,
      tasks: generateTasks(visualizationType, testType)
    };
    
    // Save test session to Redis
    await saveTestSession(session);
    
    // Return session
    res.status(201).json(session);
  } catch (error) {
    logger.error('Error creating test session:', error);
    next(error);
  }
};

/**
 * Update a test session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateTestSession = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { visualizationType, testType, description, active } = req.body;
    
    logger.debug(`Updating test session with ID: ${id}`);
    
    // Get test session from Redis
    const session = await getTestSession(id);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Test session with ID ${id} not found`,
          code: 'TEST_SESSION_NOT_FOUND'
        }
      });
    }
    
    // Update session
    const updatedSession = {
      ...session,
      visualizationType: visualizationType || session.visualizationType,
      testType: testType || session.testType,
      description: description !== undefined ? description : session.description,
      active: active !== undefined ? active : session.active,
      updatedAt: new Date().toISOString()
    };
    
    // Save updated session to Redis
    await saveTestSession(updatedSession);
    
    // Return updated session
    res.status(200).json(updatedSession);
  } catch (error) {
    logger.error(`Error updating test session with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Delete a test session
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteTestSession = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    logger.debug(`Deleting test session with ID: ${id}`);
    
    // Get test session from Redis
    const session = await getTestSession(id);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Test session with ID ${id} not found`,
          code: 'TEST_SESSION_NOT_FOUND'
        }
      });
    }
    
    // Delete test session from Redis
    await deleteTestSessionFromRedis(id);
    
    // Return success
    res.status(200).json({
      success: true,
      message: `Test session with ID ${id} deleted successfully`
    });
  } catch (error) {
    logger.error(`Error deleting test session with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Get all test results
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getTestResults = async (req, res, next) => {
  try {
    logger.debug('Getting all test results');
    
    // Get all test results from Redis
    const results = await getAllTestResults();
    
    // Return results
    res.status(200).json(results);
  } catch (error) {
    logger.error('Error getting test results:', error);
    next(error);
  }
};

/**
 * Get a test result by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getTestResultById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    logger.debug(`Getting test result with ID: ${id}`);
    
    // Get test result from Redis
    const result = await getTestResult(id);
    
    if (!result) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Test result with ID ${id} not found`,
          code: 'TEST_RESULT_NOT_FOUND'
        }
      });
    }
    
    // Return result
    res.status(200).json(result);
  } catch (error) {
    logger.error(`Error getting test result with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Submit test results
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const submitTestResults = async (req, res, next) => {
  try {
    const { session } = req.body;
    
    logger.debug('Submitting test results');
    
    // Validate session
    if (!session || !session.id) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid test session data',
          code: 'INVALID_REQUEST'
        }
      });
    }
    
    // Generate result ID
    const resultId = uuidv4();
    
    // Create result object
    const result = {
      id: resultId,
      sessionId: session.id,
      visualizationType: session.visualizationType,
      testType: session.testType,
      participantId: session.participantId,
      participantInfo: session.participantInfo,
      startTime: session.startTime,
      endTime: session.endTime,
      duration: session.duration,
      completedTasks: session.completedTasks,
      feedback: session.feedback,
      finalFeedback: session.finalFeedback,
      successRate: session.successRate,
      deviceInfo: session.deviceInfo,
      submittedAt: new Date().toISOString()
    };
    
    // Save result to Redis
    await saveTestResult(result);
    
    // Update test session completed count
    await incrementSessionCompletedCount(session.id);
    
    // Return success
    res.status(200).json({
      success: true,
      message: 'Test results submitted successfully',
      resultId
    });
  } catch (error) {
    logger.error('Error submitting test results:', error);
    next(error);
  }
};

/**
 * Delete a test result
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteTestResult = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    logger.debug(`Deleting test result with ID: ${id}`);
    
    // Get test result from Redis
    const result = await getTestResult(id);
    
    if (!result) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Test result with ID ${id} not found`,
          code: 'TEST_RESULT_NOT_FOUND'
        }
      });
    }
    
    // Delete test result from Redis
    await deleteTestResultFromRedis(id);
    
    // Return success
    res.status(200).json({
      success: true,
      message: `Test result with ID ${id} deleted successfully`
    });
  } catch (error) {
    logger.error(`Error deleting test result with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Export test result
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const exportTestResult = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    logger.debug(`Exporting test result with ID: ${id}`);
    
    // Get test result from Redis
    const result = await getTestResult(id);
    
    if (!result) {
      return res.status(404).json({
        success: false,
        error: {
          message: `Test result with ID ${id} not found`,
          code: 'TEST_RESULT_NOT_FOUND'
        }
      });
    }
    
    // Set response headers
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename=test-result-${id}.json`);
    
    // Send result as JSON
    res.status(200).send(JSON.stringify(result, null, 2));
  } catch (error) {
    logger.error(`Error exporting test result with ID ${req.params.id}:`, error);
    next(error);
  }
};

/**
 * Get all test sessions from Redis
 * @returns {Array} - All test sessions
 */
const getAllTestSessions = async () => {
  try {
    // Get all session IDs
    const sessionIds = await redisClient.smembers(TEST_SESSIONS_KEY);
    
    // Get sessions
    const sessions = [];
    
    for (const id of sessionIds) {
      const session = await getTestSession(id);
      if (session) {
        sessions.push(session);
      }
    }
    
    return sessions;
  } catch (error) {
    logger.error('Error getting all test sessions from Redis:', error);
    throw error;
  }
};

/**
 * Get a test session from Redis
 * @param {string} id - Test session ID
 * @returns {Object|null} - Test session or null if not found
 */
const getTestSession = async (id) => {
  try {
    // Get session from Redis
    const sessionJson = await redisClient.get(`${TEST_SESSIONS_KEY}:${id}`);
    
    if (!sessionJson) {
      return null;
    }
    
    return JSON.parse(sessionJson);
  } catch (error) {
    logger.error(`Error getting test session with ID ${id} from Redis:`, error);
    throw error;
  }
};

/**
 * Save a test session to Redis
 * @param {Object} session - Test session to save
 */
const saveTestSession = async (session) => {
  try {
    // Save session to Redis
    await redisClient.set(`${TEST_SESSIONS_KEY}:${session.id}`, JSON.stringify(session));
    
    // Add session ID to set
    await redisClient.sadd(TEST_SESSIONS_KEY, session.id);
  } catch (error) {
    logger.error(`Error saving test session with ID ${session.id} to Redis:`, error);
    throw error;
  }
};

/**
 * Delete a test session from Redis
 * @param {string} id - Test session ID
 */
const deleteTestSessionFromRedis = async (id) => {
  try {
    // Delete session from Redis
    await redisClient.del(`${TEST_SESSIONS_KEY}:${id}`);
    
    // Remove session ID from set
    await redisClient.srem(TEST_SESSIONS_KEY, id);
  } catch (error) {
    logger.error(`Error deleting test session with ID ${id} from Redis:`, error);
    throw error;
  }
};

/**
 * Increment the completed count for a test session
 * @param {string} id - Test session ID
 */
const incrementSessionCompletedCount = async (id) => {
  try {
    // Get session from Redis
    const session = await getTestSession(id);
    
    if (!session) {
      return;
    }
    
    // Increment completed count
    session.completedCount = (session.completedCount || 0) + 1;
    session.updatedAt = new Date().toISOString();
    
    // Save updated session to Redis
    await saveTestSession(session);
  } catch (error) {
    logger.error(`Error incrementing completed count for test session with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get all test results from Redis
 * @returns {Array} - All test results
 */
const getAllTestResults = async () => {
  try {
    // Get all result IDs
    const resultIds = await redisClient.smembers(TEST_RESULTS_KEY);
    
    // Get results
    const results = [];
    
    for (const id of resultIds) {
      const result = await getTestResult(id);
      if (result) {
        results.push(result);
      }
    }
    
    return results;
  } catch (error) {
    logger.error('Error getting all test results from Redis:', error);
    throw error;
  }
};

/**
 * Get a test result from Redis
 * @param {string} id - Test result ID
 * @returns {Object|null} - Test result or null if not found
 */
const getTestResult = async (id) => {
  try {
    // Get result from Redis
    const resultJson = await redisClient.get(`${TEST_RESULTS_KEY}:${id}`);
    
    if (!resultJson) {
      return null;
    }
    
    return JSON.parse(resultJson);
  } catch (error) {
    logger.error(`Error getting test result with ID ${id} from Redis:`, error);
    throw error;
  }
};

/**
 * Save a test result to Redis
 * @param {Object} result - Test result to save
 */
const saveTestResult = async (result) => {
  try {
    // Save result to Redis
    await redisClient.set(`${TEST_RESULTS_KEY}:${result.id}`, JSON.stringify(result));
    
    // Add result ID to set
    await redisClient.sadd(TEST_RESULTS_KEY, result.id);
  } catch (error) {
    logger.error(`Error saving test result with ID ${result.id} to Redis:`, error);
    throw error;
  }
};

/**
 * Delete a test result from Redis
 * @param {string} id - Test result ID
 */
const deleteTestResultFromRedis = async (id) => {
  try {
    // Delete result from Redis
    await redisClient.del(`${TEST_RESULTS_KEY}:${id}`);
    
    // Remove result ID from set
    await redisClient.srem(TEST_RESULTS_KEY, id);
  } catch (error) {
    logger.error(`Error deleting test result with ID ${id} from Redis:`, error);
    throw error;
  }
};

/**
 * Generate tasks for a test session
 * @param {string} visualizationType - Type of visualization
 * @param {string} testType - Type of test
 * @returns {Array} - List of tasks
 */
const generateTasks = (visualizationType, testType) => {
  // Common tasks for all visualization types
  const commonTasks = [
    {
      id: `${visualizationType}_explore`,
      name: 'Explore the visualization',
      description: 'Take a moment to explore the visualization and understand what it shows.',
      difficulty: 'easy',
      visualizationType,
      expectedTime: 60 // seconds
    },
    {
      id: `${visualizationType}_filter`,
      name: 'Apply filters',
      description: 'Try to filter the data shown in the visualization.',
      difficulty: 'easy',
      visualizationType,
      expectedTime: 30
    },
    {
      id: `${visualizationType}_export`,
      name: 'Export visualization',
      description: 'Export the visualization as an image or data file.',
      difficulty: 'medium',
      visualizationType,
      expectedTime: 30
    }
  ];
  
  // Visualization-specific tasks
  const specificTasks = {
    triDomainTensor: [
      {
        id: 'triDomainTensor_rotate',
        name: 'Rotate the tensor',
        description: 'Rotate the 3D tensor visualization to view it from different angles.',
        difficulty: 'easy',
        visualizationType,
        expectedTime: 30
      },
      {
        id: 'triDomainTensor_identify',
        name: 'Identify strongest connection',
        description: 'Identify which domains have the strongest connection in the tensor.',
        difficulty: 'medium',
        visualizationType,
        expectedTime: 45
      }
    ],
    harmonyIndex: [
      {
        id: 'harmonyIndex_trend',
        name: 'Analyze harmony trend',
        description: 'Analyze the trend of the harmony index over time.',
        difficulty: 'medium',
        visualizationType,
        expectedTime: 45
      },
      {
        id: 'harmonyIndex_domain',
        name: 'Compare domain scores',
        description: 'Compare the harmony scores across different domains.',
        difficulty: 'medium',
        visualizationType,
        expectedTime: 45
      }
    ],
    riskControlFusion: [
      {
        id: 'riskControlFusion_identify',
        name: 'Identify highest risk area',
        description: 'Identify the area with the highest risk in the visualization.',
        difficulty: 'medium',
        visualizationType,
        expectedTime: 45
      },
      {
        id: 'riskControlFusion_compare',
        name: 'Compare risk and control',
        description: 'Compare the risk level and control coverage for a specific domain.',
        difficulty: 'hard',
        visualizationType,
        expectedTime: 60
      }
    ],
    resonanceSpectrogram: [
      {
        id: 'resonanceSpectrogram_pattern',
        name: 'Identify resonance pattern',
        description: 'Identify a resonance pattern in the spectrogram.',
        difficulty: 'hard',
        visualizationType,
        expectedTime: 60
      },
      {
        id: 'resonanceSpectrogram_predict',
        name: 'Predict future state',
        description: 'Use the spectrogram to predict a future state of the system.',
        difficulty: 'hard',
        visualizationType,
        expectedTime: 90
      }
    ],
    unifiedComplianceSecurity: [
      {
        id: 'unifiedComplianceSecurity_trace',
        name: 'Trace requirement to implementation',
        description: 'Trace a compliance requirement to its implementation.',
        difficulty: 'medium',
        visualizationType,
        expectedTime: 60
      },
      {
        id: 'unifiedComplianceSecurity_gap',
        name: 'Identify compliance gap',
        description: 'Identify a gap in compliance coverage.',
        difficulty: 'hard',
        visualizationType,
        expectedTime: 90
      }
    ]
  };
  
  // Combine common tasks with visualization-specific tasks
  return [
    ...commonTasks,
    ...(specificTasks[visualizationType] || [])
  ];
};

module.exports = {
  getTestSessions,
  getTestSessionById,
  createTestSession,
  updateTestSession,
  deleteTestSession,
  getTestResults,
  getTestResultById,
  submitTestResults,
  deleteTestResult,
  exportTestResult
};
