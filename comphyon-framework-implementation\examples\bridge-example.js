/**
 * Bridge Example
 *
 * This example demonstrates the usage of the Bridge component.
 */

const {
  DomainTranslationLayer,
  UnifiedRiskScoring,
  CrossDomainIntegration,
  createBridgeSystem,
  createEnhancedBridgeSystem
} = require('../bridge');

// Import engines
const { createEnhancedCSMESystem } = require('../csme');
const { createEnhancedCSFESystem } = require('../csfe');
const { createEnhancedCSDESystem } = require('../csde');

// Example 1: Using individual components
console.log('Example 1: Using individual components');

// Create components
const domainTranslationLayer = new DomainTranslationLayer();
const unifiedRiskScoring = new UnifiedRiskScoring();
const crossDomainIntegration = new CrossDomainIntegration();

// Start components
unifiedRiskScoring.start();
crossDomainIntegration.start();

// Translate metrics between domains
const translation1 = domainTranslationLayer.translateMetric('cyber', 'financial', 'policy_entropy', 0.7);
console.log(`Translated metric: ${translation1.sourceMetric} (${translation1.sourceValue.toFixed(4)}) from ${translation1.sourceDomain} to ${translation1.targetMetric} (${translation1.targetValue.toFixed(4)}) in ${translation1.targetDomain}`);

const translation2 = domainTranslationLayer.translateMetric('financial', 'biological', 'transaction_entropy', 0.6);
console.log(`Translated metric: ${translation2.sourceMetric} (${translation2.sourceValue.toFixed(4)}) from ${translation2.sourceDomain} to ${translation2.targetMetric} (${translation2.targetValue.toFixed(4)}) in ${translation2.targetDomain}`);

// Update domain risk scores
unifiedRiskScoring.updateDomainRiskScore('cyber', 0.6);
unifiedRiskScoring.updateDomainRiskScore('financial', 0.5);
unifiedRiskScoring.updateDomainRiskScore('biological', 0.4);

// Get unified risk score
const unifiedRiskScore = unifiedRiskScoring.getUnifiedRiskScore();
console.log(`Unified Risk Score: ${unifiedRiskScore.toFixed(4)} (${unifiedRiskScoring.getRiskStatus()})`);

// Add risk factor
const riskFactor = unifiedRiskScoring.addRiskFactor({
  domain: 'cyber',
  type: 'vulnerability',
  impact: 0.7,
  likelihood: 0.6,
  description: 'Critical security vulnerability'
});
console.log(`Added risk factor: ${riskFactor.id} (${riskFactor.domain})`);

// Register cross-domain pattern
const pattern = crossDomainIntegration.registerPattern({
  name: 'Cross-Domain Risk Pattern',
  description: 'Detects correlated risks across domains',
  domains: ['cyber', 'financial', 'biological'],
  conditions: [
    {
      domain: 'cyber',
      key: 'policy_entropy',
      operator: 'gt',
      value: 0.7
    },
    {
      domain: 'financial',
      key: 'transaction_entropy',
      operator: 'gt',
      value: 0.6
    }
  ],
  action: 'alert',
  priority: 'high'
});
console.log(`Registered pattern: ${pattern.id} (${pattern.name})`);

// Process domain data
crossDomainIntegration.processDomainData('cyber', 'policy_entropy', 0.8, {
  source: 'test',
  description: 'High policy entropy'
});

// Process domain event
const crossDomainEvents = crossDomainIntegration.processDomainEvent('cyber', {
  id: 'event-1',
  type: 'policy_violation',
  severity: 'high',
  description: 'Policy violation detected',
  timestamp: Date.now()
});
console.log(`Generated ${crossDomainEvents.length} cross-domain events`);

// Stop components
unifiedRiskScoring.stop();
crossDomainIntegration.stop();

// Example 2: Using the basic Bridge system
console.log('\nExample 2: Using the basic Bridge system');

// Create Bridge system
const bridgeSystem = createBridgeSystem({
  domainTranslationLayerOptions: {
    enableLogging: true
  },
  unifiedRiskScoringOptions: {
    enableLogging: true
  },
  crossDomainIntegrationOptions: {
    enableLogging: true
  }
});

// Start components
bridgeSystem.unifiedRiskScoring.start();
bridgeSystem.crossDomainIntegration.start();

// Translate metrics between domains
const translation3 = bridgeSystem.domainTranslationLayer.translateMetric('cyber', 'financial', 'policy_entropy', 0.7);
console.log(`Translated metric: ${translation3.sourceMetric} (${translation3.sourceValue.toFixed(4)}) from ${translation3.sourceDomain} to ${translation3.targetMetric} (${translation3.targetValue.toFixed(4)}) in ${translation3.targetDomain}`);

// Update domain risk scores
bridgeSystem.unifiedRiskScoring.updateDomainRiskScore('cyber', 0.6);
bridgeSystem.unifiedRiskScoring.updateDomainRiskScore('financial', 0.5);
bridgeSystem.unifiedRiskScoring.updateDomainRiskScore('biological', 0.4);

// Get unified risk score
const unifiedRiskScore2 = bridgeSystem.unifiedRiskScoring.getUnifiedRiskScore();
console.log(`Unified Risk Score: ${unifiedRiskScore2.toFixed(4)} (${bridgeSystem.unifiedRiskScoring.getRiskStatus()})`);

// Stop components
bridgeSystem.unifiedRiskScoring.stop();
bridgeSystem.crossDomainIntegration.stop();

// Example 3: Using the enhanced Bridge system
console.log('\nExample 3: Using the enhanced Bridge system');

// Create engine systems
const csdeSystem = createEnhancedCSDESystem({
  enableLogging: true
});

const csfeSystem = createEnhancedCSFESystem({
  enableLogging: true
});

const csmeSystem = createEnhancedCSMESystem({
  enableLogging: true
});

// Create enhanced Bridge system
const enhancedBridgeSystem = createEnhancedBridgeSystem(
  {
    enableLogging: true
  },
  {
    // We'll use empty objects for now since we're just demonstrating the Bridge
    csde: {},
    csfe: {},
    csme: {}
  }
);

// Start system
enhancedBridgeSystem.start();

// Process domain data
const cyberResult = enhancedBridgeSystem.processDomainData('cyber', 'policy_entropy', 0.7, {
  source: 'test',
  description: 'High policy entropy'
});
console.log(`Processed cyber data with ${cyberResult.translations.length} translations`);

const financialResult = enhancedBridgeSystem.processDomainData('financial', 'transaction_entropy', 0.6, {
  source: 'test',
  description: 'Elevated transaction entropy'
});
console.log(`Processed financial data with ${financialResult.translations.length} translations`);

const biologicalResult = enhancedBridgeSystem.processDomainData('biological', 'inflammation_level', 0.5, {
  source: 'test',
  description: 'Moderate inflammation level'
});
console.log(`Processed biological data with ${biologicalResult.translations.length} translations`);

// Process domain events
const cyberEventResult = enhancedBridgeSystem.processDomainEvent('cyber', {
  id: 'event-2',
  type: 'policy_violation',
  severity: 'high',
  description: 'Policy violation detected',
  timestamp: Date.now()
});
console.log(`Processed cyber event with ${cyberEventResult.crossDomainEvents.length} cross-domain events`);

// Register custom pattern
enhancedBridgeSystem.registerPattern({
  name: 'Tri-Domain Risk Pattern',
  description: 'Detects correlated risks across all three domains',
  domains: ['cyber', 'financial', 'biological'],
  conditions: [
    {
      domain: 'cyber',
      key: 'policy_entropy',
      operator: 'gt',
      value: 0.6
    },
    {
      domain: 'financial',
      key: 'transaction_entropy',
      operator: 'gt',
      value: 0.5
    },
    {
      domain: 'biological',
      key: 'inflammation_level',
      operator: 'gt',
      value: 0.4
    }
  ],
  action: 'alert',
  priority: 'critical',
  alertType: 'system',
  alertMessage: 'Critical tri-domain risk pattern detected'
});

// Get cross-domain risk assessment
const riskAssessment = enhancedBridgeSystem.getCrossDomainRiskAssessment();
console.log('Cross-Domain Risk Assessment:');
console.log(`- Unified Risk Score: ${riskAssessment.unifiedRiskScore.toFixed(4)}`);
console.log(`- Risk Status: ${riskAssessment.riskStatus}`);
console.log('- Domain Risk Scores:');
console.log(`  - Cyber: ${riskAssessment.domainRiskScores.cyber.toFixed(4)}`);
console.log(`  - Financial: ${riskAssessment.domainRiskScores.financial.toFixed(4)}`);
console.log(`  - Biological: ${riskAssessment.domainRiskScores.biological.toFixed(4)}`);
console.log('- Domain Correlations:');
console.log(`  - Cyber-Financial: ${riskAssessment.domainCorrelations.cyberFinancial.toFixed(4)}`);
console.log(`  - Cyber-Biological: ${riskAssessment.domainCorrelations.cyberBiological.toFixed(4)}`);
console.log(`  - Financial-Biological: ${riskAssessment.domainCorrelations.financialBiological.toFixed(4)}`);

// Get unified state
const unifiedState = enhancedBridgeSystem.getUnifiedState();
console.log('Unified State Summary:');
console.log(`- Risk Status: ${unifiedState.unifiedRiskScoring.riskStatus}`);
console.log(`- Pattern Count: ${unifiedState.crossDomainIntegration.patternCount}`);

// Get unified metrics
const unifiedMetrics = enhancedBridgeSystem.getUnifiedMetrics();
console.log('Unified Metrics Summary:');
console.log(`- Translations: ${unifiedMetrics.domainTranslationLayer.totalTranslations}`);
console.log(`- Risk Updates: ${unifiedMetrics.unifiedRiskScoring.totalUpdates}`);
console.log(`- Data Points Processed: ${unifiedMetrics.crossDomainIntegration.dataPointsProcessed}`);

// Stop system
enhancedBridgeSystem.stop();

console.log('\nBridge example completed successfully!');
