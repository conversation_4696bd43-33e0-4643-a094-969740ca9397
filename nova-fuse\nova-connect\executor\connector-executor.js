/**
 * NovaFuse Universal API Connector Executor
 * 
 * This module executes API requests defined in connector templates and
 * transforms the responses according to the mapping configuration.
 */

const axios = require('axios');
const jsonpath = require('jsonpath');
const connectorRegistry = require('../registry/connector-registry');
const authenticationManager = require('../auth/authentication-manager');
const transformationFunctions = require('../transform/transformation-functions');

class ConnectorExecutor {
  constructor() {
    this.axiosInstances = new Map();
  }

  /**
   * Initialize the connector executor
   */
  async initialize() {
    console.log('Connector Executor initialized');
    return true;
  }

  /**
   * Execute an endpoint from a connector
   * 
   * @param {string} connectorId - The ID of the connector
   * @param {string} endpointId - The ID of the endpoint to execute
   * @param {string} credentialId - The ID of the credentials to use
   * @param {Object} parameters - The parameters to pass to the endpoint
   * @returns {Object} - The response data
   */
  async executeEndpoint(connectorId, endpointId, credentialId, parameters = {}) {
    try {
      // Get the connector template
      const connector = connectorRegistry.getConnector(connectorId);
      
      if (!connector) {
        throw new Error(`Connector '${connectorId}' not found`);
      }
      
      // Get the endpoint configuration
      const endpoint = connector.endpoints.find(e => e.id === endpointId);
      
      if (!endpoint) {
        throw new Error(`Endpoint '${endpointId}' not found in connector '${connectorId}'`);
      }
      
      // Get the credentials
      const credentials = authenticationManager.getCredentials(credentialId);
      
      if (!credentials) {
        throw new Error(`Credentials '${credentialId}' not found`);
      }
      
      // Build the request
      const request = await this.buildRequest(connector, endpoint, parameters);
      
      // Authenticate the request
      const authenticatedRequest = await authenticationManager.authenticateRequest(connector, credentials, request);
      
      // Execute the request
      const response = await this.executeRequest(authenticatedRequest);
      
      // Transform the response if a mapping is defined
      if (connector.mappings) {
        const mapping = connector.mappings.find(m => m.sourceEndpoint === endpointId);
        
        if (mapping) {
          return this.transformResponse(response.data, mapping);
        }
      }
      
      return response.data;
    } catch (error) {
      console.error(`Error executing endpoint '${endpointId}' for connector '${connectorId}':`, error);
      throw error;
    }
  }

  /**
   * Build a request object from a connector and endpoint configuration
   * 
   * @param {Object} connector - The connector configuration
   * @param {Object} endpoint - The endpoint configuration
   * @param {Object} parameters - The parameters to pass to the endpoint
   * @returns {Object} - The request object
   */
  async buildRequest(connector, endpoint, parameters = {}) {
    // Build the URL
    let url = connector.configuration.baseUrl + endpoint.path;
    
    // Replace path parameters
    if (endpoint.parameters && endpoint.parameters.path) {
      for (const [key, type] of Object.entries(endpoint.parameters.path)) {
        const value = parameters[key];
        
        if (!value && type === 'string') {
          throw new Error(`Missing required path parameter '${key}'`);
        }
        
        url = url.replace(`{{${key}}}`, encodeURIComponent(value));
      }
    }
    
    // Replace template variables in the URL
    url = this.replaceTemplateVariables(url, parameters);
    
    // Build the request object
    const request = {
      method: endpoint.method,
      url,
      headers: { ...connector.configuration.headers },
      timeout: connector.configuration.timeout || 30000
    };
    
    // Add query parameters
    if (endpoint.parameters && endpoint.parameters.query) {
      const queryParams = {};
      
      for (const [key, defaultValue] of Object.entries(endpoint.parameters.query)) {
        const value = parameters[key] !== undefined ? parameters[key] : defaultValue;
        
        if (value !== undefined) {
          queryParams[key] = value;
        }
      }
      
      if (Object.keys(queryParams).length > 0) {
        request.params = queryParams;
      }
    }
    
    // Add request body
    if (endpoint.parameters && endpoint.parameters.body) {
      let body = { ...endpoint.parameters.body };
      
      // Replace template variables in the body
      body = this.replaceTemplateVariables(body, parameters);
      
      request.data = body;
    }
    
    return request;
  }

  /**
   * Replace template variables in a string or object
   * 
   * @param {string|Object} template - The template to process
   * @param {Object} variables - The variables to replace
   * @returns {string|Object} - The processed template
   */
  replaceTemplateVariables(template, variables) {
    if (typeof template === 'string') {
      return template.replace(/{{([^}]+)}}/g, (match, key) => {
        return variables[key] !== undefined ? variables[key] : match;
      });
    }
    
    if (typeof template === 'object' && template !== null) {
      const result = Array.isArray(template) ? [] : {};
      
      for (const [key, value] of Object.entries(template)) {
        result[key] = this.replaceTemplateVariables(value, variables);
      }
      
      return result;
    }
    
    return template;
  }

  /**
   * Execute an HTTP request
   * 
   * @param {Object} request - The request object
   * @returns {Object} - The response object
   */
  async executeRequest(request) {
    try {
      // Create or get an Axios instance for this request
      const axiosInstance = this.getAxiosInstance(request.url);
      
      // Execute the request
      const response = await axiosInstance(request);
      
      return response;
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        throw new Error(`Request failed with status ${error.response.status}: ${error.response.statusText}`);
      } else if (error.request) {
        // The request was made but no response was received
        throw new Error(`No response received: ${error.message}`);
      } else {
        // Something happened in setting up the request that triggered an Error
        throw new Error(`Request setup failed: ${error.message}`);
      }
    }
  }

  /**
   * Get or create an Axios instance for a URL
   * 
   * @param {string} url - The base URL
   * @returns {Function} - The Axios instance
   */
  getAxiosInstance(url) {
    const baseUrl = new URL(url).origin;
    
    if (!this.axiosInstances.has(baseUrl)) {
      const instance = axios.create({
        baseURL: baseUrl
      });
      
      this.axiosInstances.set(baseUrl, instance);
    }
    
    return this.axiosInstances.get(baseUrl);
  }

  /**
   * Transform a response according to a mapping configuration
   * 
   * @param {Object} data - The response data
   * @param {Object} mapping - The mapping configuration
   * @returns {Object} - The transformed data
   */
  transformResponse(data, mapping) {
    try {
      const result = {};
      
      // Apply each transformation
      for (const transformation of mapping.transformations) {
        const { source, target, transform } = transformation;
        
        // Extract the source value using JSONPath
        const sourceValues = jsonpath.query(data, source);
        
        // Apply the transformation function
        const transformedValues = sourceValues.map(value => 
          transformationFunctions.applyTransformation(transform, value, transformation.parameters)
        );
        
        // Set the target value
        result[target] = transformedValues.length === 1 ? transformedValues[0] : transformedValues;
      }
      
      return {
        targetSystem: mapping.targetSystem,
        targetEntity: mapping.targetEntity,
        data: result
      };
    } catch (error) {
      console.error('Error transforming response:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const connectorExecutor = new ConnectorExecutor();

module.exports = connectorExecutor;
