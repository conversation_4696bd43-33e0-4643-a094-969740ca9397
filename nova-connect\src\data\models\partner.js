/**
 * NovaFuse Universal API Connector - Partner Model
 * 
 * This module defines the MongoDB schema for partner information.
 */

const mongoose = require('mongoose');
const { Schema } = mongoose;
const crypto = require('crypto');

// Define partner schema
const PartnerSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    unique: true,
    index: true
  },
  name: { 
    type: String, 
    required: true 
  },
  description: String,
  website: String,
  logo: String,
  apiKey: {
    type: String,
    required: true,
    unique: true
  },
  apiSecret: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'suspended'],
    default: 'pending',
    index: true
  },
  tier: {
    type: String,
    enum: ['basic', 'pro', 'enterprise'],
    default: 'basic',
    index: true
  },
  contactInfo: {
    primaryEmail: { type: String, required: true },
    technicalEmail: String,
    billingEmail: String,
    phone: String,
    address: {
      street: String,
      city: String,
      state: String,
      postalCode: String,
      country: String
    }
  },
  limits: {
    maxRequests: { type: Number, default: 10000 }, // Requests per day
    maxConcurrentRequests: { type: Number, default: 10 },
    maxConnectors: { type: Number, default: 5 },
    maxCredentials: { type: Number, default: 10 }
  },
  billing: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'pro', 'enterprise', 'custom'],
      default: 'free'
    },
    billingCycle: {
      type: String,
      enum: ['monthly', 'quarterly', 'annually'],
      default: 'monthly'
    },
    paymentMethod: {
      type: String,
      enum: ['credit_card', 'invoice', 'wire_transfer'],
      default: 'credit_card'
    },
    billingId: String, // ID in billing system
    lastBillingDate: Date,
    nextBillingDate: Date
  },
  settings: {
    webhookUrl: String,
    notificationPreferences: {
      email: { type: Boolean, default: true },
      webhook: { type: Boolean, default: false }
    },
    ipWhitelist: [String],
    allowedDomains: [String],
    customization: {
      primaryColor: String,
      secondaryColor: String,
      logoUrl: String
    }
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
}, {
  timestamps: true,
  versionKey: true,
  toJSON: {
    transform: function(doc, ret) {
      // Don't expose API secret in JSON
      delete ret.apiSecret;
      return ret;
    }
  }
});

// Pre-save hook to generate ID and API credentials if not provided
PartnerSchema.pre('save', function(next) {
  // Generate ID if not provided
  if (!this.id) {
    const normalizedName = this.name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    this.id = `partner-${normalizedName}-${crypto.randomBytes(4).toString('hex')}`;
  }
  
  // Generate API key and secret if not provided
  if (!this.apiKey) {
    this.apiKey = `nf-${crypto.randomBytes(16).toString('hex')}`;
  }
  
  if (!this.apiSecret) {
    this.apiSecret = crypto.randomBytes(32).toString('hex');
  }
  
  next();
});

// Method to validate API credentials
PartnerSchema.statics.validateApiCredentials = async function(apiKey, apiSecret) {
  const partner = await this.findOne({ apiKey, status: 'active' });
  
  if (!partner) {
    return null;
  }
  
  // Compare API secret
  if (partner.apiSecret !== apiSecret) {
    return null;
  }
  
  return partner;
};

// Method to check if partner has reached their limits
PartnerSchema.methods.checkLimits = async function(type, currentCount) {
  switch (type) {
    case 'requests':
      return currentCount < this.limits.maxRequests;
    case 'concurrentRequests':
      return currentCount < this.limits.maxConcurrentRequests;
    case 'connectors':
      return currentCount < this.limits.maxConnectors;
    case 'credentials':
      return currentCount < this.limits.maxCredentials;
    default:
      return true;
  }
};

// Method to regenerate API credentials
PartnerSchema.methods.regenerateApiCredentials = async function() {
  this.apiKey = `nf-${crypto.randomBytes(16).toString('hex')}`;
  this.apiSecret = crypto.randomBytes(32).toString('hex');
  
  return this.save();
};

// Create the model
const Partner = mongoose.model('Partner', PartnerSchema);

module.exports = Partner;
