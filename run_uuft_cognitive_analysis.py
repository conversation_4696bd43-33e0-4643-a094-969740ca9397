#!/usr/bin/env python3
"""
UUFT Cognitive Systems Analysis Runner

This script runs the complete UUFT cognitive analysis pipeline:
1. Creates neural network models with various architectures
2. Analyzes the models for 18/82 patterns in weights and activations
3. Produces a comprehensive report on the findings
"""

import os
import sys
import logging
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
from uuft_cognitive_analyzer import UUFTCognitiveAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_cognitive_run.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Cognitive_Run')

# Constants
RESULTS_DIR = "uuft_results/cognitive"
os.makedirs(RESULTS_DIR, exist_ok=True)

class SimpleNN(nn.Module):
    """A simple neural network with configurable architecture."""
    
    def __init__(self, input_size=784, hidden_sizes=[128, 64], output_size=10):
        super(SimpleNN, self).__init__()
        
        self.layers = nn.ModuleList()
        
        # Input layer
        self.layers.append(nn.Linear(input_size, hidden_sizes[0]))
        
        # Hidden layers
        for i in range(len(hidden_sizes) - 1):
            self.layers.append(nn.Linear(hidden_sizes[i], hidden_sizes[i+1]))
        
        # Output layer
        self.layers.append(nn.Linear(hidden_sizes[-1], output_size))
    
    def forward(self, x):
        for i, layer in enumerate(self.layers[:-1]):
            x = F.relu(layer(x))
        
        # Output layer (no activation for flexibility)
        x = self.layers[-1](x)
        return x

class CNN(nn.Module):
    """A simple convolutional neural network."""
    
    def __init__(self, input_channels=1, num_classes=10):
        super(CNN, self).__init__()
        
        # Convolutional layers
        self.conv1 = nn.Conv2d(input_channels, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.pool = nn.MaxPool2d(2)
        
        # Fully connected layers
        self.fc1 = nn.Linear(64 * 7 * 7, 128)
        self.fc2 = nn.Linear(128, num_classes)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(0.5)
    
    def forward(self, x):
        # Assuming input is [batch_size, channels, 28, 28]
        x = F.relu(self.conv1(x))
        x = self.pool(x)
        x = F.relu(self.conv2(x))
        x = self.pool(x)
        
        # Flatten
        x = x.view(-1, 64 * 7 * 7)
        
        # Fully connected layers
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        
        return x

class RNN(nn.Module):
    """A simple recurrent neural network."""
    
    def __init__(self, input_size=1, hidden_size=64, num_layers=1, output_size=1):
        super(RNN, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # RNN layer
        self.rnn = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        
        # Output layer
        self.fc = nn.Linear(hidden_size, output_size)
    
    def forward(self, x):
        # Initialize hidden state
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        
        # Forward propagate RNN
        out, _ = self.rnn(x, (h0, c0))
        
        # Decode the hidden state of the last time step
        out = self.fc(out[:, -1, :])
        
        return out

def create_neural_networks():
    """Create a variety of neural network architectures for analysis."""
    logger.info("Creating neural network models")
    
    models = []
    
    # Simple neural networks with different architectures
    models.append(("SimpleNN_2Layer", SimpleNN(input_size=784, hidden_sizes=[128, 64], output_size=10)))
    models.append(("SimpleNN_3Layer", SimpleNN(input_size=784, hidden_sizes=[256, 128, 64], output_size=10)))
    models.append(("SimpleNN_4Layer", SimpleNN(input_size=784, hidden_sizes=[512, 256, 128, 64], output_size=10)))
    
    # Neural network with 18/82 inspired architecture
    models.append(("SimpleNN_1882", SimpleNN(input_size=784, hidden_sizes=[180, 82], output_size=10)))
    
    # CNN
    models.append(("CNN_MNIST", CNN(input_channels=1, num_classes=10)))
    
    # RNN
    models.append(("RNN_TimeSeries", RNN(input_size=1, hidden_size=64, num_layers=2, output_size=1)))
    
    # Create a model with π-inspired architecture
    pi_model = SimpleNN(input_size=784, hidden_sizes=[314, 159, 26], output_size=10)
    models.append(("SimpleNN_Pi", pi_model))
    
    logger.info(f"Created {len(models)} neural network models")
    return models

def analyze_neural_networks(models):
    """Analyze neural networks for 18/82 patterns."""
    logger.info("Analyzing neural network models")
    
    analyzer = UUFTCognitiveAnalyzer()
    results = []
    
    for model_name, model in models:
        logger.info(f"Analyzing model: {model_name}")
        
        # Create sample input for activation analysis
        if "CNN" in model_name:
            sample_input = torch.randn(1, 1, 28, 28)  # [batch_size, channels, height, width]
        elif "RNN" in model_name:
            sample_input = torch.randn(1, 10, 1)  # [batch_size, seq_length, features]
        else:
            sample_input = torch.randn(1, 784)  # [batch_size, features]
        
        # Analyze model
        result = analyzer.analyze_neural_network(
            model=model,
            sample_input=sample_input,
            model_name=model_name
        )
        
        # Visualize weight distribution
        analyzer.visualize_weight_distribution(
            model=model,
            model_name=model_name,
            save_path=os.path.join(RESULTS_DIR, f"{model_name.lower()}_weights.png")
        )
        
        results.append(result)
    
    logger.info(f"Analyzed {len(results)} neural network models")
    return results

def create_comprehensive_report(results):
    """Create a comprehensive report on the cognitive analysis findings."""
    logger.info("Creating comprehensive report")
    
    # Create report using the analyzer's report function
    analyzer = UUFTCognitiveAnalyzer()
    summary = analyzer.create_cognitive_report(
        analysis_results=results,
        report_path=os.path.join(RESULTS_DIR, "cognitive_report.json")
    )
    
    # Create HTML report
    html_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>UUFT Cognitive Systems Analysis Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #2c3e50; }}
            .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; }}
            table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .high {{ color: green; }}
            .medium {{ color: orange; }}
            .low {{ color: red; }}
            .chart {{ margin-top: 30px; }}
        </style>
    </head>
    <body>
        <h1>UUFT Cognitive Systems Analysis Report</h1>
        <p>Date: {datetime.now().strftime('%Y-%m-%d')}</p>
        
        <div class="summary">
            <h2>Summary</h2>
            <p>Total models analyzed: {summary['total_models_analyzed']}</p>
            <p>Models with 18/82 pattern: {summary['models_with_1882_pattern']} ({summary['models_with_1882_pattern']/summary['total_models_analyzed']*100:.1f}%)</p>
            <p>Average 18/82 pattern score: {summary['average_1882_pattern_score']:.4f}</p>
            <p>Average π relationship score: {summary['average_pi_relationship_score']:.4f}</p>
        </div>
        
        <h2>Model Details</h2>
        <table>
            <tr>
                <th>Model</th>
                <th>Overall 18/82 Score</th>
                <th>Weight 18/82 Score</th>
                <th>Activation 18/82 Score</th>
                <th>Overall π Score</th>
                <th>Weight π Score</th>
                <th>Activation π Score</th>
            </tr>
    """
    
    # Add rows for each model
    for model_detail in summary['model_details']:
        # Determine color classes based on scores
        overall_1882_class = ""
        if model_detail.get('overall_1882_pattern_score', 0) > 0.7:
            overall_1882_class = "high"
        elif model_detail.get('overall_1882_pattern_score', 0) > 0.4:
            overall_1882_class = "medium"
        else:
            overall_1882_class = "low"
            
        overall_pi_class = ""
        if model_detail.get('overall_pi_relationship_score', 0) > 0.7:
            overall_pi_class = "high"
        elif model_detail.get('overall_pi_relationship_score', 0) > 0.4:
            overall_pi_class = "medium"
        else:
            overall_pi_class = "low"
        
        # Add row
        html_report += f"""
            <tr>
                <td>{model_detail['name']}</td>
                <td class="{overall_1882_class}">{model_detail.get('overall_1882_pattern_score', 0):.4f}</td>
                <td>{model_detail.get('weight_1882_pattern_score', 0):.4f}</td>
                <td>{model_detail.get('activation_1882_pattern_score', 0):.4f if 'activation_1882_pattern_score' in model_detail else 'N/A'}</td>
                <td class="{overall_pi_class}">{model_detail.get('overall_pi_relationship_score', 0):.4f}</td>
                <td>{model_detail.get('weight_pi_relationship_score', 0):.4f}</td>
                <td>{model_detail.get('activation_pi_relationship_score', 0):.4f if 'activation_pi_relationship_score' in model_detail else 'N/A'}</td>
            </tr>
        """
    
    # Add visualizations and conclusions
    html_report += """
        </table>
        
        <div class="chart">
            <h2>Visualizations</h2>
            <p>Weight distribution visualizations for each model are available in the results directory.</p>
        </div>
        
        <h2>Conclusions</h2>
        <p>This analysis demonstrates the presence of 18/82 patterns and π relationships in neural network architectures.</p>
        <p>Key findings:</p>
        <ul>
    """
    
    # Add conclusions based on results
    if summary['average_1882_pattern_score'] > 0.5:
        html_report += "<li>Neural networks exhibit significant 18/82 patterns in their weight distributions, suggesting this may be an emergent property of effective neural architectures.</li>"
    else:
        html_report += "<li>18/82 patterns are present but not dominant in neural network architectures, suggesting they may be one of several organizing principles.</li>"
    
    if summary['average_pi_relationship_score'] > 0.5:
        html_report += "<li>π-related relationships are prevalent in neural networks, potentially indicating a connection to fundamental mathematical principles.</li>"
    
    # Add model-specific observations
    for model_detail in summary['model_details']:
        if "1882" in model_detail['name'] and model_detail.get('overall_1882_pattern_score', 0) > 0.7:
            html_report += f"<li>The explicitly 18/82-inspired architecture ({model_detail['name']}) shows strong 18/82 patterns, confirming the design intention.</li>"
        
        if "Pi" in model_detail['name'] and model_detail.get('overall_pi_relationship_score', 0) > 0.7:
            html_report += f"<li>The π-inspired architecture ({model_detail['name']}) exhibits strong π relationships, confirming the design intention.</li>"
    
    html_report += """
        </ul>
        <p>These findings support the UUFT framework's hypothesis that 18/82 patterns and π relationships may represent fundamental organizing principles in cognitive systems, including artificial neural networks.</p>
    </body>
    </html>
    """
    
    # Save HTML report
    with open(os.path.join(RESULTS_DIR, "cognitive_report.html"), 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    logger.info("Comprehensive report created successfully")
    
    return summary

def main():
    """Run the complete UUFT cognitive analysis pipeline."""
    logger.info("Starting UUFT cognitive analysis pipeline")
    
    # Step 1: Create neural network models
    models = create_neural_networks()
    
    # Step 2: Analyze neural networks
    results = analyze_neural_networks(models)
    
    # Step 3: Create comprehensive report
    summary = create_comprehensive_report(results)
    
    logger.info("UUFT cognitive analysis pipeline completed successfully")
    
    # Print location of report
    print(f"\nAnalysis complete! Comprehensive report available at:")
    print(f"  {os.path.abspath(os.path.join(RESULTS_DIR, 'cognitive_report.html'))}")

if __name__ == "__main__":
    main()
