const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/security/controls/routes');
const models = require('../../../../apis/security/controls/models');

// Mock the models
jest.mock('../../../../apis/security/controls/models', () => ({
  controlFrameworks: [
    {
      id: 'cf-********',
      name: 'NIST SP 800-53',
      description: 'Security and Privacy Controls for Information Systems and Organizations',
      version: 'Rev. 5',
      category: 'government',
      status: 'active',
      controls: [
        {
          id: 'control-12345',
          identifier: 'AC-1',
          title: 'Access Control Policy and Procedures',
          description: 'The organization develops, documents, and disseminates an access control policy...',
          category: 'access-control',
          priority: 'P1',
          status: 'active'
        },
        {
          id: 'control-67890',
          identifier: 'AC-2',
          title: 'Account Management',
          description: 'The organization manages information system accounts...',
          category: 'access-control',
          priority: 'P1',
          status: 'active'
        }
      ],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'cf-********',
      name: 'ISO 27001',
      description: 'Information Security Management System Requirements',
      version: '2022',
      category: 'international',
      status: 'active',
      controls: [
        {
          id: 'control-54321',
          identifier: 'A.5.1',
          title: 'Information Security Policies',
          description: 'To provide management direction and support for information security...',
          category: 'policies',
          priority: 'high',
          status: 'active'
        }
      ],
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-01T00:00:00Z'
    }
  ],
  controlTests: [
    {
      id: 'ct-********',
      name: 'Annual Access Control Review',
      description: 'Annual review of access control policies and procedures',
      controlId: 'control-12345',
      frameworkId: 'cf-********',
      type: 'manual',
      frequency: 'annual',
      status: 'active',
      testSteps: [
        {
          id: 'step-12345',
          order: 1,
          description: 'Review access control policy document',
          expectedResult: 'Policy document exists and is up to date',
          evidence: 'Document screenshot or reference'
        },
        {
          id: 'step-67890',
          order: 2,
          description: 'Verify policy approval by management',
          expectedResult: 'Approval signature or record exists',
          evidence: 'Approval record screenshot'
        }
      ],
      lastExecuted: '2023-03-15T00:00:00Z',
      nextScheduled: '2024-03-15T00:00:00Z',
      owner: 'Security Team',
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-03-15T00:00:00Z'
    },
    {
      id: 'ct-********',
      name: 'Quarterly Account Review',
      description: 'Quarterly review of user accounts and privileges',
      controlId: 'control-67890',
      frameworkId: 'cf-********',
      type: 'automated',
      frequency: 'quarterly',
      status: 'active',
      testSteps: [
        {
          id: 'step-54321',
          order: 1,
          description: 'Run account audit script',
          expectedResult: 'Script executes successfully and generates report',
          evidence: 'Audit report'
        }
      ],
      lastExecuted: '2023-04-01T00:00:00Z',
      nextScheduled: '2023-07-01T00:00:00Z',
      owner: 'IT Team',
      createdAt: '2023-01-20T00:00:00Z',
      updatedAt: '2023-04-01T00:00:00Z'
    }
  ],
  testResults: [
    {
      id: 'tr-********',
      testId: 'ct-********',
      executionDate: '2023-03-15T00:00:00Z',
      status: 'passed',
      summary: 'All test steps passed successfully',
      stepResults: [
        {
          stepId: 'step-12345',
          status: 'passed',
          actualResult: 'Policy document reviewed and confirmed up to date',
          evidence: 'https://example.com/evidence/policy-screenshot.png',
          notes: 'Policy was updated in January 2023'
        },
        {
          stepId: 'step-67890',
          status: 'passed',
          actualResult: 'Approval signature verified',
          evidence: 'https://example.com/evidence/approval-record.png',
          notes: 'Approved by CISO on January 10, 2023'
        }
      ],
      tester: 'Jane Smith',
      reviewedBy: 'John Doe',
      reviewDate: '2023-03-20T00:00:00Z',
      createdAt: '2023-03-15T00:00:00Z',
      updatedAt: '2023-03-20T00:00:00Z'
    },
    {
      id: 'tr-********',
      testId: 'ct-********',
      executionDate: '2023-04-01T00:00:00Z',
      status: 'failed',
      summary: 'Test failed due to inactive accounts found',
      stepResults: [
        {
          stepId: 'step-54321',
          status: 'failed',
          actualResult: 'Script executed but found 5 inactive accounts not removed',
          evidence: 'https://example.com/evidence/account-audit-report.pdf',
          notes: 'Inactive accounts belong to former employees'
        }
      ],
      tester: 'Automated System',
      reviewedBy: 'Jane Smith',
      reviewDate: '2023-04-02T00:00:00Z',
      createdAt: '2023-04-01T00:00:00Z',
      updatedAt: '2023-04-02T00:00:00Z'
    }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/security/controls', router);

describe('Control Testing API Integration Tests', () => {
  describe('GET /security/controls/frameworks', () => {
    it('should return all control frameworks with default pagination', async () => {
      const response = await request(app).get('/security/controls/frameworks');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter frameworks by category', async () => {
      const response = await request(app).get('/security/controls/frameworks?category=government');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].category).toBe('government');
    });
  });

  describe('GET /security/controls/frameworks/:id', () => {
    it('should return a specific control framework by ID', async () => {
      const response = await request(app).get('/security/controls/frameworks/cf-********');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('cf-********');
      expect(response.body.data.name).toBe('NIST SP 800-53');
    });

    it('should return 404 if framework not found', async () => {
      const response = await request(app).get('/security/controls/frameworks/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /security/controls/frameworks/:frameworkId/controls/:controlId', () => {
    it('should return a specific control by ID', async () => {
      const response = await request(app).get('/security/controls/frameworks/cf-********/controls/control-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('control-12345');
      expect(response.body.data.identifier).toBe('AC-1');
    });

    it('should return 404 if control not found', async () => {
      const response = await request(app).get('/security/controls/frameworks/cf-********/controls/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /security/controls/tests', () => {
    it('should return all control tests with default pagination', async () => {
      const response = await request(app).get('/security/controls/tests');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter tests by type', async () => {
      const response = await request(app).get('/security/controls/tests?type=manual');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].type).toBe('manual');
    });

    it('should filter tests by frequency', async () => {
      const response = await request(app).get('/security/controls/tests?frequency=quarterly');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].frequency).toBe('quarterly');
    });
  });

  describe('GET /security/controls/tests/:id', () => {
    it('should return a specific control test by ID', async () => {
      const response = await request(app).get('/security/controls/tests/ct-********');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('ct-********');
      expect(response.body.data.name).toBe('Annual Access Control Review');
    });

    it('should return 404 if test not found', async () => {
      const response = await request(app).get('/security/controls/tests/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /security/controls/tests', () => {
    it('should create a new control test', async () => {
      const newTest = {
        name: 'Monthly Password Policy Check',
        description: 'Monthly verification of password policy enforcement',
        controlId: 'control-12345',
        frameworkId: 'cf-********',
        type: 'automated',
        frequency: 'monthly',
        status: 'active',
        testSteps: [
          {
            order: 1,
            description: 'Run password policy audit script',
            expectedResult: 'All accounts comply with password policy',
            evidence: 'Audit report'
          }
        ],
        owner: 'Security Team'
      };

      const response = await request(app)
        .post('/security/controls/tests')
        .send(newTest);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Control test created successfully');
      expect(response.body.data.name).toBe('Monthly Password Policy Check');
      expect(response.body.data.type).toBe('automated');
    });

    it('should return 400 for invalid input', async () => {
      const invalidTest = {
        // Missing required fields
        description: 'Invalid test'
      };

      const response = await request(app)
        .post('/security/controls/tests')
        .send(invalidTest);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /security/controls/tests/:id', () => {
    it('should update an existing control test', async () => {
      const updatedTest = {
        name: 'Updated Test Name',
        frequency: 'semi-annual'
      };

      const response = await request(app)
        .put('/security/controls/tests/ct-********')
        .send(updatedTest);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Control test updated successfully');
      expect(response.body.data.name).toBe('Updated Test Name');
      expect(response.body.data.frequency).toBe('semi-annual');
    });

    it('should return 404 if test not found', async () => {
      const response = await request(app)
        .put('/security/controls/tests/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /security/controls/tests/:id', () => {
    it('should delete an existing control test', async () => {
      const response = await request(app).delete('/security/controls/tests/ct-********');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Control test deleted successfully');
    });

    it('should return 404 if test not found', async () => {
      const response = await request(app).delete('/security/controls/tests/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /security/controls/results', () => {
    it('should return all test results with default pagination', async () => {
      const response = await request(app).get('/security/controls/results');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter results by status', async () => {
      const response = await request(app).get('/security/controls/results?status=passed');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('passed');
    });

    it('should filter results by test ID', async () => {
      const response = await request(app).get('/security/controls/results?testId=ct-********');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].testId).toBe('ct-********');
    });
  });

  describe('GET /security/controls/results/:id', () => {
    it('should return a specific test result by ID', async () => {
      const response = await request(app).get('/security/controls/results/tr-********');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('tr-********');
      expect(response.body.data.status).toBe('passed');
    });

    it('should return 404 if result not found', async () => {
      const response = await request(app).get('/security/controls/results/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /security/controls/results', () => {
    it('should create a new test result', async () => {
      const newResult = {
        testId: 'ct-********',
        status: 'passed',
        summary: 'All test steps passed successfully',
        stepResults: [
          {
            stepId: 'step-12345',
            status: 'passed',
            actualResult: 'Policy document reviewed and confirmed up to date',
            evidence: 'https://example.com/evidence/policy-screenshot.png',
            notes: 'Policy was updated in January 2023'
          },
          {
            stepId: 'step-67890',
            status: 'passed',
            actualResult: 'Approval signature verified',
            evidence: 'https://example.com/evidence/approval-record.png',
            notes: 'Approved by CISO on January 10, 2023'
          }
        ],
        tester: 'Jane Smith'
      };

      const response = await request(app)
        .post('/security/controls/results')
        .send(newResult);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Test result created successfully');
      expect(response.body.data.testId).toBe('ct-********');
      expect(response.body.data.status).toBe('passed');
    });

    it('should return 400 for invalid input', async () => {
      const invalidResult = {
        // Missing required fields
        summary: 'Invalid result'
      };

      const response = await request(app)
        .post('/security/controls/results')
        .send(invalidResult);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /security/controls/results/:id', () => {
    it('should update an existing test result', async () => {
      const updatedResult = {
        status: 'failed',
        summary: 'Updated test result summary'
      };

      const response = await request(app)
        .put('/security/controls/results/tr-********')
        .send(updatedResult);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Test result updated successfully');
      expect(response.body.data.status).toBe('failed');
      expect(response.body.data.summary).toBe('Updated test result summary');
    });

    it('should return 404 if result not found', async () => {
      const response = await request(app)
        .put('/security/controls/results/non-existent-id')
        .send({ status: 'failed' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /security/controls/results/:id/review', () => {
    it('should review a test result', async () => {
      const reviewData = {
        reviewedBy: 'John Doe',
        reviewNotes: 'Reviewed and approved'
      };

      const response = await request(app)
        .post('/security/controls/results/tr-********/review')
        .send(reviewData);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Test result reviewed successfully');
      expect(response.body.data.reviewedBy).toBe('John Doe');
    });

    it('should return 404 if result not found', async () => {
      const response = await request(app)
        .post('/security/controls/results/non-existent-id/review')
        .send({ reviewedBy: 'John Doe' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });
});
