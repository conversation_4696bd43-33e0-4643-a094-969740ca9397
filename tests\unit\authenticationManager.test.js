/**
 * Authentication Manager Tests
 *
 * This file contains unit tests for the authentication manager service.
 */

const crypto = require('crypto');
const authenticationManager = require('../../services/authenticationManager');

// Mock crypto module
jest.mock('crypto');

describe('Authentication Manager', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('generateAuthHeaders', () => {
    it('should generate OAuth2 headers', async () => {
      const integration = {
        authType: 'oauth2',
        config: {
          clientId: 'test-client-id',
          clientSecret: 'test-client-secret',
          accessToken: 'test-access-token'
        }
      };

      const headers = await authenticationManager.generateAuthHeaders(integration);

      expect(headers).toEqual({
        'Authorization': 'Bearer mock-oauth2-token'
      });
    });

    it('should generate API key headers', async () => {
      const integration = {
        authType: 'api_key',
        config: {
          apiKeyName: 'X-Custom-API-Key',
          apiKey: 'test-api-key'
        }
      };

      const headers = await authenticationManager.generateAuthHeaders(integration);

      expect(headers).toEqual({
        'X-Custom-API-Key': 'test-api-key'
      });
    });

    it('should use default API key header name if not provided', async () => {
      const integration = {
        authType: 'api_key',
        config: {
          apiKey: 'test-api-key'
        }
      };

      const headers = await authenticationManager.generateAuthHeaders(integration);

      expect(headers).toEqual({
        'X-API-Key': 'test-api-key'
      });
    });

    it('should generate Basic Auth headers', async () => {
      const integration = {
        authType: 'basic_auth',
        config: {
          username: 'test-user',
          password: 'test-password'
        }
      };

      // Expected base64 encoded credentials: 'test-user:test-password'
      const expectedCredentials = Buffer.from('test-user:test-password').toString('base64');

      const headers = await authenticationManager.generateAuthHeaders(integration);

      expect(headers).toEqual({
        'Authorization': `Basic ${expectedCredentials}`
      });
    });

    it('should generate custom auth headers', async () => {
      const integration = {
        authType: 'custom',
        config: {
          headers: {
            'X-Custom-Header': 'custom-value',
            'Another-Header': 'another-value'
          }
        }
      };

      const headers = await authenticationManager.generateAuthHeaders(integration);

      expect(headers).toEqual({
        'X-Custom-Header': 'custom-value',
        'Another-Header': 'another-value'
      });
    });

    it('should return empty object for custom auth with no headers', async () => {
      const integration = {
        authType: 'custom',
        config: {}
      };

      const headers = await authenticationManager.generateAuthHeaders(integration);

      expect(headers).toEqual({});
    });

    it('should throw error for unsupported auth type', async () => {
      const integration = {
        authType: 'unsupported',
        config: {}
      };

      await expect(authenticationManager.generateAuthHeaders(integration))
        .rejects
        .toThrow('Unsupported authentication type: unsupported');
    });
  });

  describe('encryptConfig', () => {
    it('should encrypt configuration data', async () => {
      const config = {
        username: 'test-user',
        password: 'test-password'
      };

      const encryptedConfig = await authenticationManager.encryptConfig(config);

      // In the current implementation, it just returns the original config
      expect(encryptedConfig).toEqual(config);
    });
  });

  describe('decryptConfig', () => {
    it('should decrypt configuration data', async () => {
      const encryptedConfig = {
        username: 'test-user',
        password: 'encrypted-password'
      };

      const decryptedConfig = await authenticationManager.decryptConfig(encryptedConfig);

      // In the current implementation, it just returns the original config
      expect(decryptedConfig).toEqual(encryptedConfig);
    });
  });

  describe('validateAuthConfig', () => {
    it('should validate OAuth2 configuration', async () => {
      const validConfig = {
        clientId: 'test-client-id',
        clientSecret: 'test-client-secret',
        accessToken: 'test-access-token'
      };

      const invalidConfig = {
        clientId: 'test-client-id'
      };

      const validResult = await authenticationManager.validateAuthConfig('oauth2', validConfig);
      const invalidResult = await authenticationManager.validateAuthConfig('oauth2', invalidConfig);

      expect(validResult).toBe(true);
      expect(invalidResult).toBe(false);
    });

    it('should validate OAuth2 configuration with refresh token', async () => {
      const validConfig = {
        clientId: 'test-client-id',
        clientSecret: 'test-client-secret',
        refreshToken: 'test-refresh-token'
      };

      const validResult = await authenticationManager.validateAuthConfig('oauth2', validConfig);

      expect(validResult).toBe(true);
    });

    it('should validate API key configuration', async () => {
      const validConfig = {
        apiKey: 'test-api-key'
      };

      const invalidConfig = {
        apiKeyName: 'X-API-Key'
      };

      const validResult = await authenticationManager.validateAuthConfig('api_key', validConfig);
      const invalidResult = await authenticationManager.validateAuthConfig('api_key', invalidConfig);

      expect(validResult).toBe(true);
      expect(invalidResult).toBe(false);
    });

    it('should validate Basic Auth configuration', async () => {
      const validConfig = {
        username: 'test-user',
        password: 'test-password'
      };

      const invalidConfig = {
        username: 'test-user'
      };

      const validResult = await authenticationManager.validateAuthConfig('basic_auth', validConfig);
      const invalidResult = await authenticationManager.validateAuthConfig('basic_auth', invalidConfig);

      expect(validResult).toBe(true);
      expect(invalidResult).toBe(false);
    });

    it('should validate custom auth configuration', async () => {
      const config = {
        customField: 'custom-value'
      };

      const result = await authenticationManager.validateAuthConfig('custom', config);

      // In the current implementation, it always returns true for custom auth
      expect(result).toBe(true);
    });

    it('should return false for unsupported auth type', async () => {
      const config = {
        someField: 'some-value'
      };

      const result = await authenticationManager.validateAuthConfig('unsupported', config);

      expect(result).toBe(false);
    });
  });
});
