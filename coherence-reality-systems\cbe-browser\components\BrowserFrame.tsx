'use client';

interface BrowserFrameProps {
  url: string;
  isLoading: boolean;
  consciousnessData: {
    overall: number;
    coherence: number;
    psiSnap: boolean;
    analysisTime: number;
  };
}

export default function BrowserFrame({ url, isLoading, consciousnessData }: BrowserFrameProps) {
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-900/50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mb-6 mx-auto" />
          <div className="text-white text-xl mb-2">Analyzing consciousness patterns...</div>
          <div className="text-gray-400 text-sm mb-4">Scanning for Ψ-coherence and divine alignment</div>
          
          {/* Loading Steps */}
          <div className="space-y-2 text-left max-w-md mx-auto">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">Triadic intention encoding (A⊗B⊕C)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">UUFT field stabilization</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">Consciousness threshold verification</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">KetherNet crown consensus</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-500/10 border-green-400/30';
    if (score >= 70) return 'bg-yellow-500/10 border-yellow-400/30';
    return 'bg-red-500/10 border-red-400/30';
  };

  return (
    <div className="flex-1 relative bg-white rounded-lg m-4 overflow-hidden shadow-2xl">
      {/* Browser Content */}
      <div className="h-full relative">
        {url ? (
          <iframe
            src={url}
            className="w-full h-full border-none"
            title="Website Content"
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
          />
        ) : (
          <div className="h-full flex items-center justify-center bg-gray-100">
            <div className="text-center text-gray-500">
              <div className="text-6xl mb-4">🌐</div>
              <div className="text-xl">Enter a URL to browse with consciousness</div>
              <div className="text-sm mt-2 text-gray-400">CBE will analyze and enhance your browsing experience</div>
            </div>
          </div>
        )}
      </div>

      {/* Consciousness Overlay */}
      {url && (
        <div className="absolute top-6 right-6 bg-black/80 backdrop-blur-lg rounded-xl p-4 min-w-64 shadow-xl border border-purple-500/30">
          <div className="flex items-center space-x-2 mb-4">
            <span className="text-lg">🧬</span>
            <span className="font-semibold text-white text-sm">Live Analysis</span>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse ml-auto"></div>
          </div>
          
          <div className="space-y-3">
            {/* Overall Score */}
            <div className={`rounded-lg p-3 border ${getScoreBackground(consciousnessData.overall)}`}>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-300">Overall Coherence</span>
                <span className={`font-mono text-sm font-bold ${getScoreColor(consciousnessData.overall)}`}>
                  {consciousnessData.overall}%
                </span>
              </div>
            </div>

            {/* Ψ-Snap Status */}
            <div className={`rounded-lg p-3 border ${consciousnessData.psiSnap ? 'bg-green-500/10 border-green-400/30' : 'bg-red-500/10 border-red-400/30'}`}>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-300">Ψ-Snap</span>
                <span className={`font-mono text-sm font-bold ${consciousnessData.psiSnap ? 'text-green-400' : 'text-red-400'}`}>
                  {consciousnessData.psiSnap ? 'ACTIVE' : 'INACTIVE'}
                </span>
              </div>
            </div>

            {/* Analysis Time */}
            <div className="rounded-lg p-3 border border-gray-600 bg-gray-800/50">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-300">Analysis Time</span>
                <span className="font-mono text-sm text-purple-300">
                  {consciousnessData.analysisTime}ms
                </span>
              </div>
            </div>

            {/* Enhancement Status */}
            <div className="rounded-lg p-3 border border-purple-500/30 bg-purple-500/10">
              <div className="text-center">
                <div className="text-xs text-purple-300 mb-1">CBE Enhancement</div>
                <div className="text-sm font-semibold text-white">
                  {consciousnessData.overall >= 90 ? '🌟 DIVINE' : 
                   consciousnessData.overall >= 70 ? '✨ ENHANCED' : '⚠️ FILTERED'}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-4 space-y-2">
            <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-3 rounded-lg text-xs font-medium transition-colors">
              🔍 Deep Scan
            </button>
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg text-xs font-medium transition-colors">
              ⚡ Boost Ψ-Level
            </button>
          </div>
        </div>
      )}

      {/* Consciousness Enhancement Effects */}
      {url && consciousnessData.overall >= 90 && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-yellow-400 via-purple-500 to-pink-500 animate-pulse"></div>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-pink-500 via-purple-500 to-yellow-400 animate-pulse"></div>
        </div>
      )}

      {/* Low Consciousness Warning */}
      {url && consciousnessData.overall < 50 && (
        <div className="absolute inset-0 bg-red-900/20 backdrop-blur-sm flex items-center justify-center">
          <div className="bg-red-900/80 backdrop-blur-lg rounded-xl p-6 max-w-md text-center border border-red-500/50">
            <div className="text-4xl mb-4">⚠️</div>
            <h3 className="text-xl font-bold text-white mb-2">Low Consciousness Content</h3>
            <p className="text-red-200 mb-4">
              This content has a consciousness level below the CBE threshold.
            </p>
            <div className="text-2xl font-mono text-red-300 mb-4">
              Ψᶜʰ: {consciousnessData.overall}
            </div>
            <p className="text-sm text-red-200 mb-4">
              Required: 2847
            </p>
            <button className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
              Continue After Meditation
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
