/**
 * Monitoring Routes
 * 
 * This file defines the routes for the monitoring API.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../middleware/asyncHandler');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const monitoringController = require('../controllers/MonitoringController');

/**
 * @route   GET /api/monitoring/metrics
 * @desc    Get monitoring metrics
 * @access  Private
 */
router.get(
  '/metrics',
  authenticate,
  authorize('read:monitoring'),
  asyncHandler(monitoringController.getMetrics)
);

/**
 * @route   GET /api/monitoring/alerts
 * @desc    Get monitoring alerts
 * @access  Private
 */
router.get(
  '/alerts',
  authenticate,
  authorize('read:monitoring'),
  async<PERSON><PERSON><PERSON>(monitoringController.getAlerts)
);

module.exports = router;
