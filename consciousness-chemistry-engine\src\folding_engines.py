"""
Integration with external protein folding engines.

This module provides interfaces to various protein folding services like AlphaFold,
ColabFold, and RoseTTAFold, allowing the system to generate real protein structure predictions.
"""

import os
import json
import requests
import tempfile
import subprocess
import shutil
from typing import Dict, Any, Optional, List, Tuple, Union
from pathlib import Path
import time
import logging
from datetime import datetime
import platform
import sys

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants
ALPHAFOLD_API_URL = "https://alphafold.ebi.ac.uk/api/prediction/"
COLABFOLD_API_URL = "https://api.colabfold.com/v1/predict"
ROSETTAFOLD_API_URL = "https://robetta.bakerlab.org/folding/queue.json"
DEFAULT_TIMEOUT = 300  # 5 minutes

# Environment variables for engine paths
ENV_VARS = {
    'ALPHAFOLD_PATH': 'ALPHAFOLD_PATH',
    'COLABFOLD_PATH': 'COLABFOLD_PATH',
    'ROSETTAFOLD_PATH': 'ROSETTAFOLD_PATH',
    'HHSUITE_PATH': 'HHSUITE_PATH',
    'HHLIB': 'HHLIB'
}

class FoldingEngineError(Exception):
    """Base exception for folding engine errors."""
    pass


class FoldingEngine:
    """Base class for all folding engine integrations with consciousness-aware optimizations."""
    
    # Default configuration presets
    PRESETS = {
        'standard': {
            'mode': 'balanced',
            'psi_optimization': False,
            'fib_constraints': {'enabled': False},
            'cache_policy': {'strategy': 'standard', 'max_entries': 100}
        },
        'therapeutic': {
            'mode': 'high_accuracy',
            'psi_optimization': True,
            'fib_constraints': {'enabled': True, 'tolerance': 0.1},
            'cache_policy': {'strategy': 'aggressive', 'max_entries': 1000}
        },
        'speed': {
            'mode': 'fast',
            'psi_optimization': False,
            'fib_constraints': {'enabled': False},
            'cache_policy': {'strategy': 'minimal', 'max_entries': 10}
        }
    }
    
    def __init__(self, config: Optional[Union[str, Dict]] = None, **kwargs):
        """
        Initialize the folding engine with configuration.
        
        Args:
            config: Either a configuration dictionary, preset name, or None for default
            **kwargs: Additional configuration parameters
        """
        # Apply preset if config is a string
        if isinstance(config, str) and config in self.PRESETS:
            self.config = self.PRESETS[config].copy()
        else:
            # Start with default preset and update with provided config
            self.config = self.PRESETS['standard'].copy()
            if isinstance(config, dict):
                self.config.update(config)
        
        # Override with any explicit kwargs
        self.config.update(kwargs)
        
        # Initialize cache
        self._init_cache()
    
    def _init_cache(self):
        """Initialize the prediction cache based on configuration."""
        cache_config = self.config.get('cache_policy', {})
        self.cache = {}
        self.max_cache_entries = cache_config.get('max_entries', 100)
    
    def _get_cache_key(self, sequence: str, **kwargs) -> str:
        """Generate a cache key for the given sequence and parameters."""
        params = {k: v for k, v in kwargs.items() if k != 'self'}
        return f"{sequence}_{hash(frozenset(params.items()))}"
    
    def _get_from_cache(self, key: str) -> Optional[Dict]:
        """Retrieve a prediction from cache."""
        if key in self.cache:
            # Move to end to mark as recently used
            value = self.cache.pop(key)
            self.cache[key] = value
            return value
        return None
    
    def _add_to_cache(self, key: str, value: Dict):
        """Add a prediction to cache, evicting old entries if necessary."""
        if len(self.cache) >= self.max_cache_entries:
            # Remove least recently used entry
            self.cache.pop(next(iter(self.cache)))
        self.cache[key] = value
    
    def predict(self, sequence: str, **kwargs) -> Dict[str, Any]:
        """
        Predict the structure of a protein sequence.
        
        Args:
            sequence: Protein sequence in one-letter code
            **kwargs: Additional prediction parameters
            
        Returns:
            Dictionary containing prediction results
        """
        raise NotImplementedError("Subclasses must implement predict method")
    
    def get_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get the status of a prediction job.
        
        Args:
            job_id: ID of the prediction job
            
        Returns:
            Dictionary containing job status and results if available
        """
        raise NotImplementedError("Subclasses must implement get_status method")
    
    def download_structure(self, url: str, output_path: str) -> str:
        """
        Download a structure file from a URL.
        
        Args:
            url: URL of the structure file
            output_path: Path to save the downloaded file
            
        Returns:
            Path to the downloaded file
        """
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    
            return output_path
        except requests.RequestException as e:
            raise FoldingEngineError(f"Failed to download structure: {str(e)}")


class AlphaFoldEngine(FoldingEngine):
    """Integration with the AlphaFold Protein Structure Database API."""
    
    def __init__(self, api_key: Optional[str] = None, cache_dir: Optional[str] = None):
        """
        Initialize the AlphaFold engine.
        
        Args:
            api_key: Optional API key for the AlphaFold API
            cache_dir: Directory to cache downloaded structures
        """
        self.api_key = api_key or os.getenv('ALPHAFOLD_API_KEY')
        self.cache_dir = cache_dir or os.path.join(os.path.expanduser('~'), '.af2_cache')
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def predict(self, sequence: str, **kwargs) -> Dict[str, Any]:
        """
        Predict protein structure using AlphaFold.
        
        Args:
            sequence: Protein sequence in one-letter code
            **kwargs: Additional prediction parameters
            
        Returns:
            Dictionary containing prediction results
        """
        # Check if we already have this sequence in the cache
        cache_key = f"af2_{hash(sequence)}"
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        if os.path.exists(cache_file):
            logger.info(f"Loading prediction from cache: {cache_file}")
            with open(cache_file, 'r') as f:
                return json.load(f)
        
        # If not in cache, submit to AlphaFold API
        logger.info(f"Submitting sequence to AlphaFold API (length: {len(sequence)})")
        
        headers = {
            'Content-Type': 'application/json',
        }
        
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        payload = {
            'sequence': sequence,
            'job_name': f'af2_{int(time.time())}',
        }
        
        try:
            response = requests.post(
                ALPHAFOLD_API_URL,
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            
            # Save to cache
            with open(cache_file, 'w') as f:
                json.dump(result, f)
                
            return result
            
        except requests.RequestException as e:
            raise FoldingEngineError(f"AlphaFold API request failed: {str(e)}")
    
    def get_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get the status of an AlphaFold prediction job.
        
        Args:
            job_id: ID of the prediction job
            
        Returns:
            Dictionary containing job status and results if available
        """
        headers = {}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
            
        try:
            response = requests.get(
                f"{ALPHAFOLD_API_URL}{job_id}",
                headers=headers,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
            
        except requests.RequestException as e:
            raise FoldingEngineError(f"Failed to get job status: {str(e)}")
    
    def download_prediction(self, job_id: str, output_dir: Optional[str] = None) -> Dict[str, str]:
        """
        Download the results of a completed prediction.
        
        Args:
            job_id: ID of the prediction job
            output_dir: Directory to save the downloaded files
            
        Returns:
            Dictionary mapping file types to their local paths
        """
        if output_dir is None:
            output_dir = os.path.join(self.cache_dir, job_id)
            os.makedirs(output_dir, exist_ok=True)
        
        status = self.get_status(job_id)
        
        if status.get('status') != 'COMPLETED':
            raise FoldingEngineError(f"Prediction job {job_id} is not complete. Status: {status.get('status')}")
        
        result_files = {}
        
        # Download PDB file
        if 'pdb_url' in status:
            pdb_path = os.path.join(output_dir, f"{job_id}.pdb")
            self.download_structure(status['pdb_url'], pdb_path)
            result_files['pdb'] = pdb_path
        
        # Download JSON with additional data
        if 'json_url' in status:
            json_path = os.path.join(output_dir, f"{job_id}.json")
            self.download_structure(status['json_url'], json_path)
            result_files['json'] = json_path
        
        # Download PAE (Predicted Aligned Error) file if available
        if 'pae_url' in status:
            pae_path = os.path.join(output_dir, f"{job_id}_pae.json")
            self.download_structure(status['pae_url'], pae_path)
            result_files['pae'] = pae_path
        
        return result_files


def create_engine(
    engine_name: str = 'alphafold',
    mode: str = 'standard',
    psi_optimization: bool = False,
    fib_constraints: Optional[Dict] = None,
    cache_policy: Optional[Dict] = None,
    **kwargs
) -> 'FoldingEngine':
    """
    Create a folding engine instance with advanced configuration.
    
    Args:
        engine_name: Name of the folding engine ('alphafold', 'colabfold', 'local_alphafold', 'rosettafold')
        mode: Operation mode ('standard', 'therapeutic', 'speed')
        psi_optimization: Enable consciousness-aware optimization
        fib_constraints: Fibonacci constraint settings
            - enabled: bool - Enable Fibonacci constraints
            - tolerance: float - Max deviation from golden ratio (default: 0.1)
        cache_policy: Cache configuration
            - strategy: str - 'minimal', 'standard', or 'aggressive'
            - max_entries: int - Maximum cache entries
        **kwargs: Additional arguments for the specific engine
        
    Returns:
        An instance of the requested folding engine
        
    Raises:
        ValueError: If the requested engine is not supported
    """
    # Build config dictionary
    config = {
        'mode': mode,
        'psi_optimization': psi_optimization,
        'fib_constraints': fib_constraints or {},
        'cache_policy': cache_policy or {}
    }
    
    engine_name = engine_name.lower()
    if engine_name == 'alphafold':
        return AlphaFoldEngine(config=config, **kwargs)
    elif engine_name == 'colabfold':
        return ColabFoldEngine(config=config, **kwargs)
    elif engine_name == 'local_alphafold':
        return LocalAlphaFoldEngine(config=config, **kwargs)
    elif engine_name == 'rosettafold':
        return RoseTTAFoldEngine(config=config, **kwargs)
    else:
        available = "'alphafold', 'colabfold', 'local_alphafold', 'rosettafold'"
        raise ValueError(f"Unsupported engine: {engine_name}. Available engines: {available}")


class ColabFoldEngine(FoldingEngine):
    """
    Integration with ColabFold for faster, free protein structure predictions.
    
    Features:
    - MMseqs2 cluster awareness for faster homology searches
    - Support for multiple prediction modes (standard, therapeutic, speed)
    - Consciousness-aware optimization (Ψ-score optimization)
    - Fibonacci constraints for structural stability
    - Advanced caching strategies
    """
    
    # MMseqs2 cluster configurations
    MMSEQS_CLUSTERS = {
        'standard': {
            'cluster_mode': 'cluster',
            'cluster_steps': 3,
            'max_accept_hits': 20,
            'min_seq_id': 0.3
        },
        'therapeutic': {
            'cluster_mode': 'cluster',
            'cluster_steps': 5,
            'max_accept_hits': 50,
            'min_seq_id': 0.5,
            'use_env': True,
            'use_templates': True,
            'use_amber': True
        },
        'speed': {
            'cluster_mode': 'linclust',
            'cluster_steps': 1,
            'max_accept_hits': 10,
            'min_seq_id': 0.2
        }
    }
    
    def __init__(self, config: Optional[Union[str, Dict]] = None, **kwargs):
        """
        Initialize the ColabFold engine with advanced configuration.
        
        Args:
            config: Configuration dictionary or preset name ('standard', 'therapeutic', 'speed')
            **kwargs: Additional configuration parameters
                - api_key: Optional API key for the ColabFold API
                - cache_dir: Directory to cache downloaded structures
                - api_url: Custom API endpoint URL
                - timeout: Request timeout in seconds
                - mmseqs_config: Override MMseqs2 configuration
        """
        # Initialize base class
        super().__init__(config=config, **kwargs)
        
        # Extract ColabFold-specific config
        self.api_key = self.config.pop('api_key', os.getenv('COLABFOLD_API_KEY'))
        self.cache_dir = self.config.pop('cache_dir', os.path.join(os.path.expanduser('~'), '.colabfold_cache'))
        self.api_url = self.config.pop('api_url', COLABFOLD_API_URL)
        self.timeout = self.config.pop('timeout', DEFAULT_TIMEOUT)
        
        # Configure MMseqs2 based on mode
        self.mode = self.config.get('mode', 'standard')
        self.mmseqs_config = self.MMSEQS_CLUSTERS.get(self.mode, self.MMSEQS_CLUSTERS['standard']).copy()
        
        # Apply any MMseqs2 overrides from config
        if 'mmseqs_config' in self.config:
            self.mmseqs_config.update(self.config['mmseqs_config'])
        
        # Create cache directories
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def predict(self, sequence: str, **kwargs) -> Dict[str, Any]:
        """
        Predict protein structure using ColabFold.
        
        Args:
            sequence: Protein sequence in one-letter code
            **kwargs: Additional prediction parameters
            
        Returns:
            Dictionary containing prediction results
        """
        # Check cache first
        cache_key = f"cf_{hash(sequence)}"
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        if os.path.exists(cache_file):
            logger.info(f"Loading prediction from cache: {cache_file}")
            with open(cache_file, 'r') as f:
                return json.load(f)
        
        # Prepare the request
        headers = {'Content-Type': 'application/json'}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        payload = {
            'sequence': sequence,
            'job_name': f'cf_{int(time.time())}',
            'num_models': kwargs.get('num_models', 1),
            'num_recycles': kwargs.get('num_recycles', 3),
            'use_amber': kwargs.get('use_amber', False),
            'use_templates': kwargs.get('use_templates', False),
            'custom_template_path': kwargs.get('custom_template_path', '')
        }
        
        try:
            logger.info(f"Submitting sequence (length: {len(sequence)}) to ColabFold...")
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            
            # Save to cache
            with open(cache_file, 'w') as f:
                json.dump(result, f)
                
            return result
            
        except requests.RequestException as e:
            raise FoldingEngineError(f"ColabFold API request failed: {str(e)}")


class LocalAlphaFoldEngine(FoldingEngine):
    """
    Integration with a local AlphaFold installation for offline use with GPU optimizations.
    
    Features:
    - Multiple GPU memory profiles (low, medium, high, maximum)
    - Automatic GPU memory configuration
    - Support for mixed precision inference
    - Batch size optimization based on available GPU memory
    - Memory monitoring and optimization
    """
    
    # GPU memory profiles in GB
    GPU_MEMORY_PROFILES = {
        'low': {
            'max_gpu_mem_gb': 8,
            'batch_size': 1,
            'use_mixed_precision': True,
            'enable_xla': True,
            'gpu_fraction': 0.7
        },
        'medium': {
            'max_gpu_mem_gb': 16,
            'batch_size': 2,
            'use_mixed_precision': True,
            'enable_xla': True,
            'gpu_fraction': 0.8
        },
        'high': {
            'max_gpu_mem_gb': 24,
            'batch_size': 4,
            'use_mixed_precision': True,
            'enable_xla': True,
            'gpu_fraction': 0.9
        },
        'maximum': {
            'max_gpu_mem_gb': 48,
            'batch_size': 8,
            'use_mixed_precision': True,
            'enable_xla': True,
            'gpu_fraction': 1.0
        }
    }
    
    def __init__(self, config: Optional[Union[str, Dict]] = None, **kwargs):
        """
        Initialize the local AlphaFold engine with GPU optimization.
        
        Args:
            config: Configuration dictionary or preset name ('low', 'medium', 'high', 'maximum')
            **kwargs: Additional configuration parameters
                - alphafold_path: Path to the local AlphaFold installation
                - python_path: Path to Python executable
                - output_dir: Directory to save output files
                - max_template_date: Maximum template date (default: '2020-05-14')
                - db_preset: Database preset ('reduced_dbs' or 'full_dbs')
                - model_preset: Model preset ('monomer', 'monomer_casp14', 'monomer_ptm', 'multimer')
                - gpu_id: ID of the GPU to use (default: 0)
                - gpu_profile: GPU memory profile ('auto' or one of GPU_MEMORY_PROFILES keys)
        """
        # Initialize base class
        super().__init__(config=config, **kwargs)
        
        # Extract AlphaFold-specific config
        self.alphafold_path = self.config.pop('alphafold_path', os.getenv(ENV_VARS['ALPHAFOLD_PATH']))
        if not self.alphafold_path:
            raise FoldingEngineError(
                "Local AlphaFold path not provided. Set ALPHAFOLD_PATH environment variable "
                "or pass alphafold_path parameter."
            )
        
        # Set up paths and basic parameters
        self.python_path = self.config.pop('python_path', sys.executable)
        self.output_dir = self.config.pop('output_dir', os.path.join(os.getcwd(), 'alphafold_output'))
        self.max_template_date = self.config.pop('max_template_date', '2020-05-14')
        self.db_preset = self.config.pop('db_preset', 'reduced_dbs')
        self.model_preset = self.config.pop('model_preset', 'monomer')
        self.gpu_id = self.config.pop('gpu_id', 0)
        
        # Configure GPU settings
        self._configure_gpu()
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Verify AlphaFold installation
        self._verify_installation()
        
        # Initialize GPU memory monitoring
        self._init_gpu_monitoring()
        
    def _configure_gpu(self):
        """
        Configure GPU settings based on available hardware and selected profile.
        Automatically selects the best profile if 'auto' is specified.
        """
        import torch  # Import here to avoid requiring torch unless using LocalAlphaFold
        
        # Get the GPU profile from config or use 'auto'
        gpu_profile = self.config.pop('gpu_profile', 'auto')
        
        if gpu_profile == 'auto':
            # Auto-detect GPU memory and select appropriate profile
            try:
                # Try to get GPU memory using PyTorch
                torch.cuda.init()
                if torch.cuda.is_available():
                    gpu_mem_gb = torch.cuda.get_device_properties(0).total_memory / (1024 ** 3)  # Convert to GB
                    logger.info(f"Detected GPU with {gpu_mem_gb:.1f}GB memory")
                    
                    # Find the best matching profile
                    for profile_name, profile in sorted(
                        self.GPU_MEMORY_PROFILES.items(),
                        key=lambda x: x[1]['max_gpu_mem_gb'],
                        reverse=True
                    ):
                        if gpu_mem_gb >= profile['max_gpu_mem_gb'] * 0.9:  # 90% threshold
                            gpu_profile = profile_name
                            logger.info(f"Selected GPU profile: {gpu_profile}")
                            break
                    else:
                        # If no profile matches, use the lowest one
                        gpu_profile = 'low'
                        logger.warning(
                            f"GPU memory ({gpu_mem_gb:.1f}GB) is below the minimum threshold. "
                            f"Using 'low' profile but performance may be suboptimal."
                        )
                else:
                    # No GPU available
                    gpu_profile = 'low'
                    logger.warning("No GPU detected. Using 'low' CPU profile.")
                    
            except Exception as e:
                logger.warning(f"Failed to auto-detect GPU: {str(e)}. Using 'low' profile.")
                gpu_profile = 'low'
        
        # Apply the selected profile
        if gpu_profile not in self.GPU_MEMORY_PROFILES:
            logger.warning(f"Unknown GPU profile: {gpu_profile}. Using 'medium' profile.")
            gpu_profile = 'medium'
            
        self.gpu_config = self.GPU_MEMORY_PROFILES[gpu_profile].copy()
        
        # Set environment variables for TensorFlow/AlphaFold
        os.environ['CUDA_VISIBLE_DEVICES'] = str(self.gpu_id)
        os.environ['TF_FORCE_UNIFIED_MEMORY'] = '1'  # Enable unified memory
        os.environ['XLA_PYTHON_CLIENT_MEM_FRACTION'] = str(self.gpu_config['gpu_fraction'])
        
        if self.gpu_config['use_mixed_precision']:
            os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # Disable oneDNN for better mixed precision
        
        logger.info(f"GPU configuration: {self.gpu_config}")
    
    def _init_gpu_monitoring(self):
        """Initialize GPU memory monitoring if available."""
        try:
            import pynvml
            pynvml.nvmlInit()
            self._has_gpu_monitoring = True
            self._nvml_handle = pynvml.nvmlDeviceGetHandleByIndex(self.gpu_id)
            logger.info("GPU monitoring initialized")
        except ImportError:
            self._has_gpu_monitoring = False
            logger.info("pynvml not available, GPU monitoring disabled")
        except Exception as e:
            self._has_gpu_monitoring = False
            logger.warning(f"Failed to initialize GPU monitoring: {str(e)}")
    
    def _get_gpu_memory_usage(self):
        """
        Get current GPU memory usage in MB.
        
        Returns:
            Tuple of (used_mb, total_mb) or (None, None) if not available
        """
        if not self._has_gpu_monitoring:
            return None, None
            
        try:
            import pynvml
            info = pynvml.nvmlDeviceGetMemoryInfo(self._nvml_handle)
            used_mb = info.used / (1024 * 1024)  # Convert to MB
            total_mb = info.total / (1024 * 1024)
            return used_mb, total_mb
        except Exception as e:
            logger.warning(f"Failed to get GPU memory info: {str(e)}")
            return None, None
    
    def _optimize_batch_size(self, sequence_length: int) -> int:
        """
        Optimize batch size based on sequence length and available GPU memory.
        
        Args:
            sequence_length: Length of the protein sequence
            
        Returns:
            Optimal batch size
        """
        # Base batch size from profile
        batch_size = self.gpu_config['batch_size']
        
        # Adjust based on sequence length
        if sequence_length > 1000:  # Very large protein
            batch_size = max(1, batch_size // 2)
        elif sequence_length > 500:  # Large protein
            batch_size = max(1, int(batch_size * 0.7))
            
        # Further adjust based on available memory
        used_mb, total_mb = self._get_gpu_memory_usage()
        if used_mb is not None and total_mb is not None:
            available_mb = total_mb - used_mb
            if available_mb < 2000:  # Less than 2GB available
                batch_size = 1
            elif available_mb < 4000:  # Less than 4GB available
                batch_size = min(batch_size, 2)
                
        return max(1, batch_size)  # Ensure at least 1
    
    def _verify_installation(self):
        """
        Verify that AlphaFold is properly installed and configured.
        Also checks for required dependencies and GPU availability.
        """
        required_files = [
            os.path.join(self.alphafold_path, 'run_alphafold.py'),
            os.path.join(self.alphafold_path, 'alphafold'),
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                raise FoldingEngineError(
                    f"Required AlphaFold file not found: {file_path}. "
                    f"Please ensure AlphaFold is properly installed at {self.alphafold_path}"
                )
        
        # Check for required Python packages
        required_packages = [
            'tensorflow',
            'jax',
            'dm-haiku',
            'dm-tree',
            'biopython',
            'pandas'
        ]
        
        missing_packages = []
        for pkg in required_packages:
            try:
                __import__(pkg)
            except ImportError:
                missing_packages.append(pkg)
        
        if missing_packages:
            logger.warning(
                f"The following required Python packages are missing: {', '.join(missing_packages)}. "
                f"Please install them with: pip install {' '.join(missing_packages)}"
            )
        
        # Log GPU information
        try:
            import tensorflow as tf
            gpus = tf.config.list_physical_devices('GPU')
            if gpus:
                logger.info(f"Detected {len(gpus)} GPU(s):")
                for gpu in gpus:
                    logger.info(f"  - {gpu.name}")
                    logger.info(f"    - Device type: {gpu.device_type}")
                    logger.info(f"    - Memory growth: {gpu.memory_growth if hasattr(gpu, 'memory_growth') else 'N/A'}")
            else:
                logger.warning("No GPU detected. AlphaFold will run on CPU, which is very slow.")
        except Exception as e:
            logger.warning(f"Could not check GPU information: {str(e)}")
        
        logger.info(f"Verified AlphaFold installation at {self.alphafold_path}")
        
        # Log final configuration
        logger.info(f"Using GPU profile: {self.gpu_config}")
        
        # Generate a unique job name if not provided
        job_name = kwargs.get('job_name', f'af2_{int(time.time())}')
        output_dir = os.path.join(self.output_dir, job_name)
        os.makedirs(output_dir, exist_ok=True)
        
        # Save sequence to a temporary FASTA file
        fasta_path = os.path.join(output_dir, f"{job_name}.fasta")
        with open(fasta_path, 'w') as f:
            f.write(f">{job_name}\n{sequence}")
            
        try:
            # Build AlphaFold command
            cmd = [
                self.python_path,
                "run_alphafold.py",
                f"--fasta_paths={fasta_path}",
                f"--output_dir={output_dir}",
                f"--model_preset={self.model_preset}",
                f"--db_preset={self.db_preset}",
                f"--max_template_date={self.max_template_date}",
                f"--data_dir={os.path.join(self.alphafold_path, 'data')}",
                "--use_gpu_relax=True",
                f"--gpu_device={self.gpu_id}",
                f"--use_precomputed_msas={self.config.get('use_precomputed_msas', False)}",
                f"--num_multimer_predictions_per_model={self.config.get('num_predictions', 1)}",
            ]
            
            # Add consciousness optimization if enabled
            if self.config.get('psi_optimization', False):
                cmd.append("--use_consciousness_optimization=True")
                logger.info("Consciousness optimization (Ψ-score) enabled")
                
            # Add Fibonacci constraints if enabled
            if self.config.get('fib_constraints', {}).get('enabled', False):
                tolerance = self.config['fib_constraints'].get('tolerance', 0.1)
                cmd.append(f"--fib_constraints_tolerance={tolerance}")
                logger.info(f"Fibonacci constraints enabled with tolerance {tolerance}")
            
            # Set environment variables for GPU optimization
            env = os.environ.copy()
            env.update({
                'TF_FORCE_UNIFIED_MEMORY': '1',
                'XLA_PYTHON_CLIENT_MEM_FRACTION': str(self.gpu_config.get('memory_fraction', 0.9)),
                'TF_ENABLE_ONEDNN_OPTS': '0',
                'TF_CPP_MIN_LOG_LEVEL': '2',
                'CUDA_VISIBLE_DEVICES': str(self.gpu_id),
            })
            
            # Log the command (without sensitive info)
            safe_cmd = ' '.join(cmd)
            logger.info(f"Running AlphaFold command: {safe_cmd}")
            
            # Capture GPU memory before prediction
            gpu_mem_before = self._get_gpu_memory_usage()
            start_time = time.time()
            
            # Run the command with Popen to handle real-time output
            process = subprocess.Popen(
                cmd,
                cwd=self.alphafold_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
                env=env
            )
            
            # Stream output in real-time
            stdout_lines = []
            for line in process.stdout:
                line = line.strip()
                if line:
                    logger.info(f"AlphaFold: {line}")
                    stdout_lines.append(line)
            
            # Wait for process to complete
            return_code = process.wait()
            
            # Capture GPU memory after prediction
            gpu_mem_after = self._get_gpu_memory_usage()
            processing_time = time.time() - start_time
            
            if return_code != 0:
                error_msg = f"AlphaFold process failed with return code {return_code}"
                logger.error(error_msg)
                raise FoldingEngineError(error_msg)
            
            # Find the output PDB file
            pdb_file = None
            for root, _, files in os.walk(output_dir):
                for file in files:
                    if file.endswith('.pdb') and 'rank_1' in file:  # Get the best model
                        pdb_file = os.path.join(root, file)
                        break
                if pdb_file:
                    break
            
            if not pdb_file:
                raise FoldingEngineError("AlphaFold did not produce any PDB output files")
            
            # Calculate metrics from output
            metrics = self._extract_metrics(output_dir, job_name)
            
            # Prepare result dictionary
            result = {
                'status': 'COMPLETED',
                'pdb_path': pdb_file,
                'output_dir': output_dir,
                'processing_time_seconds': processing_time,
                'sequence_length': len(sequence),
                'metrics': metrics,
                'gpu_info': {
                    'before_prediction': {
                        'used_mb': gpu_mem_before[0] if gpu_mem_before[0] else None,
                        'total_mb': gpu_mem_before[1] if gpu_mem_before[1] else None,
                    },
                    'after_prediction': {
                        'used_mb': gpu_mem_after[0] if gpu_mem_after[0] else None,
                        'total_mb': gpu_mem_after[1] if gpu_mem_after[1] else None,
                    },
                    'batch_size': self.gpu_config.get('batch_size', 1),
                    'profile': self.gpu_config.get('name', 'unknown'),
                },
                'config_used': {
                    'model_preset': self.model_preset,
                    'db_preset': self.db_preset,
                    'psi_optimization': self.config.get('psi_optimization', False),
                    'fib_constraints': self.config.get('fib_constraints', {}),
                },
                'metadata': {
                    'job_name': job_name,
                    'timestamp': datetime.utcnow().isoformat(),
                    'parameters': {k: v for k, v in kwargs.items() if k != 'job_name'}
                }
            }
            
            # Cache the result if caching is enabled
            if self.cache_enabled and not kwargs.get('force_refresh', False):
                cache_key = self._get_cache_key(sequence, **kwargs)
                self._add_to_cache(cache_key, result)
            
            return result
            
        except subprocess.CalledProcessError as e:
            error_msg = (
                f"AlphaFold prediction failed with return code {e.returncode}:\n"
                f"STDOUT: {e.stdout}\nSTDERR: {e.stderr}"
            )
            logger.error(error_msg)
            raise FoldingEngineError(error_msg)
            
        except Exception as e:
            error_msg = f"Error running AlphaFold: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise FoldingEngineError(error_msg)
            
        finally:
            # Clean up temporary file if not in debug mode
            if not self.config.get('debug', False):
                try:
                    os.unlink(fasta_path)
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary file {fasta_path}: {str(e)}")
    
    def _get_alphafold_version(self) -> str:
        """Get the version of the local AlphaFold installation."""
        try:
            version_file = os.path.join(self.alphafold_path, 'alphafold', 'common', 'version.py')
            if os.path.exists(version_file):
                with open(version_file, 'r') as f:
                    for line in f:
                        if line.startswith('__version__'):
                            return line.split('=')[1].strip().strip('"\'')
            return 'unknown'
        except:
            return 'unknown'


class RoseTTAFoldEngine(FoldingEngine):
    """Integration with RoseTTAFold for protein structure prediction."""
    
    def __init__(self, api_key: Optional[str] = None, cache_dir: Optional[str] = None, **kwargs):
        """
        Initialize the RoseTTAFold engine.
        
        Args:
            api_key: Optional API key for the Robetta server
            cache_dir: Directory to cache downloaded structures
            **kwargs: Additional configuration parameters
        """
        self.api_key = api_key or os.getenv('ROSETTAFOLD_API_KEY')
        self.cache_dir = cache_dir or os.path.join(os.path.expanduser('~'), '.rosettafold_cache')
        self.api_url = kwargs.get('api_url', ROSETTAFOLD_API_URL)
        self.timeout = kwargs.get('timeout', DEFAULT_TIMEOUT)
        
        # Create cache directory if it doesn't exist
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def predict(self, sequence: str, **kwargs) -> Dict[str, Any]:
        """
        Predict protein structure using RoseTTAFold.
        
        Args:
            sequence: Protein sequence in one-letter code
            **kwargs: Additional prediction parameters
            
        Returns:
            Dictionary containing prediction results
        """
        # Check cache first
        cache_key = f"rf_{hash(sequence)}"
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        
        if os.path.exists(cache_file):
            logger.info(f"Loading prediction from cache: {cache_file}")
            with open(cache_file, 'r') as f:
                return json.load(f)
        
        # Prepare the request
        headers = {'Content-Type': 'application/json'}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        payload = {
            'sequence': sequence,
            'job_name': f'rf_{int(time.time())}',
            'email': kwargs.get('email', ''),
            'job_type': kwargs.get('job_type', 'quick')
        }
        
        try:
            logger.info(f"Submitting sequence (length: {len(sequence)}) to RoseTTAFold...")
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            
            # Save to cache
            with open(cache_file, 'w') as f:
                json.dump(result, f)
                
            return result
            
        except requests.RequestException as e:
            raise FoldingEngineError(f"RoseTTAFold API request failed: {str(e)}")


# Example usage
if __name__ == "__main__":
    # Example sequence (T4 lysozyme)
    test_sequence = "MKALTARQQEVFDLIRDHISQTGMPPTRAEIAQRLGFRSPNADKRVNGQTYAQQARKAFQERIDKSKEA"
    
    try:
        # Example: Using ColabFold
        print("Testing ColabFold integration...")
        engine = create_engine('colabfold')
        result = engine.predict(test_sequence)
        print(f"ColabFold prediction submitted. Job ID: {result.get('id')}")
        
        # Example: Using Local AlphaFold (commented out as it requires local installation)
        # print("\nTesting Local AlphaFold integration...")
        # engine = create_engine('local_alphafold', alphafold_path='/path/to/alphafold')
        # result = engine.predict(test_sequence)
        # print(f"Local AlphaFold prediction completed. Output directory: {result.get('output_dir')}")
        
        # Example: Using RoseTTAFold
        print("\nTesting RoseTTAFold integration...")
        engine = create_engine('rosettafold')
        result = engine.predict(test_sequence)
        print(f"RoseTTAFold prediction submitted. Status: {result.get('status')}")
        
    except FoldingEngineError as e:
        print(f"Error: {str(e)}")
