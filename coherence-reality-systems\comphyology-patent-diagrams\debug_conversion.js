const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const inputFile = 'FIG1_uuft_core_architecture.mmd';
const outputFile = 'DEBUG_OUTPUT.svg';

console.log('=== Mermaid CLI Debug Test ===');
console.log(`Working directory: ${process.cwd()}`);
console.log(`Input file: ${inputFile}`);
console.log(`Output file: ${outputFile}`);

// Verify input file exists
if (!fs.existsSync(inputFile)) {
    console.error(`❌ Error: Input file ${inputFile} not found`);
    process.exit(1);
}
console.log('✅ Input file exists');

// Check Mermaid CLI version
console.log('\n=== Checking Mermaid CLI ===');
exec('npx @mermaid-js/mermaid-cli --version', (error, stdout, stderr) => {
    console.log('Mermaid CLI version:');
    console.log(stdout || stderr || 'No version output');
    
    if (error) {
        console.error('❌ Error getting Mermaid CLI version:', error.message);
        return;
    }
    
    // Try direct conversion
    console.log('\n=== Testing Direct Conversion ===');
    const cmd = `npx @mermaid-js/mermaid-cli -i "${inputFile}" -o "${outputFile}"`;
    console.log(`Running: ${cmd}`);
    
    const process = exec(cmd);
    
    process.stdout.on('data', (data) => {
        console.log(`stdout: ${data}`);
    });
    
    process.stderr.on('data', (data) => {
        console.error(`stderr: ${data}`);
    });
    
    process.on('close', (code) => {
        console.log(`\nProcess exited with code ${code}`);
        
        // Check if output file was created
        if (fs.existsSync(outputFile)) {
            console.log(`✅ Output file created: ${path.resolve(outputFile)}`);
            console.log(`File size: ${fs.statSync(outputFile).size} bytes`);
        } else {
            console.log('❌ Output file was not created');
            
            // Check directory contents
            console.log('\n=== Directory Contents ===');
            fs.readdirSync('.').forEach(file => {
                console.log(`- ${file} (${fs.statSync(file).size} bytes)`);
            });
        }
    });
});
