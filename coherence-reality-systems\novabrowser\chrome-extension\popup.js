// NovaBrowser Chrome Extension - Popup Script

class NovaBrowserPopup {
    constructor() {
        this.currentData = null;
        this.init();
    }

    async init() {
        console.log('🌐 NovaBrowser popup initialized');
        
        // Set up event listeners
        document.getElementById('refresh-btn').addEventListener('click', () => this.refreshAnalysis());
        document.getElementById('auto-fix-btn').addEventListener('click', () => this.autoFixViolations());
        document.getElementById('toggle-overlay-btn').addEventListener('click', () => this.toggleOverlay());
        
        // Listen for messages from content script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'ANALYSIS_COMPLETE') {
                this.displayResults(message.data);
            }
        });
        
        // Get current tab and request analysis
        this.requestAnalysis();
    }

    async requestAnalysis() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Inject content script if not already present
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content-script.js']
            });
            
            // Request analysis from content script
            chrome.tabs.sendMessage(tab.id, { type: 'REQUEST_ANALYSIS' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.log('Content script not ready, will wait for analysis...');
                }
            });
            
        } catch (error) {
            console.error('Failed to request analysis:', error);
            this.showError('Failed to analyze page');
        }
    }

    displayResults(data) {
        console.log('📊 Displaying results:', data);
        this.currentData = data;
        
        // Hide loading, show content
        document.getElementById('loading').style.display = 'none';
        document.getElementById('content').style.display = 'block';
        
        // Update coherence
        document.getElementById('coherence-value').textContent = `${data.coherence.overall}%`;
        this.updateIndicator('coherence-indicator', data.coherence.overall, [82, 60]);
        document.getElementById('coherence-status').textContent = 
            data.coherence.overall >= 82 ? '⚡ Ψ-Snap ACTIVE' : '⚠️ Below Ψ-Snap threshold';
        
        // Update accessibility
        document.getElementById('accessibility-value').textContent = `${data.accessibility.score}%`;
        this.updateIndicator('accessibility-indicator', data.accessibility.score, [90, 70]);
        document.getElementById('accessibility-status').textContent = 
            `${data.accessibility.violations.length} violations found`;
        
        // Update security
        document.getElementById('security-value').textContent = data.security.level;
        const securityScore = data.security.level === 'LOW' ? 90 : data.security.level === 'MEDIUM' ? 60 : 30;
        this.updateIndicator('security-indicator', securityScore, [80, 50]);
        document.getElementById('security-status').textContent = 
            `${data.security.threats} threats detected`;
        
        // Show violations if any
        if (data.accessibility.violations.length > 0) {
            this.showViolations(data.accessibility.violations);
            document.getElementById('auto-fix-btn').style.display = 'block';
        } else {
            document.getElementById('violations-section').style.display = 'none';
            document.getElementById('auto-fix-btn').style.display = 'none';
        }
    }

    updateIndicator(elementId, value, thresholds) {
        const indicator = document.getElementById(elementId);
        if (value >= thresholds[0]) {
            indicator.className = 'status-indicator status-high';
        } else if (value >= thresholds[1]) {
            indicator.className = 'status-indicator status-medium';
        } else {
            indicator.className = 'status-indicator status-low';
        }
    }

    showViolations(violations) {
        const section = document.getElementById('violations-section');
        const list = document.getElementById('violations-list');
        
        list.innerHTML = '';
        violations.forEach(violation => {
            const item = document.createElement('div');
            item.className = 'violation-item';
            item.textContent = violation;
            list.appendChild(item);
        });
        
        section.style.display = 'block';
    }

    async refreshAnalysis() {
        console.log('🔄 Refreshing analysis...');
        
        // Show loading
        document.getElementById('loading').style.display = 'block';
        document.getElementById('content').style.display = 'none';
        
        // Request new analysis
        this.requestAnalysis();
    }

    async autoFixViolations() {
        console.log('🔧 Requesting auto-fix...');
        
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            chrome.tabs.sendMessage(tab.id, { type: 'AUTO_FIX' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('Auto-fix failed:', chrome.runtime.lastError);
                } else {
                    console.log('✅ Auto-fix completed');
                    // Refresh analysis after fix
                    setTimeout(() => this.refreshAnalysis(), 1000);
                }
            });
            
        } catch (error) {
            console.error('Auto-fix error:', error);
        }
    }

    async toggleOverlay() {
        console.log('👁️ Toggling overlay...');
        
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            chrome.tabs.sendMessage(tab.id, { type: 'TOGGLE_OVERLAY' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('Toggle overlay failed:', chrome.runtime.lastError);
                }
            });
            
        } catch (error) {
            console.error('Toggle overlay error:', error);
        }
    }

    showError(message) {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('content').innerHTML = `
            <div style="text-align: center; padding: 40px 20px; color: #ff4757;">
                <div style="font-size: 48px; margin-bottom: 15px;">⚠️</div>
                <div style="font-weight: bold; margin-bottom: 10px;">Analysis Failed</div>
                <div style="font-size: 12px; opacity: 0.8;">${message}</div>
                <button class="action-btn" onclick="location.reload()" style="margin-top: 20px;">
                    🔄 Retry
                </button>
            </div>
        `;
        document.getElementById('content').style.display = 'block';
    }
}

// Initialize popup
const popup = new NovaBrowserPopup();
