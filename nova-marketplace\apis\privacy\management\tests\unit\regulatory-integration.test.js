/**
 * Regulatory Integration Tests
 *
 * This file contains unit tests for the regulatory integration module.
 */

// Use rewire to access private functions
const rewire = require('rewire');
const regulatoryIntegrationRewire = rewire('../../regulatory-integration');
const regulatoryIntegration = require('../../regulatory-integration');
const { logger } = require('../../utils/logger');
const axios = require('axios');
const cache = require('../../cache');
const config = require('../../config/regulatory-api');

// Mock dependencies
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

jest.mock('axios');

jest.mock('../../cache', () => ({
  get: jest.fn(),
  set: jest.fn(),
  delete: jest.fn()
}));

jest.mock('../../config/regulatory-api', () => ({
  baseUrl: 'https://api.novagrc.com/regulatory',
  apiVersion: 'v1',
  apiKey: 'test-api-key',
  timeout: 5000,
  cacheTtl: {
    regulations: 86400,
    requirements: 86400,
    jurisdictions: 86400,
    updates: 3600
  },
  retry: {
    maxRetries: 3,
    retryDelay: 1000
  }
}));

describe('Regulatory Integration', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('Utility Functions', () => {
    // Get private functions
    const buildApiUrl = regulatoryIntegrationRewire.__get__('buildApiUrl');
    const getRequestOptions = regulatoryIntegrationRewire.__get__('getRequestOptions');

    it('should build API URL correctly', () => {
      // Test with various endpoints
      expect(buildApiUrl('/test')).toBe('https://api.novagrc.com/regulatory/v1/test');
      expect(buildApiUrl('/regulations/gdpr')).toBe('https://api.novagrc.com/regulatory/v1/regulations/gdpr');
      expect(buildApiUrl('/jurisdictions/eu')).toBe('https://api.novagrc.com/regulatory/v1/jurisdictions/eu');

      // Test with endpoint that already has a leading slash
      expect(buildApiUrl('test')).toBe('https://api.novagrc.com/regulatory/v1test');
    });

    it('should get request options with correct headers', () => {
      const options = getRequestOptions();

      // Verify headers
      expect(options.headers).toBeDefined();
      // The Authorization header will contain 'Bearer undefined' since the API key is undefined in the test environment
      expect(options.headers.Authorization).toMatch(/^Bearer /);
      expect(options.headers['Content-Type']).toBe('application/json');
      expect(options.headers.Accept).toBe('application/json');

      // Verify timeout
      expect(options.timeout).toBe(5000);
    });
  });

  describe('getRegulationDetails', () => {
    it('should retrieve regulation details from cache if available', async () => {
      // Mock data
      const regulationId = 'gdpr';
      const mockRegulation = {
        id: 'gdpr',
        name: 'General Data Protection Regulation',
        jurisdiction: 'EU',
        effectiveDate: '2018-05-25',
        requirements: [
          { id: 'gdpr-1', title: 'Lawful Basis for Processing' },
          { id: 'gdpr-2', title: 'Data Subject Rights' }
        ]
      };

      // Mock cache hit
      cache.get.mockReturnValueOnce(mockRegulation);

      // Call the function
      const result = await regulatoryIntegration.getRegulationDetails(regulationId);

      // Verify the result
      expect(result).toEqual(mockRegulation);

      // Verify cache was checked
      expect(cache.get).toHaveBeenCalledWith(`regulation:${regulationId}`);

      // Verify API was not called
      expect(axios.get).not.toHaveBeenCalled();
    });

    it('should retrieve regulation details from API if not in cache', async () => {
      // Mock data
      const regulationId = 'gdpr';
      const mockRegulation = {
        id: 'gdpr',
        name: 'General Data Protection Regulation',
        jurisdiction: 'EU',
        effectiveDate: '2018-05-25',
        requirements: [
          { id: 'gdpr-1', title: 'Lawful Basis for Processing' },
          { id: 'gdpr-2', title: 'Data Subject Rights' }
        ]
      };

      // Mock cache miss
      cache.get.mockReturnValueOnce(null);

      // Mock API response
      axios.get.mockResolvedValueOnce({ data: mockRegulation });

      // Call the function
      const result = await regulatoryIntegration.getRegulationDetails(regulationId);

      // Verify the result
      expect(result).toEqual(mockRegulation);

      // Verify cache was checked
      expect(cache.get).toHaveBeenCalledWith(`regulation:${regulationId}`);

      // Verify API was called
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/regulations/${regulationId}`),
        expect.any(Object)
      );

      // Verify result was cached
      expect(cache.set).toHaveBeenCalledWith(
        `regulation:${regulationId}`,
        mockRegulation,
        expect.any(Number)
      );
    });

    it('should handle API errors', async () => {
      // Mock data
      const regulationId = 'gdpr';

      // Mock cache miss
      cache.get.mockReturnValueOnce(null);

      // Mock API error
      const mockError = new Error('API error');
      axios.get.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(regulatoryIntegration.getRegulationDetails(regulationId)).rejects.toThrow(
        'Failed to retrieve regulation details'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error retrieving regulation details'),
        mockError
      );
    });
  });

  describe('getComplianceRequirements', () => {
    it('should retrieve compliance requirements from cache if available', async () => {
      // Mock data
      const regulationId = 'gdpr';
      const mockRequirements = [
        { id: 'gdpr-1', title: 'Lawful Basis for Processing', description: 'Must have valid lawful basis' },
        { id: 'gdpr-2', title: 'Data Subject Rights', description: 'Must honor data subject rights' }
      ];

      // Mock cache hit
      cache.get.mockReturnValueOnce(mockRequirements);

      // Call the function
      const result = await regulatoryIntegration.getComplianceRequirements(regulationId);

      // Verify the result
      expect(result).toEqual(mockRequirements);

      // Verify cache was checked
      expect(cache.get).toHaveBeenCalledWith(`requirements:${regulationId}`);

      // Verify API was not called
      expect(axios.get).not.toHaveBeenCalled();
    });

    it('should retrieve compliance requirements from API if not in cache', async () => {
      // Mock data
      const regulationId = 'gdpr';
      const mockRequirements = [
        { id: 'gdpr-1', title: 'Lawful Basis for Processing', description: 'Must have valid lawful basis' },
        { id: 'gdpr-2', title: 'Data Subject Rights', description: 'Must honor data subject rights' }
      ];

      // Mock cache miss
      cache.get.mockReturnValueOnce(null);

      // Mock API response
      axios.get.mockResolvedValueOnce({ data: mockRequirements });

      // Call the function
      const result = await regulatoryIntegration.getComplianceRequirements(regulationId);

      // Verify the result
      expect(result).toEqual(mockRequirements);

      // Verify cache was checked
      expect(cache.get).toHaveBeenCalledWith(`requirements:${regulationId}`);

      // Verify API was called
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/regulations/${regulationId}/requirements`),
        expect.any(Object)
      );

      // Verify result was cached
      expect(cache.set).toHaveBeenCalledWith(
        `requirements:${regulationId}`,
        mockRequirements,
        expect.any(Number)
      );
    });

    it('should handle API errors', async () => {
      // Mock data
      const regulationId = 'gdpr';

      // Mock cache miss
      cache.get.mockReturnValueOnce(null);

      // Mock API error
      const mockError = new Error('API error');
      axios.get.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(regulatoryIntegration.getComplianceRequirements(regulationId)).rejects.toThrow(
        'Failed to retrieve compliance requirements'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error retrieving compliance requirements'),
        mockError
      );
    });
  });

  describe('getJurisdictionInfo', () => {
    it('should retrieve jurisdiction information from cache if available', async () => {
      // Mock data
      const jurisdictionCode = 'eu';
      const mockJurisdiction = {
        code: 'eu',
        name: 'European Union',
        regulations: ['gdpr', 'eprivacy'],
        dataProtectionAuthority: 'European Data Protection Board'
      };

      // Mock cache hit
      cache.get.mockReturnValueOnce(mockJurisdiction);

      // Call the function
      const result = await regulatoryIntegration.getJurisdictionInfo(jurisdictionCode);

      // Verify the result
      expect(result).toEqual(mockJurisdiction);

      // Verify cache was checked
      expect(cache.get).toHaveBeenCalledWith(`jurisdiction:${jurisdictionCode}`);

      // Verify API was not called
      expect(axios.get).not.toHaveBeenCalled();
    });

    it('should retrieve jurisdiction information from API if not in cache', async () => {
      // Mock data
      const jurisdictionCode = 'eu';
      const mockJurisdiction = {
        code: 'eu',
        name: 'European Union',
        regulations: ['gdpr', 'eprivacy'],
        dataProtectionAuthority: 'European Data Protection Board'
      };

      // Mock cache miss
      cache.get.mockReturnValueOnce(null);

      // Mock API response
      axios.get.mockResolvedValueOnce({ data: mockJurisdiction });

      // Call the function
      const result = await regulatoryIntegration.getJurisdictionInfo(jurisdictionCode);

      // Verify the result
      expect(result).toEqual(mockJurisdiction);

      // Verify cache was checked
      expect(cache.get).toHaveBeenCalledWith(`jurisdiction:${jurisdictionCode}`);

      // Verify API was called
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/jurisdictions/${jurisdictionCode}`),
        expect.any(Object)
      );

      // Verify result was cached
      expect(cache.set).toHaveBeenCalledWith(
        `jurisdiction:${jurisdictionCode}`,
        mockJurisdiction,
        expect.any(Number)
      );
    });

    it('should handle API errors', async () => {
      // Mock data
      const jurisdictionCode = 'eu';

      // Mock cache miss
      cache.get.mockReturnValueOnce(null);

      // Mock API error
      const mockError = new Error('API error');
      axios.get.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(regulatoryIntegration.getJurisdictionInfo(jurisdictionCode)).rejects.toThrow(
        'Failed to retrieve jurisdiction information'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error retrieving jurisdiction information'),
        mockError
      );
    });
  });

  describe('getRegulatoryUpdates', () => {
    it('should retrieve regulatory updates from cache if available', async () => {
      // Mock data
      const mockUpdates = [
        {
          id: 'update-1',
          regulation: 'gdpr',
          title: 'New EDPB Guidelines',
          description: 'New guidelines on consent',
          publicationDate: '2023-01-01',
          effectiveDate: '2023-03-01'
        },
        {
          id: 'update-2',
          regulation: 'ccpa',
          title: 'CPRA Amendments',
          description: 'New amendments to CCPA',
          publicationDate: '2023-01-15',
          effectiveDate: '2023-07-01'
        }
      ];

      // Mock cache hit
      cache.get.mockReturnValueOnce(mockUpdates);

      // Call the function
      const result = await regulatoryIntegration.getRegulatoryUpdates();

      // Verify the result
      expect(result).toEqual(mockUpdates);

      // Verify cache was checked
      expect(cache.get).toHaveBeenCalledWith('regulatory-updates');

      // Verify API was not called
      expect(axios.get).not.toHaveBeenCalled();
    });

    it('should retrieve regulatory updates from API if not in cache', async () => {
      // Mock data
      const mockUpdates = [
        {
          id: 'update-1',
          regulation: 'gdpr',
          title: 'New EDPB Guidelines',
          description: 'New guidelines on consent',
          publicationDate: '2023-01-01',
          effectiveDate: '2023-03-01'
        },
        {
          id: 'update-2',
          regulation: 'ccpa',
          title: 'CPRA Amendments',
          description: 'New amendments to CCPA',
          publicationDate: '2023-01-15',
          effectiveDate: '2023-07-01'
        }
      ];

      // Mock cache miss
      cache.get.mockReturnValueOnce(null);

      // Mock API response
      axios.get.mockResolvedValueOnce({ data: mockUpdates });

      // Call the function
      const result = await regulatoryIntegration.getRegulatoryUpdates();

      // Verify the result
      expect(result).toEqual(mockUpdates);

      // Verify cache was checked
      expect(cache.get).toHaveBeenCalledWith('regulatory-updates');

      // Verify API was called
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/regulatory-updates'),
        expect.any(Object)
      );

      // Verify result was cached
      expect(cache.set).toHaveBeenCalledWith(
        'regulatory-updates',
        mockUpdates,
        expect.any(Number)
      );
    });

    it('should handle API errors', async () => {
      // Mock cache miss
      cache.get.mockReturnValueOnce(null);

      // Mock API error
      const mockError = new Error('API error');
      axios.get.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(regulatoryIntegration.getRegulatoryUpdates()).rejects.toThrow(
        'Failed to retrieve regulatory updates'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error retrieving regulatory updates'),
        mockError
      );
    });
  });

  describe('getComplianceStatus', () => {
    it('should retrieve compliance status for a regulation', async () => {
      // Mock data
      const regulationId = 'gdpr';
      const organizationId = 'org-123';
      const mockStatus = {
        regulationId: 'gdpr',
        organizationId: 'org-123',
        status: 'partial',
        completedRequirements: ['gdpr-1', 'gdpr-3'],
        pendingRequirements: ['gdpr-2', 'gdpr-4'],
        lastAssessmentDate: '2023-01-01',
        overallScore: 75
      };

      // Mock API response
      axios.get.mockResolvedValueOnce({ data: mockStatus });

      // Call the function
      const result = await regulatoryIntegration.getComplianceStatus(regulationId, organizationId);

      // Verify the result
      expect(result).toEqual(mockStatus);

      // Verify API was called
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/compliance-status/${regulationId}`),
        expect.objectContaining({
          params: expect.objectContaining({ organizationId })
        })
      );
    });

    it('should handle API errors', async () => {
      // Mock data
      const regulationId = 'gdpr';
      const organizationId = 'org-123';

      // Mock API error
      const mockError = new Error('API error');
      axios.get.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(regulatoryIntegration.getComplianceStatus(regulationId, organizationId)).rejects.toThrow(
        'Failed to retrieve compliance status'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error retrieving compliance status'),
        mockError
      );
    });
  });

  describe('submitComplianceReport', () => {
    it('should submit a compliance report', async () => {
      // Mock data
      const regulationId = 'gdpr';
      const reportData = {
        organizationId: 'org-123',
        reportingPeriod: '2023-Q1',
        completedRequirements: ['gdpr-1', 'gdpr-3'],
        pendingRequirements: ['gdpr-2', 'gdpr-4'],
        notes: 'Progress on implementation'
      };
      const mockResponse = {
        id: 'report-123',
        status: 'submitted',
        submissionDate: '2023-04-01',
        ...reportData
      };

      // Mock API response
      axios.post.mockResolvedValueOnce({ data: mockResponse });

      // Call the function
      const result = await regulatoryIntegration.submitComplianceReport(regulationId, reportData);

      // Verify the result
      expect(result).toEqual(mockResponse);

      // Verify API was called
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining(`/regulations/${regulationId}/compliance-reports`),
        reportData,
        expect.any(Object)
      );

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Submitted compliance report')
      );
    });

    it('should handle API errors', async () => {
      // Mock data
      const regulationId = 'gdpr';
      const reportData = {
        organizationId: 'org-123',
        reportingPeriod: '2023-Q1'
      };

      // Mock API error
      const mockError = new Error('API error');
      axios.post.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(regulatoryIntegration.submitComplianceReport(regulationId, reportData)).rejects.toThrow(
        'Failed to submit compliance report'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error submitting compliance report'),
        mockError
      );
    });
  });

  describe('refreshRegulatoryData', () => {
    it('should refresh regulatory data and clear cache', async () => {
      // Mock data
      const mockRegulations = [
        { id: 'gdpr', name: 'General Data Protection Regulation' },
        { id: 'ccpa', name: 'California Consumer Privacy Act' }
      ];

      // Mock API response
      axios.get.mockResolvedValueOnce({ data: mockRegulations });

      // Call the function
      await regulatoryIntegration.refreshRegulatoryData();

      // Verify API was called
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/regulations'),
        expect.any(Object)
      );

      // Verify cache was cleared for each regulation
      expect(cache.delete).toHaveBeenCalledWith('regulatory-updates');
      expect(cache.delete).toHaveBeenCalledWith('regulation:gdpr');
      expect(cache.delete).toHaveBeenCalledWith('requirements:gdpr');
      expect(cache.delete).toHaveBeenCalledWith('regulation:ccpa');
      expect(cache.delete).toHaveBeenCalledWith('requirements:ccpa');

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Refreshed regulatory data')
      );
    });

    it('should handle API errors', async () => {
      // Mock API error
      const mockError = new Error('API error');
      axios.get.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(regulatoryIntegration.refreshRegulatoryData()).rejects.toThrow(
        'Failed to refresh regulatory data'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error refreshing regulatory data'),
        mockError
      );
    });
  });
});
