"""
Relationship Manager for the Universal Vendor Risk Management System.

This module provides functionality for managing vendor relationships and fourth-party risks.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional, Set

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RelationshipManager:
    """
    Manager for vendor relationships.
    
    This class is responsible for managing vendor relationships and fourth-party risks.
    """
    
    def __init__(self, relationships_dir: Optional[str] = None):
        """
        Initialize the Relationship Manager.
        
        Args:
            relationships_dir: Path to a directory for storing relationship information
        """
        logger.info("Initializing Relationship Manager")
        
        # Set the relationships directory
        self.relationships_dir = relationships_dir or os.path.join(os.getcwd(), 'relationship_data')
        
        # Create the relationships directory if it doesn't exist
        os.makedirs(self.relationships_dir, exist_ok=True)
        
        # Dictionary to store relationships in memory
        self.relationships: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store relationship types
        self.relationship_types: Dict[str, Dict[str, Any]] = {}
        
        # Load relationships from disk
        self._load_relationships_from_disk()
        
        # Register default relationship types
        self._register_default_relationship_types()
        
        logger.info(f"Relationship Manager initialized with {len(self.relationships)} relationships")
    
    def _register_default_relationship_types(self) -> None:
        """Register default relationship types."""
        # Direct vendor relationship
        self.register_relationship_type({
            'id': 'direct_vendor',
            'name': 'Direct Vendor',
            'description': 'Direct vendor relationship',
            'risk_inheritance': 0.8  # 80% of vendor's risk is inherited
        })
        
        # Fourth-party relationship (vendor of vendor)
        self.register_relationship_type({
            'id': 'fourth_party',
            'name': 'Fourth Party',
            'description': 'Vendor of vendor relationship',
            'risk_inheritance': 0.5  # 50% of fourth party's risk is inherited
        })
        
        # Service provider relationship
        self.register_relationship_type({
            'id': 'service_provider',
            'name': 'Service Provider',
            'description': 'Service provider relationship',
            'risk_inheritance': 0.7  # 70% of service provider's risk is inherited
        })
        
        # Data processor relationship
        self.register_relationship_type({
            'id': 'data_processor',
            'name': 'Data Processor',
            'description': 'Data processor relationship',
            'risk_inheritance': 0.9  # 90% of data processor's risk is inherited
        })
        
        # Technology partner relationship
        self.register_relationship_type({
            'id': 'technology_partner',
            'name': 'Technology Partner',
            'description': 'Technology partner relationship',
            'risk_inheritance': 0.6  # 60% of technology partner's risk is inherited
        })
    
    def register_relationship_type(self, type_data: Dict[str, Any]) -> None:
        """
        Register a relationship type.
        
        Args:
            type_data: The relationship type data
            
        Raises:
            ValueError: If the type data is invalid
        """
        # Validate the type data
        if 'id' not in type_data:
            raise ValueError("Relationship type ID is required")
        
        if 'name' not in type_data:
            raise ValueError("Relationship type name is required")
        
        if 'risk_inheritance' not in type_data:
            raise ValueError("Relationship type risk inheritance is required")
        
        # Register the type
        type_id = type_data['id']
        self.relationship_types[type_id] = type_data
        
        logger.info(f"Registered relationship type: {type_id}")
    
    def get_relationship_type(self, type_id: str) -> Dict[str, Any]:
        """
        Get a relationship type.
        
        Args:
            type_id: The ID of the relationship type
            
        Returns:
            The relationship type
            
        Raises:
            ValueError: If the relationship type does not exist
        """
        if type_id not in self.relationship_types:
            raise ValueError(f"Relationship type not found: {type_id}")
        
        return self.relationship_types[type_id]
    
    def get_all_relationship_types(self) -> List[Dict[str, Any]]:
        """
        Get all relationship types.
        
        Returns:
            List of relationship types
        """
        return list(self.relationship_types.values())
    
    def create_relationship(self, relationship_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new vendor relationship.
        
        Args:
            relationship_data: The relationship data
            
        Returns:
            The created relationship
            
        Raises:
            ValueError: If the relationship data is invalid
        """
        logger.info("Creating new vendor relationship")
        
        # Validate the relationship data
        self._validate_relationship_data(relationship_data)
        
        # Generate a unique relationship ID
        relationship_id = str(uuid.uuid4())
        
        # Create the relationship object
        relationship = {
            'id': relationship_id,
            'from_vendor_id': relationship_data['from_vendor_id'],
            'to_vendor_id': relationship_data['to_vendor_id'],
            'type_id': relationship_data['type_id'],
            'description': relationship_data.get('description', ''),
            'data_shared': relationship_data.get('data_shared', []),
            'services_provided': relationship_data.get('services_provided', []),
            'criticality': relationship_data.get('criticality', 'medium'),
            'status': relationship_data.get('status', 'active'),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the relationship in memory
        self.relationships[relationship_id] = relationship
        
        # Store the relationship on disk
        self._save_relationship_to_disk(relationship)
        
        logger.info(f"Vendor relationship created: {relationship_id}")
        
        return relationship
    
    def get_relationship(self, relationship_id: str) -> Dict[str, Any]:
        """
        Get a vendor relationship.
        
        Args:
            relationship_id: The ID of the relationship
            
        Returns:
            The relationship
            
        Raises:
            ValueError: If the relationship does not exist
        """
        logger.info(f"Getting vendor relationship: {relationship_id}")
        
        if relationship_id not in self.relationships:
            raise ValueError(f"Vendor relationship not found: {relationship_id}")
        
        return self.relationships[relationship_id]
    
    def update_relationship(self, relationship_id: str, relationship_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a vendor relationship.
        
        Args:
            relationship_id: The ID of the relationship
            relationship_data: The updated relationship data
            
        Returns:
            The updated relationship
            
        Raises:
            ValueError: If the relationship does not exist
            ValueError: If the relationship data is invalid
        """
        logger.info(f"Updating vendor relationship: {relationship_id}")
        
        # Check if the relationship exists
        if relationship_id not in self.relationships:
            raise ValueError(f"Vendor relationship not found: {relationship_id}")
        
        # Get the existing relationship
        relationship = self.relationships[relationship_id]
        
        # Update the relationship data
        if 'type_id' in relationship_data:
            # Validate the type ID
            if relationship_data['type_id'] not in self.relationship_types:
                raise ValueError(f"Invalid relationship type ID: {relationship_data['type_id']}")
            
            relationship['type_id'] = relationship_data['type_id']
        
        if 'description' in relationship_data:
            relationship['description'] = relationship_data['description']
        
        if 'data_shared' in relationship_data:
            relationship['data_shared'] = relationship_data['data_shared']
        
        if 'services_provided' in relationship_data:
            relationship['services_provided'] = relationship_data['services_provided']
        
        if 'criticality' in relationship_data:
            relationship['criticality'] = relationship_data['criticality']
        
        if 'status' in relationship_data:
            relationship['status'] = relationship_data['status']
        
        # Update the updated_at timestamp
        relationship['updated_at'] = self._get_current_timestamp()
        
        # Store the updated relationship on disk
        self._save_relationship_to_disk(relationship)
        
        logger.info(f"Vendor relationship updated: {relationship_id}")
        
        return relationship
    
    def delete_relationship(self, relationship_id: str) -> None:
        """
        Delete a vendor relationship.
        
        Args:
            relationship_id: The ID of the relationship
            
        Raises:
            ValueError: If the relationship does not exist
        """
        logger.info(f"Deleting vendor relationship: {relationship_id}")
        
        # Check if the relationship exists
        if relationship_id not in self.relationships:
            raise ValueError(f"Vendor relationship not found: {relationship_id}")
        
        # Remove the relationship from memory
        del self.relationships[relationship_id]
        
        # Remove the relationship from disk
        self._delete_relationship_from_disk(relationship_id)
        
        logger.info(f"Vendor relationship deleted: {relationship_id}")
    
    def get_vendor_relationships(self, vendor_id: str, direction: str = 'both') -> List[Dict[str, Any]]:
        """
        Get all relationships for a vendor.
        
        Args:
            vendor_id: The ID of the vendor
            direction: The relationship direction ('from', 'to', or 'both')
            
        Returns:
            List of relationships for the vendor
        """
        logger.info(f"Getting relationships for vendor: {vendor_id}")
        
        if direction == 'from':
            return [r for r in self.relationships.values() if r['from_vendor_id'] == vendor_id]
        elif direction == 'to':
            return [r for r in self.relationships.values() if r['to_vendor_id'] == vendor_id]
        else:  # 'both'
            return [r for r in self.relationships.values() 
                   if r['from_vendor_id'] == vendor_id or r['to_vendor_id'] == vendor_id]
    
    def get_fourth_parties(self, vendor_id: str, risk_threshold: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all fourth-party vendors (vendors of vendors).
        
        Args:
            vendor_id: The ID of the vendor
            risk_threshold: Optional risk threshold ('high', 'medium', 'low')
            
        Returns:
            List of fourth-party vendors
        """
        logger.info(f"Getting fourth-party vendors for vendor: {vendor_id}")
        
        # Get direct vendor relationships
        direct_relationships = self.get_vendor_relationships(vendor_id, direction='from')
        
        # Get fourth-party relationships
        fourth_parties = []
        
        for direct_rel in direct_relationships:
            direct_vendor_id = direct_rel['to_vendor_id']
            fourth_party_rels = self.get_vendor_relationships(direct_vendor_id, direction='from')
            
            for fourth_rel in fourth_party_rels:
                fourth_party = {
                    'id': fourth_rel['to_vendor_id'],
                    'direct_vendor_id': direct_vendor_id,
                    'direct_relationship_id': direct_rel['id'],
                    'fourth_party_relationship_id': fourth_rel['id'],
                    'path': [vendor_id, direct_vendor_id, fourth_rel['to_vendor_id']],
                    'risk_inheritance': self._calculate_risk_inheritance(direct_rel, fourth_rel)
                }
                
                # Add risk level if provided
                if 'risk_level' in fourth_rel:
                    fourth_party['risk_level'] = fourth_rel['risk_level']
                    
                    # Filter by risk threshold if provided
                    if risk_threshold:
                        if risk_threshold == 'high' and fourth_party['risk_level'] != 'high':
                            continue
                        elif risk_threshold == 'medium' and fourth_party['risk_level'] not in ['high', 'medium']:
                            continue
                
                fourth_parties.append(fourth_party)
        
        logger.info(f"Found {len(fourth_parties)} fourth-party vendors for vendor: {vendor_id}")
        
        return fourth_parties
    
    def get_vendor_dependency_graph(self, vendor_id: str, max_depth: int = 3) -> Dict[str, Any]:
        """
        Get the vendor dependency graph.
        
        Args:
            vendor_id: The ID of the vendor
            max_depth: Maximum depth of the dependency graph
            
        Returns:
            The vendor dependency graph
        """
        logger.info(f"Getting vendor dependency graph for vendor: {vendor_id}")
        
        # Initialize the graph
        graph = {
            'nodes': [],
            'edges': [],
            'root_vendor_id': vendor_id
        }
        
        # Set to track visited vendors
        visited: Set[str] = set()
        
        # Recursive function to build the graph
        def build_graph(current_id: str, depth: int) -> None:
            if depth > max_depth or current_id in visited:
                return
            
            visited.add(current_id)
            
            # Add the current vendor as a node
            graph['nodes'].append({
                'id': current_id,
                'depth': depth
            })
            
            # Get outgoing relationships
            relationships = self.get_vendor_relationships(current_id, direction='from')
            
            for rel in relationships:
                to_vendor_id = rel['to_vendor_id']
                
                # Add the edge
                graph['edges'].append({
                    'from': current_id,
                    'to': to_vendor_id,
                    'relationship_id': rel['id'],
                    'type_id': rel['type_id']
                })
                
                # Recursively build the graph
                build_graph(to_vendor_id, depth + 1)
        
        # Build the graph starting from the root vendor
        build_graph(vendor_id, 0)
        
        logger.info(f"Built vendor dependency graph with {len(graph['nodes'])} nodes and {len(graph['edges'])} edges")
        
        return graph
    
    def calculate_inherited_risk(self, vendor_id: str, risk_scores: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate the inherited risk from vendor relationships.
        
        Args:
            vendor_id: The ID of the vendor
            risk_scores: Dictionary of risk scores by vendor ID
            
        Returns:
            The inherited risk information
        """
        logger.info(f"Calculating inherited risk for vendor: {vendor_id}")
        
        # Get direct vendor relationships
        direct_relationships = self.get_vendor_relationships(vendor_id, direction='from')
        
        # Calculate inherited risk from direct vendors
        direct_inherited_risk = 0.0
        direct_risk_sources = []
        
        for rel in direct_relationships:
            to_vendor_id = rel['to_vendor_id']
            
            # Skip if no risk score available
            if to_vendor_id not in risk_scores:
                continue
            
            # Get the risk score
            risk_score = risk_scores[to_vendor_id]
            
            # Get the relationship type
            type_id = rel['type_id']
            rel_type = self.get_relationship_type(type_id)
            
            # Calculate inherited risk
            inheritance_factor = rel_type['risk_inheritance']
            vendor_risk = risk_score.get('overall_score', 0)
            inherited = vendor_risk * inheritance_factor
            
            # Add to total
            direct_inherited_risk += inherited
            
            # Add to sources
            direct_risk_sources.append({
                'vendor_id': to_vendor_id,
                'relationship_id': rel['id'],
                'relationship_type': rel_type['name'],
                'vendor_risk': vendor_risk,
                'inheritance_factor': inheritance_factor,
                'inherited_risk': inherited
            })
        
        # Get fourth-party vendors
        fourth_parties = self.get_fourth_parties(vendor_id)
        
        # Calculate inherited risk from fourth-party vendors
        fourth_party_inherited_risk = 0.0
        fourth_party_risk_sources = []
        
        for fourth in fourth_parties:
            fourth_vendor_id = fourth['id']
            
            # Skip if no risk score available
            if fourth_vendor_id not in risk_scores:
                continue
            
            # Get the risk score
            risk_score = risk_scores[fourth_vendor_id]
            
            # Calculate inherited risk
            inheritance_factor = fourth['risk_inheritance']
            vendor_risk = risk_score.get('overall_score', 0)
            inherited = vendor_risk * inheritance_factor
            
            # Add to total
            fourth_party_inherited_risk += inherited
            
            # Add to sources
            fourth_party_risk_sources.append({
                'vendor_id': fourth_vendor_id,
                'direct_vendor_id': fourth['direct_vendor_id'],
                'path': fourth['path'],
                'vendor_risk': vendor_risk,
                'inheritance_factor': inheritance_factor,
                'inherited_risk': inherited
            })
        
        # Calculate total inherited risk
        total_inherited_risk = direct_inherited_risk + fourth_party_inherited_risk
        
        # Normalize to 0-100 scale
        normalized_risk = min(100, total_inherited_risk)
        
        # Determine risk level
        if normalized_risk >= 70:
            risk_level = 'high'
        elif normalized_risk >= 40:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        # Create the inherited risk object
        inherited_risk = {
            'vendor_id': vendor_id,
            'score_type': 'inherited',
            'overall_score': normalized_risk,
            'risk_level': risk_level,
            'direct_inherited_risk': direct_inherited_risk,
            'fourth_party_inherited_risk': fourth_party_inherited_risk,
            'direct_risk_sources': direct_risk_sources,
            'fourth_party_risk_sources': fourth_party_risk_sources,
            'direct_vendor_count': len(direct_risk_sources),
            'fourth_party_count': len(fourth_party_risk_sources)
        }
        
        logger.info(f"Inherited risk calculated for vendor: {vendor_id}")
        
        return inherited_risk
    
    def _calculate_risk_inheritance(self, direct_rel: Dict[str, Any], fourth_rel: Dict[str, Any]) -> float:
        """
        Calculate the risk inheritance factor for a fourth-party relationship.
        
        Args:
            direct_rel: The direct vendor relationship
            fourth_rel: The fourth-party relationship
            
        Returns:
            The risk inheritance factor
        """
        # Get the relationship types
        direct_type_id = direct_rel['type_id']
        fourth_type_id = fourth_rel['type_id']
        
        direct_type = self.get_relationship_type(direct_type_id)
        fourth_type = self.get_relationship_type(fourth_type_id)
        
        # Calculate the combined inheritance factor
        direct_factor = direct_type['risk_inheritance']
        fourth_factor = fourth_type['risk_inheritance']
        
        # Multiply the factors to get the combined inheritance
        combined_factor = direct_factor * fourth_factor
        
        return combined_factor
    
    def _validate_relationship_data(self, relationship_data: Dict[str, Any]) -> None:
        """
        Validate relationship data.
        
        Args:
            relationship_data: The relationship data to validate
            
        Raises:
            ValueError: If the relationship data is invalid
        """
        # Check required fields
        if 'from_vendor_id' not in relationship_data:
            raise ValueError("From vendor ID is required")
        
        if 'to_vendor_id' not in relationship_data:
            raise ValueError("To vendor ID is required")
        
        if 'type_id' not in relationship_data:
            raise ValueError("Relationship type ID is required")
        
        # Validate type ID
        if relationship_data['type_id'] not in self.relationship_types:
            raise ValueError(f"Invalid relationship type ID: {relationship_data['type_id']}")
        
        # Validate criticality if provided
        if 'criticality' in relationship_data:
            valid_criticality = ['critical', 'high', 'medium', 'low']
            if relationship_data['criticality'] not in valid_criticality:
                raise ValueError(f"Invalid criticality: {relationship_data['criticality']}")
        
        # Validate status if provided
        if 'status' in relationship_data:
            valid_statuses = ['active', 'inactive', 'pending', 'terminated']
            if relationship_data['status'] not in valid_statuses:
                raise ValueError(f"Invalid status: {relationship_data['status']}")
    
    def _load_relationships_from_disk(self) -> None:
        """Load relationships from disk."""
        try:
            # Get all JSON files in the relationships directory
            relationship_files = [f for f in os.listdir(self.relationships_dir) if f.endswith('.json')]
            
            for relationship_file in relationship_files:
                try:
                    # Load the relationship from disk
                    file_path = os.path.join(self.relationships_dir, relationship_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        relationship = json.load(f)
                    
                    # Store the relationship in memory
                    relationship_id = relationship.get('id')
                    
                    if relationship_id:
                        self.relationships[relationship_id] = relationship
                        logger.info(f"Loaded relationship from disk: {relationship_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load relationship from {relationship_file}: {e}")
            
            logger.info(f"Loaded {len(self.relationships)} relationships from disk")
        
        except Exception as e:
            logger.error(f"Failed to load relationships from disk: {e}")
    
    def _save_relationship_to_disk(self, relationship: Dict[str, Any]) -> None:
        """
        Save a relationship to disk.
        
        Args:
            relationship: The relationship to save
        """
        try:
            # Get the relationship ID
            relationship_id = relationship.get('id')
            
            if not relationship_id:
                raise ValueError("Relationship ID is missing")
            
            # Create the file path
            file_path = os.path.join(self.relationships_dir, f"{relationship_id}.json")
            
            # Save the relationship to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(relationship, f, indent=2)
            
            logger.info(f"Saved relationship to disk: {relationship_id}")
        
        except Exception as e:
            logger.error(f"Failed to save relationship to disk: {e}")
    
    def _delete_relationship_from_disk(self, relationship_id: str) -> None:
        """
        Delete a relationship from disk.
        
        Args:
            relationship_id: The ID of the relationship
        """
        try:
            # Create the file path
            file_path = os.path.join(self.relationships_dir, f"{relationship_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted relationship from disk: {relationship_id}")
            else:
                logger.warning(f"Relationship file not found on disk: {relationship_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete relationship from disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
