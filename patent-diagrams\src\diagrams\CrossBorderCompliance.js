import React from 'react';
import {
  <PERSON>agram<PERSON>rame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel
} from '../components/DiagramComponents';

const CrossBorderCompliance = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>CROSS-BORDER TRANSACTION COMPLIANCE OVERLAYS</ContainerLabel>
      </ContainerBox>
      
      {/* Top row components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>901</ComponentNumber>
        <ComponentLabel>Transaction</ComponentLabel>
        Monitor
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>902</ComponentNumber>
        <ComponentLabel>Jurisdiction</ComponentLabel>
        Mapping
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>903</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Overlay
      </ComponentBox>
      
      <Arrow left="690px" top="130px" width="50px" />
      
      <ComponentBox left="650px" top="100px" width="130px" style={{ left: '650px' }}>
        <ComponentNumber>904</ComponentNumber>
        <ComponentLabel>Enforcement</ComponentLabel>
        Action
      </ComponentBox>
      
      {/* Bottom row components */}
      <ComponentBox left="80px" top="250px" width="130px">
        <ComponentNumber>905</ComponentNumber>
        <ComponentLabel>Entity</ComponentLabel>
        Resolution
      </ComponentBox>
      
      <VerticalArrow left="145px" top="160px" height="90px" />
      
      <ComponentBox left="320px" top="250px" width="130px">
        <ComponentNumber>906</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Database
      </ComponentBox>
      
      <VerticalArrow left="385px" top="160px" height="90px" />
      
      <ComponentBox left="560px" top="250px" width="130px">
        <ComponentNumber>907</ComponentNumber>
        <ComponentLabel>Conflict</ComponentLabel>
        Resolution
      </ComponentBox>
      
      <VerticalArrow left="625px" top="160px" height="90px" />
      
      <ComponentBox left="650px" top="250px" width="130px" style={{ left: '650px' }}>
        <ComponentNumber>908</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Report
      </ComponentBox>
      
      <VerticalArrow left="715px" top="160px" height="90px" />
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>909</ComponentNumber>
        <ComponentLabel>Jurisdiction</ComponentLabel>
        Determination
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>910</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Hierarchy
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>911</ComponentNumber>
        <ComponentLabel>Dynamic</ComponentLabel>
        Update
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>912</ComponentNumber>
        <ComponentLabel>Audit Trail</ComponentLabel>
        Generator
      </ComponentBox>
    </DiagramFrame>
  );
};

export default CrossBorderCompliance;
