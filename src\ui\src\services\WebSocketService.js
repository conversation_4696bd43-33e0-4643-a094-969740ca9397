/**
 * WebSocketService
 * 
 * This service provides a connection to the WebSocket server for real-time communication.
 */

class WebSocketService {
  constructor(url = 'ws://localhost:3001/ws') {
    this.url = url;
    this.socket = null;
    this.clientId = `ui-client-${Date.now()}`;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectInterval = 5000; // 5 seconds
    this.pingInterval = 30000; // 30 seconds
    this.pingIntervalId = null;
    this.messageId = 0;
    this.pendingMessages = new Map(); // messageId -> { resolve, reject, timeout }
    this.eventListeners = new Map(); // event -> Set of listeners
    this.subscriptions = new Set(); // Set of channels
  }

  /**
   * Connect to the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when connected
   */
  connect() {
    if (this.isConnected) {
      return Promise.resolve();
    }

    if (this.isConnecting) {
      return new Promise((resolve, reject) => {
        this.addEventListener('connected', () => resolve());
        this.addEventListener('error', (error) => reject(error));
      });
    }

    this.isConnecting = true;

    return new Promise((resolve, reject) => {
      try {
        // Build URL with client ID
        const url = new URL(this.url);
        url.searchParams.append('clientId', this.clientId);

        // Create WebSocket
        this.socket = new WebSocket(url.toString());

        // Set up event handlers
        this.socket.onopen = () => {
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;

          // Start ping interval
          this.pingIntervalId = setInterval(() => this._ping(), this.pingInterval);

          // Emit connected event
          this._emitEvent('connected', { clientId: this.clientId });

          // Resubscribe to channels
          this.subscriptions.forEach((channel) => {
            this.subscribe(channel).catch((error) => {
              console.error(`Error resubscribing to channel ${channel}:`, error);
            });
          });

          resolve();
        };

        this.socket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this._handleMessage(message);
          } catch (error) {
            console.error('Error parsing message:', error);
          }
        };

        this.socket.onclose = (event) => {
          this.isConnected = false;
          this.isConnecting = false;

          // Clear ping interval
          if (this.pingIntervalId) {
            clearInterval(this.pingIntervalId);
            this.pingIntervalId = null;
          }

          // Reject pending messages
          this.pendingMessages.forEach((pending, messageId) => {
            clearTimeout(pending.timeout);
            pending.reject(new Error(`WebSocket closed (${event.code}: ${event.reason})`));
            this.pendingMessages.delete(messageId);
          });

          // Emit disconnected event
          this._emitEvent('disconnected', { code: event.code, reason: event.reason });

          // Reconnect if not a normal closure
          if (event.code !== 1000 && event.code !== 1001) {
            this._reconnect();
          }

          if (this.isConnecting) {
            reject(new Error(`WebSocket closed (${event.code}: ${event.reason})`));
          }
        };

        this.socket.onerror = (error) => {
          // Emit error event
          this._emitEvent('error', error);

          if (this.isConnecting) {
            reject(error);
          }
        };
      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the WebSocket server
   * @param {number} [code=1000] - Close code
   * @param {string} [reason='Client disconnecting'] - Close reason
   * @returns {Promise<void>} - Promise that resolves when disconnected
   */
  disconnect(code = 1000, reason = 'Client disconnecting') {
    if (!this.isConnected && !this.isConnecting) {
      return Promise.resolve();
    }

    // Clear intervals
    if (this.pingIntervalId) {
      clearInterval(this.pingIntervalId);
      this.pingIntervalId = null;
    }

    // Clear pending messages
    this.pendingMessages.forEach((pending, messageId) => {
      clearTimeout(pending.timeout);
      pending.reject(new Error('Client disconnecting'));
      this.pendingMessages.delete(messageId);
    });

    // Close WebSocket
    if (this.socket) {
      if (this.socket.readyState === WebSocket.OPEN) {
        this.socket.close(code, reason);
      }

      this.socket = null;
    }

    // Update state
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;

    return Promise.resolve();
  }

  /**
   * Send a message to the server
   * @param {Object} message - Message to send
   * @param {number} [timeout=10000] - Timeout in milliseconds
   * @returns {Promise<Object>} - Promise that resolves with the response
   */
  send(message, timeout = 10000) {
    if (!this.isConnected) {
      return Promise.reject(new Error('Not connected to server'));
    }

    // Add message ID if not provided
    if (!message.id) {
      message.id = ++this.messageId;
    }

    // Add timestamp if not provided
    if (!message.timestamp) {
      message.timestamp = Date.now();
    }

    // Send message
    return new Promise((resolve, reject) => {
      try {
        const messageStr = JSON.stringify(message);
        this.socket.send(messageStr);

        // Set timeout for response
        const timeoutId = setTimeout(() => {
          this.pendingMessages.delete(message.id);
          reject(new Error(`Timeout waiting for response to message ${message.id}`));
        }, timeout);

        // Store pending message
        this.pendingMessages.set(message.id, {
          resolve,
          reject,
          timeout: timeoutId,
          sentAt: Date.now()
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Subscribe to a channel
   * @param {string} channel - Channel name
   * @returns {Promise<Object>} - Promise that resolves when subscribed
   */
  subscribe(channel) {
    // Send subscribe message
    return this.send({
      type: 'subscribe',
      channel
    }).then((response) => {
      // Store subscription
      this.subscriptions.add(channel);
      return response;
    });
  }

  /**
   * Unsubscribe from a channel
   * @param {string} channel - Channel name
   * @returns {Promise<Object>} - Promise that resolves when unsubscribed
   */
  unsubscribe(channel) {
    // Send unsubscribe message
    return this.send({
      type: 'unsubscribe',
      channel
    }).then((response) => {
      // Remove subscription
      this.subscriptions.delete(channel);
      return response;
    });
  }

  /**
   * Publish a message to a channel
   * @param {string} channel - Channel name
   * @param {*} data - Message data
   * @returns {Promise<Object>} - Promise that resolves when published
   */
  publish(channel, data) {
    return this.send({
      type: 'publish',
      channel,
      data
    });
  }

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {Function} listener - Event listener
   */
  addEventListener(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }

    this.eventListeners.get(event).add(listener);
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {Function} listener - Event listener
   */
  removeEventListener(event, listener) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).delete(listener);

      if (this.eventListeners.get(event).size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * Handle WebSocket message
   * @param {Object} message - Message data
   * @private
   */
  _handleMessage(message) {
    // Resolve pending message
    if (message.id && this.pendingMessages.has(message.id)) {
      const pending = this.pendingMessages.get(message.id);
      clearTimeout(pending.timeout);
      pending.resolve(message);
      this.pendingMessages.delete(message.id);
      return;
    }

    // Handle message based on type
    switch (message.type) {
      case 'welcome':
        this._emitEvent('welcome', message);
        break;

      case 'pong':
        this._emitEvent('pong', {
          rtt: Date.now() - message.timestamp,
          timestamp: message.timestamp
        });
        break;

      case 'message':
        this._emitEvent(`message:${message.channel}`, message.data);
        this._emitEvent('message', message);
        break;

      case 'error':
        this._emitEvent('server-error', {
          error: message.error,
          timestamp: message.timestamp
        });
        break;

      default:
        this._emitEvent('message', message);
    }
  }

  /**
   * Emit event
   * @param {string} event - Event name
   * @param {*} data - Event data
   * @private
   */
  _emitEvent(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach((listener) => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Send ping to server
   * @private
   */
  _ping() {
    this.send({
      type: 'ping',
      timestamp: Date.now()
    }).catch((error) => {
      console.error('Error sending ping:', error);
    });
  }

  /**
   * Reconnect to the WebSocket server
   * @private
   */
  _reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnect attempts reached');
      return;
    }

    this.reconnectAttempts++;

    const delay = Math.min(
      this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1),
      30000 // Max 30 seconds
    );

    console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      this.connect().catch((error) => {
        console.error('Error reconnecting:', error);
      });
    }, delay);
  }
}

// Create singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
