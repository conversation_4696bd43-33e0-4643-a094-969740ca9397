/**
 * Two-Factor Authentication Service
 * 
 * This service handles two-factor authentication operations.
 */

const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const fs = require('fs').promises;
const path = require('path');
const { ValidationError, AuthenticationError } = require('../utils/errors');
const UserService = require('./UserService');

class TwoFactorService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.userService = new UserService(dataDir);
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Generate a new secret for a user
   * 
   * @param {string} userId - User ID
   * @param {string} username - Username
   * @param {string} issuer - Issuer name (default: 'NovaConnect')
   * @returns {Object} - Secret and QR code data URL
   */
  async generateSecret(userId, username, issuer = 'NovaConnect') {
    try {
      // Generate a new secret
      const secret = speakeasy.generateSecret({
        length: 20,
        name: `${issuer}:${username}`,
        issuer: issuer
      });

      // Generate QR code
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

      // Store temporary secret for the user
      // Note: In a real implementation, this would be stored in a database
      // with an expiration time
      const user = await this.userService.findUserById(userId);
      
      if (!user) {
        throw new ValidationError(`User with ID ${userId} not found`);
      }
      
      // Update user with temporary secret
      await this.userService.updateUser(userId, {
        twoFactorTempSecret: secret.base32,
        twoFactorTempSecretCreated: new Date().toISOString()
      });

      return {
        secret: secret.base32,
        qrCode: qrCodeUrl
      };
    } catch (error) {
      console.error('Error generating 2FA secret:', error);
      throw error;
    }
  }

  /**
   * Verify a token and enable 2FA for a user
   * 
   * @param {string} userId - User ID
   * @param {string} token - Token to verify
   * @returns {boolean} - Whether the token is valid
   */
  async verifyAndEnable(userId, token) {
    try {
      const user = await this.userService.findUserById(userId);
      
      if (!user) {
        throw new ValidationError(`User with ID ${userId} not found`);
      }
      
      if (!user.twoFactorTempSecret) {
        throw new ValidationError('No temporary secret found for user');
      }
      
      // Check if temporary secret has expired (10 minutes)
      const tempSecretCreated = new Date(user.twoFactorTempSecretCreated);
      const now = new Date();
      const expirationTime = 10 * 60 * 1000; // 10 minutes in milliseconds
      
      if (now.getTime() - tempSecretCreated.getTime() > expirationTime) {
        throw new ValidationError('Temporary secret has expired');
      }
      
      // Verify token
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorTempSecret,
        encoding: 'base32',
        token: token,
        window: 1 // Allow 1 step before and after current time
      });
      
      if (!verified) {
        throw new ValidationError('Invalid token');
      }
      
      // Enable 2FA for user
      await this.userService.updateUser(userId, {
        twoFactorEnabled: true,
        twoFactorSecret: user.twoFactorTempSecret,
        twoFactorTempSecret: null,
        twoFactorTempSecretCreated: null
      });
      
      return true;
    } catch (error) {
      console.error('Error verifying and enabling 2FA:', error);
      throw error;
    }
  }

  /**
   * Verify a token for login
   * 
   * @param {string} userId - User ID
   * @param {string} token - Token to verify
   * @returns {boolean} - Whether the token is valid
   */
  async verifyToken(userId, token) {
    try {
      const user = await this.userService.findUserById(userId);
      
      if (!user) {
        throw new ValidationError(`User with ID ${userId} not found`);
      }
      
      if (!user.twoFactorEnabled || !user.twoFactorSecret) {
        throw new ValidationError('Two-factor authentication is not enabled for this user');
      }
      
      // Verify token
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token: token,
        window: 1 // Allow 1 step before and after current time
      });
      
      if (!verified) {
        throw new AuthenticationError('Invalid token');
      }
      
      return true;
    } catch (error) {
      console.error('Error verifying 2FA token:', error);
      throw error;
    }
  }

  /**
   * Disable 2FA for a user
   * 
   * @param {string} userId - User ID
   * @returns {boolean} - Whether 2FA was disabled
   */
  async disable(userId) {
    try {
      const user = await this.userService.findUserById(userId);
      
      if (!user) {
        throw new ValidationError(`User with ID ${userId} not found`);
      }
      
      if (!user.twoFactorEnabled) {
        throw new ValidationError('Two-factor authentication is not enabled for this user');
      }
      
      // Disable 2FA for user
      await this.userService.updateUser(userId, {
        twoFactorEnabled: false,
        twoFactorSecret: null,
        twoFactorTempSecret: null,
        twoFactorTempSecretCreated: null
      });
      
      return true;
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      throw error;
    }
  }

  /**
   * Check if 2FA is enabled for a user
   * 
   * @param {string} userId - User ID
   * @returns {boolean} - Whether 2FA is enabled
   */
  async isEnabled(userId) {
    try {
      const user = await this.userService.findUserById(userId);
      
      if (!user) {
        throw new ValidationError(`User with ID ${userId} not found`);
      }
      
      return !!user.twoFactorEnabled;
    } catch (error) {
      console.error('Error checking if 2FA is enabled:', error);
      throw error;
    }
  }
}

module.exports = TwoFactorService;
