# NovaGRC APIs

<div align="center">
<h3>Comprehensive GRC API Suite for NovaFuse Platform</h3>
</div>

> NovaGRC APIs provide a comprehensive suite of Governance, Risk, and Compliance APIs that power the NovaFuse platform.

## Overview

NovaGRC APIs is a key component of the NovaFuse platform, providing the GRC functionality that powers various NovaFuse products. These APIs can be used individually or together to create a comprehensive GRC solution.

## API Suite

NovaGRC APIs includes the following APIs:

1. **Privacy Management API**
   - Data processing activities management
   - Data subject requests handling
   - Consent management
   - Privacy impact assessments
   - Privacy notices management
   - Data breach management

2. **Regulatory Compliance API**
   - Compliance frameworks management
   - Regulatory requirements
   - Gap analysis
   - Compliance reporting
   - Regulatory updates monitoring
   - Remediation tracking

3. **Security Assessment API**
   - Vulnerability management
   - Security controls assessment
   - Threat modeling
   - Risk assessment
   - Security incident management
   - Security scans

4. **Control Testing API**
   - Control inventory
   - Control design assessment
   - Test plans and execution
   - Evidence collection
   - Remediation management
   - Control effectiveness reporting

5. **ESG API**
   - Environmental metrics tracking
   - Social responsibility management
   - Governance oversight
   - ESG reporting
   - Stakeholder engagement
   - Sustainability initiatives tracking

6. **Compliance Automation API**
   - Automated assessments
   - Continuous monitoring
   - Workflow automation
   - Task scheduling
   - Document generation
   - Integration with security tools

## Architecture

NovaGRC APIs are built using a microservices architecture with the following components:

- **API Gateway**: Routes requests to the appropriate API
- **Authentication Service**: Manages API authentication and authorization
- **API Services**: Individual API implementations
- **Data Layer**: MongoDB database for storing GRC data
- **Integration Layer**: Connects with external systems and data sources

## Implementation Status

| API | Status | Completion | Notes |
|-----|--------|------------|-------|
| Privacy Management API | Complete | 100% | All endpoints implemented and tested |
| Regulatory Compliance API | Complete | 100% | All endpoints implemented and tested |
| Security Assessment API | Complete | 100% | All endpoints implemented and tested |
| Control Testing API | Complete | 100% | All endpoints implemented and tested |
| ESG API | Complete | 100% | All endpoints implemented and tested |
| Compliance Automation API | Complete | 100% | All endpoints implemented and tested |

## Repository Structure

The repository is structured as follows:

```
nova-grc-apis/
├── apis/
│   ├── privacy/
│   │   └── management/
│   │       ├── controllers.js
│   │       ├── models.js
│   │       ├── routes.js
│   │       ├── validation.js
│   │       └── README.md
│   ├── compliance/
│   │   ├── regulatory/
│   │   │   ├── controllers.js
│   │   │   ├── models.js
│   │   │   ├── routes.js
│   │   │   ├── validation.js
│   │   │   └── README.md
│   │   └── automation/
│   │       ├── controllers.js
│   │       ├── models.js
│   │       ├── routes.js
│   │       ├── validation.js
│   │       └── README.md
│   ├── security/
│   │   └── assessment/
│   │       ├── controllers.js
│   │       ├── models.js
│   │       ├── routes.js
│   │       ├── validation.js
│   │       └── README.md
│   ├── control/
│   │   └── testing/
│   │       ├── controllers.js
│   │       ├── models.js
│   │       ├── routes.js
│   │       ├── validation.js
│   │       └── README.md
│   └── esg/
│       ├── controllers.js
│       ├── models.js
│       ├── routes.js
│       ├── validation.js
│       └── README.md
├── common/
│   ├── middleware/
│   ├── models/
│   ├── services/
│   └── utils/
├── config/
├── docs/
│   ├── api-ui-mapping.md
│   ├── feature-flag-implementation.md
│   ├── novaassist-ai-capabilities.md
│   └── novaassist-ai-implementation-plan.md
├── tests/
└── server.js
```

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- MongoDB 4.x or higher
- npm or yarn
- Docker (optional, for containerized deployment)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/Dartan1983/nova-grc-apis.git
   cd nova-grc-apis
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Configure environment variables:
   ```
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Start the service:
   ```
   npm start
   ```

### Development

1. Start in development mode:
   ```
   npm run dev
   ```

2. Run tests:
   ```
   npm test
   ```

3. Run with test coverage:
   ```
   npm run test:coverage
   ```

## API Documentation

API documentation is available at the following endpoints when running the server:

- Privacy Management API: `/api/privacy/management/docs`
- Regulatory Compliance API: `/api/compliance/docs`
- Security Assessment API: `/api/security/assessment/docs`
- Control Testing API: `/api/control/testing/docs`
- ESG API: `/api/esg/docs`
- Compliance Automation API: `/api/compliance/automation/docs`

## Product Tiers and Feature Flags

NovaFuse offers the following product tiers:

1. **NovaPrime**: Premium, full-featured offering with all capabilities
2. **NovaCore**: Mid-tier offering with essential functionality
3. **NovaShield**: Security-focused offering
4. **NovaLearn**: Training and education-focused offering

Feature flags are used to control access to features based on the user's product tier. See the [Feature Flag Implementation](docs/feature-flag-implementation.md) document for details.

## NovaAssist AI Integration

NovaAssist AI is an intelligent assistant integrated into the NovaFuse platform that helps users navigate complex GRC requirements, interpret compliance data, and make better decisions. The AI capabilities are tiered across different product offerings.

For more information, see the following documents:

- [NovaAssist AI Capabilities](docs/novaassist-ai-capabilities.md)
- [NovaAssist AI Implementation Plan](docs/novaassist-ai-implementation-plan.md)

## API-UI Mapping

The [API-UI Mapping](docs/api-ui-mapping.md) document maps the NovaGRC APIs to the NovaFuse UI components and product tiers.

## Folder Structure

```
nova-grc-apis/
├── apis/
│   ├── privacy/
│   │   └── management/
│   │       ├── controllers/
│   │       ├── models/
│   │       ├── routes/
│   │       ├── services/
│   │       ├── validation/
│   │       └── index.js
│   ├── compliance/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── validation/
│   │   └── index.js
│   ├── security/
│   │   └── assessment/
│   │       ├── controllers/
│   │       ├── models/
│   │       ├── routes/
│   │       ├── services/
│   │       ├── validation/
│   │       └── index.js
│   ├── control/
│   │   └── testing/
│   │       ├── controllers/
│   │       ├── models/
│   │       ├── routes/
│   │       ├── services/
│   │       ├── validation/
│   │       └── index.js
│   └── esg/
│       ├── controllers/
│       ├── models/
│       ├── routes/
│       ├── services/
│       ├── validation/
│       └── index.js
├── common/
│   ├── middleware/
│   ├── models/
│   ├── services/
│   └── utils/
├── config/
├── docs/
├── tests/
└── server.js
```

## Contributing

Please read our [Contributing Guide](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

For more information, contact the NovaFuse <NAME_EMAIL>.
