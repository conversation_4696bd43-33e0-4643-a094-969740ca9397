import { render, screen } from '@testing-library/react';
import { test, expect, beforeAll, afterEach, afterAll } from 'vitest';
import { QuantumDashboard } from '../src/components/QuantumDashboard';

class NovaQuantumMockServer {
  constructor({ qubitCapacity, latency }) {
    this.qubitCapacity = qubitCapacity;
    this.latency = latency;
    this.activeConnections = 0;
  }

  async connect() {
    this.activeConnections++;
    return new Promise(resolve => setTimeout(resolve, this.latency));
  }

  async disconnect() {
    this.activeConnections--;
    return Promise.resolve();
  }
}

const simulateMobile = () => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: query === '(max-width: 768px)',
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });};

const testRenderPerformance = async (component, { duration = 1000 } = {}) => {
  const start = performance.now();
  let frames = 0;
  
  const checkFrame = () => {
    frames++;
    if (performance.now() - start < duration) {
      requestAnimationFrame(checkFrame);
    }
  };
  
  checkFrame();
  
  // Wait for the test duration
  await new Promise(resolve => setTimeout(resolve, duration));
  
  const elapsed = (performance.now() - start) / 1000; // Convert to seconds
  const fps = frames / elapsed;
  
  return { fps, frames, duration: elapsed };
};

describe('Quantum Dashboard Fusion', () => {
  let mockServer;
  
  beforeAll(() => {
    mockServer = new NovaQuantumMockServer({
      qubitCapacity: 100000,
      latency: 50 // ms
    });
  });

  test('should maintain 60fps under load', async () => {
    const { fps } = await testRenderPerformance(
      <QuantumDashboard />,
      { duration: 3000 } // 3 second test for more stable measurement
    );
    console.log(`Average FPS: ${fps.toFixed(1)}`);
    expect(fps).toBeGreaterThan(55);
  }, 10000); // 10s timeout

  test('should auto-downgrade on mobile', () => {
    simulateMobile();
    render(<QuantumDashboard />);
    const qualityIndicator = screen.getByTestId('quality-level');
    expect(qualityIndicator).toHaveTextContent('medium');
  });

  test('should handle server disconnection gracefully', async () => {
    const { unmount } = render(<QuantumDashboard />);
    await mockServer.disconnect();
    unmount();
    expect(mockServer.activeConnections).toBe(0);
  });
});
