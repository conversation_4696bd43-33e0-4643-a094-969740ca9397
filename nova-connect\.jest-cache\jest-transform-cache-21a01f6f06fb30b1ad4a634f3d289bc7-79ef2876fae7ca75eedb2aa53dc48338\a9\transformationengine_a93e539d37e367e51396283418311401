b4a448742ca274f87d1a0b442321de15
/**
 * NovaConnect Transformation Engine
 * 
 * High-performance data normalization engine capable of transforming data
 * from various sources into a standardized format at sub-millisecond speeds.
 */

const {
  performance
} = require('perf_hooks');
class TransformationEngine {
  constructor(options = {}) {
    this.options = {
      enableMetrics: true,
      enableCaching: true,
      ...options
    };

    // Initialize transformers registry
    this.transformers = {
      // String transformations
      lowercase: value => typeof value === 'string' ? value.toLowerCase() : value,
      uppercase: value => typeof value === 'string' ? value.toUpperCase() : value,
      trim: value => typeof value === 'string' ? value.trim() : value,
      // Date transformations
      isoToUnix: value => typeof value === 'string' ? new Date(value).getTime() : value,
      unixToIso: value => typeof value === 'number' ? new Date(value).toISOString() : value,
      // Number transformations
      toNumber: value => !isNaN(parseFloat(value)) ? parseFloat(value) : value,
      // Object transformations
      toJson: value => typeof value === 'object' ? JSON.stringify(value) : value,
      fromJson: value => {
        if (typeof value !== 'string') return value;
        try {
          return JSON.parse(value);
        } catch (e) {
          return value;
        }
      },
      // Array transformations
      join: (value, separator = ',') => Array.isArray(value) ? value.join(separator) : value,
      split: (value, separator = ',') => typeof value === 'string' ? value.split(separator) : value,
      // Security transformations
      mask: (value, pattern = 'xxxx') => {
        if (typeof value !== 'string') return value;
        return value.substring(0, 4) + pattern;
      }
    };

    // Initialize transformation cache
    this.cache = new Map();
  }

  /**
   * Register a custom transformer
   * @param {string} name - Name of the transformer
   * @param {Function} transformer - Transformer function
   */
  registerTransformer(name, transformer) {
    if (typeof transformer !== 'function') {
      throw new Error('Transformer must be a function');
    }
    this.transformers[name] = transformer;
  }

  /**
   * Transform data according to the provided rules
   * @param {Object} data - Source data to transform
   * @param {Array} rules - Transformation rules
   * @returns {Object} - Transformed data
   */
  transform(data, rules) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;

    // Check cache if enabled
    if (this.options.enableCaching) {
      const cacheKey = this._generateCacheKey(data, rules);
      const cachedResult = this.cache.get(cacheKey);
      if (cachedResult) {
        return cachedResult;
      }
    }
    const result = {};

    // Apply each transformation rule
    for (const rule of rules) {
      try {
        // Get value from source path
        const value = this._getValueByPath(data, rule.source);

        // Apply transformation if specified
        let transformedValue = value;
        if (rule.transform) {
          if (typeof rule.transform === 'string' && this.transformers[rule.transform]) {
            // Single transformation
            transformedValue = this.transformers[rule.transform](value, rule.transformParams);
          } else if (Array.isArray(rule.transform)) {
            // Chain of transformations
            transformedValue = rule.transform.reduce((currentValue, transformName) => {
              if (this.transformers[transformName]) {
                return this.transformers[transformName](currentValue, rule.transformParams);
              }
              return currentValue;
            }, value);
          }
        }

        // Set value in target path
        this._setValueByPath(result, rule.target, transformedValue);
      } catch (error) {
        // Log error but continue with other rules
        console.error(`Error applying transformation rule: ${error.message}`, rule);
      }
    }

    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      const cacheKey = this._generateCacheKey(data, rules);
      this.cache.set(cacheKey, result);
    }

    // Record metrics if enabled
    if (this.options.enableMetrics) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Store metrics for this transformation
      this._recordMetrics(duration, rules.length);
    }
    return result;
  }

  /**
   * Transform batch data according to the provided rules
   * @param {Object} data - Source data containing arrays
   * @param {Array} rules - Transformation rules with array paths
   * @returns {Object} - Transformed data
   */
  batchTransform(data, rules) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;

    // Find array paths in rules
    const arrayPaths = rules.filter(rule => rule.source.includes('[*]'));

    // If no array paths, use regular transform
    if (arrayPaths.length === 0) {
      return this.transform(data, rules);
    }

    // Parse the source array path from the first array rule
    const sourceArrayPath = arrayPaths[0].source.split('[*]')[0];
    const sourceArray = this._getValueByPath(data, sourceArrayPath);
    if (!Array.isArray(sourceArray)) {
      throw new Error(`Path ${sourceArrayPath} does not point to an array`);
    }

    // Create result object with transformed items
    const result = {};
    const items = [];

    // Transform each item in the array
    for (const item of sourceArray) {
      // Create item-specific rules by replacing array notation
      const itemRules = rules.map(rule => {
        if (!rule.source.includes('[*]')) {
          return rule;
        }
        return {
          source: rule.source.replace(`${sourceArrayPath}[*]`, '').replace(/^\./, ''),
          target: rule.target.replace(/\[.*\]/, ''),
          transform: rule.transform,
          transformParams: rule.transformParams
        };
      });

      // Transform the item
      const transformedItem = this.transform({
        item
      }, itemRules);
      items.push(transformedItem);
    }

    // Set the items in the result
    const targetArrayPath = arrayPaths[0].target.split('[*]')[0];
    this._setValueByPath(result, targetArrayPath, items);

    // Record metrics if enabled
    if (this.options.enableMetrics) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Store metrics for this batch transformation
      this._recordMetrics(duration, rules.length * sourceArray.length, true);
    }
    return result;
  }

  /**
   * Get metrics for the transformation engine
   * @returns {Object} - Metrics object
   */
  getMetrics() {
    return this.metrics;
  }

  /**
   * Clear the transformation cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Generate a cache key for the data and rules
   * @private
   */
  _generateCacheKey(data, rules) {
    // Simple implementation - in production would use a more efficient approach
    return JSON.stringify({
      data,
      rules
    });
  }

  /**
   * Record metrics for a transformation
   * @private
   */
  _recordMetrics(duration, rulesCount, isBatch = false) {
    if (!this.metrics) {
      this.metrics = {
        transformations: 0,
        totalDuration: 0,
        averageDuration: 0,
        batchTransformations: 0,
        batchTotalDuration: 0,
        batchAverageDuration: 0,
        rulesApplied: 0
      };
    }
    if (isBatch) {
      this.metrics.batchTransformations++;
      this.metrics.batchTotalDuration += duration;
      this.metrics.batchAverageDuration = this.metrics.batchTotalDuration / this.metrics.batchTransformations;
    } else {
      this.metrics.transformations++;
      this.metrics.totalDuration += duration;
      this.metrics.averageDuration = this.metrics.totalDuration / this.metrics.transformations;
    }
    this.metrics.rulesApplied += rulesCount;
  }

  /**
   * Get a value from an object by path
   * @private
   */
  _getValueByPath(obj, path) {
    if (!path) return obj;

    // Handle array notation
    const normalizedPath = path.replace(/\[(\w+)\]/g, '.$1');
    const parts = normalizedPath.split('.');
    let current = obj;
    for (const part of parts) {
      if (part === '') continue;
      if (current === null || current === undefined) return undefined;
      current = current[part];
    }
    return current;
  }

  /**
   * Set a value in an object by path
   * @private
   */
  _setValueByPath(obj, path, value) {
    if (!path) return;

    // Handle array notation
    const normalizedPath = path.replace(/\[(\w+)\]/g, '.$1');
    const parts = normalizedPath.split('.');
    let current = obj;
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (part === '') continue;
      if (!(part in current)) {
        // Create object or array based on next part
        current[part] = parts[i + 1].match(/^\d+$/) ? [] : {};
      }
      current = current[part];
    }
    const lastPart = parts[parts.length - 1];
    if (lastPart !== '') {
      current[lastPart] = value;
    }
  }
}
module.exports = TransformationEngine;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJwZXJmb3JtYW5jZSIsInJlcXVpcmUiLCJUcmFuc2Zvcm1hdGlvbkVuZ2luZSIsImNvbnN0cnVjdG9yIiwib3B0aW9ucyIsImVuYWJsZU1ldHJpY3MiLCJlbmFibGVDYWNoaW5nIiwidHJhbnNmb3JtZXJzIiwibG93ZXJjYXNlIiwidmFsdWUiLCJ0b0xvd2VyQ2FzZSIsInVwcGVyY2FzZSIsInRvVXBwZXJDYXNlIiwidHJpbSIsImlzb1RvVW5peCIsIkRhdGUiLCJnZXRUaW1lIiwidW5peFRvSXNvIiwidG9JU09TdHJpbmciLCJ0b051bWJlciIsImlzTmFOIiwicGFyc2VGbG9hdCIsInRvSnNvbiIsIkpTT04iLCJzdHJpbmdpZnkiLCJmcm9tSnNvbiIsInBhcnNlIiwiZSIsImpvaW4iLCJzZXBhcmF0b3IiLCJBcnJheSIsImlzQXJyYXkiLCJzcGxpdCIsIm1hc2siLCJwYXR0ZXJuIiwic3Vic3RyaW5nIiwiY2FjaGUiLCJNYXAiLCJyZWdpc3RlclRyYW5zZm9ybWVyIiwibmFtZSIsInRyYW5zZm9ybWVyIiwiRXJyb3IiLCJ0cmFuc2Zvcm0iLCJkYXRhIiwicnVsZXMiLCJzdGFydFRpbWUiLCJub3ciLCJjYWNoZUtleSIsIl9nZW5lcmF0ZUNhY2hlS2V5IiwiY2FjaGVkUmVzdWx0IiwiZ2V0IiwicmVzdWx0IiwicnVsZSIsIl9nZXRWYWx1ZUJ5UGF0aCIsInNvdXJjZSIsInRyYW5zZm9ybWVkVmFsdWUiLCJ0cmFuc2Zvcm1QYXJhbXMiLCJyZWR1Y2UiLCJjdXJyZW50VmFsdWUiLCJ0cmFuc2Zvcm1OYW1lIiwiX3NldFZhbHVlQnlQYXRoIiwidGFyZ2V0IiwiZXJyb3IiLCJjb25zb2xlIiwibWVzc2FnZSIsInNldCIsImVuZFRpbWUiLCJkdXJhdGlvbiIsIl9yZWNvcmRNZXRyaWNzIiwibGVuZ3RoIiwiYmF0Y2hUcmFuc2Zvcm0iLCJhcnJheVBhdGhzIiwiZmlsdGVyIiwiaW5jbHVkZXMiLCJzb3VyY2VBcnJheVBhdGgiLCJzb3VyY2VBcnJheSIsIml0ZW1zIiwiaXRlbSIsIml0ZW1SdWxlcyIsIm1hcCIsInJlcGxhY2UiLCJ0cmFuc2Zvcm1lZEl0ZW0iLCJwdXNoIiwidGFyZ2V0QXJyYXlQYXRoIiwiZ2V0TWV0cmljcyIsIm1ldHJpY3MiLCJjbGVhckNhY2hlIiwiY2xlYXIiLCJydWxlc0NvdW50IiwiaXNCYXRjaCIsInRyYW5zZm9ybWF0aW9ucyIsInRvdGFsRHVyYXRpb24iLCJhdmVyYWdlRHVyYXRpb24iLCJiYXRjaFRyYW5zZm9ybWF0aW9ucyIsImJhdGNoVG90YWxEdXJhdGlvbiIsImJhdGNoQXZlcmFnZUR1cmF0aW9uIiwicnVsZXNBcHBsaWVkIiwib2JqIiwicGF0aCIsIm5vcm1hbGl6ZWRQYXRoIiwicGFydHMiLCJjdXJyZW50IiwicGFydCIsInVuZGVmaW5lZCIsImkiLCJtYXRjaCIsImxhc3RQYXJ0IiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbInRyYW5zZm9ybWF0aW9uLWVuZ2luZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE5vdmFDb25uZWN0IFRyYW5zZm9ybWF0aW9uIEVuZ2luZVxuICogXG4gKiBIaWdoLXBlcmZvcm1hbmNlIGRhdGEgbm9ybWFsaXphdGlvbiBlbmdpbmUgY2FwYWJsZSBvZiB0cmFuc2Zvcm1pbmcgZGF0YVxuICogZnJvbSB2YXJpb3VzIHNvdXJjZXMgaW50byBhIHN0YW5kYXJkaXplZCBmb3JtYXQgYXQgc3ViLW1pbGxpc2Vjb25kIHNwZWVkcy5cbiAqL1xuXG5jb25zdCB7IHBlcmZvcm1hbmNlIH0gPSByZXF1aXJlKCdwZXJmX2hvb2tzJyk7XG5cbmNsYXNzIFRyYW5zZm9ybWF0aW9uRW5naW5lIHtcbiAgY29uc3RydWN0b3Iob3B0aW9ucyA9IHt9KSB7XG4gICAgdGhpcy5vcHRpb25zID0ge1xuICAgICAgZW5hYmxlTWV0cmljczogdHJ1ZSxcbiAgICAgIGVuYWJsZUNhY2hpbmc6IHRydWUsXG4gICAgICAuLi5vcHRpb25zXG4gICAgfTtcbiAgICBcbiAgICAvLyBJbml0aWFsaXplIHRyYW5zZm9ybWVycyByZWdpc3RyeVxuICAgIHRoaXMudHJhbnNmb3JtZXJzID0ge1xuICAgICAgLy8gU3RyaW5nIHRyYW5zZm9ybWF0aW9uc1xuICAgICAgbG93ZXJjYXNlOiAodmFsdWUpID0+IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycgPyB2YWx1ZS50b0xvd2VyQ2FzZSgpIDogdmFsdWUsXG4gICAgICB1cHBlcmNhc2U6ICh2YWx1ZSkgPT4gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyA/IHZhbHVlLnRvVXBwZXJDYXNlKCkgOiB2YWx1ZSxcbiAgICAgIHRyaW06ICh2YWx1ZSkgPT4gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyA/IHZhbHVlLnRyaW0oKSA6IHZhbHVlLFxuICAgICAgXG4gICAgICAvLyBEYXRlIHRyYW5zZm9ybWF0aW9uc1xuICAgICAgaXNvVG9Vbml4OiAodmFsdWUpID0+IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycgPyBuZXcgRGF0ZSh2YWx1ZSkuZ2V0VGltZSgpIDogdmFsdWUsXG4gICAgICB1bml4VG9Jc286ICh2YWx1ZSkgPT4gdHlwZW9mIHZhbHVlID09PSAnbnVtYmVyJyA/IG5ldyBEYXRlKHZhbHVlKS50b0lTT1N0cmluZygpIDogdmFsdWUsXG4gICAgICBcbiAgICAgIC8vIE51bWJlciB0cmFuc2Zvcm1hdGlvbnNcbiAgICAgIHRvTnVtYmVyOiAodmFsdWUpID0+ICFpc05hTihwYXJzZUZsb2F0KHZhbHVlKSkgPyBwYXJzZUZsb2F0KHZhbHVlKSA6IHZhbHVlLFxuICAgICAgXG4gICAgICAvLyBPYmplY3QgdHJhbnNmb3JtYXRpb25zXG4gICAgICB0b0pzb246ICh2YWx1ZSkgPT4gdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyA/IEpTT04uc3RyaW5naWZ5KHZhbHVlKSA6IHZhbHVlLFxuICAgICAgZnJvbUpzb246ICh2YWx1ZSkgPT4ge1xuICAgICAgICBpZiAodHlwZW9mIHZhbHVlICE9PSAnc3RyaW5nJykgcmV0dXJuIHZhbHVlO1xuICAgICAgICB0cnkge1xuICAgICAgICAgIHJldHVybiBKU09OLnBhcnNlKHZhbHVlKTtcbiAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIFxuICAgICAgLy8gQXJyYXkgdHJhbnNmb3JtYXRpb25zXG4gICAgICBqb2luOiAodmFsdWUsIHNlcGFyYXRvciA9ICcsJykgPT4gQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZS5qb2luKHNlcGFyYXRvcikgOiB2YWx1ZSxcbiAgICAgIHNwbGl0OiAodmFsdWUsIHNlcGFyYXRvciA9ICcsJykgPT4gdHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyA/IHZhbHVlLnNwbGl0KHNlcGFyYXRvcikgOiB2YWx1ZSxcbiAgICAgIFxuICAgICAgLy8gU2VjdXJpdHkgdHJhbnNmb3JtYXRpb25zXG4gICAgICBtYXNrOiAodmFsdWUsIHBhdHRlcm4gPSAneHh4eCcpID0+IHtcbiAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3N0cmluZycpIHJldHVybiB2YWx1ZTtcbiAgICAgICAgcmV0dXJuIHZhbHVlLnN1YnN0cmluZygwLCA0KSArIHBhdHRlcm47XG4gICAgICB9XG4gICAgfTtcbiAgICBcbiAgICAvLyBJbml0aWFsaXplIHRyYW5zZm9ybWF0aW9uIGNhY2hlXG4gICAgdGhpcy5jYWNoZSA9IG5ldyBNYXAoKTtcbiAgfVxuICBcbiAgLyoqXG4gICAqIFJlZ2lzdGVyIGEgY3VzdG9tIHRyYW5zZm9ybWVyXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lIC0gTmFtZSBvZiB0aGUgdHJhbnNmb3JtZXJcbiAgICogQHBhcmFtIHtGdW5jdGlvbn0gdHJhbnNmb3JtZXIgLSBUcmFuc2Zvcm1lciBmdW5jdGlvblxuICAgKi9cbiAgcmVnaXN0ZXJUcmFuc2Zvcm1lcihuYW1lLCB0cmFuc2Zvcm1lcikge1xuICAgIGlmICh0eXBlb2YgdHJhbnNmb3JtZXIgIT09ICdmdW5jdGlvbicpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVHJhbnNmb3JtZXIgbXVzdCBiZSBhIGZ1bmN0aW9uJyk7XG4gICAgfVxuICAgIFxuICAgIHRoaXMudHJhbnNmb3JtZXJzW25hbWVdID0gdHJhbnNmb3JtZXI7XG4gIH1cbiAgXG4gIC8qKlxuICAgKiBUcmFuc2Zvcm0gZGF0YSBhY2NvcmRpbmcgdG8gdGhlIHByb3ZpZGVkIHJ1bGVzXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBkYXRhIC0gU291cmNlIGRhdGEgdG8gdHJhbnNmb3JtXG4gICAqIEBwYXJhbSB7QXJyYXl9IHJ1bGVzIC0gVHJhbnNmb3JtYXRpb24gcnVsZXNcbiAgICogQHJldHVybnMge09iamVjdH0gLSBUcmFuc2Zvcm1lZCBkYXRhXG4gICAqL1xuICB0cmFuc2Zvcm0oZGF0YSwgcnVsZXMpIHtcbiAgICBjb25zdCBzdGFydFRpbWUgPSB0aGlzLm9wdGlvbnMuZW5hYmxlTWV0cmljcyA/IHBlcmZvcm1hbmNlLm5vdygpIDogMDtcbiAgICBcbiAgICAvLyBDaGVjayBjYWNoZSBpZiBlbmFibGVkXG4gICAgaWYgKHRoaXMub3B0aW9ucy5lbmFibGVDYWNoaW5nKSB7XG4gICAgICBjb25zdCBjYWNoZUtleSA9IHRoaXMuX2dlbmVyYXRlQ2FjaGVLZXkoZGF0YSwgcnVsZXMpO1xuICAgICAgY29uc3QgY2FjaGVkUmVzdWx0ID0gdGhpcy5jYWNoZS5nZXQoY2FjaGVLZXkpO1xuICAgICAgXG4gICAgICBpZiAoY2FjaGVkUmVzdWx0KSB7XG4gICAgICAgIHJldHVybiBjYWNoZWRSZXN1bHQ7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IHJlc3VsdCA9IHt9O1xuICAgIFxuICAgIC8vIEFwcGx5IGVhY2ggdHJhbnNmb3JtYXRpb24gcnVsZVxuICAgIGZvciAoY29uc3QgcnVsZSBvZiBydWxlcykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gR2V0IHZhbHVlIGZyb20gc291cmNlIHBhdGhcbiAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLl9nZXRWYWx1ZUJ5UGF0aChkYXRhLCBydWxlLnNvdXJjZSk7XG4gICAgICAgIFxuICAgICAgICAvLyBBcHBseSB0cmFuc2Zvcm1hdGlvbiBpZiBzcGVjaWZpZWRcbiAgICAgICAgbGV0IHRyYW5zZm9ybWVkVmFsdWUgPSB2YWx1ZTtcbiAgICAgICAgaWYgKHJ1bGUudHJhbnNmb3JtKSB7XG4gICAgICAgICAgaWYgKHR5cGVvZiBydWxlLnRyYW5zZm9ybSA9PT0gJ3N0cmluZycgJiYgdGhpcy50cmFuc2Zvcm1lcnNbcnVsZS50cmFuc2Zvcm1dKSB7XG4gICAgICAgICAgICAvLyBTaW5nbGUgdHJhbnNmb3JtYXRpb25cbiAgICAgICAgICAgIHRyYW5zZm9ybWVkVmFsdWUgPSB0aGlzLnRyYW5zZm9ybWVyc1tydWxlLnRyYW5zZm9ybV0odmFsdWUsIHJ1bGUudHJhbnNmb3JtUGFyYW1zKTtcbiAgICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocnVsZS50cmFuc2Zvcm0pKSB7XG4gICAgICAgICAgICAvLyBDaGFpbiBvZiB0cmFuc2Zvcm1hdGlvbnNcbiAgICAgICAgICAgIHRyYW5zZm9ybWVkVmFsdWUgPSBydWxlLnRyYW5zZm9ybS5yZWR1Y2UoKGN1cnJlbnRWYWx1ZSwgdHJhbnNmb3JtTmFtZSkgPT4ge1xuICAgICAgICAgICAgICBpZiAodGhpcy50cmFuc2Zvcm1lcnNbdHJhbnNmb3JtTmFtZV0pIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy50cmFuc2Zvcm1lcnNbdHJhbnNmb3JtTmFtZV0oY3VycmVudFZhbHVlLCBydWxlLnRyYW5zZm9ybVBhcmFtcyk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgcmV0dXJuIGN1cnJlbnRWYWx1ZTtcbiAgICAgICAgICAgIH0sIHZhbHVlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgXG4gICAgICAgIC8vIFNldCB2YWx1ZSBpbiB0YXJnZXQgcGF0aFxuICAgICAgICB0aGlzLl9zZXRWYWx1ZUJ5UGF0aChyZXN1bHQsIHJ1bGUudGFyZ2V0LCB0cmFuc2Zvcm1lZFZhbHVlKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIC8vIExvZyBlcnJvciBidXQgY29udGludWUgd2l0aCBvdGhlciBydWxlc1xuICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBhcHBseWluZyB0cmFuc2Zvcm1hdGlvbiBydWxlOiAke2Vycm9yLm1lc3NhZ2V9YCwgcnVsZSk7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIC8vIENhY2hlIHJlc3VsdCBpZiBjYWNoaW5nIGlzIGVuYWJsZWRcbiAgICBpZiAodGhpcy5vcHRpb25zLmVuYWJsZUNhY2hpbmcpIHtcbiAgICAgIGNvbnN0IGNhY2hlS2V5ID0gdGhpcy5fZ2VuZXJhdGVDYWNoZUtleShkYXRhLCBydWxlcyk7XG4gICAgICB0aGlzLmNhY2hlLnNldChjYWNoZUtleSwgcmVzdWx0KTtcbiAgICB9XG4gICAgXG4gICAgLy8gUmVjb3JkIG1ldHJpY3MgaWYgZW5hYmxlZFxuICAgIGlmICh0aGlzLm9wdGlvbnMuZW5hYmxlTWV0cmljcykge1xuICAgICAgY29uc3QgZW5kVGltZSA9IHBlcmZvcm1hbmNlLm5vdygpO1xuICAgICAgY29uc3QgZHVyYXRpb24gPSBlbmRUaW1lIC0gc3RhcnRUaW1lO1xuICAgICAgXG4gICAgICAvLyBTdG9yZSBtZXRyaWNzIGZvciB0aGlzIHRyYW5zZm9ybWF0aW9uXG4gICAgICB0aGlzLl9yZWNvcmRNZXRyaWNzKGR1cmF0aW9uLCBydWxlcy5sZW5ndGgpO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIFxuICAvKipcbiAgICogVHJhbnNmb3JtIGJhdGNoIGRhdGEgYWNjb3JkaW5nIHRvIHRoZSBwcm92aWRlZCBydWxlc1xuICAgKiBAcGFyYW0ge09iamVjdH0gZGF0YSAtIFNvdXJjZSBkYXRhIGNvbnRhaW5pbmcgYXJyYXlzXG4gICAqIEBwYXJhbSB7QXJyYXl9IHJ1bGVzIC0gVHJhbnNmb3JtYXRpb24gcnVsZXMgd2l0aCBhcnJheSBwYXRoc1xuICAgKiBAcmV0dXJucyB7T2JqZWN0fSAtIFRyYW5zZm9ybWVkIGRhdGFcbiAgICovXG4gIGJhdGNoVHJhbnNmb3JtKGRhdGEsIHJ1bGVzKSB7XG4gICAgY29uc3Qgc3RhcnRUaW1lID0gdGhpcy5vcHRpb25zLmVuYWJsZU1ldHJpY3MgPyBwZXJmb3JtYW5jZS5ub3coKSA6IDA7XG4gICAgXG4gICAgLy8gRmluZCBhcnJheSBwYXRocyBpbiBydWxlc1xuICAgIGNvbnN0IGFycmF5UGF0aHMgPSBydWxlcy5maWx0ZXIocnVsZSA9PiBydWxlLnNvdXJjZS5pbmNsdWRlcygnWypdJykpO1xuICAgIFxuICAgIC8vIElmIG5vIGFycmF5IHBhdGhzLCB1c2UgcmVndWxhciB0cmFuc2Zvcm1cbiAgICBpZiAoYXJyYXlQYXRocy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiB0aGlzLnRyYW5zZm9ybShkYXRhLCBydWxlcyk7XG4gICAgfVxuICAgIFxuICAgIC8vIFBhcnNlIHRoZSBzb3VyY2UgYXJyYXkgcGF0aCBmcm9tIHRoZSBmaXJzdCBhcnJheSBydWxlXG4gICAgY29uc3Qgc291cmNlQXJyYXlQYXRoID0gYXJyYXlQYXRoc1swXS5zb3VyY2Uuc3BsaXQoJ1sqXScpWzBdO1xuICAgIGNvbnN0IHNvdXJjZUFycmF5ID0gdGhpcy5fZ2V0VmFsdWVCeVBhdGgoZGF0YSwgc291cmNlQXJyYXlQYXRoKTtcbiAgICBcbiAgICBpZiAoIUFycmF5LmlzQXJyYXkoc291cmNlQXJyYXkpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFBhdGggJHtzb3VyY2VBcnJheVBhdGh9IGRvZXMgbm90IHBvaW50IHRvIGFuIGFycmF5YCk7XG4gICAgfVxuICAgIFxuICAgIC8vIENyZWF0ZSByZXN1bHQgb2JqZWN0IHdpdGggdHJhbnNmb3JtZWQgaXRlbXNcbiAgICBjb25zdCByZXN1bHQgPSB7fTtcbiAgICBjb25zdCBpdGVtcyA9IFtdO1xuICAgIFxuICAgIC8vIFRyYW5zZm9ybSBlYWNoIGl0ZW0gaW4gdGhlIGFycmF5XG4gICAgZm9yIChjb25zdCBpdGVtIG9mIHNvdXJjZUFycmF5KSB7XG4gICAgICAvLyBDcmVhdGUgaXRlbS1zcGVjaWZpYyBydWxlcyBieSByZXBsYWNpbmcgYXJyYXkgbm90YXRpb25cbiAgICAgIGNvbnN0IGl0ZW1SdWxlcyA9IHJ1bGVzLm1hcChydWxlID0+IHtcbiAgICAgICAgaWYgKCFydWxlLnNvdXJjZS5pbmNsdWRlcygnWypdJykpIHtcbiAgICAgICAgICByZXR1cm4gcnVsZTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzb3VyY2U6IHJ1bGUuc291cmNlLnJlcGxhY2UoYCR7c291cmNlQXJyYXlQYXRofVsqXWAsICcnKS5yZXBsYWNlKC9eXFwuLywgJycpLFxuICAgICAgICAgIHRhcmdldDogcnVsZS50YXJnZXQucmVwbGFjZSgvXFxbLipcXF0vLCAnJyksXG4gICAgICAgICAgdHJhbnNmb3JtOiBydWxlLnRyYW5zZm9ybSxcbiAgICAgICAgICB0cmFuc2Zvcm1QYXJhbXM6IHJ1bGUudHJhbnNmb3JtUGFyYW1zXG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gVHJhbnNmb3JtIHRoZSBpdGVtXG4gICAgICBjb25zdCB0cmFuc2Zvcm1lZEl0ZW0gPSB0aGlzLnRyYW5zZm9ybSh7IGl0ZW0gfSwgaXRlbVJ1bGVzKTtcbiAgICAgIGl0ZW1zLnB1c2godHJhbnNmb3JtZWRJdGVtKTtcbiAgICB9XG4gICAgXG4gICAgLy8gU2V0IHRoZSBpdGVtcyBpbiB0aGUgcmVzdWx0XG4gICAgY29uc3QgdGFyZ2V0QXJyYXlQYXRoID0gYXJyYXlQYXRoc1swXS50YXJnZXQuc3BsaXQoJ1sqXScpWzBdO1xuICAgIHRoaXMuX3NldFZhbHVlQnlQYXRoKHJlc3VsdCwgdGFyZ2V0QXJyYXlQYXRoLCBpdGVtcyk7XG4gICAgXG4gICAgLy8gUmVjb3JkIG1ldHJpY3MgaWYgZW5hYmxlZFxuICAgIGlmICh0aGlzLm9wdGlvbnMuZW5hYmxlTWV0cmljcykge1xuICAgICAgY29uc3QgZW5kVGltZSA9IHBlcmZvcm1hbmNlLm5vdygpO1xuICAgICAgY29uc3QgZHVyYXRpb24gPSBlbmRUaW1lIC0gc3RhcnRUaW1lO1xuICAgICAgXG4gICAgICAvLyBTdG9yZSBtZXRyaWNzIGZvciB0aGlzIGJhdGNoIHRyYW5zZm9ybWF0aW9uXG4gICAgICB0aGlzLl9yZWNvcmRNZXRyaWNzKGR1cmF0aW9uLCBydWxlcy5sZW5ndGggKiBzb3VyY2VBcnJheS5sZW5ndGgsIHRydWUpO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIFxuICAvKipcbiAgICogR2V0IG1ldHJpY3MgZm9yIHRoZSB0cmFuc2Zvcm1hdGlvbiBlbmdpbmVcbiAgICogQHJldHVybnMge09iamVjdH0gLSBNZXRyaWNzIG9iamVjdFxuICAgKi9cbiAgZ2V0TWV0cmljcygpIHtcbiAgICByZXR1cm4gdGhpcy5tZXRyaWNzO1xuICB9XG4gIFxuICAvKipcbiAgICogQ2xlYXIgdGhlIHRyYW5zZm9ybWF0aW9uIGNhY2hlXG4gICAqL1xuICBjbGVhckNhY2hlKCkge1xuICAgIHRoaXMuY2FjaGUuY2xlYXIoKTtcbiAgfVxuICBcbiAgLyoqXG4gICAqIEdlbmVyYXRlIGEgY2FjaGUga2V5IGZvciB0aGUgZGF0YSBhbmQgcnVsZXNcbiAgICogQHByaXZhdGVcbiAgICovXG4gIF9nZW5lcmF0ZUNhY2hlS2V5KGRhdGEsIHJ1bGVzKSB7XG4gICAgLy8gU2ltcGxlIGltcGxlbWVudGF0aW9uIC0gaW4gcHJvZHVjdGlvbiB3b3VsZCB1c2UgYSBtb3JlIGVmZmljaWVudCBhcHByb2FjaFxuICAgIHJldHVybiBKU09OLnN0cmluZ2lmeSh7IGRhdGEsIHJ1bGVzIH0pO1xuICB9XG4gIFxuICAvKipcbiAgICogUmVjb3JkIG1ldHJpY3MgZm9yIGEgdHJhbnNmb3JtYXRpb25cbiAgICogQHByaXZhdGVcbiAgICovXG4gIF9yZWNvcmRNZXRyaWNzKGR1cmF0aW9uLCBydWxlc0NvdW50LCBpc0JhdGNoID0gZmFsc2UpIHtcbiAgICBpZiAoIXRoaXMubWV0cmljcykge1xuICAgICAgdGhpcy5tZXRyaWNzID0ge1xuICAgICAgICB0cmFuc2Zvcm1hdGlvbnM6IDAsXG4gICAgICAgIHRvdGFsRHVyYXRpb246IDAsXG4gICAgICAgIGF2ZXJhZ2VEdXJhdGlvbjogMCxcbiAgICAgICAgYmF0Y2hUcmFuc2Zvcm1hdGlvbnM6IDAsXG4gICAgICAgIGJhdGNoVG90YWxEdXJhdGlvbjogMCxcbiAgICAgICAgYmF0Y2hBdmVyYWdlRHVyYXRpb246IDAsXG4gICAgICAgIHJ1bGVzQXBwbGllZDogMFxuICAgICAgfTtcbiAgICB9XG4gICAgXG4gICAgaWYgKGlzQmF0Y2gpIHtcbiAgICAgIHRoaXMubWV0cmljcy5iYXRjaFRyYW5zZm9ybWF0aW9ucysrO1xuICAgICAgdGhpcy5tZXRyaWNzLmJhdGNoVG90YWxEdXJhdGlvbiArPSBkdXJhdGlvbjtcbiAgICAgIHRoaXMubWV0cmljcy5iYXRjaEF2ZXJhZ2VEdXJhdGlvbiA9IHRoaXMubWV0cmljcy5iYXRjaFRvdGFsRHVyYXRpb24gLyB0aGlzLm1ldHJpY3MuYmF0Y2hUcmFuc2Zvcm1hdGlvbnM7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMubWV0cmljcy50cmFuc2Zvcm1hdGlvbnMrKztcbiAgICAgIHRoaXMubWV0cmljcy50b3RhbER1cmF0aW9uICs9IGR1cmF0aW9uO1xuICAgICAgdGhpcy5tZXRyaWNzLmF2ZXJhZ2VEdXJhdGlvbiA9IHRoaXMubWV0cmljcy50b3RhbER1cmF0aW9uIC8gdGhpcy5tZXRyaWNzLnRyYW5zZm9ybWF0aW9ucztcbiAgICB9XG4gICAgXG4gICAgdGhpcy5tZXRyaWNzLnJ1bGVzQXBwbGllZCArPSBydWxlc0NvdW50O1xuICB9XG4gIFxuICAvKipcbiAgICogR2V0IGEgdmFsdWUgZnJvbSBhbiBvYmplY3QgYnkgcGF0aFxuICAgKiBAcHJpdmF0ZVxuICAgKi9cbiAgX2dldFZhbHVlQnlQYXRoKG9iaiwgcGF0aCkge1xuICAgIGlmICghcGF0aCkgcmV0dXJuIG9iajtcbiAgICBcbiAgICAvLyBIYW5kbGUgYXJyYXkgbm90YXRpb25cbiAgICBjb25zdCBub3JtYWxpemVkUGF0aCA9IHBhdGgucmVwbGFjZSgvXFxbKFxcdyspXFxdL2csICcuJDEnKTtcbiAgICBjb25zdCBwYXJ0cyA9IG5vcm1hbGl6ZWRQYXRoLnNwbGl0KCcuJyk7XG4gICAgXG4gICAgbGV0IGN1cnJlbnQgPSBvYmo7XG4gICAgZm9yIChjb25zdCBwYXJ0IG9mIHBhcnRzKSB7XG4gICAgICBpZiAocGFydCA9PT0gJycpIGNvbnRpbnVlO1xuICAgICAgaWYgKGN1cnJlbnQgPT09IG51bGwgfHwgY3VycmVudCA9PT0gdW5kZWZpbmVkKSByZXR1cm4gdW5kZWZpbmVkO1xuICAgICAgY3VycmVudCA9IGN1cnJlbnRbcGFydF07XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBjdXJyZW50O1xuICB9XG4gIFxuICAvKipcbiAgICogU2V0IGEgdmFsdWUgaW4gYW4gb2JqZWN0IGJ5IHBhdGhcbiAgICogQHByaXZhdGVcbiAgICovXG4gIF9zZXRWYWx1ZUJ5UGF0aChvYmosIHBhdGgsIHZhbHVlKSB7XG4gICAgaWYgKCFwYXRoKSByZXR1cm47XG4gICAgXG4gICAgLy8gSGFuZGxlIGFycmF5IG5vdGF0aW9uXG4gICAgY29uc3Qgbm9ybWFsaXplZFBhdGggPSBwYXRoLnJlcGxhY2UoL1xcWyhcXHcrKVxcXS9nLCAnLiQxJyk7XG4gICAgY29uc3QgcGFydHMgPSBub3JtYWxpemVkUGF0aC5zcGxpdCgnLicpO1xuICAgIFxuICAgIGxldCBjdXJyZW50ID0gb2JqO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcGFydHMubGVuZ3RoIC0gMTsgaSsrKSB7XG4gICAgICBjb25zdCBwYXJ0ID0gcGFydHNbaV07XG4gICAgICBpZiAocGFydCA9PT0gJycpIGNvbnRpbnVlO1xuICAgICAgXG4gICAgICBpZiAoIShwYXJ0IGluIGN1cnJlbnQpKSB7XG4gICAgICAgIC8vIENyZWF0ZSBvYmplY3Qgb3IgYXJyYXkgYmFzZWQgb24gbmV4dCBwYXJ0XG4gICAgICAgIGN1cnJlbnRbcGFydF0gPSBwYXJ0c1tpICsgMV0ubWF0Y2goL15cXGQrJC8pID8gW10gOiB7fTtcbiAgICAgIH1cbiAgICAgIGN1cnJlbnQgPSBjdXJyZW50W3BhcnRdO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBsYXN0UGFydCA9IHBhcnRzW3BhcnRzLmxlbmd0aCAtIDFdO1xuICAgIGlmIChsYXN0UGFydCAhPT0gJycpIHtcbiAgICAgIGN1cnJlbnRbbGFzdFBhcnRdID0gdmFsdWU7XG4gICAgfVxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gVHJhbnNmb3JtYXRpb25FbmdpbmU7XG4iXSwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxNQUFNO0VBQUVBO0FBQVksQ0FBQyxHQUFHQyxPQUFPLENBQUMsWUFBWSxDQUFDO0FBRTdDLE1BQU1DLG9CQUFvQixDQUFDO0VBQ3pCQyxXQUFXQSxDQUFDQyxPQUFPLEdBQUcsQ0FBQyxDQUFDLEVBQUU7SUFDeEIsSUFBSSxDQUFDQSxPQUFPLEdBQUc7TUFDYkMsYUFBYSxFQUFFLElBQUk7TUFDbkJDLGFBQWEsRUFBRSxJQUFJO01BQ25CLEdBQUdGO0lBQ0wsQ0FBQzs7SUFFRDtJQUNBLElBQUksQ0FBQ0csWUFBWSxHQUFHO01BQ2xCO01BQ0FDLFNBQVMsRUFBR0MsS0FBSyxJQUFLLE9BQU9BLEtBQUssS0FBSyxRQUFRLEdBQUdBLEtBQUssQ0FBQ0MsV0FBVyxDQUFDLENBQUMsR0FBR0QsS0FBSztNQUM3RUUsU0FBUyxFQUFHRixLQUFLLElBQUssT0FBT0EsS0FBSyxLQUFLLFFBQVEsR0FBR0EsS0FBSyxDQUFDRyxXQUFXLENBQUMsQ0FBQyxHQUFHSCxLQUFLO01BQzdFSSxJQUFJLEVBQUdKLEtBQUssSUFBSyxPQUFPQSxLQUFLLEtBQUssUUFBUSxHQUFHQSxLQUFLLENBQUNJLElBQUksQ0FBQyxDQUFDLEdBQUdKLEtBQUs7TUFFakU7TUFDQUssU0FBUyxFQUFHTCxLQUFLLElBQUssT0FBT0EsS0FBSyxLQUFLLFFBQVEsR0FBRyxJQUFJTSxJQUFJLENBQUNOLEtBQUssQ0FBQyxDQUFDTyxPQUFPLENBQUMsQ0FBQyxHQUFHUCxLQUFLO01BQ25GUSxTQUFTLEVBQUdSLEtBQUssSUFBSyxPQUFPQSxLQUFLLEtBQUssUUFBUSxHQUFHLElBQUlNLElBQUksQ0FBQ04sS0FBSyxDQUFDLENBQUNTLFdBQVcsQ0FBQyxDQUFDLEdBQUdULEtBQUs7TUFFdkY7TUFDQVUsUUFBUSxFQUFHVixLQUFLLElBQUssQ0FBQ1csS0FBSyxDQUFDQyxVQUFVLENBQUNaLEtBQUssQ0FBQyxDQUFDLEdBQUdZLFVBQVUsQ0FBQ1osS0FBSyxDQUFDLEdBQUdBLEtBQUs7TUFFMUU7TUFDQWEsTUFBTSxFQUFHYixLQUFLLElBQUssT0FBT0EsS0FBSyxLQUFLLFFBQVEsR0FBR2MsSUFBSSxDQUFDQyxTQUFTLENBQUNmLEtBQUssQ0FBQyxHQUFHQSxLQUFLO01BQzVFZ0IsUUFBUSxFQUFHaEIsS0FBSyxJQUFLO1FBQ25CLElBQUksT0FBT0EsS0FBSyxLQUFLLFFBQVEsRUFBRSxPQUFPQSxLQUFLO1FBQzNDLElBQUk7VUFDRixPQUFPYyxJQUFJLENBQUNHLEtBQUssQ0FBQ2pCLEtBQUssQ0FBQztRQUMxQixDQUFDLENBQUMsT0FBT2tCLENBQUMsRUFBRTtVQUNWLE9BQU9sQixLQUFLO1FBQ2Q7TUFDRixDQUFDO01BRUQ7TUFDQW1CLElBQUksRUFBRUEsQ0FBQ25CLEtBQUssRUFBRW9CLFNBQVMsR0FBRyxHQUFHLEtBQUtDLEtBQUssQ0FBQ0MsT0FBTyxDQUFDdEIsS0FBSyxDQUFDLEdBQUdBLEtBQUssQ0FBQ21CLElBQUksQ0FBQ0MsU0FBUyxDQUFDLEdBQUdwQixLQUFLO01BQ3RGdUIsS0FBSyxFQUFFQSxDQUFDdkIsS0FBSyxFQUFFb0IsU0FBUyxHQUFHLEdBQUcsS0FBSyxPQUFPcEIsS0FBSyxLQUFLLFFBQVEsR0FBR0EsS0FBSyxDQUFDdUIsS0FBSyxDQUFDSCxTQUFTLENBQUMsR0FBR3BCLEtBQUs7TUFFN0Y7TUFDQXdCLElBQUksRUFBRUEsQ0FBQ3hCLEtBQUssRUFBRXlCLE9BQU8sR0FBRyxNQUFNLEtBQUs7UUFDakMsSUFBSSxPQUFPekIsS0FBSyxLQUFLLFFBQVEsRUFBRSxPQUFPQSxLQUFLO1FBQzNDLE9BQU9BLEtBQUssQ0FBQzBCLFNBQVMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUdELE9BQU87TUFDeEM7SUFDRixDQUFDOztJQUVEO0lBQ0EsSUFBSSxDQUFDRSxLQUFLLEdBQUcsSUFBSUMsR0FBRyxDQUFDLENBQUM7RUFDeEI7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7QUFDQTtFQUNFQyxtQkFBbUJBLENBQUNDLElBQUksRUFBRUMsV0FBVyxFQUFFO0lBQ3JDLElBQUksT0FBT0EsV0FBVyxLQUFLLFVBQVUsRUFBRTtNQUNyQyxNQUFNLElBQUlDLEtBQUssQ0FBQyxnQ0FBZ0MsQ0FBQztJQUNuRDtJQUVBLElBQUksQ0FBQ2xDLFlBQVksQ0FBQ2dDLElBQUksQ0FBQyxHQUFHQyxXQUFXO0VBQ3ZDOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtFQUNFRSxTQUFTQSxDQUFDQyxJQUFJLEVBQUVDLEtBQUssRUFBRTtJQUNyQixNQUFNQyxTQUFTLEdBQUcsSUFBSSxDQUFDekMsT0FBTyxDQUFDQyxhQUFhLEdBQUdMLFdBQVcsQ0FBQzhDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQzs7SUFFcEU7SUFDQSxJQUFJLElBQUksQ0FBQzFDLE9BQU8sQ0FBQ0UsYUFBYSxFQUFFO01BQzlCLE1BQU15QyxRQUFRLEdBQUcsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ0wsSUFBSSxFQUFFQyxLQUFLLENBQUM7TUFDcEQsTUFBTUssWUFBWSxHQUFHLElBQUksQ0FBQ2IsS0FBSyxDQUFDYyxHQUFHLENBQUNILFFBQVEsQ0FBQztNQUU3QyxJQUFJRSxZQUFZLEVBQUU7UUFDaEIsT0FBT0EsWUFBWTtNQUNyQjtJQUNGO0lBRUEsTUFBTUUsTUFBTSxHQUFHLENBQUMsQ0FBQzs7SUFFakI7SUFDQSxLQUFLLE1BQU1DLElBQUksSUFBSVIsS0FBSyxFQUFFO01BQ3hCLElBQUk7UUFDRjtRQUNBLE1BQU1uQyxLQUFLLEdBQUcsSUFBSSxDQUFDNEMsZUFBZSxDQUFDVixJQUFJLEVBQUVTLElBQUksQ0FBQ0UsTUFBTSxDQUFDOztRQUVyRDtRQUNBLElBQUlDLGdCQUFnQixHQUFHOUMsS0FBSztRQUM1QixJQUFJMkMsSUFBSSxDQUFDVixTQUFTLEVBQUU7VUFDbEIsSUFBSSxPQUFPVSxJQUFJLENBQUNWLFNBQVMsS0FBSyxRQUFRLElBQUksSUFBSSxDQUFDbkMsWUFBWSxDQUFDNkMsSUFBSSxDQUFDVixTQUFTLENBQUMsRUFBRTtZQUMzRTtZQUNBYSxnQkFBZ0IsR0FBRyxJQUFJLENBQUNoRCxZQUFZLENBQUM2QyxJQUFJLENBQUNWLFNBQVMsQ0FBQyxDQUFDakMsS0FBSyxFQUFFMkMsSUFBSSxDQUFDSSxlQUFlLENBQUM7VUFDbkYsQ0FBQyxNQUFNLElBQUkxQixLQUFLLENBQUNDLE9BQU8sQ0FBQ3FCLElBQUksQ0FBQ1YsU0FBUyxDQUFDLEVBQUU7WUFDeEM7WUFDQWEsZ0JBQWdCLEdBQUdILElBQUksQ0FBQ1YsU0FBUyxDQUFDZSxNQUFNLENBQUMsQ0FBQ0MsWUFBWSxFQUFFQyxhQUFhLEtBQUs7Y0FDeEUsSUFBSSxJQUFJLENBQUNwRCxZQUFZLENBQUNvRCxhQUFhLENBQUMsRUFBRTtnQkFDcEMsT0FBTyxJQUFJLENBQUNwRCxZQUFZLENBQUNvRCxhQUFhLENBQUMsQ0FBQ0QsWUFBWSxFQUFFTixJQUFJLENBQUNJLGVBQWUsQ0FBQztjQUM3RTtjQUNBLE9BQU9FLFlBQVk7WUFDckIsQ0FBQyxFQUFFakQsS0FBSyxDQUFDO1VBQ1g7UUFDRjs7UUFFQTtRQUNBLElBQUksQ0FBQ21ELGVBQWUsQ0FBQ1QsTUFBTSxFQUFFQyxJQUFJLENBQUNTLE1BQU0sRUFBRU4sZ0JBQWdCLENBQUM7TUFDN0QsQ0FBQyxDQUFDLE9BQU9PLEtBQUssRUFBRTtRQUNkO1FBQ0FDLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLHVDQUF1Q0EsS0FBSyxDQUFDRSxPQUFPLEVBQUUsRUFBRVosSUFBSSxDQUFDO01BQzdFO0lBQ0Y7O0lBRUE7SUFDQSxJQUFJLElBQUksQ0FBQ2hELE9BQU8sQ0FBQ0UsYUFBYSxFQUFFO01BQzlCLE1BQU15QyxRQUFRLEdBQUcsSUFBSSxDQUFDQyxpQkFBaUIsQ0FBQ0wsSUFBSSxFQUFFQyxLQUFLLENBQUM7TUFDcEQsSUFBSSxDQUFDUixLQUFLLENBQUM2QixHQUFHLENBQUNsQixRQUFRLEVBQUVJLE1BQU0sQ0FBQztJQUNsQzs7SUFFQTtJQUNBLElBQUksSUFBSSxDQUFDL0MsT0FBTyxDQUFDQyxhQUFhLEVBQUU7TUFDOUIsTUFBTTZELE9BQU8sR0FBR2xFLFdBQVcsQ0FBQzhDLEdBQUcsQ0FBQyxDQUFDO01BQ2pDLE1BQU1xQixRQUFRLEdBQUdELE9BQU8sR0FBR3JCLFNBQVM7O01BRXBDO01BQ0EsSUFBSSxDQUFDdUIsY0FBYyxDQUFDRCxRQUFRLEVBQUV2QixLQUFLLENBQUN5QixNQUFNLENBQUM7SUFDN0M7SUFFQSxPQUFPbEIsTUFBTTtFQUNmOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtFQUNFbUIsY0FBY0EsQ0FBQzNCLElBQUksRUFBRUMsS0FBSyxFQUFFO0lBQzFCLE1BQU1DLFNBQVMsR0FBRyxJQUFJLENBQUN6QyxPQUFPLENBQUNDLGFBQWEsR0FBR0wsV0FBVyxDQUFDOEMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDOztJQUVwRTtJQUNBLE1BQU15QixVQUFVLEdBQUczQixLQUFLLENBQUM0QixNQUFNLENBQUNwQixJQUFJLElBQUlBLElBQUksQ0FBQ0UsTUFBTSxDQUFDbUIsUUFBUSxDQUFDLEtBQUssQ0FBQyxDQUFDOztJQUVwRTtJQUNBLElBQUlGLFVBQVUsQ0FBQ0YsTUFBTSxLQUFLLENBQUMsRUFBRTtNQUMzQixPQUFPLElBQUksQ0FBQzNCLFNBQVMsQ0FBQ0MsSUFBSSxFQUFFQyxLQUFLLENBQUM7SUFDcEM7O0lBRUE7SUFDQSxNQUFNOEIsZUFBZSxHQUFHSCxVQUFVLENBQUMsQ0FBQyxDQUFDLENBQUNqQixNQUFNLENBQUN0QixLQUFLLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQzVELE1BQU0yQyxXQUFXLEdBQUcsSUFBSSxDQUFDdEIsZUFBZSxDQUFDVixJQUFJLEVBQUUrQixlQUFlLENBQUM7SUFFL0QsSUFBSSxDQUFDNUMsS0FBSyxDQUFDQyxPQUFPLENBQUM0QyxXQUFXLENBQUMsRUFBRTtNQUMvQixNQUFNLElBQUlsQyxLQUFLLENBQUMsUUFBUWlDLGVBQWUsNkJBQTZCLENBQUM7SUFDdkU7O0lBRUE7SUFDQSxNQUFNdkIsTUFBTSxHQUFHLENBQUMsQ0FBQztJQUNqQixNQUFNeUIsS0FBSyxHQUFHLEVBQUU7O0lBRWhCO0lBQ0EsS0FBSyxNQUFNQyxJQUFJLElBQUlGLFdBQVcsRUFBRTtNQUM5QjtNQUNBLE1BQU1HLFNBQVMsR0FBR2xDLEtBQUssQ0FBQ21DLEdBQUcsQ0FBQzNCLElBQUksSUFBSTtRQUNsQyxJQUFJLENBQUNBLElBQUksQ0FBQ0UsTUFBTSxDQUFDbUIsUUFBUSxDQUFDLEtBQUssQ0FBQyxFQUFFO1VBQ2hDLE9BQU9yQixJQUFJO1FBQ2I7UUFFQSxPQUFPO1VBQ0xFLE1BQU0sRUFBRUYsSUFBSSxDQUFDRSxNQUFNLENBQUMwQixPQUFPLENBQUMsR0FBR04sZUFBZSxLQUFLLEVBQUUsRUFBRSxDQUFDLENBQUNNLE9BQU8sQ0FBQyxLQUFLLEVBQUUsRUFBRSxDQUFDO1VBQzNFbkIsTUFBTSxFQUFFVCxJQUFJLENBQUNTLE1BQU0sQ0FBQ21CLE9BQU8sQ0FBQyxRQUFRLEVBQUUsRUFBRSxDQUFDO1VBQ3pDdEMsU0FBUyxFQUFFVSxJQUFJLENBQUNWLFNBQVM7VUFDekJjLGVBQWUsRUFBRUosSUFBSSxDQUFDSTtRQUN4QixDQUFDO01BQ0gsQ0FBQyxDQUFDOztNQUVGO01BQ0EsTUFBTXlCLGVBQWUsR0FBRyxJQUFJLENBQUN2QyxTQUFTLENBQUM7UUFBRW1DO01BQUssQ0FBQyxFQUFFQyxTQUFTLENBQUM7TUFDM0RGLEtBQUssQ0FBQ00sSUFBSSxDQUFDRCxlQUFlLENBQUM7SUFDN0I7O0lBRUE7SUFDQSxNQUFNRSxlQUFlLEdBQUdaLFVBQVUsQ0FBQyxDQUFDLENBQUMsQ0FBQ1YsTUFBTSxDQUFDN0IsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUM1RCxJQUFJLENBQUM0QixlQUFlLENBQUNULE1BQU0sRUFBRWdDLGVBQWUsRUFBRVAsS0FBSyxDQUFDOztJQUVwRDtJQUNBLElBQUksSUFBSSxDQUFDeEUsT0FBTyxDQUFDQyxhQUFhLEVBQUU7TUFDOUIsTUFBTTZELE9BQU8sR0FBR2xFLFdBQVcsQ0FBQzhDLEdBQUcsQ0FBQyxDQUFDO01BQ2pDLE1BQU1xQixRQUFRLEdBQUdELE9BQU8sR0FBR3JCLFNBQVM7O01BRXBDO01BQ0EsSUFBSSxDQUFDdUIsY0FBYyxDQUFDRCxRQUFRLEVBQUV2QixLQUFLLENBQUN5QixNQUFNLEdBQUdNLFdBQVcsQ0FBQ04sTUFBTSxFQUFFLElBQUksQ0FBQztJQUN4RTtJQUVBLE9BQU9sQixNQUFNO0VBQ2Y7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7RUFDRWlDLFVBQVVBLENBQUEsRUFBRztJQUNYLE9BQU8sSUFBSSxDQUFDQyxPQUFPO0VBQ3JCOztFQUVBO0FBQ0Y7QUFDQTtFQUNFQyxVQUFVQSxDQUFBLEVBQUc7SUFDWCxJQUFJLENBQUNsRCxLQUFLLENBQUNtRCxLQUFLLENBQUMsQ0FBQztFQUNwQjs7RUFFQTtBQUNGO0FBQ0E7QUFDQTtFQUNFdkMsaUJBQWlCQSxDQUFDTCxJQUFJLEVBQUVDLEtBQUssRUFBRTtJQUM3QjtJQUNBLE9BQU9yQixJQUFJLENBQUNDLFNBQVMsQ0FBQztNQUFFbUIsSUFBSTtNQUFFQztJQUFNLENBQUMsQ0FBQztFQUN4Qzs7RUFFQTtBQUNGO0FBQ0E7QUFDQTtFQUNFd0IsY0FBY0EsQ0FBQ0QsUUFBUSxFQUFFcUIsVUFBVSxFQUFFQyxPQUFPLEdBQUcsS0FBSyxFQUFFO0lBQ3BELElBQUksQ0FBQyxJQUFJLENBQUNKLE9BQU8sRUFBRTtNQUNqQixJQUFJLENBQUNBLE9BQU8sR0FBRztRQUNiSyxlQUFlLEVBQUUsQ0FBQztRQUNsQkMsYUFBYSxFQUFFLENBQUM7UUFDaEJDLGVBQWUsRUFBRSxDQUFDO1FBQ2xCQyxvQkFBb0IsRUFBRSxDQUFDO1FBQ3ZCQyxrQkFBa0IsRUFBRSxDQUFDO1FBQ3JCQyxvQkFBb0IsRUFBRSxDQUFDO1FBQ3ZCQyxZQUFZLEVBQUU7TUFDaEIsQ0FBQztJQUNIO0lBRUEsSUFBSVAsT0FBTyxFQUFFO01BQ1gsSUFBSSxDQUFDSixPQUFPLENBQUNRLG9CQUFvQixFQUFFO01BQ25DLElBQUksQ0FBQ1IsT0FBTyxDQUFDUyxrQkFBa0IsSUFBSTNCLFFBQVE7TUFDM0MsSUFBSSxDQUFDa0IsT0FBTyxDQUFDVSxvQkFBb0IsR0FBRyxJQUFJLENBQUNWLE9BQU8sQ0FBQ1Msa0JBQWtCLEdBQUcsSUFBSSxDQUFDVCxPQUFPLENBQUNRLG9CQUFvQjtJQUN6RyxDQUFDLE1BQU07TUFDTCxJQUFJLENBQUNSLE9BQU8sQ0FBQ0ssZUFBZSxFQUFFO01BQzlCLElBQUksQ0FBQ0wsT0FBTyxDQUFDTSxhQUFhLElBQUl4QixRQUFRO01BQ3RDLElBQUksQ0FBQ2tCLE9BQU8sQ0FBQ08sZUFBZSxHQUFHLElBQUksQ0FBQ1AsT0FBTyxDQUFDTSxhQUFhLEdBQUcsSUFBSSxDQUFDTixPQUFPLENBQUNLLGVBQWU7SUFDMUY7SUFFQSxJQUFJLENBQUNMLE9BQU8sQ0FBQ1csWUFBWSxJQUFJUixVQUFVO0VBQ3pDOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0VBQ0VuQyxlQUFlQSxDQUFDNEMsR0FBRyxFQUFFQyxJQUFJLEVBQUU7SUFDekIsSUFBSSxDQUFDQSxJQUFJLEVBQUUsT0FBT0QsR0FBRzs7SUFFckI7SUFDQSxNQUFNRSxjQUFjLEdBQUdELElBQUksQ0FBQ2xCLE9BQU8sQ0FBQyxZQUFZLEVBQUUsS0FBSyxDQUFDO0lBQ3hELE1BQU1vQixLQUFLLEdBQUdELGNBQWMsQ0FBQ25FLEtBQUssQ0FBQyxHQUFHLENBQUM7SUFFdkMsSUFBSXFFLE9BQU8sR0FBR0osR0FBRztJQUNqQixLQUFLLE1BQU1LLElBQUksSUFBSUYsS0FBSyxFQUFFO01BQ3hCLElBQUlFLElBQUksS0FBSyxFQUFFLEVBQUU7TUFDakIsSUFBSUQsT0FBTyxLQUFLLElBQUksSUFBSUEsT0FBTyxLQUFLRSxTQUFTLEVBQUUsT0FBT0EsU0FBUztNQUMvREYsT0FBTyxHQUFHQSxPQUFPLENBQUNDLElBQUksQ0FBQztJQUN6QjtJQUVBLE9BQU9ELE9BQU87RUFDaEI7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7RUFDRXpDLGVBQWVBLENBQUNxQyxHQUFHLEVBQUVDLElBQUksRUFBRXpGLEtBQUssRUFBRTtJQUNoQyxJQUFJLENBQUN5RixJQUFJLEVBQUU7O0lBRVg7SUFDQSxNQUFNQyxjQUFjLEdBQUdELElBQUksQ0FBQ2xCLE9BQU8sQ0FBQyxZQUFZLEVBQUUsS0FBSyxDQUFDO0lBQ3hELE1BQU1vQixLQUFLLEdBQUdELGNBQWMsQ0FBQ25FLEtBQUssQ0FBQyxHQUFHLENBQUM7SUFFdkMsSUFBSXFFLE9BQU8sR0FBR0osR0FBRztJQUNqQixLQUFLLElBQUlPLENBQUMsR0FBRyxDQUFDLEVBQUVBLENBQUMsR0FBR0osS0FBSyxDQUFDL0IsTUFBTSxHQUFHLENBQUMsRUFBRW1DLENBQUMsRUFBRSxFQUFFO01BQ3pDLE1BQU1GLElBQUksR0FBR0YsS0FBSyxDQUFDSSxDQUFDLENBQUM7TUFDckIsSUFBSUYsSUFBSSxLQUFLLEVBQUUsRUFBRTtNQUVqQixJQUFJLEVBQUVBLElBQUksSUFBSUQsT0FBTyxDQUFDLEVBQUU7UUFDdEI7UUFDQUEsT0FBTyxDQUFDQyxJQUFJLENBQUMsR0FBR0YsS0FBSyxDQUFDSSxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUNDLEtBQUssQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLEdBQUcsQ0FBQyxDQUFDO01BQ3ZEO01BQ0FKLE9BQU8sR0FBR0EsT0FBTyxDQUFDQyxJQUFJLENBQUM7SUFDekI7SUFFQSxNQUFNSSxRQUFRLEdBQUdOLEtBQUssQ0FBQ0EsS0FBSyxDQUFDL0IsTUFBTSxHQUFHLENBQUMsQ0FBQztJQUN4QyxJQUFJcUMsUUFBUSxLQUFLLEVBQUUsRUFBRTtNQUNuQkwsT0FBTyxDQUFDSyxRQUFRLENBQUMsR0FBR2pHLEtBQUs7SUFDM0I7RUFDRjtBQUNGO0FBRUFrRyxNQUFNLENBQUNDLE9BQU8sR0FBRzFHLG9CQUFvQiIsImlnbm9yZUxpc3QiOltdfQ==