/**
 * Subject Request Service Tests
 * 
 * This file contains unit tests for the Subject Request service.
 */

// Mock the models module before importing the service
jest.mock('../../models', () => {
  // Create mock instances
  const mockSubjectRequest = {
    _id: 'mock-id-1',
    requestType: 'access',
    dataSubjectName: '<PERSON>',
    dataSubjectEmail: '<EMAIL>',
    requestDetails: 'I want to access my data',
    status: 'pending',
    save: jest.fn().mockResolvedValue({ 
      _id: 'mock-id-1',
      requestType: 'access',
      status: 'pending'
    }),
    toJSON: jest.fn().mockReturnValue({ _id: 'mock-id-1' })
  };

  // Create mock constructor
  const MockSubjectRequest = jest.fn(() => mockSubjectRequest);
  
  // Add static methods
  MockSubjectRequest.find = jest.fn().mockReturnValue({
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockResolvedValue([mockSubjectRequest])
  });
  
  MockSubjectRequest.findById = jest.fn().mockResolvedValue(mockSubjectRequest);
  MockSubjectRequest.countDocuments = jest.fn().mockResolvedValue(1);
  
  return {
    SubjectRequest: MockSubjectRequest
  };
});

// Mock the logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn()
  }
}));

// Import the service and mocked dependencies
const { subjectRequestService } = require('../../services');
const { SubjectRequest } = require('../../models');
const { logger } = require('../../utils/logger');

describe('Subject Request Service', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });
  
  describe('getAllRequests', () => {
    it('should return all requests with pagination', async () => {
      // Setup options
      const options = {
        page: 1,
        limit: 10,
        filter: { status: 'pending' },
        sort: { createdAt: -1 }
      };
      
      // Call the service method
      const result = await subjectRequestService.getAllRequests(options);
      
      // Verify the model methods were called correctly
      expect(SubjectRequest.find).toHaveBeenCalledWith(options.filter);
      expect(SubjectRequest.countDocuments).toHaveBeenCalledWith(options.filter);
      
      // Verify the result
      expect(result).toEqual({
        data: expect.any(Array),
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
      
      // Verify logging
      expect(logger.error).not.toHaveBeenCalled();
    });
    
    it('should use default options if not provided', async () => {
      // Call the service method with empty options
      const result = await subjectRequestService.getAllRequests({});
      
      // Verify the model methods were called with default values
      expect(SubjectRequest.find).toHaveBeenCalledWith({});
      expect(SubjectRequest.countDocuments).toHaveBeenCalledWith({});
      
      // Verify the result
      expect(result).toEqual({
        data: expect.any(Array),
        pagination: {
          total: 1,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });
    
    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      SubjectRequest.find.mockImplementationOnce(() => {
        throw error;
      });
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.getAllRequests({}))
        .rejects
        .toThrow('Database error');
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting subject requests'));
    });
  });
  
  describe('getRequestById', () => {
    it('should return a request by ID', async () => {
      // Call the service method
      const result = await subjectRequestService.getRequestById('mock-id-1');
      
      // Verify the model methods were called correctly
      expect(SubjectRequest.findById).toHaveBeenCalledWith('mock-id-1');
      
      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        _id: 'mock-id-1'
      }));
      
      // Verify logging
      expect(logger.error).not.toHaveBeenCalled();
    });
    
    it('should throw NotFoundError if the request is not found', async () => {
      // Setup model to return null
      SubjectRequest.findById.mockResolvedValueOnce(null);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.getRequestById('non-existent-id'))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data subject request not found'
        });
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting subject request by ID'));
    });
    
    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      SubjectRequest.findById.mockRejectedValueOnce(error);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.getRequestById('mock-id-1'))
        .rejects
        .toThrow('Database error');
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting subject request by ID'));
    });
  });
  
  describe('createRequest', () => {
    it('should create a new request', async () => {
      // Setup request data
      const requestData = {
        requestType: 'access',
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I want to access my data'
      };
      
      // Call the service method
      const result = await subjectRequestService.createRequest(requestData);
      
      // Verify the model constructor was called with the correct data
      expect(SubjectRequest).toHaveBeenCalledWith(requestData);
      
      // Verify save was called
      expect(SubjectRequest().save).toHaveBeenCalled();
      
      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        _id: 'mock-id-1'
      }));
      
      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Created new subject request'));
      expect(logger.error).not.toHaveBeenCalled();
    });
    
    it('should handle errors', async () => {
      // Setup request data
      const requestData = {
        requestType: 'access',
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        requestDetails: 'I want to access my data'
      };
      
      // Setup error
      const error = new Error('Database error');
      SubjectRequest().save.mockRejectedValueOnce(error);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.createRequest(requestData))
        .rejects
        .toThrow('Database error');
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error creating subject request'));
    });
  });
  
  describe('updateRequest', () => {
    it('should update a request', async () => {
      // Setup update data
      const updateData = {
        status: 'in-progress',
        assignedTo: 'user-456'
      };
      
      // Setup mock request object with properties to be updated
      const mockRequest = {
        _id: 'mock-id-1',
        status: 'pending',
        assignedTo: null,
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          status: 'in-progress',
          assignedTo: 'user-456'
        })
      };
      
      // Override the findById mock for this test
      SubjectRequest.findById.mockResolvedValueOnce(mockRequest);
      
      // Call the service method
      const result = await subjectRequestService.updateRequest('mock-id-1', updateData);
      
      // Verify the model methods were called correctly
      expect(SubjectRequest.findById).toHaveBeenCalledWith('mock-id-1');
      
      // Verify the request was updated correctly
      expect(mockRequest.status).toBe('in-progress');
      expect(mockRequest.assignedTo).toBe('user-456');
      
      // Verify save was called
      expect(mockRequest.save).toHaveBeenCalled();
      
      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        _id: 'mock-id-1',
        status: 'in-progress',
        assignedTo: 'user-456'
      }));
      
      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Updated subject request'));
      expect(logger.error).not.toHaveBeenCalled();
    });
    
    it('should throw NotFoundError if the request is not found', async () => {
      // Setup update data
      const updateData = {
        status: 'in-progress',
        assignedTo: 'user-456'
      };
      
      // Setup model to return null
      SubjectRequest.findById.mockResolvedValueOnce(null);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.updateRequest('non-existent-id', updateData))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data subject request not found'
        });
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting subject request by ID'));
    });
    
    it('should handle errors', async () => {
      // Setup update data
      const updateData = {
        status: 'in-progress',
        assignedTo: 'user-456'
      };
      
      // Setup error
      const error = new Error('Database error');
      SubjectRequest.findById.mockRejectedValueOnce(error);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.updateRequest('mock-id-1', updateData))
        .rejects
        .toThrow('Database error');
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error updating subject request'));
    });
  });
  
  describe('processRequest', () => {
    it('should process a request', async () => {
      // Setup mock request object
      const mockRequest = {
        _id: 'mock-id-1',
        status: 'pending',
        affectedSystems: [],
        save: jest.fn().mockResolvedValue({
          _id: 'mock-id-1',
          status: 'in-progress',
          affectedSystems: [
            {
              systemId: 'system-1',
              systemName: 'CRM System',
              status: 'in-progress',
              details: 'Processing data export'
            },
            {
              systemId: 'system-2',
              systemName: 'Marketing System',
              status: 'pending',
              details: 'Scheduled for processing'
            }
          ]
        })
      };
      
      // Override the findById mock for this test
      SubjectRequest.findById.mockResolvedValueOnce(mockRequest);
      
      // Call the service method
      const result = await subjectRequestService.processRequest('mock-id-1');
      
      // Verify the model methods were called correctly
      expect(SubjectRequest.findById).toHaveBeenCalledWith('mock-id-1');
      
      // Verify the request was updated correctly
      expect(mockRequest.status).toBe('in-progress');
      expect(mockRequest.affectedSystems).toHaveLength(2);
      
      // Verify save was called
      expect(mockRequest.save).toHaveBeenCalled();
      
      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        _id: 'mock-id-1',
        status: 'in-progress',
        affectedSystems: expect.arrayContaining([
          expect.objectContaining({
            systemId: 'system-1',
            systemName: 'CRM System'
          })
        ])
      }));
      
      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Processed subject request'));
      expect(logger.error).not.toHaveBeenCalled();
    });
    
    it('should throw NotFoundError if the request is not found', async () => {
      // Setup model to return null
      SubjectRequest.findById.mockResolvedValueOnce(null);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.processRequest('non-existent-id'))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data subject request not found'
        });
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting subject request by ID'));
    });
    
    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      SubjectRequest.findById.mockRejectedValueOnce(error);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.processRequest('mock-id-1'))
        .rejects
        .toThrow('Database error');
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error processing subject request'));
    });
  });
  
  describe('generateDataExport', () => {
    it('should generate a data export for a request', async () => {
      // Setup mock request object
      const mockRequest = {
        _id: 'mock-id-1',
        requestType: 'access',
        dataSubjectEmail: '<EMAIL>',
        status: 'in-progress'
      };
      
      // Override the findById mock for this test
      SubjectRequest.findById.mockResolvedValueOnce(mockRequest);
      
      // Mock Date.now to return a consistent value
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => 1234567890);
      
      try {
        // Call the service method
        const result = await subjectRequestService.generateDataExport('mock-id-1');
        
        // Verify the model methods were called correctly
        expect(SubjectRequest.findById).toHaveBeenCalledWith('mock-id-1');
        
        // Verify the result
        expect(result).toEqual(expect.objectContaining({
          id: 'mock-id-1',
          exportId: expect.stringContaining('export-'),
          exportUrl: expect.stringContaining('https://api.novafuse.com/privacy/management/exports/export-'),
          exportFormat: 'json',
          exportSize: '1.2 MB',
          exportCreatedAt: expect.any(Date),
          exportExpiresAt: expect.any(Date)
        }));
        
        // Verify logging
        expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Generated data export'));
        expect(logger.error).not.toHaveBeenCalled();
      } finally {
        // Restore Date.now
        Date.now = originalDateNow;
      }
    });
    
    it('should throw NotFoundError if the request is not found', async () => {
      // Setup model to return null
      SubjectRequest.findById.mockResolvedValueOnce(null);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.generateDataExport('non-existent-id'))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data subject request not found'
        });
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting subject request by ID'));
    });
    
    it('should throw ValidationError if request type is not access', async () => {
      // Setup mock request object with non-access request type
      const mockRequest = {
        _id: 'mock-id-1',
        requestType: 'erasure',
        dataSubjectEmail: '<EMAIL>',
        status: 'in-progress'
      };
      
      // Override the findById mock for this test
      SubjectRequest.findById.mockResolvedValueOnce(mockRequest);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.generateDataExport('mock-id-1'))
        .rejects
        .toMatchObject({
          name: 'ValidationError',
          message: 'Data export is only available for access requests'
        });
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error generating data export'));
    });
    
    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      SubjectRequest.findById.mockRejectedValueOnce(error);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.generateDataExport('mock-id-1'))
        .rejects
        .toThrow('Database error');
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error generating data export'));
    });
  });
  
  describe('identifyAffectedSystems', () => {
    it('should identify affected systems for a request', async () => {
      // Setup mock request object
      const mockRequest = {
        _id: 'mock-id-1',
        dataSubjectEmail: '<EMAIL>',
        status: 'in-progress'
      };
      
      // Override the findById mock for this test
      SubjectRequest.findById.mockResolvedValueOnce(mockRequest);
      
      // Call the service method
      const result = await subjectRequestService.identifyAffectedSystems('mock-id-1');
      
      // Verify the model methods were called correctly
      expect(SubjectRequest.findById).toHaveBeenCalledWith('mock-id-1');
      
      // Verify the result
      expect(result).toEqual(expect.objectContaining({
        id: 'mock-id-1',
        dataSubjectEmail: '<EMAIL>',
        affectedSystems: expect.arrayContaining([
          expect.objectContaining({
            systemId: expect.any(String),
            systemName: expect.any(String),
            dataCategories: expect.any(Array),
            processingPurposes: expect.any(Array)
          })
        ])
      }));
      
      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('Identified affected systems'));
      expect(logger.error).not.toHaveBeenCalled();
    });
    
    it('should throw NotFoundError if the request is not found', async () => {
      // Setup model to return null
      SubjectRequest.findById.mockResolvedValueOnce(null);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.identifyAffectedSystems('non-existent-id'))
        .rejects
        .toMatchObject({
          name: 'NotFoundError',
          message: 'Data subject request not found'
        });
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error getting subject request by ID'));
    });
    
    it('should handle errors', async () => {
      // Setup error
      const error = new Error('Database error');
      SubjectRequest.findById.mockRejectedValueOnce(error);
      
      // Call the service method and expect it to throw
      await expect(subjectRequestService.identifyAffectedSystems('mock-id-1'))
        .rejects
        .toThrow('Database error');
      
      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(expect.stringContaining('Error identifying affected systems'));
    });
  });
});
