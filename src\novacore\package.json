{"name": "novacore", "version": "1.0.0", "description": "NovaCore - The Genesis Pairing of NovaSphere and NovaConnect", "main": "server.js", "scripts": {"start": "ts-node server.ts", "dev": "nodemon --exec ts-node server.ts", "build": "tsc", "test": "jest"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "uuid": "^9.0.0"}, "devDependencies": {"@types/body-parser": "^1.19.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.1", "@types/node": "^18.16.3", "@types/uuid": "^9.0.1", "jest": "^29.5.0", "nodemon": "^2.0.22", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.4"}}