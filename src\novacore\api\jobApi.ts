/**
 * jobApi.ts
 * 
 * API endpoints for collection job management in the NovaCore system.
 */

import express from 'express';
import { ConnectorService } from '../services/ConnectorService';
import { EvidenceService } from '../services/EvidenceService';

// Create router
const router = express.Router();

// Create services
const evidenceService = new EvidenceService();
const connectorService = new ConnectorService(evidenceService);

/**
 * GET /api/jobs
 * Get all jobs with optional filtering
 */
router.get('/', async (req, res) => {
  try {
    // In a real implementation, this would retrieve jobs with filtering
    // For now, we'll return an empty array
    res.json({ items: [], total: 0 });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/jobs/:id
 * Get job by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const job = await connectorService.getCollectionJob(req.params.id);
    res.json(job);
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

/**
 * POST /api/jobs/:id/execute
 * Execute job
 */
router.post('/:id/execute', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Execute job
    const job = await connectorService.executeCollectionJob(id);
    
    res.json(job);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * POST /api/jobs/:id/cancel
 * Cancel job
 */
router.post('/:id/cancel', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Cancel job
    const job = await connectorService.cancelCollectionJob(id);
    
    res.json(job);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

export default router;
