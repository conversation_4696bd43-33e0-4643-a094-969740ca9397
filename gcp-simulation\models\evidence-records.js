/**
 * Evidence Records Data Model
 * 
 * This file defines the data structure for evidence records in the NovaFuse GCP simulation.
 * It includes detailed evidence information, metadata, and relationships to compliance controls.
 */

// Sample evidence records data
const evidenceRecords = [
  {
    id: "evidence-001",
    type: "IAM_POLICY",
    name: "IAM Policy - Project-level Access Controls",
    description: "IAM policy implementing least privilege principle for project-level access",
    resource: {
      type: "project",
      id: "project-123",
      name: "NovaFuse Production"
    },
    timestamp: "2023-06-15T14:22:31Z",
    expiryDate: "2024-06-15T14:22:31Z",
    content: {
      format: "json",
      data: {
        bindings: [
          {
            role: "roles/viewer",
            members: ["group:<EMAIL>"]
          },
          {
            role: "roles/editor",
            members: ["group:<EMAIL>"]
          },
          {
            role: "roles/owner",
            members: ["user:<EMAIL>"]
          }
        ],
        etag: "BwWKmjvelug=",
        version: 1
      }
    },
    metadata: {
      collector: "IAM Policy Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "IAM Policy Validator",
      verificationTimestamp: "2023-06-15T14:25:00Z",
      hashValue: "a1b2c3d4e5f6g7h8i9j0"
    },
    relatedControls: [
      { framework: "GDPR", controlId: "gdpr-art5-1f", status: "PASS" },
      { framework: "HIPAA", controlId: "hipaa-164-312a", status: "PASS" },
      { framework: "SOC2", controlId: "soc2-cc61", status: "PASS" }
    ],
    tags: ["iam", "access-control", "least-privilege"],
    notes: "Policy implements least privilege principle with appropriate role separation"
  },
  {
    id: "evidence-002",
    type: "AUDIT_LOG",
    name: "BigQuery Access Audit Logs",
    description: "Audit logs showing authorized data access to BigQuery datasets",
    resource: {
      type: "bigquery-dataset",
      id: "bigquery-dataset-1",
      name: "Customer Data"
    },
    timestamp: "2023-06-14T09:17:45Z",
    expiryDate: "2024-06-14T09:17:45Z",
    content: {
      format: "json",
      data: {
        logEntries: [
          {
            timestamp: "2023-06-14T09:15:22Z",
            principalEmail: "<EMAIL>",
            methodName: "google.cloud.bigquery.v2.JobService.Query",
            resourceName: "projects/project-123/datasets/customer_data/tables/transactions",
            status: "SUCCESS"
          },
          {
            timestamp: "2023-06-14T09:16:45Z",
            principalEmail: "<EMAIL>",
            methodName: "google.cloud.bigquery.v2.JobService.GetQueryResults",
            resourceName: "projects/project-123/datasets/customer_data/tables/transactions",
            status: "SUCCESS"
          }
        ]
      }
    },
    metadata: {
      collector: "Cloud Logging Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "Log Integrity Validator",
      verificationTimestamp: "2023-06-14T09:20:00Z",
      hashValue: "b2c3d4e5f6g7h8i9j0k1"
    },
    relatedControls: [
      { framework: "GDPR", controlId: "gdpr-art5-1a", status: "PASS" },
      { framework: "HIPAA", controlId: "hipaa-164-312b", status: "PASS" },
      { framework: "PCI-DSS", controlId: "pci-dss-req10", status: "PASS" }
    ],
    tags: ["audit-logs", "data-access", "bigquery"],
    notes: "Logs show proper authorization for data access with complete audit trail"
  },
  {
    id: "evidence-003",
    type: "STORAGE_POLICY",
    name: "Cloud Storage Bucket Policy",
    description: "Storage bucket policy for PII data",
    resource: {
      type: "storage-bucket",
      id: "bucket-pii-data",
      name: "PII Data Bucket"
    },
    timestamp: "2023-06-10T11:05:22Z",
    expiryDate: "2024-06-10T11:05:22Z",
    content: {
      format: "json",
      data: {
        acl: {
          owner: "project-owners-123456789",
          entries: [
            {
              entity: "project-owners-123456789",
              role: "OWNER"
            },
            {
              entity: "project-editors-123456789",
              role: "WRITER"
            },
            {
              entity: "project-viewers-123456789",
              role: "READER"
            }
          ]
        },
        encryption: {
          defaultKmsKeyName: "projects/project-123/locations/global/keyRings/pii-keyring/cryptoKeys/pii-key"
        },
        lifecycle: {
          rule: []
        }
      }
    },
    metadata: {
      collector: "Storage Policy Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "Storage Policy Validator",
      verificationTimestamp: "2023-06-10T11:10:00Z",
      hashValue: "c3d4e5f6g7h8i9j0k1l2"
    },
    relatedControls: [
      { framework: "GDPR", controlId: "gdpr-art5-1e", status: "FAIL" },
      { framework: "HIPAA", controlId: "hipaa-164-312c1", status: "PARTIAL" },
      { framework: "PCI-DSS", controlId: "pci-dss-req3", status: "PARTIAL" }
    ],
    tags: ["storage", "data-retention", "encryption"],
    notes: "Bucket has proper access controls and encryption, but missing lifecycle policy for data retention"
  },
  {
    id: "evidence-004",
    type: "DATA_CATALOG",
    name: "Data Catalog Entries",
    description: "Data catalog entries with purpose specifications",
    resource: {
      type: "data-catalog",
      id: "data-catalog-1",
      name: "Enterprise Data Catalog"
    },
    timestamp: "2023-06-10T11:30:00Z",
    expiryDate: "2024-06-10T11:30:00Z",
    content: {
      format: "json",
      data: {
        entries: [
          {
            name: "projects/project-123/locations/us-central1/entryGroups/customer_data/entries/transactions",
            type: "TABLE",
            description: "Customer transaction data",
            schema: {
              columns: [
                { name: "customer_id", type: "STRING", description: "Customer identifier", tags: ["PII", "SENSITIVE"] },
                { name: "transaction_date", type: "TIMESTAMP", description: "Date of transaction" },
                { name: "amount", type: "FLOAT", description: "Transaction amount" },
                { name: "product_id", type: "STRING", description: "Product identifier" }
              ]
            },
            usagePurpose: "Service Delivery",
            retentionPeriod: "3 years",
            dataOwner: "Finance Department",
            tags: ["PII", "FINANCIAL", "CUSTOMER_DATA"]
          }
        ]
      }
    },
    metadata: {
      collector: "Data Catalog Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "Data Governance Validator",
      verificationTimestamp: "2023-06-10T11:35:00Z",
      hashValue: "d4e5f6g7h8i9j0k1l2m3"
    },
    relatedControls: [
      { framework: "GDPR", controlId: "gdpr-art5-1b", status: "PASS" },
      { framework: "GDPR", controlId: "gdpr-art5-1c", status: "PARTIAL" }
    ],
    tags: ["data-catalog", "purpose-specification", "data-classification"],
    notes: "Data catalog entries include purpose specifications and data classification"
  },
  {
    id: "evidence-005",
    type: "DLP_SCAN",
    name: "DLP Scan Results",
    description: "DLP scan results showing proper data classification",
    resource: {
      type: "storage-bucket",
      id: "gcs-bucket-1",
      name: "Marketing Data Bucket"
    },
    timestamp: "2023-06-12T08:45:22Z",
    expiryDate: "2024-06-12T08:45:22Z",
    content: {
      format: "json",
      data: {
        scanId: "dlp-scan-123456",
        status: "COMPLETED",
        startTime: "2023-06-12T08:30:00Z",
        endTime: "2023-06-12T08:45:22Z",
        resourcesScanned: 1250,
        findings: [
          {
            infoType: "EMAIL_ADDRESS",
            count: 532,
            location: "gs://marketing-data-bucket/customer_list.csv",
            status: "PROTECTED"
          },
          {
            infoType: "PHONE_NUMBER",
            count: 498,
            location: "gs://marketing-data-bucket/customer_list.csv",
            status: "PROTECTED"
          },
          {
            infoType: "CREDIT_CARD_NUMBER",
            count: 0,
            status: "NOT_FOUND"
          }
        ]
      }
    },
    metadata: {
      collector: "DLP Scanner",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "DLP Validator",
      verificationTimestamp: "2023-06-12T08:50:00Z",
      hashValue: "e5f6g7h8i9j0k1l2m3n4"
    },
    relatedControls: [
      { framework: "GDPR", controlId: "gdpr-art5-1b", status: "PASS" },
      { framework: "GDPR", controlId: "gdpr-art5-1c", status: "PASS" },
      { framework: "PCI-DSS", controlId: "pci-dss-req3", status: "PASS" }
    ],
    tags: ["dlp", "data-classification", "pii-detection"],
    notes: "DLP scan confirms proper classification and protection of personal data"
  },
  {
    id: "evidence-006",
    type: "KMS_CONFIG",
    name: "KMS Configuration",
    description: "KMS configuration for data encryption",
    resource: {
      type: "kms-key",
      id: "kms-key-1",
      name: "Data Encryption Key"
    },
    timestamp: "2023-06-07T09:10:00Z",
    expiryDate: "2024-06-07T09:10:00Z",
    content: {
      format: "json",
      data: {
        name: "projects/project-123/locations/global/keyRings/data-protection/cryptoKeys/data-encryption",
        purpose: "ENCRYPT_DECRYPT",
        createTime: "2023-01-15T00:00:00Z",
        rotationPeriod: "7776000s", // 90 days
        nextRotationTime: "2023-07-15T00:00:00Z",
        versionTemplate: {
          algorithm: "GOOGLE_SYMMETRIC_ENCRYPTION",
          protectionLevel: "SOFTWARE"
        },
        primary: {
          state: "ENABLED",
          createTime: "2023-04-15T00:00:00Z"
        }
      }
    },
    metadata: {
      collector: "KMS Configuration Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "Encryption Validator",
      verificationTimestamp: "2023-06-07T09:15:00Z",
      hashValue: "f6g7h8i9j0k1l2m3n4o5"
    },
    relatedControls: [
      { framework: "GDPR", controlId: "gdpr-art5-1f", status: "PASS" },
      { framework: "HIPAA", controlId: "hipaa-164-312e1", status: "PASS" },
      { framework: "PCI-DSS", controlId: "pci-dss-req3", status: "PASS" }
    ],
    tags: ["encryption", "key-management", "data-protection"],
    notes: "KMS configuration includes appropriate key rotation and protection level"
  },
  {
    id: "evidence-007",
    type: "VPC_CONFIG",
    name: "VPC Service Controls Configuration",
    description: "VPC Service Controls perimeter configuration",
    resource: {
      type: "vpc-sc-perimeter",
      id: "vpc-sc-perimeter-1",
      name: "Data Protection Perimeter"
    },
    timestamp: "2023-06-12T10:45:00Z",
    expiryDate: "2024-06-12T10:45:00Z",
    content: {
      format: "json",
      data: {
        name: "accessPolicies/123456789/servicePerimeters/data_protection",
        title: "Data Protection Perimeter",
        description: "Service perimeter for data protection",
        status: {
          resources: [
            "projects/123456789"
          ],
          restrictedServices: [
            "bigquery.googleapis.com",
            "storage.googleapis.com",
            "cloudfunctions.googleapis.com"
          ],
          vpcAccessibleServices: {
            allowedServices: [
              "bigquery.googleapis.com",
              "storage.googleapis.com"
            ],
            enableRestriction: true
          },
          ingressPolicies: [
            {
              sourceIdentities: ["user:<EMAIL>"],
              sources: [
                {
                  resource: "projects/admin-project"
                }
              ],
              identityType: "ANY_IDENTITY"
            }
          ],
          egressPolicies: []
        }
      }
    },
    metadata: {
      collector: "VPC Configuration Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "Network Security Validator",
      verificationTimestamp: "2023-06-12T10:50:00Z",
      hashValue: "g7h8i9j0k1l2m3n4o5p6"
    },
    relatedControls: [
      { framework: "GDPR", controlId: "gdpr-art5-1f", status: "PASS" },
      { framework: "HIPAA", controlId: "hipaa-164-312a", status: "PASS" },
      { framework: "PCI-DSS", controlId: "pci-dss-req1", status: "PASS" }
    ],
    tags: ["network-security", "service-perimeter", "access-control"],
    notes: "VPC Service Controls perimeter properly restricts access to sensitive data services"
  },
  {
    id: "evidence-008",
    type: "FIREWALL_CONFIG",
    name: "Firewall Rules Configuration",
    description: "Firewall rules for PCI environment",
    resource: {
      type: "firewall-rule",
      id: "fw-rule-pci-1",
      name: "PCI Environment Firewall"
    },
    timestamp: "2023-05-10T09:30:00Z",
    expiryDate: "2024-05-10T09:30:00Z",
    content: {
      format: "json",
      data: {
        name: "pci-environment-firewall",
        network: "projects/project-123/global/networks/pci-network",
        priority: 1000,
        direction: "INGRESS",
        sourceRanges: ["10.0.0.0/8"],
        targetTags: ["pci-environment"],
        allowed: [
          {
            IPProtocol: "tcp",
            ports: ["443"]
          }
        ],
        denied: [],
        logConfig: {
          enable: true
        }
      }
    },
    metadata: {
      collector: "Firewall Configuration Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "Network Security Validator",
      verificationTimestamp: "2023-05-10T09:35:00Z",
      hashValue: "h8i9j0k1l2m3n4o5p6q7"
    },
    relatedControls: [
      { framework: "PCI-DSS", controlId: "pci-dss-req1", status: "PASS" }
    ],
    tags: ["network-security", "firewall", "pci-dss"],
    notes: "Firewall rules properly restrict access to PCI environment with appropriate logging"
  },
  {
    id: "evidence-009",
    type: "LOGGING_CONFIG",
    name: "Logging Configuration",
    description: "Logging configuration for PCI environment",
    resource: {
      type: "logging-sink",
      id: "logging-sink-pci-1",
      name: "PCI Environment Logging"
    },
    timestamp: "2023-05-09T14:30:00Z",
    expiryDate: "2024-05-09T14:30:00Z",
    content: {
      format: "json",
      data: {
        name: "projects/project-123/sinks/pci-environment-logging",
        destination: "bigquery.googleapis.com/projects/project-123/datasets/pci_logs",
        filter: "resource.type=gce_instance AND resource.labels.tag:pci-environment",
        writerIdentity: "serviceAccount:<EMAIL>",
        includeChildren: true,
        bigqueryOptions: {
          usePartitionedTables: true
        }
      }
    },
    metadata: {
      collector: "Logging Configuration Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "Logging Validator",
      verificationTimestamp: "2023-05-09T14:35:00Z",
      hashValue: "i9j0k1l2m3n4o5p6q7r8"
    },
    relatedControls: [
      { framework: "PCI-DSS", controlId: "pci-dss-req10", status: "PASS" },
      { framework: "HIPAA", controlId: "hipaa-164-312b", status: "PASS" },
      { framework: "SOC2", controlId: "soc2-cc72", status: "PASS" }
    ],
    tags: ["logging", "audit-trail", "pci-dss"],
    notes: "Logging configuration captures all required audit events for PCI environment"
  },
  {
    id: "evidence-010",
    type: "MONITORING_ALERT",
    name: "Monitoring Alert Configuration",
    description: "Alert policy for cardholder data access",
    resource: {
      type: "alert-policy",
      id: "alert-policy-pci-1",
      name: "PCI Data Access Alert"
    },
    timestamp: "2023-05-12T09:15:00Z",
    expiryDate: "2024-05-12T09:15:00Z",
    content: {
      format: "json",
      data: {
        name: "projects/project-123/alertPolicies/pci-data-access-alert",
        displayName: "PCI Data Access Alert",
        documentation: {
          content: "Alert for suspicious access to cardholder data",
          mimeType: "text/markdown"
        },
        conditions: [
          {
            displayName: "Unusual access pattern to cardholder data",
            conditionThreshold: {
              filter: "resource.type=bigquery_resource AND resource.labels.dataset_id=cardholder_data AND protoPayload.methodName=google.cloud.bigquery.v2.JobService.Query",
              aggregations: [
                {
                  alignmentPeriod: "60s",
                  perSeriesAligner: "ALIGN_RATE",
                  crossSeriesReducer: "REDUCE_COUNT"
                }
              ],
              comparison: "COMPARISON_GT",
              thresholdValue: 10,
              duration: "0s"
            }
          }
        ],
        alertStrategy: {
          notificationRateLimit: {
            period: "300s"
          },
          autoClose: "604800s"
        },
        notificationChannels: [
          "projects/project-123/notificationChannels/email-security-team"
        ]
      }
    },
    metadata: {
      collector: "Monitoring Configuration Collector",
      collectionMethod: "API",
      verificationStatus: "VERIFIED",
      verifiedBy: "Monitoring Validator",
      verificationTimestamp: "2023-05-12T09:20:00Z",
      hashValue: "j0k1l2m3n4o5p6q7r8s9"
    },
    relatedControls: [
      { framework: "PCI-DSS", controlId: "pci-dss-req10", status: "PASS" },
      { framework: "SOC2", controlId: "soc2-cc72", status: "PASS" }
    ],
    tags: ["monitoring", "alerts", "pci-dss"],
    notes: "Alert policy properly configured to detect unusual access patterns to cardholder data"
  }
];

// Cloud Storage Evidence Binder configuration
const evidenceBinder = {
  bucketName: "novafuse-evidence-store",
  evidenceTypes: [
    {
      type: "IAM_POLICY_CHANGE",
      retention: "7y",
      metadata: ["actor", "resource", "timestamp", "before", "after", "justification"],
      format: "JSON",
      encryptionKey: "projects/novafuse/locations/global/keyRings/compliance/cryptoKeys/evidence"
    },
    {
      type: "VULNERABILITY_REMEDIATION",
      retention: "5y",
      metadata: ["findingId", "resource", "timestamp", "action", "verifier", "testResults"],
      format: "JSON",
      encryptionKey: "projects/novafuse/locations/global/keyRings/compliance/cryptoKeys/evidence"
    },
    {
      type: "COMPLIANCE_ATTESTATION",
      retention: "10y",
      metadata: ["framework", "control", "timestamp", "attestor", "evidence", "expiryDate"],
      format: "PDF",
      encryptionKey: "projects/novafuse/locations/global/keyRings/compliance/cryptoKeys/evidence"
    },
    {
      type: "AUDIT_LOG",
      retention: "3y",
      metadata: ["service", "action", "actor", "resource", "timestamp", "status"],
      format: "JSON",
      encryptionKey: "projects/novafuse/locations/global/keyRings/compliance/cryptoKeys/evidence"
    },
    {
      type: "CONFIGURATION_SNAPSHOT",
      retention: "3y",
      metadata: ["resource", "configuration", "timestamp", "validator"],
      format: "JSON",
      encryptionKey: "projects/novafuse/locations/global/keyRings/compliance/cryptoKeys/evidence"
    }
  ],
  indexingStrategy: "BigQuery",
  accessControls: {
    viewers: ["roles/novafuse.complianceViewer", "roles/novafuse.auditor"],
    administrators: ["roles/novafuse.complianceAdmin"]
  },
  retentionPolicy: {
    enforced: true,
    overrideProtection: true
  },
  encryptionConfig: {
    keyRotationPeriod: "90d",
    keyBackup: true
  }
};

module.exports = { evidenceRecords, evidenceBinder };
