/**
 * Run CSDE Integration
 * 
 * This script demonstrates how to use the CSDE integration in a production environment.
 * It processes security findings from various sources and outputs the transformed data.
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');
const CSEDConnector = require('../src/connectors/csde-connector');

// Configuration
const CONFIG = {
  csdeApiUrl: process.env.CSDE_API_URL || 'http://localhost:3010',
  enableCaching: process.env.ENABLE_CACHING !== 'false',
  enableMetrics: process.env.ENABLE_METRICS !== 'false',
  cacheSize: parseInt(process.env.CACHE_SIZE || '1000', 10),
  domain: process.env.DOMAIN || 'security',
  inputDir: process.env.INPUT_DIR || path.join(__dirname, '../data/input'),
  outputDir: process.env.OUTPUT_DIR || path.join(__dirname, '../data/output'),
  batchSize: parseInt(process.env.BATCH_SIZE || '10', 10),
  logLevel: process.env.LOG_LEVEL || 'info'
};

// Create logger
const logger = {
  debug: (...args) => {
    if (CONFIG.logLevel === 'debug') {
      console.log('[DEBUG]', ...args);
    }
  },
  info: (...args) => {
    if (['debug', 'info'].includes(CONFIG.logLevel)) {
      console.log('[INFO]', ...args);
    }
  },
  warn: (...args) => {
    if (['debug', 'info', 'warn'].includes(CONFIG.logLevel)) {
      console.warn('[WARN]', ...args);
    }
  },
  error: (...args) => {
    console.error('[ERROR]', ...args);
  }
};

// Create directories if they don't exist
function createDirectories() {
  logger.debug('Creating directories...');
  
  if (!fs.existsSync(CONFIG.inputDir)) {
    fs.mkdirSync(CONFIG.inputDir, { recursive: true });
    logger.info(`Created input directory: ${CONFIG.inputDir}`);
  }
  
  if (!fs.existsSync(CONFIG.outputDir)) {
    fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    logger.info(`Created output directory: ${CONFIG.outputDir}`);
  }
}

// Load findings from input directory
function loadFindings() {
  logger.info(`Loading findings from ${CONFIG.inputDir}...`);
  
  const findings = [];
  const files = fs.readdirSync(CONFIG.inputDir).filter(file => file.endsWith('.json'));
  
  for (const file of files) {
    const filePath = path.join(CONFIG.inputDir, file);
    logger.debug(`Loading file: ${filePath}`);
    
    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      
      if (Array.isArray(data)) {
        findings.push(...data);
        logger.debug(`Loaded ${data.length} findings from ${file}`);
      } else {
        findings.push(data);
        logger.debug(`Loaded 1 finding from ${file}`);
      }
    } catch (error) {
      logger.error(`Error loading file ${file}:`, error.message);
    }
  }
  
  logger.info(`Loaded ${findings.length} findings from ${files.length} files`);
  return findings;
}

// Save findings to output directory
function saveFindings(findings, source) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const outputFile = path.join(CONFIG.outputDir, `${source}-${timestamp}.json`);
  
  logger.info(`Saving ${findings.length} findings to ${outputFile}...`);
  
  fs.writeFileSync(outputFile, JSON.stringify(findings, null, 2));
  
  logger.info(`Saved ${findings.length} findings to ${outputFile}`);
  return outputFile;
}

// Process findings with CSDE
async function processFindings(findings, source) {
  logger.info(`Processing ${findings.length} findings from ${source}...`);
  
  // Create CSDE connector
  const connector = new CSEDConnector({
    id: 'csde-connector',
    name: 'CSDE Connector',
    csdeApiUrl: CONFIG.csdeApiUrl,
    enableCaching: CONFIG.enableCaching,
    enableMetrics: CONFIG.enableMetrics,
    cacheSize: CONFIG.cacheSize,
    domain: CONFIG.domain
  });
  
  // Process findings in batches
  const batches = [];
  for (let i = 0; i < findings.length; i += CONFIG.batchSize) {
    batches.push(findings.slice(i, i + CONFIG.batchSize));
  }
  
  logger.info(`Processing ${findings.length} findings in ${batches.length} batches...`);
  
  const results = [];
  let totalDuration = 0;
  
  for (let i = 0; i < batches.length; i++) {
    const batch = batches[i];
    logger.debug(`Processing batch ${i + 1}/${batches.length} (${batch.length} findings)...`);
    
    const startTime = performance.now();
    const batchResults = await connector.processBatch(batch);
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    totalDuration += duration;
    results.push(...batchResults);
    
    logger.debug(`Batch ${i + 1} processed in ${duration.toFixed(2)}ms (${(duration / batch.length).toFixed(2)}ms per finding)`);
  }
  
  logger.info(`Processed ${findings.length} findings in ${totalDuration.toFixed(2)}ms (${(totalDuration / findings.length).toFixed(2)}ms per finding)`);
  
  // Log metrics
  const metrics = connector.metrics;
  const csdeMetrics = connector.csdeIntegration.getMetrics();
  
  logger.info('Connector Metrics:', {
    totalOperations: metrics.totalOperations,
    successfulOperations: metrics.successfulOperations,
    failedOperations: metrics.failedOperations,
    averageDuration: metrics.averageDuration
  });
  
  logger.info('CSDE Metrics:', {
    totalRequests: csdeMetrics.totalRequests,
    successfulRequests: csdeMetrics.successfulRequests,
    failedRequests: csdeMetrics.failedRequests,
    averageLatency: csdeMetrics.averageLatency,
    cacheHits: csdeMetrics.cacheHits,
    cacheMisses: csdeMetrics.cacheMisses
  });
  
  return results;
}

// Extract remediation actions from findings
function extractRemediationActions(findings) {
  logger.info('Extracting remediation actions...');
  
  const remediationActions = [];
  
  for (const finding of findings) {
    if (finding._remediation) {
      for (const action of finding._remediation) {
        remediationActions.push({
          findingId: finding.finding_id,
          action
        });
      }
    }
  }
  
  logger.info(`Extracted ${remediationActions.length} remediation actions`);
  
  return remediationActions;
}

// Main function
async function main() {
  try {
    logger.info('Starting CSDE Integration');
    logger.debug('Configuration:', CONFIG);
    
    // Create directories
    createDirectories();
    
    // Load findings
    const findings = loadFindings();
    
    if (findings.length === 0) {
      logger.warn('No findings found. Exiting.');
      return;
    }
    
    // Process findings
    const processedFindings = await processFindings(findings, 'gcp-security-command-center');
    
    // Save processed findings
    const outputFile = saveFindings(processedFindings, 'processed');
    
    // Extract and save remediation actions
    const remediationActions = extractRemediationActions(processedFindings);
    const remediationFile = path.join(CONFIG.outputDir, 'remediation-actions.json');
    fs.writeFileSync(remediationFile, JSON.stringify(remediationActions, null, 2));
    
    logger.info(`Saved ${remediationActions.length} remediation actions to ${remediationFile}`);
    
    // Generate summary
    const summary = {
      timestamp: new Date().toISOString(),
      inputFindings: findings.length,
      processedFindings: processedFindings.length,
      remediationActions: remediationActions.length,
      outputFile,
      remediationFile
    };
    
    const summaryFile = path.join(CONFIG.outputDir, 'summary.json');
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    
    logger.info(`Saved summary to ${summaryFile}`);
    logger.info('CSDE Integration completed successfully');
    
    return summary;
  } catch (error) {
    logger.error('Error running CSDE Integration:', error);
    throw error;
  }
}

// Run the script if executed directly
if (require.main === module) {
  main().catch(error => {
    logger.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { main, loadFindings, processFindings, extractRemediationActions };
