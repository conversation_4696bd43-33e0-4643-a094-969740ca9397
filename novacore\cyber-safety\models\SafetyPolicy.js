/**
 * NovaCore Safety Policy Model
 * 
 * This model defines the schema for safety policies in the Cyber-Safety platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define condition schema
const conditionSchema = new Schema({
  type: { 
    type: String, 
    required: true, 
    enum: ['endpoint', 'method', 'user', 'data', 'time', 'custom'], 
    default: 'endpoint' 
  },
  operator: { 
    type: String, 
    required: true, 
    enum: ['equals', 'contains', 'startsWith', 'endsWith', 'regex', 'gt', 'lt', 'gte', 'lte'], 
    default: 'equals' 
  },
  value: { 
    type: Schema.Types.Mixed, 
    required: true 
  },
  field: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define action schema
const actionSchema = new Schema({
  type: { 
    type: String, 
    required: true, 
    enum: ['allow', 'deny', 'log', 'alert', 'modify', 'verify'], 
    default: 'allow' 
  },
  parameters: { 
    type: Map, 
    of: Schema.Types.Mixed 
  }
}, { _id: false });

// Define safety policy schema
const safetyPolicySchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  enabled: { 
    type: Boolean, 
    default: true 
  },
  severity: { 
    type: String, 
    required: true, 
    enum: ['low', 'medium', 'high', 'critical'], 
    default: 'medium' 
  },
  conditions: { 
    type: [conditionSchema], 
    default: [] 
  },
  actions: { 
    type: [actionSchema], 
    default: [] 
  },
  logicalOperator: { 
    type: String, 
    enum: ['and', 'or'], 
    default: 'and' 
  },
  priority: { 
    type: Number, 
    default: 100 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
safetyPolicySchema.index({ name: 1 }, { unique: true });
safetyPolicySchema.index({ enabled: 1 });
safetyPolicySchema.index({ severity: 1 });
safetyPolicySchema.index({ priority: 1 });
safetyPolicySchema.index({ tags: 1 });
safetyPolicySchema.index({ createdAt: 1 });

// Add methods
safetyPolicySchema.methods.isActive = function() {
  return this.enabled;
};

safetyPolicySchema.methods.isCritical = function() {
  return this.severity === 'critical';
};

// Add statics
safetyPolicySchema.statics.findActive = function() {
  return this.find({ enabled: true }).sort({ priority: 1 });
};

safetyPolicySchema.statics.findBySeverity = function(severity) {
  return this.find({ severity }).sort({ priority: 1 });
};

safetyPolicySchema.statics.findByTags = function(tags) {
  return this.find({ tags: { $all: tags } }).sort({ priority: 1 });
};

// Create model
const SafetyPolicy = mongoose.model('SafetyPolicy', safetyPolicySchema);

module.exports = SafetyPolicy;
