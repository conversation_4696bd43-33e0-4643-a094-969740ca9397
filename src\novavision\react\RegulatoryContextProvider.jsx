import React, { createContext, useContext, useState, useEffect } from 'react';

// Create the regulatory context
const RegulatoryContext = createContext();

/**
 * RegulatoryContextProvider - Provides regulatory context for UI rendering
 * 
 * This component provides regulatory context information to all child components,
 * allowing the UI to adapt based on regulatory requirements.
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.jurisdiction - User's jurisdiction
 * @param {string} props.userRole - User's role
 * @returns {React.ReactElement} Provider component
 */
export const RegulatoryContextProvider = ({ children, jurisdiction, userRole }) => {
  const [activeRegulations, setActiveRegulations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Fetch active regulations based on jurisdiction
  useEffect(() => {
    const fetchRegulations = async () => {
      try {
        setLoading(true);
        
        // In a real implementation, this would fetch from an API
        // For now, we'll simulate the response
        const regulations = await simulateFetchRegulations(jurisdiction);
        
        setActiveRegulations(regulations);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };
    
    fetchRegulations();
  }, [jurisdiction]);
  
  // Simulate fetching regulations
  const simulateFetchRegulations = async (jurisdiction) => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Return regulations based on jurisdiction
    switch (jurisdiction.toLowerCase()) {
      case 'eu':
        return ['GDPR', 'ISO27001'];
      case 'us-healthcare':
        return ['HIPAA', 'ISO27001'];
      case 'us-finance':
        return ['PCI_DSS', 'SOX'];
      case 'us-general':
        return ['CCPA'];
      case 'global':
        return ['GDPR', 'CCPA', 'ISO27001'];
      default:
        return ['ISO27001']; // Default fallback
    }
  };
  
  // Create the context value
  const contextValue = {
    activeRegulations,
    jurisdiction,
    userRole,
    loading,
    error
  };
  
  return (
    <RegulatoryContext.Provider value={contextValue}>
      {children}
    </RegulatoryContext.Provider>
  );
};

/**
 * useRegulatoryContext - Hook to access the regulatory context
 * 
 * @returns {Object} Regulatory context value
 */
export const useRegulatoryContext = () => {
  const context = useContext(RegulatoryContext);
  if (context === undefined) {
    throw new Error('useRegulatoryContext must be used within a RegulatoryContextProvider');
  }
  return context;
};

export default RegulatoryContextProvider;
