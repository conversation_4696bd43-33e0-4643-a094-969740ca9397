"""
Demo script for the Universal Compliance Tracking Optimizer (UCTO).

This script demonstrates how to use the UCTO to track, optimize, report on,
and analyze compliance requirements and activities.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCTO
from ucto import TrackingManager, OptimizationManager, ReportingManager, AnalyticsManager

def main():
    """Run the UCTO demo."""
    logger.info("Starting UCTO demo")
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'ucto_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize the managers
    tracking_manager = TrackingManager()
    optimization_manager = OptimizationManager()
    reporting_manager = ReportingManager()
    analytics_manager = AnalyticsManager()
    
    # Create requirements
    logger.info("Creating requirements")
    
    gdpr_requirement = tracking_manager.create_requirement({
        'name': 'Data Subject Rights',
        'description': 'Implement processes for handling data subject rights requests',
        'framework': 'GDPR',
        'category': 'privacy',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': '2023-12-31',
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'data_subject_rights', 'privacy']
    })
    
    hipaa_requirement = tracking_manager.create_requirement({
        'name': 'Security Risk Assessment',
        'description': 'Conduct a security risk assessment',
        'framework': 'HIPAA',
        'category': 'security',
        'priority': 'medium',
        'status': 'pending',
        'due_date': '2023-11-30',
        'assigned_to': 'security_officer',
        'tags': ['hipaa', 'risk_assessment', 'security']
    })
    
    soc2_requirement = tracking_manager.create_requirement({
        'name': 'Access Control Review',
        'description': 'Review access controls for all systems',
        'framework': 'SOC2',
        'category': 'security',
        'priority': 'medium',
        'status': 'completed',
        'due_date': '2023-10-31',
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'access_control', 'security']
    })
    
    logger.info("Requirements created")
    
    # Create activities
    logger.info("Creating activities")
    
    gdpr_activity1 = tracking_manager.create_activity({
        'name': 'Document Data Subject Rights Process',
        'description': 'Create documentation for handling data subject rights requests',
        'requirement_id': gdpr_requirement['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': '2023-09-01',
        'end_date': '2023-09-15',
        'assigned_to': 'privacy_officer',
        'notes': 'Documentation completed and reviewed'
    })
    
    gdpr_activity2 = tracking_manager.create_activity({
        'name': 'Implement Data Subject Rights Portal',
        'description': 'Develop a portal for users to submit data subject rights requests',
        'requirement_id': gdpr_requirement['id'],
        'type': 'task',
        'status': 'in_progress',
        'start_date': '2023-09-16',
        'end_date': '2023-10-31',
        'assigned_to': 'developer',
        'notes': 'Development in progress'
    })
    
    hipaa_activity = tracking_manager.create_activity({
        'name': 'Conduct Security Risk Assessment',
        'description': 'Perform a comprehensive security risk assessment',
        'requirement_id': hipaa_requirement['id'],
        'type': 'audit',
        'status': 'pending',
        'start_date': '2023-11-01',
        'end_date': '2023-11-30',
        'assigned_to': 'security_officer',
        'notes': 'Scheduled for November'
    })
    
    soc2_activity = tracking_manager.create_activity({
        'name': 'Review Access Controls',
        'description': 'Review access controls for all systems',
        'requirement_id': soc2_requirement['id'],
        'type': 'review',
        'status': 'completed',
        'start_date': '2023-10-01',
        'end_date': '2023-10-15',
        'assigned_to': 'security_officer',
        'notes': 'Review completed, all controls verified'
    })
    
    logger.info("Activities created")
    
    # Get all requirements and activities
    requirements = [gdpr_requirement, hipaa_requirement, soc2_requirement]
    activities = [gdpr_activity1, gdpr_activity2, hipaa_activity, soc2_activity]
    
    # Apply optimization
    logger.info("Applying optimization")
    
    optimization_result = optimization_manager.optimize('resource_allocation', requirements, activities)
    
    # Save the optimization result to a file
    optimization_path = os.path.join(output_dir, 'optimization_result.json')
    
    with open(optimization_path, 'w', encoding='utf-8') as f:
        json.dump(optimization_result, f, indent=2)
    
    logger.info(f"Saved optimization result to {optimization_path}")
    
    # Generate a report
    logger.info("Generating report")
    
    report = reporting_manager.generate_report('summary', requirements, activities, {
        'title': 'Compliance Summary Report'
    })
    
    # Save the report to a file
    report_path = os.path.join(output_dir, f"{report['id']}.json")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Saved report to {report_path}")
    
    # Perform analytics
    logger.info("Performing analytics")
    
    analytics_result = analytics_manager.analyze('risk', requirements, activities)
    
    # Save the analytics result to a file
    analytics_path = os.path.join(output_dir, 'analytics_result.json')
    
    with open(analytics_path, 'w', encoding='utf-8') as f:
        json.dump(analytics_result, f, indent=2)
    
    logger.info(f"Saved analytics result to {analytics_path}")
    
    logger.info("UCTO demo completed successfully")

if __name__ == "__main__":
    main()
