/**
 * Role-Based Access Control (RBAC) Service
 *
 * This service provides comprehensive role-based access control for NovaConnect,
 * including role management, permission management, and access control.
 */

const mongoose = require('mongoose');
const logger = require('../../config/logger');
const Role = require('../models/Role');
const Permission = require('../models/Permission');
const UserRole = require('../models/UserRole');
const User = require('../models/User');
const cacheService = require('./CacheService');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');

class RBACService {
  constructor() {
    // Cache keys
    this.CACHE_KEYS = {
      USER_PERMISSIONS: 'rbac:user-permissions:',
      USER_PERMISSION: 'rbac:permission:',
      ALL_ROLES: 'rbac:all-roles',
      ALL_PERMISSIONS: 'rbac:all-permissions'
    };

    // Default roles and permissions
    this.defaultRoles = [
      {
        name: 'Administrator',
        description: 'Full access to all resources',
        permissions: ['*'],
        isSystem: true,
        isDefault: false
      },
      {
        name: 'Manager',
        description: 'Access to most resources, but cannot manage users or system settings',
        permissions: [
          'connector:*',
          'workflow:*',
          'normalization:*',
          'monitoring:*',
          'team:view',
          'team:edit',
          'user:view'
        ],
        isSystem: true,
        isDefault: false
      },
      {
        name: 'User',
        description: 'Access to connectors, data normalization, and workflows',
        permissions: [
          'connector:view',
          'connector:use',
          'workflow:view',
          'workflow:use',
          'normalization:view',
          'normalization:use',
          'monitoring:view'
        ],
        isSystem: true,
        isDefault: true
      },
      {
        name: 'Viewer',
        description: 'Read-only access to connectors, data normalization, and workflows',
        permissions: [
          'connector:view',
          'workflow:view',
          'normalization:view',
          'monitoring:view'
        ],
        isSystem: true,
        isDefault: false
      }
    ];

    this.defaultPermissions = [
      // Connector permissions
      {
        name: 'View Connectors',
        description: 'View connectors and their configurations',
        resource: 'connector',
        action: 'view',
        isSystem: true
      },
      {
        name: 'Create Connectors',
        description: 'Create new connectors',
        resource: 'connector',
        action: 'create',
        isSystem: true
      },
      {
        name: 'Edit Connectors',
        description: 'Edit existing connectors',
        resource: 'connector',
        action: 'edit',
        isSystem: true
      },
      {
        name: 'Delete Connectors',
        description: 'Delete connectors',
        resource: 'connector',
        action: 'delete',
        isSystem: true
      },
      {
        name: 'Use Connectors',
        description: 'Use connectors to make API calls',
        resource: 'connector',
        action: 'use',
        isSystem: true
      },

      // Workflow permissions
      {
        name: 'View Workflows',
        description: 'View workflows and their configurations',
        resource: 'workflow',
        action: 'view',
        isSystem: true
      },
      {
        name: 'Create Workflows',
        description: 'Create new workflows',
        resource: 'workflow',
        action: 'create',
        isSystem: true
      },
      {
        name: 'Edit Workflows',
        description: 'Edit existing workflows',
        resource: 'workflow',
        action: 'edit',
        isSystem: true
      },
      {
        name: 'Delete Workflows',
        description: 'Delete workflows',
        resource: 'workflow',
        action: 'delete',
        isSystem: true
      },
      {
        name: 'Use Workflows',
        description: 'Execute workflows',
        resource: 'workflow',
        action: 'use',
        isSystem: true
      },

      // Normalization permissions
      {
        name: 'View Normalization',
        description: 'View normalization configurations',
        resource: 'normalization',
        action: 'view',
        isSystem: true
      },
      {
        name: 'Create Normalization',
        description: 'Create new normalization configurations',
        resource: 'normalization',
        action: 'create',
        isSystem: true
      },
      {
        name: 'Edit Normalization',
        description: 'Edit existing normalization configurations',
        resource: 'normalization',
        action: 'edit',
        isSystem: true
      },
      {
        name: 'Delete Normalization',
        description: 'Delete normalization configurations',
        resource: 'normalization',
        action: 'delete',
        isSystem: true
      },
      {
        name: 'Use Normalization',
        description: 'Use normalization to transform data',
        resource: 'normalization',
        action: 'use',
        isSystem: true
      },

      // Monitoring permissions
      {
        name: 'View Monitoring',
        description: 'View monitoring data',
        resource: 'monitoring',
        action: 'view',
        isSystem: true
      },
      {
        name: 'Configure Monitoring',
        description: 'Configure monitoring settings',
        resource: 'monitoring',
        action: 'configure',
        isSystem: true
      },

      // User permissions
      {
        name: 'View Users',
        description: 'View users and their details',
        resource: 'user',
        action: 'view',
        isSystem: true
      },
      {
        name: 'Create Users',
        description: 'Create new users',
        resource: 'user',
        action: 'create',
        isSystem: true
      },
      {
        name: 'Edit Users',
        description: 'Edit existing users',
        resource: 'user',
        action: 'edit',
        isSystem: true
      },
      {
        name: 'Delete Users',
        description: 'Delete users',
        resource: 'user',
        action: 'delete',
        isSystem: true
      },

      // Team permissions
      {
        name: 'View Teams',
        description: 'View teams and their details',
        resource: 'team',
        action: 'view',
        isSystem: true
      },
      {
        name: 'Create Teams',
        description: 'Create new teams',
        resource: 'team',
        action: 'create',
        isSystem: true
      },
      {
        name: 'Edit Teams',
        description: 'Edit existing teams',
        resource: 'team',
        action: 'edit',
        isSystem: true
      },
      {
        name: 'Delete Teams',
        description: 'Delete teams',
        resource: 'team',
        action: 'delete',
        isSystem: true
      },

      // Admin permissions
      {
        name: 'System Administration',
        description: 'Administer system settings',
        resource: 'admin',
        action: 'system',
        isSystem: true
      },
      {
        name: 'Billing Administration',
        description: 'Administer billing settings',
        resource: 'admin',
        action: 'billing',
        isSystem: true
      }
    ];

    // Initialize the database with default roles and permissions
    this.initializeDatabase();
  }

  /**
   * Initialize the database with default roles and permissions
   */
  async initializeDatabase() {
    try {
      // Create default permissions if they don't exist
      logger.info('Initializing default permissions');

      for (const permissionData of this.defaultPermissions) {
        try {
          // Check if permission already exists
          const existingPermission = await Permission.findOne({
            resource: permissionData.resource,
            action: permissionData.action
          });

          if (!existingPermission) {
            const permission = new Permission({
              name: permissionData.name,
              description: permissionData.description,
              resource: permissionData.resource,
              action: permissionData.action,
              isSystem: permissionData.isSystem
            });

            await permission.save();
            logger.info(`Created permission: ${permissionData.name}`);
          }
        } catch (err) {
          logger.warn(`Error creating permission ${permissionData.name}: ${err.message}`);
        }
      }

      // Create default roles if they don't exist
      logger.info('Initializing default roles');

      for (const roleData of this.defaultRoles) {
        try {
          // Check if role already exists
          const existingRole = await Role.findOne({ name: roleData.name });

          if (!existingRole) {
            const role = new Role({
              name: roleData.name,
              description: roleData.description,
              permissions: roleData.permissions,
              isSystem: roleData.isSystem,
              isDefault: roleData.isDefault
            });

            await role.save();
            logger.info(`Created role: ${roleData.name}`);
          }
        } catch (err) {
          logger.warn(`Error creating role ${roleData.name}: ${err.message}`);
        }
      }

      logger.info('Database initialization completed');
    } catch (error) {
      logger.error('Error initializing RBAC database', { error: error.message });
      // Don't throw the error, just log it
    }
  }

  /**
   * Get all roles
   */
  async getAllRoles() {
    try {
      // Check cache first
      const cachedRoles = await cacheService.get(this.CACHE_KEYS.ALL_ROLES);

      if (cachedRoles !== null) {
        return cachedRoles;
      }

      const roles = await Role.find().sort({ name: 1 });

      // Cache roles for 5 minutes
      await cacheService.set(this.CACHE_KEYS.ALL_ROLES, roles, 300);

      return roles;
    } catch (error) {
      logger.error('Error getting all roles', { error: error.message });
      throw error;
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(roleId) {
    try {
      const role = await Role.findById(roleId);

      if (!role) {
        throw new NotFoundError(`Role with ID ${roleId} not found`);
      }

      return role;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error getting role by ID', { roleId, error: error.message });
      throw error;
    }
  }

  /**
   * Create a new role
   */
  async createRole(data) {
    try {
      if (!data.name) {
        throw new ValidationError('Role name is required');
      }

      if (!data.permissions || !Array.isArray(data.permissions) || data.permissions.length === 0) {
        throw new ValidationError('At least one permission is required');
      }

      // Check if role with same name already exists
      const existingRole = await Role.findOne({ name: data.name });

      if (existingRole) {
        throw new ValidationError(`Role with name '${data.name}' already exists`);
      }

      // Process and validate permissions
      const processedPermissions = [];

      for (const permission of data.permissions) {
        // Handle wildcard permission
        if (permission === '*') {
          processedPermissions.push(permission);
          continue;
        }

        try {
          // Case 1: Permission is already a valid ObjectId
          if (mongoose.Types.ObjectId.isValid(permission)) {
            const permissionExists = await Permission.exists({ _id: permission });
            if (permissionExists) {
              processedPermissions.push(permission);
              continue;
            }
          }

          // Case 2: Permission is in resource:action format
          if (typeof permission === 'string' && permission.includes(':')) {
            const [resource, action] = permission.split(':');
            const permissionDoc = await Permission.findOne({ resource, action });

            if (permissionDoc) {
              processedPermissions.push(permissionDoc._id);
              continue;
            }
          }

          // If we get here, the permission is invalid
          throw new ValidationError(`Permission ${permission} does not exist or is in an invalid format`);
        } catch (err) {
          if (err instanceof ValidationError) {
            throw err;
          }
          logger.error(`Error processing permission ${permission}`, { error: err.message });
          throw new ValidationError(`Error processing permission ${permission}: ${err.message}`);
        }
      }

      if (processedPermissions.length === 0) {
        throw new ValidationError('No valid permissions were provided');
      }

      // Process and validate inherited roles if provided
      let inheritedRoles = [];

      if (data.inheritsFrom) {
        if (!Array.isArray(data.inheritsFrom)) {
          throw new ValidationError('inheritsFrom must be an array');
        }

        // Validate each role ID
        for (const parentRoleId of data.inheritsFrom) {
          // Check if role exists (will throw NotFoundError if not found)
          await this.getRoleById(parentRoleId);
          inheritedRoles.push(parentRoleId);
        }
      }

      // Create new role
      const role = new Role({
        name: data.name,
        description: data.description || '',
        permissions: processedPermissions,
        inheritsFrom: inheritedRoles,
        isSystem: data.isSystem || false,
        isDefault: data.isDefault || false,
        createdBy: data.createdBy
      });

      await role.save();

      // Invalidate cache
      await cacheService.delete(this.CACHE_KEYS.ALL_ROLES);

      return role;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      logger.error('Error creating role', { data, error: error.message });
      throw error;
    }
  }

  /**
   * Update a role
   */
  async updateRole(roleId, data) {
    try {
      const role = await this.getRoleById(roleId);

      // Don't allow updating system roles
      if (role.isSystem) {
        throw new ValidationError('System roles cannot be updated');
      }

      // Check if name is being changed and if it already exists
      if (data.name && data.name !== role.name) {
        const existingRole = await Role.findOne({ name: data.name, _id: { $ne: roleId } });

        if (existingRole) {
          throw new ValidationError(`Role with name '${data.name}' already exists`);
        }
      }

      // Process and validate permissions if provided
      let processedPermissions = role.permissions; // Default to existing permissions

      if (data.permissions) {
        if (!Array.isArray(data.permissions) || data.permissions.length === 0) {
          throw new ValidationError('At least one permission is required');
        }

        processedPermissions = [];

        for (const permission of data.permissions) {
          // Handle wildcard permission
          if (permission === '*') {
            processedPermissions.push(permission);
            continue;
          }

          try {
            // Case 1: Permission is already a valid ObjectId
            if (mongoose.Types.ObjectId.isValid(permission)) {
              const permissionExists = await Permission.exists({ _id: permission });
              if (permissionExists) {
                processedPermissions.push(permission);
                continue;
              }
            }

            // Case 2: Permission is in resource:action format
            if (typeof permission === 'string' && permission.includes(':')) {
              const [resource, action] = permission.split(':');
              const permissionDoc = await Permission.findOne({ resource, action });

              if (permissionDoc) {
                processedPermissions.push(permissionDoc._id);
                continue;
              }
            }

            // If we get here, the permission is invalid
            throw new ValidationError(`Permission ${permission} does not exist or is in an invalid format`);
          } catch (err) {
            if (err instanceof ValidationError) {
              throw err;
            }
            logger.error(`Error processing permission ${permission}`, { error: err.message });
            throw new ValidationError(`Error processing permission ${permission}: ${err.message}`);
          }
        }

        if (processedPermissions.length === 0) {
          throw new ValidationError('No valid permissions were provided');
        }
      }

      // Process and validate inherited roles if provided
      let inheritedRoles = role.inheritsFrom || []; // Default to existing inherited roles

      if (data.inheritsFrom) {
        if (!Array.isArray(data.inheritsFrom)) {
          throw new ValidationError('inheritsFrom must be an array');
        }

        // Validate each role ID
        for (const parentRoleId of data.inheritsFrom) {
          // Skip if it's the same role (can't inherit from self)
          if (parentRoleId === roleId.toString()) {
            throw new ValidationError('A role cannot inherit from itself');
          }

          // Check if role exists
          const parentRole = await this.getRoleById(parentRoleId);

          // Check for circular inheritance
          if (await this._hasCircularInheritance(parentRoleId, [roleId])) {
            throw new ValidationError(`Circular inheritance detected with role ${parentRole.name}`);
          }
        }

        inheritedRoles = data.inheritsFrom;
      }

      // Update role
      role.name = data.name || role.name;
      role.description = data.description !== undefined ? data.description : role.description;
      role.permissions = processedPermissions;
      role.inheritsFrom = inheritedRoles;
      role.isDefault = data.isDefault !== undefined ? data.isDefault : role.isDefault;
      role.updatedBy = data.updatedBy;

      await role.save();

      // Invalidate caches
      await cacheService.delete(this.CACHE_KEYS.ALL_ROLES);

      // Invalidate user permission caches for users with this role
      const userRoles = await UserRole.find({ role: roleId });
      for (const userRole of userRoles) {
        await cacheService.delete(`${this.CACHE_KEYS.USER_PERMISSIONS}${userRole.user}`);
      }

      return role;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error updating role', { roleId, data, error: error.message });
      throw error;
    }
  }

  /**
   * Delete a role
   */
  async deleteRole(roleId) {
    try {
      const role = await this.getRoleById(roleId);

      // Don't allow deleting system roles
      if (role.isSystem) {
        throw new ValidationError('System roles cannot be deleted');
      }

      // Check if role is assigned to any users
      const userRoleCount = await UserRole.countDocuments({ role: roleId });

      if (userRoleCount > 0) {
        throw new ValidationError(`Role is assigned to ${userRoleCount} users and cannot be deleted`);
      }

      // Get users with this role for cache invalidation
      const userRoles = await UserRole.find({ role: roleId });
      const userIds = userRoles.map(ur => ur.user);

      // Delete role
      await Role.deleteOne({ _id: roleId });

      // Invalidate caches
      await cacheService.delete(this.CACHE_KEYS.ALL_ROLES);

      // Invalidate user permission caches
      for (const userId of userIds) {
        await cacheService.delete(`${this.CACHE_KEYS.USER_PERMISSIONS}${userId}`);
      }

      return { success: true, message: `Role ${roleId} deleted` };
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error deleting role', { roleId, error: error.message });
      throw error;
    }
  }

  /**
   * Get all permissions
   */
  async getAllPermissions() {
    try {
      // Check cache first
      const cachedPermissions = await cacheService.get(this.CACHE_KEYS.ALL_PERMISSIONS);

      if (cachedPermissions !== null) {
        return cachedPermissions;
      }

      const permissions = await Permission.find().sort({ resource: 1, action: 1 });

      // Cache permissions for 5 minutes
      await cacheService.set(this.CACHE_KEYS.ALL_PERMISSIONS, permissions, 300);

      return permissions;
    } catch (error) {
      logger.error('Error getting all permissions', { error: error.message });
      throw error;
    }
  }

  /**
   * Get permission by ID
   */
  async getPermissionById(permissionId) {
    try {
      const permission = await Permission.findById(permissionId);

      if (!permission) {
        throw new NotFoundError(`Permission with ID ${permissionId} not found`);
      }

      return permission;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error getting permission by ID', { permissionId, error: error.message });
      throw error;
    }
  }

  /**
   * Get permission by resource and action
   */
  async getPermissionByResourceAction(resource, action) {
    try {
      const permission = await Permission.findOne({ resource, action });

      if (!permission) {
        throw new NotFoundError(`Permission with resource '${resource}' and action '${action}' not found`);
      }

      return permission;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error getting permission by resource and action', { resource, action, error: error.message });
      throw error;
    }
  }

  /**
   * Create a new permission
   */
  async createPermission(data) {
    try {
      if (!data.name) {
        throw new ValidationError('Permission name is required');
      }

      if (!data.resource) {
        throw new ValidationError('Permission resource is required');
      }

      if (!data.action) {
        throw new ValidationError('Permission action is required');
      }

      // Check if permission with same resource and action already exists
      const existingPermission = await Permission.findOne({
        resource: data.resource,
        action: data.action
      });

      if (existingPermission) {
        throw new ValidationError(`Permission with resource '${data.resource}' and action '${data.action}' already exists`);
      }

      // Create new permission
      const permission = new Permission({
        name: data.name,
        description: data.description || '',
        resource: data.resource,
        action: data.action,
        isSystem: data.isSystem || false,
        createdBy: data.createdBy
      });

      await permission.save();

      // Invalidate cache
      await cacheService.delete(this.CACHE_KEYS.ALL_PERMISSIONS);

      return permission;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }

      logger.error('Error creating permission', { data, error: error.message });
      throw error;
    }
  }

  /**
   * Update a permission
   */
  async updatePermission(permissionId, data) {
    try {
      const permission = await this.getPermissionById(permissionId);

      // Don't allow updating system permissions
      if (permission.isSystem) {
        throw new ValidationError('System permissions cannot be updated');
      }

      // Check if resource and action are being changed and if they already exist
      if ((data.resource && data.resource !== permission.resource) ||
          (data.action && data.action !== permission.action)) {
        const existingPermission = await Permission.findOne({
          resource: data.resource || permission.resource,
          action: data.action || permission.action,
          _id: { $ne: permissionId }
        });

        if (existingPermission) {
          throw new ValidationError(`Permission with resource '${data.resource || permission.resource}' and action '${data.action || permission.action}' already exists`);
        }
      }

      // Update permission
      permission.name = data.name || permission.name;
      permission.description = data.description !== undefined ? data.description : permission.description;
      permission.resource = data.resource || permission.resource;
      permission.action = data.action || permission.action;
      permission.updatedBy = data.updatedBy;

      await permission.save();

      // Invalidate caches
      await cacheService.delete(this.CACHE_KEYS.ALL_PERMISSIONS);

      // Find roles that use this permission and invalidate related caches
      const permissionString = `${permission.resource}:${permission.action}`;
      const rolesUsingPermission = await Role.find({ permissions: { $in: [permissionId, permissionString] } });

      if (rolesUsingPermission.length > 0) {
        // Get all users with these roles
        const roleIds = rolesUsingPermission.map(role => role._id);
        const userRoles = await UserRole.find({ role: { $in: roleIds } });
        const userIds = [...new Set(userRoles.map(ur => ur.user))]; // Unique user IDs

        // Invalidate user permission caches
        for (const userId of userIds) {
          await cacheService.delete(`${this.CACHE_KEYS.USER_PERMISSIONS}${userId}`);
        }
      }

      return permission;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error updating permission', { permissionId, data, error: error.message });
      throw error;
    }
  }

  /**
   * Delete a permission
   */
  async deletePermission(permissionId) {
    try {
      const permission = await this.getPermissionById(permissionId);

      // Don't allow deleting system permissions
      if (permission.isSystem) {
        throw new ValidationError('System permissions cannot be deleted');
      }

      // Check if permission is used by any roles
      const permissionString = `${permission.resource}:${permission.action}`;
      const rolesUsingPermission = await Role.find({ permissions: { $in: [permissionId, permissionString] } });

      if (rolesUsingPermission.length > 0) {
        const roleNames = rolesUsingPermission.map(role => role.name).join(', ');
        throw new ValidationError(`Permission is used by the following roles: ${roleNames}`);
      }

      // Delete permission
      await Permission.deleteOne({ _id: permissionId });

      // Invalidate cache
      await cacheService.delete(this.CACHE_KEYS.ALL_PERMISSIONS);

      return { success: true, message: `Permission ${permissionId} deleted` };
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error deleting permission', { permissionId, error: error.message });
      throw error;
    }
  }

  /**
   * Get user roles
   */
  async getUserRoles(userId) {
    try {
      const userRoles = await UserRole.find({ user: userId }).populate('role');
      return userRoles.map(ur => ur.role);
    } catch (error) {
      logger.error('Error getting user roles', { userId, error: error.message });
      throw error;
    }
  }

  /**
   * Get users by role
   */
  async getUsersByRole(roleId) {
    try {
      const userRoles = await UserRole.find({ role: roleId }).populate('user');
      return userRoles.map(ur => ur.user);
    } catch (error) {
      logger.error('Error getting users by role', { roleId, error: error.message });
      throw error;
    }
  }

  /**
   * Assign role to user
   */
  async assignRoleToUser(userId, roleId, scope = 'global', scopeId = null) {
    try {
      // Check if role exists
      const role = await this.getRoleById(roleId);

      // Check if user exists
      const user = await User.findById(userId);

      if (!user) {
        throw new NotFoundError(`User with ID ${userId} not found`);
      }

      // Check if user already has the role in this scope
      const existingUserRole = await UserRole.findOne({
        user: userId,
        role: roleId,
        scope,
        scopeId: scopeId || null
      });

      if (existingUserRole) {
        return { success: true, message: `User ${userId} already has role ${roleId} in ${scope} scope` };
      }

      // Create new user role
      const userRole = new UserRole({
        user: userId,
        role: roleId,
        scope,
        scopeId: scopeId || null
      });

      await userRole.save();

      // Invalidate user permission caches
      await cacheService.delete(`${this.CACHE_KEYS.USER_PERMISSIONS}${userId}`);

      // Invalidate specific permission checks for this user
      const permissionCacheKeys = await cacheService.keys(`${this.CACHE_KEYS.USER_PERMISSION}${userId}:*`);
      for (const key of permissionCacheKeys) {
        await cacheService.delete(key);
      }

      return { success: true, message: `Role ${role.name} assigned to user ${userId}` };
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error assigning role to user', { userId, roleId, scope, scopeId, error: error.message });
      throw error;
    }
  }

  /**
   * Remove role from user
   */
  async removeRoleFromUser(userId, roleId, scope = 'global', scopeId = null) {
    try {
      // Check if user has the role in this scope
      const userRole = await UserRole.findOne({
        user: userId,
        role: roleId,
        scope,
        scopeId: scopeId || null
      });

      if (!userRole) {
        return { success: true, message: `User ${userId} does not have role ${roleId} in ${scope} scope` };
      }

      // Remove role from user
      await UserRole.deleteOne({ _id: userRole._id });

      // Invalidate user permission caches
      await cacheService.delete(`${this.CACHE_KEYS.USER_PERMISSIONS}${userId}`);

      // Invalidate specific permission checks for this user
      const permissionCacheKeys = await cacheService.keys(`${this.CACHE_KEYS.USER_PERMISSION}${userId}:*`);
      for (const key of permissionCacheKeys) {
        await cacheService.delete(key);
      }

      return { success: true, message: `Role removed from user ${userId}` };
    } catch (error) {
      logger.error('Error removing role from user', { userId, roleId, scope, scopeId, error: error.message });
      throw error;
    }
  }

  /**
   * Check if user has permission
   */
  async hasPermission(userId, permissionId) {
    try {
      // Check cache first
      const cacheKey = `rbac:permission:${userId}:${permissionId}`;
      const cachedResult = await cacheService.get(cacheKey);

      if (cachedResult !== null) {
        return cachedResult;
      }

      // Get user roles with populated inheritsFrom
      const userRoles = await UserRole.find({ user: userId }).populate({
        path: 'role',
        populate: {
          path: 'inheritsFrom',
          model: 'Role'
        }
      });

      if (userRoles.length === 0) {
        // Cache negative result for a shorter time (1 minute)
        await cacheService.set(cacheKey, false, 60);
        return false;
      }

      // Check if any role has the permission
      for (const userRole of userRoles) {
        const role = userRole.role;

        // Check if role has the permission directly or through inheritance
        if (await this._roleHasPermission(role, permissionId)) {
          // Cache positive result (5 minutes)
          await cacheService.set(cacheKey, true, 300);
          return true;
        }
      }

      // Cache negative result for a shorter time (1 minute)
      await cacheService.set(cacheKey, false, 60);
      return false;
    } catch (error) {
      logger.error('Error checking user permission', { userId, permissionId, error: error.message });
      return false;
    }
  }

  /**
   * Check if a role has a specific permission, including through inheritance
   * @private
   */
  async _roleHasPermission(role, permissionId) {
    // Check if role has wildcard permission
    if (role.permissions.includes('*')) {
      return true;
    }

    // Check if role has the specific permission
    if (role.permissions.includes(permissionId)) {
      return true;
    }

    // Check if permission is in format 'resource:action'
    if (typeof permissionId === 'string' && permissionId.includes(':')) {
      const [resource, action] = permissionId.split(':');

      // Check if role has wildcard permission for the resource
      const resourceWildcard = `${resource}:*`;
      if (role.permissions.includes(resourceWildcard)) {
        return true;
      }

      // Check if permission exists in the database
      try {
        const permission = await this.getPermissionByResourceAction(resource, action);
        if (role.permissions.includes(permission._id.toString())) {
          return true;
        }
      } catch (error) {
        // Permission not found, continue checking
      }
    }

    // Check inherited roles
    if (role.inheritsFrom && role.inheritsFrom.length > 0) {
      for (const parentRole of role.inheritsFrom) {
        if (await this._roleHasPermission(parentRole, permissionId)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Check if a role inheritance chain has circular references
   * @private
   */
  async _hasCircularInheritance(roleId, visitedRoles = []) {
    try {
      // Get the role
      const role = await Role.findById(roleId).populate('inheritsFrom');

      if (!role || !role.inheritsFrom || role.inheritsFrom.length === 0) {
        return false;
      }

      // Check if any of the parent roles are in the visited roles
      for (const parentRole of role.inheritsFrom) {
        if (visitedRoles.includes(parentRole._id.toString())) {
          return true;
        }

        // Recursively check parent roles
        const hasCircular = await this._hasCircularInheritance(
          parentRole._id,
          [...visitedRoles, roleId.toString()]
        );

        if (hasCircular) {
          return true;
        }
      }

      return false;
    } catch (error) {
      logger.error('Error checking circular inheritance', { roleId, visitedRoles, error: error.message });
      return false;
    }
  }

  /**
   * Get all permissions for a role, including inherited permissions
   */
  async getRolePermissions(roleId) {
    try {
      // Get the role with populated inheritsFrom
      const role = await Role.findById(roleId).populate('inheritsFrom');

      if (!role) {
        throw new NotFoundError(`Role with ID ${roleId} not found`);
      }

      // Get all permissions
      const allPermissions = await this.getAllPermissions();

      // Collect all permissions from the role and its inherited roles
      const rolePermissions = new Set();

      // Add direct permissions
      await this._addRolePermissions(role, rolePermissions, allPermissions);

      return Array.from(rolePermissions);
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error getting role permissions', { roleId, error: error.message });
      throw error;
    }
  }

  /**
   * Helper method to add permissions from a role and its inherited roles
   * @private
   */
  async _addRolePermissions(role, permissionsSet, allPermissions) {
    // If role has wildcard permission, add all permissions
    if (role.permissions.includes('*')) {
      allPermissions.forEach(permission => {
        permissionsSet.add(`${permission.resource}:${permission.action}`);
      });
      return;
    }

    // Add specific permissions
    for (const permissionId of role.permissions) {
      // Handle resource wildcard permissions
      if (typeof permissionId === 'string' && permissionId.endsWith(':*')) {
        const resourcePrefix = permissionId.split(':')[0];
        allPermissions
          .filter(p => p.resource === resourcePrefix)
          .forEach(p => permissionsSet.add(`${p.resource}:${p.action}`));
      } else {
        permissionsSet.add(permissionId);
      }
    }

    // Add permissions from inherited roles
    if (role.inheritsFrom && role.inheritsFrom.length > 0) {
      for (const parentRole of role.inheritsFrom) {
        await this._addRolePermissions(parentRole, permissionsSet, allPermissions);
      }
    }
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(userId) {
    try {
      // Check cache first
      const cacheKey = `rbac:user-permissions:${userId}`;
      const cachedPermissions = await cacheService.get(cacheKey);

      if (cachedPermissions !== null) {
        return cachedPermissions;
      }

      // Get user roles
      const userRoles = await UserRole.find({ user: userId }).populate({
        path: 'role',
        populate: {
          path: 'inheritsFrom',
          model: 'Role'
        }
      });

      if (userRoles.length === 0) {
        // Cache empty result for a short time (1 minute)
        await cacheService.set(cacheKey, [], 60);
        return [];
      }

      // Get all permissions
      const allPermissions = await this.getAllPermissions();

      // Collect all permissions from user roles
      const userPermissions = new Set();

      for (const userRole of userRoles) {
        const role = userRole.role;

        // Add permissions from this role and its inherited roles
        await this._addRolePermissions(role, userPermissions, allPermissions);
      }

      const result = Array.from(userPermissions);

      // Cache result for 5 minutes
      await cacheService.set(cacheKey, result, 300);

      return result;
    } catch (error) {
      logger.error('Error getting user permissions', { userId, error: error.message });
      throw error;
    }
  }

  /**
   * Get role inheritance tree
   *
   * This method returns a tree structure showing the role and all roles it inherits from,
   * including nested inheritance.
   */
  async getRoleInheritanceTree(roleId) {
    try {
      // Get the role with populated inheritsFrom
      const role = await Role.findById(roleId).populate('inheritsFrom');

      if (!role) {
        throw new NotFoundError(`Role with ID ${roleId} not found`);
      }

      // Build the inheritance tree
      const inheritanceTree = await this._buildInheritanceTree(role);

      return inheritanceTree;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }

      logger.error('Error getting role inheritance tree', { roleId, error: error.message });
      throw error;
    }
  }

  /**
   * Helper method to build a role inheritance tree
   * @private
   */
  async _buildInheritanceTree(role, visited = new Set()) {
    // Prevent circular references
    if (visited.has(role._id.toString())) {
      return {
        _id: role._id,
        name: role.name,
        circular: true
      };
    }

    // Add this role to visited set
    visited.add(role._id.toString());

    // Create tree node for this role
    const node = {
      _id: role._id,
      name: role.name,
      description: role.description,
      isSystem: role.isSystem,
      isDefault: role.isDefault
    };

    // Add inherited roles if any
    if (role.inheritsFrom && role.inheritsFrom.length > 0) {
      node.inheritsFrom = [];

      for (const parentRole of role.inheritsFrom) {
        // If parentRole is just an ID, fetch the full role
        const fullParentRole = mongoose.Types.ObjectId.isValid(parentRole) && !parentRole.name
          ? await Role.findById(parentRole).populate('inheritsFrom')
          : parentRole;

        if (fullParentRole) {
          // Recursively build tree for parent role
          const parentNode = await this._buildInheritanceTree(
            fullParentRole,
            new Set(visited) // Create a new copy of visited set
          );

          node.inheritsFrom.push(parentNode);
        }
      }
    }

    return node;
  }
}

module.exports = RBACService;
