
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaVision Trinity Visualization</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: '<PERSON><PERSON>', 'Segoe UI', Arial, sans-serif;
      background-color: #f5f5f5;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
    }

    .header h1 {
      margin: 0;
      font-size: 32px;
      background: linear-gradient(45deg, #4CAF50, #2196F3, #9C27B0);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-fill-color: transparent;
    }

    .header p {
      margin: 10px 0 0;
      font-size: 18px;
      color: #666;
    }

    .content {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .card {
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .card h2 {
      margin-top: 0;
      color: #2196F3;
    }

    .card p {
      margin-bottom: 20px;
    }

    .button {
      display: inline-block;
      background-color: #2196F3;
      color: white;
      padding: 10px 20px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.2s ease;
    }

    .button:hover {
      background-color: #1976D2;
    }

    .footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #ddd;
      color: #666;
    }

    @keyframes gradientShift {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }

    .header h1 {
      background-size: 200% 200%;
      animation: gradientShift 5s ease infinite;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>NovaVision Trinity Visualization</h1>
      <p>Integrated with NovaFuse Universal Ripple Stack</p>
    </div>

    <div class="content">
      <div class="card">
        <h2>Static Visualization</h2>
        <p>
          View the static HTML version of the Trinity Visualization, which demonstrates
          the "wheels within wheels" concept of the NovaFuse architecture.
        </p>
        <a href="/static" class="button">View Static Visualization</a>
      </div>

      <div class="card">
        <h2>NovaVision Integration</h2>
        <p>
          Experience the full NovaVision-integrated Trinity Visualization with real-time
          data flow from the Universal Ripple Stack.
        </p>
        <a href="/novavision" class="button">Launch NovaVision Integration</a>
      </div>
    </div>

    <div class="footer">
      <p>NovaFuse Universal Ripple Stack - Powered by Comphyology (Ψᶜ)</p>
    </div>
  </div>
</body>
</html>
  