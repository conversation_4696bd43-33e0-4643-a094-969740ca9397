import React from 'react';
import '../styles/ComplianceBadge.css';

const COMPLIANCE_LEVELS = {
  low: {
    label: 'Low',
    description: 'Standard security protocols',
    color: '#4CAF50', // Green
    icon: '✓',
  },
  standard: {
    label: 'Standard',
    description: 'Enhanced monitoring',
    color: '#2196F3', // Blue
    icon: '✓✓',
  },
  high: {
    label: 'High',
    description: 'Strict security controls',
    color: '#FFC107', // Amber
    icon: '⚠',
  },
  critical: {
    label: 'Critical',
    description: 'Maximum security protocols',
    color: '#F44336', // Red
    icon: '⚡',
  },
};

const ComplianceBadge = ({ level = 'standard', mode = 'classical' }) => {
  const compliance = COMPLIANCE_LEVELS[level.toLowerCase()] || COMPLIANCE_LEVELS.standard;
  const isQuantum = mode === 'quantum';

  return (
    <div 
      className={`compliance-badge ${level.toLowerCase()} ${isQuantum ? 'quantum' : 'classical'}`}
      style={{ '--compliance-color': compliance.color }}
    >
      <div className="badge-header">
        <span className="badge-icon">{compliance.icon}</span>
        <span className="badge-title">
          {compliance.label} Compliance
          <span className="mode-indicator">{isQuantum ? ' (Quantum)' : ' (Classical)'}</span>
        </span>
      </div>
      
      <div className="badge-content">
        <div className="compliance-level">
          <span className="level-label">Level:</span>
          <span className="level-value">{level.toUpperCase()}</span>
        </div>
        
        <div className="compliance-description">
          {compliance.description}
          {isQuantum && (
            <span className="quantum-info">
              {' '}with quantum validation
            </span>
          )}
        </div>
        
        <div className="security-features">
          <div className="feature">
            <span className="feature-icon">🔒</span>
            <span>End-to-end encrypted</span>
          </div>
          <div className="feature">
            <span className="feature-icon">
              {isQuantum ? '🔮' : '🔍'}
            </span>
            <span>{isQuantum ? 'Quantum-secured' : 'Classical security'}</span>
          </div>
        </div>
      </div>
      
      <div className="badge-footer">
        <span className="timestamp">
          Last updated: {new Date().toLocaleTimeString()}
        </span>
      </div>
    </div>
  );
};

export default ComplianceBadge;
