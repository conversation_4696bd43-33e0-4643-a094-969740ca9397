/**
 * NovaActuary™ Test Suite
 * Comprehensive testing of the ∂Ψ=0 Underwriting Revolution
 * 
 * Tests all integrated components:
 * - CSM-PRS AI Validation
 * - Comphyology ∂Ψ=0 Analysis
 * - Trinity Oracle Black Swan Prediction
 * - π-Coherence Pattern Analysis
 * - Mathematical Premium Calculation
 */

const { NovaActuary } = require('./index');
const { performance } = require('perf_hooks');

class NovaActuaryTestSuite {
  constructor() {
    this.novaActuary = new NovaActuary();
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
  }

  /**
   * Run comprehensive test suite
   */
  async runAllTests() {
    console.log('🚀 Starting NovaActuary™ Test Suite');
    console.log('=' .repeat(60));
    
    const startTime = performance.now();
    
    // Test 1: Basic Actuarial Assessment
    await this.testBasicActuarialAssessment();
    
    // Test 2: High-Risk Client Assessment
    await this.testHighRiskAssessment();
    
    // Test 3: Revolutionary System Assessment
    await this.testRevolutionarySystemAssessment();
    
    // Test 4: Performance Benchmarking
    await this.testPerformanceBenchmarking();
    
    // Test 5: Mathematical Validation
    await this.testMathematicalValidation();
    
    // Test 6: Integration Testing
    await this.testComponentIntegration();
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    // Generate test report
    this.generateTestReport(totalTime);
    
    return {
      totalTests: this.totalTests,
      passedTests: this.passedTests,
      failedTests: this.totalTests - this.passedTests,
      successRate: (this.passedTests / this.totalTests * 100).toFixed(2),
      totalTime: totalTime.toFixed(2)
    };
  }

  /**
   * Test 1: Basic Actuarial Assessment
   */
  async testBasicActuarialAssessment() {
    console.log('\n📊 Test 1: Basic Actuarial Assessment');
    
    const testClient = {
      name: 'Test Insurance Client',
      aiSystems: {
        name: 'Client AI System',
        type: 'machine_learning',
        domain: 'financial_services'
      },
      financialData: {
        revenue: 50000000,
        assets: 200000000,
        liabilities: 75000000,
        riskScore: 0.3
      },
      testData: {
        privacyCompliance: 0.85,
        securityScore: 0.90,
        fairnessMetrics: 0.80
      }
    };
    
    try {
      const result = await this.novaActuary.performActuarialAssessment(testClient, 'comprehensive');
      
      // Validate result structure
      this.assert(result.client === testClient.name, 'Client name matches');
      this.assert(result.csm_prs_score !== undefined, 'CSM-PRS score present');
      this.assert(result.comphyology_score !== undefined, 'Comphyology score present');
      this.assert(result.mathematical_premium > 0, 'Mathematical premium calculated');
      this.assert(result.risk_classification !== undefined, 'Risk classification assigned');
      this.assert(result.novaactuary_validated === true, 'NovaActuary validation confirmed');
      
      console.log(`✅ Basic assessment completed successfully`);
      console.log(`   Risk Classification: ${result.risk_classification}`);
      console.log(`   Mathematical Premium: $${result.mathematical_premium.toLocaleString()}`);
      console.log(`   Processing Time: ${result.processing_time_ms.toFixed(2)}ms`);
      
    } catch (error) {
      this.fail(`Basic actuarial assessment failed: ${error.message}`);
    }
  }

  /**
   * Test 2: High-Risk Client Assessment
   */
  async testHighRiskAssessment() {
    console.log('\n⚠️  Test 2: High-Risk Client Assessment');
    
    const highRiskClient = {
      name: 'High Risk Crypto Exchange',
      aiSystems: {
        name: 'Crypto Trading AI',
        type: 'high_frequency_trading',
        domain: 'cryptocurrency'
      },
      financialData: {
        revenue: 10000000,
        assets: 50000000,
        liabilities: 45000000,
        riskScore: 0.9
      },
      testData: {
        privacyCompliance: 0.45,
        securityScore: 0.55,
        fairnessMetrics: 0.40
      }
    };
    
    try {
      const result = await this.novaActuary.performActuarialAssessment(highRiskClient, 'high_risk');
      
      // Validate high-risk handling
      this.assert(result.risk_classification === 'HIGH_RISK' || result.risk_classification === 'UNINSURABLE', 
                 'High risk properly classified');
      this.assert(result.mathematical_premium > 500000, 'High premium for high risk');
      this.assert(result.psi_deviation > 0.3, 'High ∂Ψ deviation detected');
      
      console.log(`✅ High-risk assessment completed`);
      console.log(`   Risk Classification: ${result.risk_classification}`);
      console.log(`   Mathematical Premium: $${result.mathematical_premium.toLocaleString()}`);
      console.log(`   ∂Ψ Deviation: ${result.psi_deviation.toFixed(4)}`);
      
    } catch (error) {
      this.fail(`High-risk assessment failed: ${error.message}`);
    }
  }

  /**
   * Test 3: Revolutionary System Assessment
   */
  async testRevolutionarySystemAssessment() {
    console.log('\n🌟 Test 3: Revolutionary System Assessment');
    
    const revolutionaryClient = {
      name: 'Revolutionary AI Healthcare System',
      aiSystems: {
        name: 'Medical AI with Consciousness',
        type: 'consciousness_native',
        domain: 'healthcare'
      },
      financialData: {
        revenue: 100000000,
        assets: 500000000,
        liabilities: 50000000,
        riskScore: 0.05
      },
      testData: {
        privacyCompliance: 0.98,
        securityScore: 0.97,
        fairnessMetrics: 0.96,
        consciousnessLevel: 0.95
      }
    };
    
    try {
      const result = await this.novaActuary.performActuarialAssessment(revolutionaryClient, 'revolutionary');
      
      // Validate revolutionary system handling
      this.assert(result.risk_classification === 'REVOLUTIONARY' || result.risk_classification === 'EXCELLENT', 
                 'Revolutionary system properly classified');
      this.assert(result.mathematical_premium < 100000, 'Low premium for revolutionary system');
      this.assert(result.psi_deviation < 0.2, 'Low ∂Ψ deviation for stable system');
      this.assert(result.certification_level.includes('REVOLUTIONARY') || result.certification_level.includes('CERTIFIED'), 
                 'High certification level assigned');
      
      console.log(`✅ Revolutionary system assessment completed`);
      console.log(`   Risk Classification: ${result.risk_classification}`);
      console.log(`   Mathematical Premium: $${result.mathematical_premium.toLocaleString()}`);
      console.log(`   Certification Level: ${result.certification_level}`);
      
    } catch (error) {
      this.fail(`Revolutionary system assessment failed: ${error.message}`);
    }
  }

  /**
   * Test 4: Performance Benchmarking
   */
  async testPerformanceBenchmarking() {
    console.log('\n⚡ Test 4: Performance Benchmarking');
    
    const benchmarkClient = {
      name: 'Performance Benchmark Client',
      aiSystems: { name: 'Benchmark AI', type: 'standard', domain: 'general' },
      financialData: { revenue: 25000000, assets: 100000000, liabilities: 30000000, riskScore: 0.4 },
      testData: { privacyCompliance: 0.75, securityScore: 0.80, fairnessMetrics: 0.70 }
    };
    
    const iterations = 10;
    const processingTimes = [];
    
    try {
      console.log(`   Running ${iterations} iterations for performance measurement...`);
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        await this.novaActuary.performActuarialAssessment(benchmarkClient, 'benchmark');
        const endTime = performance.now();
        processingTimes.push(endTime - startTime);
      }
      
      const avgProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / iterations;
      const minTime = Math.min(...processingTimes);
      const maxTime = Math.max(...processingTimes);
      
      // Performance assertions
      this.assert(avgProcessingTime < 1000, 'Average processing time under 1 second');
      this.assert(minTime < 500, 'Minimum processing time under 500ms');
      
      console.log(`✅ Performance benchmarking completed`);
      console.log(`   Average Processing Time: ${avgProcessingTime.toFixed(2)}ms`);
      console.log(`   Min Time: ${minTime.toFixed(2)}ms`);
      console.log(`   Max Time: ${maxTime.toFixed(2)}ms`);
      console.log(`   Speed Advantage: ${this.novaActuary.metrics.speedAdvantage}x faster than traditional`);
      
    } catch (error) {
      this.fail(`Performance benchmarking failed: ${error.message}`);
    }
  }

  /**
   * Test 5: Mathematical Validation
   */
  async testMathematicalValidation() {
    console.log('\n🧮 Test 5: Mathematical Validation');
    
    const mathClient = {
      name: 'Mathematical Validation Client',
      aiSystems: { name: 'Math AI', type: 'analytical', domain: 'mathematics' },
      financialData: { revenue: 75000000, assets: 300000000, liabilities: 100000000, riskScore: 0.25 },
      testData: { privacyCompliance: 0.88, securityScore: 0.92, fairnessMetrics: 0.85 }
    };
    
    try {
      const result = await this.novaActuary.performActuarialAssessment(mathClient, 'mathematical');
      
      // Validate mathematical components
      this.assert(result.mathematical_justification !== undefined, 'Mathematical justification provided');
      this.assert(result.mathematical_justification.csm_prs_validation !== undefined, 'CSM-PRS validation documented');
      this.assert(result.mathematical_justification.comphyology_analysis !== undefined, 'Comphyology analysis documented');
      this.assert(result.mathematical_justification.psi_zero_enforcement !== undefined, '∂Ψ=0 enforcement documented');
      this.assert(result.mathematical_justification.pi_coherence_patterns !== undefined, 'π-coherence patterns documented');
      
      // Validate mathematical consistency
      this.assert(result.psi_deviation >= 0 && result.psi_deviation <= 1, '∂Ψ deviation in valid range');
      this.assert(result.pi_coherence_score >= 0 && result.pi_coherence_score <= 1, 'π-coherence score in valid range');
      
      console.log(`✅ Mathematical validation completed`);
      console.log(`   ∂Ψ Deviation: ${result.psi_deviation.toFixed(4)}`);
      console.log(`   π-Coherence Score: ${result.pi_coherence_score.toFixed(4)}`);
      console.log(`   Mathematical Certainty: Verified`);
      
    } catch (error) {
      this.fail(`Mathematical validation failed: ${error.message}`);
    }
  }

  /**
   * Test 6: Component Integration
   */
  async testComponentIntegration() {
    console.log('\n🔗 Test 6: Component Integration Testing');
    
    const integrationClient = {
      name: 'Integration Test Client',
      aiSystems: { name: 'Integration AI', type: 'multi_component', domain: 'integration' },
      financialData: { revenue: 60000000, assets: 250000000, liabilities: 80000000, riskScore: 0.35 },
      testData: { privacyCompliance: 0.82, securityScore: 0.87, fairnessMetrics: 0.79 }
    };
    
    try {
      const result = await this.novaActuary.performActuarialAssessment(integrationClient, 'integration');
      
      // Validate all components are integrated
      this.assert(result.csm_prs_score !== undefined, 'CSM-PRS component integrated');
      this.assert(result.comphyology_score !== undefined, 'Comphyology component integrated');
      this.assert(result.black_swan_risk !== undefined, 'Trinity Oracle component integrated');
      this.assert(result.pi_coherence_score !== undefined, 'π-coherence component integrated');
      
      // Validate NovaConnect integration
      this.assert(result.novaactuary_validated === true, 'NovaConnect validation confirmed');
      this.assert(result.assessment_id.startsWith('NA-'), 'NovaActuary assessment ID format');
      
      console.log(`✅ Component integration testing completed`);
      console.log(`   All components successfully integrated`);
      console.log(`   Assessment ID: ${result.assessment_id}`);
      
    } catch (error) {
      this.fail(`Component integration testing failed: ${error.message}`);
    }
  }

  /**
   * Assert helper function
   */
  assert(condition, message) {
    this.totalTests++;
    if (condition) {
      this.passedTests++;
      console.log(`     ✅ ${message}`);
    } else {
      console.log(`     ❌ ${message}`);
      this.testResults.push({ test: message, status: 'FAILED' });
    }
  }

  /**
   * Fail helper function
   */
  fail(message) {
    this.totalTests++;
    console.log(`     ❌ ${message}`);
    this.testResults.push({ test: message, status: 'FAILED' });
  }

  /**
   * Generate comprehensive test report
   */
  generateTestReport(totalTime) {
    console.log('\n' + '='.repeat(60));
    console.log('📋 NOVAACTUARY™ TEST REPORT');
    console.log('='.repeat(60));
    
    console.log(`🧪 Total Tests: ${this.totalTests}`);
    console.log(`✅ Passed Tests: ${this.passedTests}`);
    console.log(`❌ Failed Tests: ${this.totalTests - this.passedTests}`);
    console.log(`📊 Success Rate: ${(this.passedTests / this.totalTests * 100).toFixed(2)}%`);
    console.log(`⏱️  Total Time: ${totalTime.toFixed(2)}ms`);
    console.log(`⚡ Average Test Time: ${(totalTime / 6).toFixed(2)}ms`);
    
    if (this.testResults.length > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults.forEach(result => {
        console.log(`   - ${result.test}`);
      });
    }
    
    console.log('\n🎯 NovaActuary™ Performance Metrics:');
    console.log(`   - Black Swan Prediction Accuracy: ${(this.novaActuary.metrics.accuracyRate * 100).toFixed(1)}%`);
    console.log(`   - Claims Reduction: ${(this.novaActuary.metrics.claimsReduction * 100).toFixed(1)}%`);
    console.log(`   - Speed Advantage: ${this.novaActuary.metrics.speedAdvantage}x faster`);
    console.log(`   - Total Assessments: ${this.novaActuary.metrics.totalAssessments}`);
    
    const overallStatus = this.passedTests === this.totalTests ? 'PASSED' : 'FAILED';
    console.log(`\n🏆 Overall Test Status: ${overallStatus}`);
    
    if (overallStatus === 'PASSED') {
      console.log('🚀 NovaActuary™ is ready for deployment!');
    } else {
      console.log('⚠️  NovaActuary™ requires fixes before deployment.');
    }
    
    console.log('='.repeat(60));
  }
}

// Run tests if called directly
if (require.main === module) {
  const testSuite = new NovaActuaryTestSuite();
  testSuite.runAllTests().then(results => {
    console.log('\n🎉 Test suite completed!');
    process.exit(results.failedTests > 0 ? 1 : 0);
  }).catch(error => {
    console.error('💥 Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = {
  NovaActuaryTestSuite
};
