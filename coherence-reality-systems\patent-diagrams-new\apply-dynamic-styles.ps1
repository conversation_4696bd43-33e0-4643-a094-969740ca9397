# Script to apply dynamic styles to all patent diagram HTML files
# This script ensures all bubbles expand dynamically with content

# List of specific patent diagram files
$patentDiagrams = @(
    "cyber-safety-architecture-fixed.html",
    "unified-field-theory.html",
    "alignment-architecture-final.html",
    "novafuse-components.html",
    "hardware-software-implementation.html",
    "detailed-data-flow.html",
    "18-82-principle.html",
    "cyber-safety-incident-response.html",
    "adaptive-compliance-process.html",
    "comphyology-universal-application-framework.html",
    "financial-services-architecture.html",
    "healthcare-architecture.html",
    "visualization-output-examples.html",
    "comphyology-mathematical-framework.html",
    "uuft-networked-systems.html"
)

# Extract CSS from the template
$templatePath = Join-Path -Path "D:\novafuse-api-superstore\patent-diagrams-new" -ChildPath "patent-diagram-template.html"
$templateContent = Get-Content -Path $templatePath -Raw

# Extract CSS from template
if ($templateContent -match "(?s)<style>(.*?)</style>") {
    $cssTemplate = $Matches[1]
} else {
    Write-Error "Could not extract CSS from template"
    exit 1
}

# Process each patent diagram file
foreach ($fileName in $patentDiagrams) {
    $filePath = Join-Path -Path "D:\novafuse-api-superstore\patent-diagrams-new" -ChildPath $fileName

    # Check if file exists
    if (Test-Path $filePath) {
        Write-Host "Processing $fileName..."

        # Read the file content
        $content = Get-Content -Path $filePath -Raw

        # Create a backup of the original file
        Copy-Item -Path $filePath -Destination "$filePath.dynamic.bak"

        # Replace CSS styles
        # First, find the style section
        if ($content -match "(?s)<style>(.*?)</style>") {
            $styleSection = $Matches[0]
            $newStyleSection = "<style>`n$cssTemplate`n    </style>"
            $content = $content -replace [regex]::Escape($styleSection), $newStyleSection
        }

        # Update diagram container to be dynamic
        $content = $content -replace 'height: 700px', 'min-height: 700px'
        $content = $content -replace 'overflow: hidden;', 'overflow: visible;'

        # Update container box styles
        $content = $content -replace '<div class="container-box"', '<div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;"'

        # Update component box styles
        $content = $content -replace '<div class="component-box"', '<div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;"'

        # Convert component-number to component-number-inside if needed
        $content = $content -replace '<div class="component-number"([^>]*)>', '<div class="component-number-inside">'

        # Update component label styles
        $content = $content -replace '<div class="component-label"', '<div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;"'

        # Update component content styles
        $content = $content -replace '<div class="component-content"', '<div class="component-content" style="overflow: visible; font-size: 10px; line-height: 1.2;"'

        # Fix any height issues in arrows
        $content = $content -replace 'height: 700px', 'height: 70px'

        # Ensure inventor label is positioned correctly
        if ($content -match '<div class="inventor-label">') {
            if (!($content -match '<div class="inventor-label"[^>]*style="[^"]*position: absolute[^"]*"')) {
                $content = $content -replace '<div class="inventor-label">', '<div class="inventor-label" style="position: absolute; left: 10px; bottom: 30px; font-size: 12px; font-style: italic; color: #333; z-index: 10;">'
            }
        } else {
            # Add inventor label if missing
            $content = $content -replace '</div>\s*</body>', '    <div class="inventor-label" style="position: absolute; left: 10px; bottom: 30px; font-size: 12px; font-style: italic; color: #333; z-index: 10;">Inventor: David Nigel Irvin</div>
</div>
</body>'
        }

        # Ensure legend is positioned correctly
        if ($content -match '<div class="legend">') {
            if (!($content -match '<div class="legend"[^>]*style="[^"]*position: absolute[^"]*"')) {
                $content = $content -replace '<div class="legend">', '<div class="legend" style="position: absolute; right: 10px; bottom: 30px; background-color: white; border: 1px solid #ddd; border-radius: 4px; padding: 8px; z-index: 10; width: 200px; font-size: 12px;">'
            }
        }

        # Write the updated content back to the file
        Set-Content -Path $filePath -Value $content

        Write-Host "Updated $fileName successfully with dynamic styles."
    } else {
        Write-Host "File not found: $fileName"
    }
}

Write-Host "All patent diagram files have been updated with dynamic styles."
