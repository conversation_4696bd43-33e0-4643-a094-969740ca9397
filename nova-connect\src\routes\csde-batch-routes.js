/**
 * CSDE Batch Processing Routes
 * 
 * This module provides API routes for the CSDE batch processor.
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const CSEDConnector = require('../connectors/csde-connector');
const CSEDBatchProcessor = require('../integrations/csde-batch-processor');

// Create router
const router = express.Router();

// Create CSDE connector
let csdeConnector = null;

// Create CSDE batch processor
let csdeBatchProcessor = null;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'data', 'input');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    // Accept only JSON files
    if (file.mimetype === 'application/json') {
      cb(null, true);
    } else {
      cb(new Error('Only JSON files are allowed'));
    }
  }
});

/**
 * Initialize the CSDE connector and batch processor
 * @param {Object} options - Initialization options
 */
function initialize(options = {}) {
  const logger = options.logger || console;
  
  logger.info('Initializing CSDE batch routes');
  
  // Create CSDE connector if not already created
  if (!csdeConnector) {
    csdeConnector = new CSEDConnector({
      id: 'csde-connector',
      name: 'CSDE Connector',
      description: 'Connector for the Cyber-Safety Domain Engine (CSDE)',
      csdeApiUrl: process.env.CSDE_API_URL || 'http://localhost:3010',
      enableCaching: process.env.ENABLE_CACHING !== 'false',
      enableMetrics: process.env.ENABLE_METRICS !== 'false',
      cacheSize: parseInt(process.env.CACHE_SIZE || '1000', 10),
      domain: process.env.DOMAIN || 'security',
      logger
    });
    
    logger.info('CSDE connector created');
  }
  
  // Create CSDE batch processor if not already created
  if (!csdeBatchProcessor) {
    csdeBatchProcessor = new CSEDBatchProcessor({
      csdeConnector,
      batchSize: parseInt(process.env.BATCH_SIZE || '10', 10),
      concurrency: parseInt(process.env.CONCURRENCY || '5', 10),
      inputDir: process.env.INPUT_DIR || path.join(process.cwd(), 'data', 'input'),
      outputDir: process.env.OUTPUT_DIR || path.join(process.cwd(), 'data', 'output'),
      archiveDir: process.env.ARCHIVE_DIR || path.join(process.cwd(), 'data', 'archive'),
      enableRemediation: process.env.ENABLE_REMEDIATION !== 'false',
      autoRemediate: process.env.AUTO_REMEDIATE === 'true',
      remediationThreshold: process.env.REMEDIATION_THRESHOLD || 'HIGH',
      maxConcurrentRemediations: parseInt(process.env.MAX_CONCURRENT_REMEDIATIONS || '5', 10),
      remediationServiceUrl: process.env.REMEDIATION_SERVICE_URL,
      remediationServiceToken: process.env.REMEDIATION_SERVICE_TOKEN,
      dryRun: process.env.DRY_RUN === 'true',
      logger
    });
    
    logger.info('CSDE batch processor created');
  }
}

/**
 * Start batch processing
 */
router.post('/start', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeBatchProcessor) {
      initialize({ logger: req.app.locals.logger });
    }
    
    // Get options from request body
    const options = req.body || {};
    
    // Start processing
    const jobId = await csdeBatchProcessor.startProcessing(options);
    
    if (!jobId) {
      return res.status(409).json({
        success: false,
        message: 'Batch processing already in progress'
      });
    }
    
    // Return job ID
    res.json({
      success: true,
      jobId,
      message: 'Batch processing started'
    });
  } catch (error) {
    req.app.locals.logger.error('Error starting batch processing', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Queue batch processing job
 */
router.post('/queue', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeBatchProcessor) {
      initialize({ logger: req.app.locals.logger });
    }
    
    // Get options from request body
    const options = req.body || {};
    
    // Queue job
    const jobId = csdeBatchProcessor.queueJob(options);
    
    // Return job ID
    res.json({
      success: true,
      jobId,
      message: 'Job queued for processing'
    });
  } catch (error) {
    req.app.locals.logger.error('Error queuing batch processing job', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Cancel batch processing job
 */
router.post('/cancel/:jobId', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeBatchProcessor) {
      initialize({ logger: req.app.locals.logger });
    }
    
    // Get job ID from params
    const jobId = req.params.jobId;
    
    // Cancel job
    const cancelled = csdeBatchProcessor.cancelJob(jobId);
    
    if (!cancelled) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }
    
    // Return success
    res.json({
      success: true,
      message: 'Job cancelled'
    });
  } catch (error) {
    req.app.locals.logger.error('Error cancelling batch processing job', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get batch processing status
 */
router.get('/status', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeBatchProcessor) {
      initialize({ logger: req.app.locals.logger });
    }
    
    // Get active jobs
    const activeJobs = csdeBatchProcessor.getActiveJobs();
    
    // Get job queue
    const jobQueue = csdeBatchProcessor.getJobQueue();
    
    // Return status
    res.json({
      success: true,
      isProcessing: activeJobs.length > 0,
      activeJobs,
      queueLength: jobQueue.length,
      jobQueue
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting batch processing status', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get job status
 */
router.get('/job/:jobId', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeBatchProcessor) {
      initialize({ logger: req.app.locals.logger });
    }
    
    // Get job ID from params
    const jobId = req.params.jobId;
    
    // Get job
    const job = csdeBatchProcessor.getJob(jobId);
    
    if (!job) {
      return res.status(404).json({
        success: false,
        message: 'Job not found'
      });
    }
    
    // Return job
    res.json({
      success: true,
      job
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting job status', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get job history
 */
router.get('/history', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeBatchProcessor) {
      initialize({ logger: req.app.locals.logger });
    }
    
    // Get job history
    const jobHistory = csdeBatchProcessor.getJobHistory();
    
    // Return job history
    res.json({
      success: true,
      jobHistory
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting job history', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Clear job history
 */
router.post('/history/clear', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeBatchProcessor) {
      initialize({ logger: req.app.locals.logger });
    }
    
    // Clear job history
    csdeBatchProcessor.clearJobHistory();
    
    // Return success
    res.json({
      success: true,
      message: 'Job history cleared'
    });
  } catch (error) {
    req.app.locals.logger.error('Error clearing job history', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Upload files for processing
 */
router.post('/upload', upload.array('files', 10), async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeBatchProcessor) {
      initialize({ logger: req.app.locals.logger });
    }
    
    // Get uploaded files
    const files = req.files;
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      });
    }
    
    // Return success
    res.json({
      success: true,
      message: `${files.length} files uploaded successfully`,
      files: files.map(file => ({
        originalname: file.originalname,
        filename: file.filename,
        size: file.size
      }))
    });
  } catch (error) {
    req.app.locals.logger.error('Error uploading files', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = {
  router,
  initialize
};
