#!/usr/bin/env python3
"""
UUFT Medical Test

This script tests whether the UUFT equation can accurately model medical outcomes
by comparing its predictions with established medical models.

The test focuses on predicting treatment efficacy based on genomic, proteomic, and clinical factors.
"""

import numpy as np
import matplotlib.pyplot as plt
import math
import os
from datetime import datetime
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

# Create results directory
RESULTS_DIR = "uuft_test_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Constants
PI = math.pi
GOLDEN_RATIO = (1 + math.sqrt(5)) / 2  # φ ≈ 1.618
PI_10_CUBED = PI * 10**3  # π10³

def apply_uuft_equation(A, B, C):
    """
    Apply the UUFT equation: (A ⊗ B ⊕ C) × π10³
    
    Args:
        A: First input component (Genomic factor)
        B: Second input component (Proteomic factor)
        C: Third input component (Clinical factor)
        
    Returns:
        float: Result of applying the UUFT equation
    """
    # Tensor product (⊗) with golden ratio
    tensor_product = A * B * GOLDEN_RATIO
    
    # Fusion operator (⊕) with inverse golden ratio
    fusion_result = tensor_product + (C * (1 / GOLDEN_RATIO))
    
    # Apply pi factor
    result = fusion_result * PI_10_CUBED
    
    return result

def generate_synthetic_medical_data(n_samples=1000, seed=42):
    """
    Generate synthetic medical data for testing.
    
    The data represents:
    - Genomic factors (genetic predisposition)
    - Proteomic factors (protein expression)
    - Clinical factors (symptoms, lab tests)
    - Treatment efficacy (outcome)
    
    Args:
        n_samples: Number of samples to generate
        seed: Random seed for reproducibility
        
    Returns:
        dict: Synthetic medical data
    """
    np.random.seed(seed)
    
    # Generate input factors (normalized to 0-1 range)
    genomic_factors = np.random.beta(2, 5, n_samples)  # Genetic predisposition (right-skewed)
    proteomic_factors = np.random.beta(3, 3, n_samples)  # Protein expression (bell-shaped)
    clinical_factors = np.random.beta(5, 2, n_samples)  # Clinical severity (left-skewed)
    
    # Generate true treatment efficacy based on a complex interaction
    # We'll use a non-linear model that's different from the UUFT equation
    # to test if UUFT can approximate it
    
    # Base efficacy from genomic-proteomic interaction
    base_efficacy = 0.3 * np.sqrt(genomic_factors * proteomic_factors)
    
    # Clinical response modifier
    clinical_modifier = 0.4 * (1 - clinical_factors**2)
    
    # Interaction effects
    interaction = 0.3 * (genomic_factors * proteomic_factors * (1 - clinical_factors))
    
    # Combined treatment efficacy (0-1 scale)
    true_efficacy = base_efficacy + clinical_modifier + interaction
    
    # Clip to 0-1 range and add some noise
    true_efficacy = np.clip(true_efficacy + np.random.normal(0, 0.05, n_samples), 0, 1)
    
    return {
        "genomic_factors": genomic_factors,
        "proteomic_factors": proteomic_factors,
        "clinical_factors": clinical_factors,
        "treatment_efficacy": true_efficacy
    }

def apply_traditional_model(X_train, y_train, X_test):
    """
    Apply a traditional medical model to predict treatment efficacy.
    
    For this test, we'll use a Random Forest regressor as our "traditional" model.
    In a real medical context, this would be a validated clinical prediction model.
    
    Args:
        X_train: Training features
        y_train: Training target
        X_test: Test features
        
    Returns:
        array: Predicted treatment efficacy
    """
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    return model.predict(X_test)

def apply_uuft_medical_model(genomic, proteomic, clinical):
    """
    Apply the UUFT equation to predict treatment efficacy.
    
    Args:
        genomic: Genomic factors
        proteomic: Proteomic factors
        clinical: Clinical factors
        
    Returns:
        array: Predicted treatment efficacy
    """
    # Scale inputs to appropriate range for UUFT equation
    A = genomic  # Genomic factor
    B = proteomic  # Proteomic factor
    C = clinical  # Clinical factor
    
    # Apply UUFT equation to each sample
    results = []
    for a, b, c in zip(A, B, C):
        result = apply_uuft_equation(a, b, c)
        results.append(result)
    
    # Convert to numpy array
    results = np.array(results)
    
    # Normalize results to 0-1 range for treatment efficacy
    min_val = np.min(results)
    max_val = np.max(results)
    if max_val > min_val:
        normalized_results = (results - min_val) / (max_val - min_val)
    else:
        normalized_results = np.zeros_like(results)
    
    return normalized_results

def test_medical_predictions():
    """
    Test UUFT medical predictions against traditional medical models.
    
    Returns:
        dict: Test results
    """
    print("Testing UUFT medical predictions")
    
    # Generate synthetic medical data
    data = generate_synthetic_medical_data(n_samples=1000)
    
    # Split data into training and testing sets
    X = np.column_stack((
        data["genomic_factors"],
        data["proteomic_factors"],
        data["clinical_factors"]
    ))
    y = data["treatment_efficacy"]
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # Extract test factors
    genomic_test = X_test[:, 0]
    proteomic_test = X_test[:, 1]
    clinical_test = X_test[:, 2]
    
    # Apply traditional model
    y_pred_traditional = apply_traditional_model(X_train, y_train, X_test)
    
    # Apply UUFT model
    y_pred_uuft = apply_uuft_medical_model(genomic_test, proteomic_test, clinical_test)
    
    # Calculate metrics
    mse_traditional = mean_squared_error(y_test, y_pred_traditional)
    mse_uuft = mean_squared_error(y_test, y_pred_uuft)
    
    r2_traditional = r2_score(y_test, y_pred_traditional)
    r2_uuft = r2_score(y_test, y_pred_uuft)
    
    # Calculate accuracy (1 - normalized error)
    accuracy_traditional = 1.0 - np.sqrt(mse_traditional)
    accuracy_uuft = 1.0 - np.sqrt(mse_uuft)
    
    # Calculate performance improvement
    if mse_traditional > 0:
        performance_improvement = mse_traditional / mse_uuft
    else:
        performance_improvement = float('inf')
    
    print(f"  Traditional Model: MSE = {mse_traditional:.6f}, R² = {r2_traditional:.6f}, Accuracy = {accuracy_traditional:.6f}")
    print(f"  UUFT Model: MSE = {mse_uuft:.6f}, R² = {r2_uuft:.6f}, Accuracy = {accuracy_uuft:.6f}")
    print(f"  Performance Improvement: {performance_improvement:.2f}x")
    
    return {
        "metrics": {
            "traditional": {
                "mse": float(mse_traditional),
                "r2": float(r2_traditional),
                "accuracy": float(accuracy_traditional)
            },
            "uuft": {
                "mse": float(mse_uuft),
                "r2": float(r2_uuft),
                "accuracy": float(accuracy_uuft)
            }
        },
        "performance_improvement": float(performance_improvement),
        "predictions": {
            "y_test": y_test.tolist(),
            "y_pred_traditional": y_pred_traditional.tolist(),
            "y_pred_uuft": y_pred_uuft.tolist()
        }
    }

def test_factor_importance():
    """
    Test how different factors affect the UUFT medical predictions.
    
    Returns:
        dict: Test results
    """
    print("\nTesting factor importance in UUFT medical model")
    
    # Generate a grid of values for each factor
    factor_values = np.linspace(0.1, 0.9, 9)
    
    # Test genomic factor importance
    genomic_results = []
    for value in factor_values:
        # Fix other factors at moderate values
        proteomic = np.array([0.5])
        clinical = np.array([0.5])
        genomic = np.array([value])
        
        # Apply UUFT model
        efficacy = apply_uuft_medical_model(genomic, proteomic, clinical)[0]
        
        genomic_results.append({
            "factor_value": float(value),
            "efficacy": float(efficacy)
        })
    
    # Test proteomic factor importance
    proteomic_results = []
    for value in factor_values:
        # Fix other factors at moderate values
        genomic = np.array([0.5])
        clinical = np.array([0.5])
        proteomic = np.array([value])
        
        # Apply UUFT model
        efficacy = apply_uuft_medical_model(genomic, proteomic, clinical)[0]
        
        proteomic_results.append({
            "factor_value": float(value),
            "efficacy": float(efficacy)
        })
    
    # Test clinical factor importance
    clinical_results = []
    for value in factor_values:
        # Fix other factors at moderate values
        genomic = np.array([0.5])
        proteomic = np.array([0.5])
        clinical = np.array([value])
        
        # Apply UUFT model
        efficacy = apply_uuft_medical_model(genomic, proteomic, clinical)[0]
        
        clinical_results.append({
            "factor_value": float(value),
            "efficacy": float(efficacy)
        })
    
    return {
        "genomic_importance": genomic_results,
        "proteomic_importance": proteomic_results,
        "clinical_importance": clinical_results
    }

def plot_results(prediction_results, factor_results):
    """
    Plot the results of the medical tests.
    
    Args:
        prediction_results: Results from test_medical_predictions()
        factor_results: Results from test_factor_importance()
    """
    # Plot prediction comparison
    plt.figure(figsize=(10, 6))
    
    y_test = prediction_results["predictions"]["y_test"]
    y_pred_traditional = prediction_results["predictions"]["y_pred_traditional"]
    y_pred_uuft = prediction_results["predictions"]["y_pred_uuft"]
    
    # Sample 100 random points for clarity
    np.random.seed(42)
    indices = np.random.choice(len(y_test), 100, replace=False)
    
    plt.scatter(np.array(y_test)[indices], np.array(y_pred_traditional)[indices], 
                alpha=0.7, label='Traditional Model')
    plt.scatter(np.array(y_test)[indices], np.array(y_pred_uuft)[indices], 
                alpha=0.7, label='UUFT Model')
    
    plt.plot([0, 1], [0, 1], 'k--', label='Perfect Prediction')
    plt.xlabel('True Treatment Efficacy')
    plt.ylabel('Predicted Treatment Efficacy')
    plt.title('UUFT vs Traditional Medical Model Predictions')
    plt.legend()
    plt.grid(True)
    
    plt.savefig(os.path.join(RESULTS_DIR, "uuft_medical_predictions.png"))
    plt.close()
    
    # Plot factor importance
    plt.figure(figsize=(10, 6))
    
    genomic_values = [r["factor_value"] for r in factor_results["genomic_importance"]]
    genomic_efficacy = [r["efficacy"] for r in factor_results["genomic_importance"]]
    
    proteomic_values = [r["factor_value"] for r in factor_results["proteomic_importance"]]
    proteomic_efficacy = [r["efficacy"] for r in factor_results["proteomic_importance"]]
    
    clinical_values = [r["factor_value"] for r in factor_results["clinical_importance"]]
    clinical_efficacy = [r["efficacy"] for r in factor_results["clinical_importance"]]
    
    plt.plot(genomic_values, genomic_efficacy, 'o-', label='Genomic Factor')
    plt.plot(proteomic_values, proteomic_efficacy, 's-', label='Proteomic Factor')
    plt.plot(clinical_values, clinical_efficacy, '^-', label='Clinical Factor')
    
    plt.xlabel('Factor Value')
    plt.ylabel('Predicted Treatment Efficacy')
    plt.title('Factor Importance in UUFT Medical Model')
    plt.legend()
    plt.grid(True)
    
    plt.savefig(os.path.join(RESULTS_DIR, "uuft_medical_factor_importance.png"))
    plt.close()
    
    # Plot accuracy comparison
    plt.figure(figsize=(8, 6))
    
    models = ['Traditional', 'UUFT']
    accuracies = [
        prediction_results["metrics"]["traditional"]["accuracy"],
        prediction_results["metrics"]["uuft"]["accuracy"]
    ]
    
    bars = plt.bar(models, accuracies)
    
    # Color bars based on accuracy
    for i, bar in enumerate(bars):
        if accuracies[i] > 0.9:
            bar.set_color('green')
        elif accuracies[i] > 0.7:
            bar.set_color('yellow')
        else:
            bar.set_color('red')
    
    plt.axhline(y=0.95, color='blue', linestyle='--', label='Target (95%)')
    plt.xlabel('Model')
    plt.ylabel('Accuracy')
    plt.title('Medical Model Accuracy Comparison')
    plt.ylim(0, 1)
    plt.legend()
    
    plt.savefig(os.path.join(RESULTS_DIR, "uuft_medical_accuracy.png"))
    plt.close()

def save_results(prediction_results, factor_results):
    """
    Save the results to a text file.
    
    Args:
        prediction_results: Results from test_medical_predictions()
        factor_results: Results from test_factor_importance()
    """
    with open(os.path.join(RESULTS_DIR, "uuft_medical_test_results.txt"), "w") as f:
        f.write("UUFT Medical Test Results\n")
        f.write("=======================\n")
        f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Write prediction results
        f.write("Medical Prediction Results:\n")
        f.write("-------------------------\n")
        
        f.write("\nTraditional Model:\n")
        f.write(f"  MSE: {prediction_results['metrics']['traditional']['mse']:.6f}\n")
        f.write(f"  R²: {prediction_results['metrics']['traditional']['r2']:.6f}\n")
        f.write(f"  Accuracy: {prediction_results['metrics']['traditional']['accuracy']:.6f}\n")
        
        f.write("\nUUFT Model:\n")
        f.write(f"  MSE: {prediction_results['metrics']['uuft']['mse']:.6f}\n")
        f.write(f"  R²: {prediction_results['metrics']['uuft']['r2']:.6f}\n")
        f.write(f"  Accuracy: {prediction_results['metrics']['uuft']['accuracy']:.6f}\n")
        
        f.write(f"\nPerformance Improvement: {prediction_results['performance_improvement']:.2f}x\n")
        
        # Write factor importance results
        f.write("\nFactor Importance Results:\n")
        f.write("------------------------\n")
        
        f.write("\nGenomic Factor Importance:\n")
        for result in factor_results["genomic_importance"]:
            f.write(f"  Value: {result['factor_value']:.1f}, Efficacy: {result['efficacy']:.6f}\n")
        
        f.write("\nProteomic Factor Importance:\n")
        for result in factor_results["proteomic_importance"]:
            f.write(f"  Value: {result['factor_value']:.1f}, Efficacy: {result['efficacy']:.6f}\n")
        
        f.write("\nClinical Factor Importance:\n")
        for result in factor_results["clinical_importance"]:
            f.write(f"  Value: {result['factor_value']:.1f}, Efficacy: {result['efficacy']:.6f}\n")

def main():
    """Run the UUFT medical test."""
    print("Running UUFT Medical Test")
    print("=======================")
    
    # Test medical predictions
    prediction_results = test_medical_predictions()
    
    # Test factor importance
    factor_results = test_factor_importance()
    
    # Plot results
    plot_results(prediction_results, factor_results)
    
    # Save results
    save_results(prediction_results, factor_results)
    
    print("\nTest complete. Results saved to the uuft_test_results directory.")
    
    return prediction_results, factor_results

if __name__ == "__main__":
    main()
