<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaStore Partner Empowerment Model</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 950px;
            height: 750px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
            text-align: center;
            width: 100%;
            font-size: 13px;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #333;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .arrow {
            position: absolute;
            background-color: #333;
            width: 2px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: -90px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: -90px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .component-content {
            text-align: center;
            width: 100%;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <h1>FIG. 6: NovaStore Partner Empowerment Model</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 900px; height: 700px; left: 25px; top: 20px;">
            <div class="container-label">NOVASTORE: PARTNER EMPOWERMENT MODEL</div>
        </div>
        
        <!-- NovaStore Core -->
        <div class="container-box" style="width: 850px; height: 140px; left: 50px; top: 70px;">
            <div class="container-label">NOVASTORE MARKETPLACE</div>
        </div>
        
        <div class="component-box" style="left: 100px; top: 110px; width: 160px; height: 80px;">
            <div class="component-number">601</div>
            <div class="component-label">Component Registry</div>
            <div class="component-content">Verified Modules</div>
        </div>
        
        <div class="component-box" style="left: 280px; top: 110px; width: 160px; height: 80px;">
            <div class="component-number">602</div>
            <div class="component-label">Smart Contract Licensing</div>
            <div class="component-content">Automated Enforcement</div>
        </div>
        
        <div class="component-box" style="left: 460px; top: 110px; width: 160px; height: 80px;">
            <div class="component-number">603</div>
            <div class="component-label">Verification Pipeline</div>
            <div class="component-content">Compliance Testing</div>
        </div>
        
        <div class="component-box" style="left: 640px; top: 110px; width: 160px; height: 80px;">
            <div class="component-number">604</div>
            <div class="component-label">API Gateway</div>
            <div class="component-content">Secure Access</div>
        </div>
        
        <!-- Revenue Model -->
        <div class="container-box" style="width: 850px; height: 140px; left: 50px; top: 230px;">
            <div class="container-label">PARTNER EMPOWERMENT REVENUE MODEL</div>
        </div>
        
        <div class="component-box" style="left: 200px; top: 270px; width: 200px; height: 80px;">
            <div class="component-number">605</div>
            <div class="component-label">Partner Revenue Share</div>
            <div class="component-content">82%</div>
        </div>
        
        <div class="component-box" style="left: 500px; top: 270px; width: 200px; height: 80px;">
            <div class="component-number">606</div>
            <div class="component-label">Platform Revenue Share</div>
            <div class="component-content">18%</div>
        </div>
        
        <!-- Integration Components -->
        <div class="container-box" style="width: 850px; height: 140px; left: 50px; top: 390px;">
            <div class="container-label">INTEGRATION COMPONENTS</div>
        </div>
        
        <div class="component-box" style="left: 100px; top: 430px; width: 160px; height: 80px;">
            <div class="component-number">607</div>
            <div class="component-label">Royalty Distribution</div>
            <div class="component-content">Automated Payments</div>
        </div>
        
        <div class="component-box" style="left: 280px; top: 430px; width: 160px; height: 80px;">
            <div class="component-number">608</div>
            <div class="component-label">Certification Process</div>
            <div class="component-content">Quality Assurance</div>
        </div>
        
        <div class="component-box" style="left: 460px; top: 430px; width: 160px; height: 80px;">
            <div class="component-number">609</div>
            <div class="component-label">Usage Analytics</div>
            <div class="component-content">Performance Metrics</div>
        </div>
        
        <div class="component-box" style="left: 640px; top: 430px; width: 160px; height: 80px;">
            <div class="component-number">610</div>
            <div class="component-label">Governance Model</div>
            <div class="component-content">Ecosystem Integrity</div>
        </div>
        
        <!-- Ecosystem Impact -->
        <div class="container-box" style="width: 850px; height: 120px; left: 50px; top: 550px;">
            <div class="container-label">ECOSYSTEM IMPACT</div>
        </div>
        
        <div class="component-box" style="left: 200px; top: 590px; width: 200px; height: 60px;">
            <div class="component-number">611</div>
            <div class="component-label">Economic Incentives</div>
            <div class="component-content">Security-First Development</div>
        </div>
        
        <div class="component-box" style="left: 500px; top: 590px; width: 200px; height: 60px;">
            <div class="component-number">612</div>
            <div class="component-label">Cross-Industry Application</div>
            <div class="component-content">Regulatory Compliance</div>
        </div>
        
        <!-- Connecting arrows -->
        <div class="arrow" style="left: 180px; top: 190px; height: 40px;"></div>
        <div class="arrow" style="left: 360px; top: 190px; height: 40px;"></div>
        <div class="arrow" style="left: 540px; top: 190px; height: 40px;"></div>
        <div class="arrow" style="left: 720px; top: 190px; height: 40px;"></div>
        
        <div class="arrow" style="left: 300px; top: 350px; height: 40px;"></div>
        <div class="arrow" style="left: 600px; top: 350px; height: 40px;"></div>
        
        <div class="arrow" style="left: 180px; top: 510px; height: 40px;"></div>
        <div class="arrow" style="left: 360px; top: 510px; height: 40px;"></div>
        <div class="arrow" style="left: 540px; top: 510px; height: 40px;"></div>
        <div class="arrow" style="left: 720px; top: 510px; height: 40px;"></div>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>NovaStore Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Revenue Model (82/18 Split)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Integration Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #000;"></div>
                <div>Ecosystem Impact</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
