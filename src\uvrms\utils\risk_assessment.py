"""
Advanced risk assessment methodologies for the Universal Vendor Risk Management System.

This module provides advanced risk assessment methodologies for vendor risk management.
"""

import logging
import math
from typing import Dict, List, Any, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_inherent_risk_score(vendor_data: Dict[str, Any], 
                                 risk_factors: Dict[str, float]) -> Dict[str, Any]:
    """
    Calculate the inherent risk score for a vendor based on risk factors.
    
    Args:
        vendor_data: The vendor data
        risk_factors: Dictionary of risk factors and their weights
        
    Returns:
        The inherent risk score information
    """
    logger.info(f"Calculating inherent risk score for vendor: {vendor_data.get('id')}")
    
    # Initialize score
    total_score = 0.0
    max_possible_score = 0.0
    factor_scores = {}
    
    # Calculate score for each factor
    for factor, weight in risk_factors.items():
        factor_score = 0.0
        max_factor_score = weight * 10  # Assuming max score of 10 for each factor
        max_possible_score += max_factor_score
        
        # Calculate factor score based on vendor data
        if factor == 'data_sensitivity':
            # Score based on types of data handled
            data_types = vendor_data.get('data_types', [])
            if 'PCI' in data_types or 'PHI' in data_types:
                factor_score = weight * 10  # Highest risk
            elif 'PII' in data_types:
                factor_score = weight * 8
            elif 'confidential' in data_types:
                factor_score = weight * 6
            elif 'internal' in data_types:
                factor_score = weight * 4
            elif 'public' in data_types:
                factor_score = weight * 2
            
        elif factor == 'service_criticality':
            # Score based on service criticality
            criticality = vendor_data.get('criticality', 'medium').lower()
            if criticality == 'critical':
                factor_score = weight * 10
            elif criticality == 'high':
                factor_score = weight * 8
            elif criticality == 'medium':
                factor_score = weight * 5
            elif criticality == 'low':
                factor_score = weight * 2
            
        elif factor == 'access_level':
            # Score based on vendor's access level
            access = vendor_data.get('access_level', 'read').lower()
            if access == 'admin':
                factor_score = weight * 10
            elif access == 'write':
                factor_score = weight * 7
            elif access == 'read':
                factor_score = weight * 3
            elif access == 'none':
                factor_score = weight * 0
            
        elif factor == 'regulatory_requirements':
            # Score based on applicable regulations
            regulations = vendor_data.get('regulations', [])
            if regulations:
                # More regulations = higher risk
                factor_score = weight * min(10, len(regulations) * 2)
            
        elif factor == 'vendor_size':
            # Score based on vendor size
            size = vendor_data.get('size', 'medium').lower()
            if size == 'enterprise':
                factor_score = weight * 4  # Lower risk for large vendors
            elif size == 'mid-market':
                factor_score = weight * 6
            elif size == 'small':
                factor_score = weight * 8
            elif size == 'startup':
                factor_score = weight * 10  # Higher risk for startups
            
        elif factor == 'relationship_duration':
            # Score based on relationship duration
            duration = vendor_data.get('relationship_duration', 0)
            if duration < 1:  # Less than 1 year
                factor_score = weight * 10
            elif duration < 3:  # 1-3 years
                factor_score = weight * 7
            elif duration < 5:  # 3-5 years
                factor_score = weight * 4
            else:  # 5+ years
                factor_score = weight * 2
        
        # Add factor score to total
        total_score += factor_score
        factor_scores[factor] = factor_score / weight  # Normalize to 0-10 scale
    
    # Calculate normalized score (0-100)
    normalized_score = (total_score / max_possible_score) * 100 if max_possible_score > 0 else 0
    
    # Determine risk level
    if normalized_score >= 75:
        risk_level = 'high'
    elif normalized_score >= 40:
        risk_level = 'medium'
    else:
        risk_level = 'low'
    
    # Create the risk score object
    risk_score = {
        'vendor_id': vendor_data.get('id'),
        'score_type': 'inherent',
        'overall_score': normalized_score,
        'factor_scores': factor_scores,
        'risk_level': risk_level,
        'max_possible_score': max_possible_score,
        'total_score': total_score
    }
    
    logger.info(f"Inherent risk score calculated for vendor: {vendor_data.get('id')}")
    
    return risk_score

def calculate_residual_risk_score(inherent_risk_score: Dict[str, Any], 
                                 controls: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate the residual risk score after applying controls.
    
    Args:
        inherent_risk_score: The inherent risk score
        controls: List of controls applied to mitigate risks
        
    Returns:
        The residual risk score information
    """
    logger.info(f"Calculating residual risk score for vendor: {inherent_risk_score.get('vendor_id')}")
    
    # Get the inherent risk score
    inherent_score = inherent_risk_score.get('overall_score', 0)
    
    # Calculate control effectiveness
    total_effectiveness = 0.0
    max_effectiveness = len(controls) * 10 if controls else 1  # Avoid division by zero
    
    for control in controls:
        effectiveness = control.get('effectiveness', 'medium').lower()
        if effectiveness == 'high':
            total_effectiveness += 10
        elif effectiveness == 'medium':
            total_effectiveness += 6
        elif effectiveness == 'low':
            total_effectiveness += 3
        
        # Consider control status
        status = control.get('status', 'planned').lower()
        if status == 'planned':
            total_effectiveness *= 0.3  # Planned controls are less effective
        elif status == 'implementing':
            total_effectiveness *= 0.7  # Partially implemented controls
    
    # Calculate control effectiveness percentage
    effectiveness_percentage = (total_effectiveness / max_effectiveness) if controls else 0
    
    # Calculate residual risk score
    # Formula: Inherent Risk - (Inherent Risk * Control Effectiveness)
    residual_score = inherent_score - (inherent_score * effectiveness_percentage * 0.8)
    
    # Ensure score is within bounds
    residual_score = max(0, min(100, residual_score))
    
    # Determine risk level
    if residual_score >= 75:
        risk_level = 'high'
    elif residual_score >= 40:
        risk_level = 'medium'
    else:
        risk_level = 'low'
    
    # Create the risk score object
    risk_score = {
        'vendor_id': inherent_risk_score.get('vendor_id'),
        'score_type': 'residual',
        'inherent_score': inherent_score,
        'overall_score': residual_score,
        'control_effectiveness': effectiveness_percentage * 100,  # Convert to percentage
        'risk_level': risk_level,
        'control_count': len(controls)
    }
    
    logger.info(f"Residual risk score calculated for vendor: {inherent_risk_score.get('vendor_id')}")
    
    return risk_score

def calculate_impact_likelihood_score(risks: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate a risk score based on impact and likelihood of identified risks.
    
    Args:
        risks: List of risks for the vendor
        
    Returns:
        The impact-likelihood risk score information
    """
    logger.info(f"Calculating impact-likelihood risk score")
    
    if not risks:
        return {
            'score_type': 'impact_likelihood',
            'overall_score': 0,
            'risk_level': 'low',
            'risk_count': 0,
            'high_risk_count': 0,
            'medium_risk_count': 0,
            'low_risk_count': 0
        }
    
    # Count risks by level
    high_risks = [r for r in risks if r.get('risk_level') == 'high']
    medium_risks = [r for r in risks if r.get('risk_level') == 'medium']
    low_risks = [r for r in risks if r.get('risk_level') == 'low']
    
    # Calculate weighted score
    # Formula: (High * 10 + Medium * 5 + Low * 1) / Total Count
    weighted_score = (len(high_risks) * 10 + len(medium_risks) * 5 + len(low_risks) * 1) / len(risks)
    
    # Normalize to 0-100 scale
    normalized_score = (weighted_score / 10) * 100
    
    # Determine risk level
    if len(high_risks) >= 3 or normalized_score >= 70:
        risk_level = 'high'
    elif len(high_risks) >= 1 or len(medium_risks) >= 3 or normalized_score >= 40:
        risk_level = 'medium'
    else:
        risk_level = 'low'
    
    # Create the risk score object
    risk_score = {
        'score_type': 'impact_likelihood',
        'overall_score': normalized_score,
        'risk_level': risk_level,
        'risk_count': len(risks),
        'high_risk_count': len(high_risks),
        'medium_risk_count': len(medium_risks),
        'low_risk_count': len(low_risks)
    }
    
    logger.info(f"Impact-likelihood risk score calculated")
    
    return risk_score

def calculate_compliance_based_score(vendor_data: Dict[str, Any], 
                                    assessments: List[Dict[str, Any]],
                                    compliance_requirements: Dict[str, List[str]]) -> Dict[str, Any]:
    """
    Calculate a risk score based on compliance with requirements.
    
    Args:
        vendor_data: The vendor data
        assessments: List of assessments for the vendor
        compliance_requirements: Dictionary of compliance requirements by framework
        
    Returns:
        The compliance-based risk score information
    """
    logger.info(f"Calculating compliance-based risk score for vendor: {vendor_data.get('id')}")
    
    # Initialize compliance scores
    framework_scores = {}
    total_requirements = 0
    total_compliant = 0
    
    # Process each assessment
    for assessment in assessments:
        if assessment.get('status') != 'completed':
            continue
        
        template_id = assessment.get('template_id')
        if not template_id:
            continue
        
        # Get requirements for this framework
        requirements = compliance_requirements.get(template_id, [])
        if not requirements:
            continue
        
        # Count requirements and compliant answers
        framework_requirements = len(requirements)
        framework_compliant = 0
        
        # Check each section and question
        for section in assessment.get('sections', []):
            for question in section.get('questions', []):
                question_id = question.get('id')
                if question_id in requirements:
                    total_requirements += 1
                    
                    # Check if the answer is compliant
                    answer = question.get('answer')
                    if answer == 'yes':
                        framework_compliant += 1
                        total_compliant += 1
        
        # Calculate framework compliance percentage
        if framework_requirements > 0:
            compliance_percentage = (framework_compliant / framework_requirements) * 100
            framework_scores[template_id] = compliance_percentage
    
    # Calculate overall compliance percentage
    overall_compliance = (total_compliant / total_requirements) * 100 if total_requirements > 0 else 0
    
    # Convert compliance to risk score (higher compliance = lower risk)
    risk_score = 100 - overall_compliance
    
    # Determine risk level
    if risk_score >= 70:
        risk_level = 'high'
    elif risk_score >= 30:
        risk_level = 'medium'
    else:
        risk_level = 'low'
    
    # Create the risk score object
    compliance_score = {
        'vendor_id': vendor_data.get('id'),
        'score_type': 'compliance',
        'overall_score': risk_score,
        'compliance_percentage': overall_compliance,
        'framework_scores': framework_scores,
        'risk_level': risk_level,
        'total_requirements': total_requirements,
        'total_compliant': total_compliant
    }
    
    logger.info(f"Compliance-based risk score calculated for vendor: {vendor_data.get('id')}")
    
    return compliance_score

def calculate_industry_specific_score(vendor_data: Dict[str, Any], 
                                     industry_benchmarks: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate a risk score based on industry-specific factors.
    
    Args:
        vendor_data: The vendor data
        industry_benchmarks: Dictionary of industry benchmarks
        
    Returns:
        The industry-specific risk score information
    """
    logger.info(f"Calculating industry-specific risk score for vendor: {vendor_data.get('id')}")
    
    # Get vendor industry
    industry = vendor_data.get('industry', 'technology').lower()
    
    # Get industry benchmarks
    benchmarks = industry_benchmarks.get(industry, industry_benchmarks.get('default', {}))
    
    # Initialize scores
    factor_scores = {}
    total_score = 0
    max_score = 0
    
    # Calculate score for each factor
    for factor, benchmark in benchmarks.items():
        # Skip if factor is not in vendor data
        if factor not in vendor_data:
            continue
        
        vendor_value = vendor_data[factor]
        weight = benchmark.get('weight', 1)
        max_score += weight * 10
        
        # Calculate factor score based on benchmark type
        if benchmark.get('type') == 'range':
            # Range benchmark (e.g., financial ratios)
            min_value = benchmark.get('min', 0)
            max_value = benchmark.get('max', 1)
            optimal = benchmark.get('optimal')
            
            if optimal is not None:
                # Score based on distance from optimal value
                distance = abs(vendor_value - optimal)
                max_distance = max(abs(min_value - optimal), abs(max_value - optimal))
                factor_score = (1 - (distance / max_distance)) * 10 if max_distance > 0 else 10
            else:
                # Score based on position in range
                normalized = (vendor_value - min_value) / (max_value - min_value) if (max_value - min_value) > 0 else 0.5
                factor_score = normalized * 10
            
        elif benchmark.get('type') == 'boolean':
            # Boolean benchmark (e.g., has certification)
            expected = benchmark.get('expected', True)
            factor_score = 10 if vendor_value == expected else 0
            
        elif benchmark.get('type') == 'categorical':
            # Categorical benchmark (e.g., size category)
            categories = benchmark.get('categories', {})
            factor_score = categories.get(vendor_value, 5)
            
        else:
            # Default scoring
            factor_score = 5
        
        # Apply weight
        weighted_score = factor_score * weight
        total_score += weighted_score
        factor_scores[factor] = factor_score
    
    # Calculate normalized score (0-100)
    normalized_score = (total_score / max_score) * 100 if max_score > 0 else 50
    
    # Invert score if needed (higher score = higher risk)
    if benchmarks.get('invert_score', False):
        normalized_score = 100 - normalized_score
    
    # Determine risk level
    if normalized_score >= 70:
        risk_level = 'high'
    elif normalized_score >= 30:
        risk_level = 'medium'
    else:
        risk_level = 'low'
    
    # Create the risk score object
    industry_score = {
        'vendor_id': vendor_data.get('id'),
        'score_type': 'industry',
        'industry': industry,
        'overall_score': normalized_score,
        'factor_scores': factor_scores,
        'risk_level': risk_level,
        'benchmark_count': len(factor_scores)
    }
    
    logger.info(f"Industry-specific risk score calculated for vendor: {vendor_data.get('id')}")
    
    return industry_score

def calculate_composite_risk_score(scores: List[Dict[str, Any]], 
                                  weights: Dict[str, float]) -> Dict[str, Any]:
    """
    Calculate a composite risk score based on multiple scoring methodologies.
    
    Args:
        scores: List of risk scores from different methodologies
        weights: Dictionary of weights for each score type
        
    Returns:
        The composite risk score information
    """
    logger.info(f"Calculating composite risk score")
    
    # Initialize composite score
    total_weighted_score = 0.0
    total_weight = 0.0
    score_components = {}
    
    # Calculate weighted score for each methodology
    for score in scores:
        score_type = score.get('score_type')
        if not score_type or score_type not in weights:
            continue
        
        weight = weights[score_type]
        total_weight += weight
        
        overall_score = score.get('overall_score', 0)
        weighted_score = overall_score * weight
        total_weighted_score += weighted_score
        
        score_components[score_type] = {
            'score': overall_score,
            'weight': weight,
            'weighted_score': weighted_score,
            'risk_level': score.get('risk_level', 'medium')
        }
    
    # Calculate normalized composite score
    composite_score = total_weighted_score / total_weight if total_weight > 0 else 0
    
    # Determine risk level
    if composite_score >= 70:
        risk_level = 'high'
    elif composite_score >= 40:
        risk_level = 'medium'
    else:
        risk_level = 'low'
    
    # Create the composite score object
    composite = {
        'score_type': 'composite',
        'overall_score': composite_score,
        'risk_level': risk_level,
        'components': score_components,
        'total_weight': total_weight
    }
    
    logger.info(f"Composite risk score calculated")
    
    return composite

def get_default_inherent_risk_factors() -> Dict[str, float]:
    """
    Get default inherent risk factors and weights.
    
    Returns:
        Dictionary of risk factors and their weights
    """
    return {
        'data_sensitivity': 0.25,
        'service_criticality': 0.20,
        'access_level': 0.20,
        'regulatory_requirements': 0.15,
        'vendor_size': 0.10,
        'relationship_duration': 0.10
    }

def get_default_composite_weights() -> Dict[str, float]:
    """
    Get default weights for composite risk scoring.
    
    Returns:
        Dictionary of score types and their weights
    """
    return {
        'inherent': 0.25,
        'residual': 0.30,
        'impact_likelihood': 0.20,
        'compliance': 0.15,
        'industry': 0.10
    }

def get_default_industry_benchmarks() -> Dict[str, Dict[str, Any]]:
    """
    Get default industry benchmarks for industry-specific scoring.
    
    Returns:
        Dictionary of industry benchmarks
    """
    return {
        'technology': {
            'security_maturity': {
                'type': 'categorical',
                'weight': 2.0,
                'categories': {
                    'high': 10,
                    'medium': 5,
                    'low': 0
                }
            },
            'uptime_percentage': {
                'type': 'range',
                'weight': 1.5,
                'min': 90,
                'max': 100,
                'optimal': 99.9
            },
            'has_soc2': {
                'type': 'boolean',
                'weight': 2.0,
                'expected': True
            }
        },
        'financial': {
            'years_in_business': {
                'type': 'range',
                'weight': 1.0,
                'min': 0,
                'max': 50,
                'optimal': 20
            },
            'has_pci_dss': {
                'type': 'boolean',
                'weight': 2.5,
                'expected': True
            },
            'financial_stability': {
                'type': 'categorical',
                'weight': 2.0,
                'categories': {
                    'high': 10,
                    'medium': 5,
                    'low': 0
                }
            }
        },
        'healthcare': {
            'has_hipaa_certification': {
                'type': 'boolean',
                'weight': 3.0,
                'expected': True
            },
            'data_breach_history': {
                'type': 'categorical',
                'weight': 2.0,
                'categories': {
                    'none': 10,
                    'minor': 5,
                    'major': 0
                }
            },
            'encryption_level': {
                'type': 'categorical',
                'weight': 1.5,
                'categories': {
                    'high': 10,
                    'medium': 5,
                    'low': 0
                }
            }
        },
        'default': {
            'years_in_business': {
                'type': 'range',
                'weight': 1.0,
                'min': 0,
                'max': 50,
                'optimal': 15
            },
            'has_security_certification': {
                'type': 'boolean',
                'weight': 1.5,
                'expected': True
            }
        }
    }

def get_default_compliance_requirements() -> Dict[str, List[str]]:
    """
    Get default compliance requirements by framework.
    
    Returns:
        Dictionary of compliance requirements by framework
    """
    return {
        'security_assessment': [
            'security_policy_1',
            'security_policy_2',
            'security_policy_3',
            'access_control_1',
            'access_control_2',
            'data_protection_1',
            'data_protection_2'
        ],
        'privacy_assessment': [
            'privacy_policy_1',
            'privacy_policy_2',
            'data_subject_rights_1',
            'data_subject_rights_2',
            'data_processing_1',
            'data_processing_3'
        ],
        'business_continuity_assessment': [
            'business_continuity_1',
            'disaster_recovery_1',
            'incident_response_1'
        ]
    }
