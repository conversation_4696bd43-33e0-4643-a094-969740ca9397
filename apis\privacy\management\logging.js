/**
 * Logging Module
 * 
 * This module provides structured logging functionality for the Privacy Management API.
 */

const winston = require('winston');
const { format } = winston;

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define log level based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  return env === 'development' ? 'debug' : 'info';
};

// Define log colors
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue',
};

// Add colors to winston
winston.addColors(colors);

// Define the format for console output
const consoleFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  format.colorize({ all: true }),
  format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${info.metadata ? ' ' + JSON.stringify(info.metadata) : ''}`
  )
);

// Define the format for file output (JSON)
const fileFormat = format.combine(
  format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  format.json()
);

// Create the logger instance
const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileFormat,
  defaultMeta: { service: 'privacy-management-api' },
  transports: [
    // Write logs to console
    new winston.transports.Console({
      format: consoleFormat,
    }),
    // Write all logs with level 'error' and below to error.log
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
    }),
    // Write all logs to combined.log
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
  // Do not exit on handled exceptions
  exitOnError: false,
});

/**
 * Log an error message
 * @param {string} message - Error message
 * @param {Object} metadata - Additional metadata
 */
const error = (message, metadata = {}) => {
  logger.error(message, { metadata });
};

/**
 * Log a warning message
 * @param {string} message - Warning message
 * @param {Object} metadata - Additional metadata
 */
const warn = (message, metadata = {}) => {
  logger.warn(message, { metadata });
};

/**
 * Log an info message
 * @param {string} message - Info message
 * @param {Object} metadata - Additional metadata
 */
const info = (message, metadata = {}) => {
  logger.info(message, { metadata });
};

/**
 * Log an HTTP request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {number} responseTime - Response time in milliseconds
 */
const http = (req, res, responseTime) => {
  const { method, url, ip, headers } = req;
  const { statusCode } = res;
  
  logger.http(`${method} ${url} ${statusCode} ${responseTime}ms`, {
    metadata: {
      method,
      url,
      statusCode,
      responseTime,
      ip,
      userAgent: headers['user-agent'],
      referer: headers.referer || '',
    },
  });
};

/**
 * Log a debug message
 * @param {string} message - Debug message
 * @param {Object} metadata - Additional metadata
 */
const debug = (message, metadata = {}) => {
  logger.debug(message, { metadata });
};

/**
 * HTTP logging middleware for Express
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const httpLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log when the response is finished
  res.on('finish', () => {
    const responseTime = Date.now() - start;
    http(req, res, responseTime);
  });
  
  next();
};

module.exports = {
  error,
  warn,
  info,
  http,
  debug,
  httpLogger,
};
