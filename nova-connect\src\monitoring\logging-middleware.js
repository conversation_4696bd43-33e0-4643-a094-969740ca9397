/**
 * NovaFuse Universal API Connector - Logging Middleware
 * 
 * This module provides middleware for request and response logging.
 */

const { createLogger } = require('../utils/logger');
const { UAConnectorError } = require('../errors');

const logger = createLogger('http');

/**
 * Mask sensitive data in objects
 * 
 * @param {Object} obj - The object to mask
 * @param {Array<string>} sensitiveFields - The sensitive field names
 * @returns {Object} - The masked object
 */
function maskSensitiveData(obj, sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization', 'apiKey']) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  const masked = { ...obj };
  
  for (const key of Object.keys(masked)) {
    const lowerKey = key.toLowerCase();
    
    if (sensitiveFields.some(field => lowerKey.includes(field))) {
      masked[key] = '********';
    } else if (typeof masked[key] === 'object') {
      masked[key] = maskSensitiveData(masked[key], sensitiveFields);
    }
  }
  
  return masked;
}

/**
 * Request logging middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function requestLoggingMiddleware(req, res, next) {
  // Skip logging for health check and metrics endpoints
  if (req.path === '/health' || req.path === '/metrics') {
    return next();
  }
  
  // Generate request ID if not present
  if (!req.id) {
    req.id = Math.random().toString(36).substring(2, 15);
  }
  
  // Log request
  const requestData = {
    request_id: req.id,
    method: req.method,
    url: req.originalUrl,
    headers: maskSensitiveData(req.headers),
    query: maskSensitiveData(req.query),
    body: maskSensitiveData(req.body),
    ip: req.ip || req.connection.remoteAddress
  };
  
  logger.info(`Request: ${req.method} ${req.originalUrl}`, requestData);
  
  // Record start time
  req.startTime = Date.now();
  
  // Capture response
  const originalEnd = res.end;
  res.end = function(...args) {
    // Calculate duration
    const duration = Date.now() - req.startTime;
    
    // Log response
    const responseData = {
      request_id: req.id,
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      duration: duration,
      headers: maskSensitiveData(res.getHeaders())
    };
    
    if (res.statusCode >= 400) {
      logger.warn(`Response: ${res.statusCode} ${req.method} ${req.originalUrl} (${duration}ms)`, responseData);
    } else {
      logger.info(`Response: ${res.statusCode} ${req.method} ${req.originalUrl} (${duration}ms)`, responseData);
    }
    
    // Call original end
    return originalEnd.apply(res, args);
  };
  
  next();
}

/**
 * Error logging middleware
 * 
 * @param {Error} err - The error
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function errorLoggingMiddleware(err, req, res, next) {
  // Log error
  const errorData = {
    request_id: req.id,
    method: req.method,
    url: req.originalUrl,
    error: err.message,
    stack: err.stack
  };
  
  if (err instanceof UAConnectorError) {
    errorData.error_type = err.name;
    errorData.error_code = err.code;
    errorData.error_id = err.errorId;
    errorData.severity = err.severity;
    
    if (err.severity === 'error') {
      logger.error(`Error: ${err.name} (${err.code}) - ${err.message}`, errorData);
    } else {
      logger.warn(`Error: ${err.name} (${err.code}) - ${err.message}`, errorData);
    }
  } else {
    logger.error(`Error: ${err.name || 'Error'} - ${err.message}`, errorData);
  }
  
  next(err);
}

module.exports = {
  requestLoggingMiddleware,
  errorLoggingMiddleware
};
