/**
 * NovaConnect Performance Tests - Data Normalization
 * 
 * These tests validate the performance of the data normalization engine,
 * ensuring it meets the <100ms requirement.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  
  // Record response time in global metrics if available
  if (global.recordResponseTime) {
    global.recordResponseTime(duration);
  }
  
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  transformUrl: '/api/transform'
};

// Test data
const samplePayload = {
  source: {
    id: 'finding-123',
    name: 'Unpatched vulnerability',
    severity: 'HIGH',
    category: 'VULNERABILITY',
    createTime: '2023-01-01T00:00:00Z',
    resource: {
      name: 'projects/test-project/instances/test-instance',
      type: 'google.compute.Instance'
    }
  },
  transformationRules: [
    { source: 'source.id', target: 'id' },
    { source: 'source.name', target: 'title' },
    { source: 'source.severity', target: 'severity', transform: 'lowercase' },
    { source: 'source.category', target: 'type', transform: 'lowercase' },
    { source: 'source.createTime', target: 'createdAt', transform: 'isoToUnix' },
    { source: 'source.resource.name', target: 'resourceName' },
    { source: 'source.resource.type', target: 'resourceType' }
  ]
};

describe('Data Normalization Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(30000);
  
  // Test single transformation performance
  it('should normalize data within 100ms', async () => {
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.transformUrl}`, samplePayload);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('id', 'finding-123');
    expect(result.data).toHaveProperty('severity', 'high');
    expect(duration).toBeLessThan(100); // Should normalize in less than 100ms
    
    console.log(`Data normalization completed in ${duration.toFixed(2)} ms`);
  });
  
  // Test batch transformation performance
  it('should normalize 100 items within acceptable time', async () => {
    // Generate batch payload with 100 items
    const batchPayload = {
      source: {
        findings: Array(100).fill(0).map((_, i) => ({
          id: `finding-${i}`,
          name: `Finding ${i}`,
          severity: ['HIGH', 'MEDIUM', 'LOW'][i % 3],
          category: ['VULNERABILITY', 'MISCONFIGURATION', 'THREAT'][i % 3],
          createTime: new Date().toISOString(),
          resource: {
            name: `projects/test-project/instances/instance-${i}`,
            type: 'google.compute.Instance'
          }
        }))
      },
      transformationRules: [
        { source: 'findings[*].id', target: 'items[*].id' },
        { source: 'findings[*].name', target: 'items[*].title' },
        { source: 'findings[*].severity', target: 'items[*].severity', transform: 'lowercase' },
        { source: 'findings[*].category', target: 'items[*].type', transform: 'lowercase' },
        { source: 'findings[*].createTime', target: 'items[*].createdAt', transform: 'isoToUnix' },
        { source: 'findings[*].resource.name', target: 'items[*].resourceName' },
        { source: 'findings[*].resource.type', target: 'items[*].resourceType' }
      ]
    };
    
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.transformUrl}`, batchPayload);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('items');
    expect(result.data.items).toHaveLength(100);
    
    // Average time per item should be less than 100ms
    const averageTimePerItem = duration / 100;
    expect(averageTimePerItem).toBeLessThan(100);
    
    console.log(`Batch normalization (100 items) completed in ${duration.toFixed(2)} ms`);
    console.log(`Average time per item: ${averageTimePerItem.toFixed(2)} ms`);
  });
  
  // Test peak load simulation
  it('should handle peak load simulation for 50K events', async () => {
    // For this test, we'll use a smaller batch and extrapolate
    // to avoid running an actual test with 50K events
    const batchSize = 500; // Use 500 items for the simulation
    
    // Generate batch payload
    const batchPayload = {
      source: {
        findings: Array(batchSize).fill(0).map((_, i) => ({
          id: `finding-${i}`,
          name: `Finding ${i}`,
          severity: ['HIGH', 'MEDIUM', 'LOW'][i % 3],
          category: ['VULNERABILITY', 'MISCONFIGURATION', 'THREAT'][i % 3],
          createTime: new Date().toISOString(),
          resource: {
            name: `projects/test-project/instances/instance-${i}`,
            type: 'google.compute.Instance'
          }
        }))
      },
      transformationRules: [
        { source: 'findings[*].id', target: 'items[*].id' },
        { source: 'findings[*].name', target: 'items[*].title' },
        { source: 'findings[*].severity', target: 'items[*].severity', transform: 'lowercase' },
        { source: 'findings[*].category', target: 'items[*].type', transform: 'lowercase' },
        { source: 'findings[*].createTime', target: 'items[*].createdAt', transform: 'isoToUnix' },
        { source: 'findings[*].resource.name', target: 'items[*].resourceName' },
        { source: 'findings[*].resource.type', target: 'items[*].resourceType' }
      ]
    };
    
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.transformUrl}`, batchPayload);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('items');
    expect(result.data.items).toHaveLength(batchSize);
    
    // Calculate throughput (items per second)
    const throughput = (batchSize / duration) * 1000;
    
    console.log(`Peak load simulation (${batchSize} items) completed in ${duration.toFixed(2)} ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} items/second`);
    
    // Calculate if we can meet the 50K in 15 minutes requirement
    const itemsPerSecondNeeded = 50000 / (15 * 60); // 55.56 items/second
    expect(throughput).toBeGreaterThan(itemsPerSecondNeeded);
    
    // Extrapolate to full load
    const timeFor50K = (50000 / throughput) * 1000; // ms
    const timeInMinutes = timeFor50K / (1000 * 60); // minutes
    
    console.log(`Estimated time to process 50,000 items: ${timeInMinutes.toFixed(2)} minutes`);
    expect(timeInMinutes).toBeLessThanOrEqual(15);
  });
  
  // Test concurrent transformations
  it('should handle concurrent transformations efficiently', async () => {
    const concurrentRequests = 10;
    const transformationFn = () => axios.post(`${config.baseUrl}${config.transformUrl}`, samplePayload);
    
    const { result, duration } = await measureExecutionTime(async () => {
      const promises = Array(concurrentRequests).fill(0).map(() => transformationFn());
      return await Promise.all(promises);
    });
    
    expect(result).toHaveLength(concurrentRequests);
    result.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', 'finding-123');
    });
    
    // Average time per transformation should be reasonable
    const averageTime = duration / concurrentRequests;
    
    console.log(`${concurrentRequests} concurrent transformations completed in ${duration.toFixed(2)} ms`);
    console.log(`Average time per transformation: ${averageTime.toFixed(2)} ms`);
    
    // While we can't guarantee <100ms for concurrent operations,
    // we can ensure the system handles concurrency well
    expect(averageTime).toBeLessThan(200);
  });
});
