/**
 * TRI-MARKET DOMAINS (TAS™) COMPONENT
 * Triadic Asset Segmentation with separate consciousness lanes
 * Real-time MTPH tracking, resource allocation, and consciousness arbitration
 * Enhanced market-specific intelligence and dynamic bandwidth optimization
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend
} from 'chart.js';
import {
  ChartPieIcon,
  ArrowRightIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CpuChipIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

ChartJS.register(ArcElement, Tooltip, Legend);

export default function TriMarketAllocation({ selectedMarket, onMarketSelect }) {
  const [tasData, setTasData] = useState({
    segmentation_active: true,
    resource_allocation: { STOCKS: 0.33, CRYPTO: 0.33, FOREX: 0.34 },
    market_coherence_scores: { STOCKS: 0.75, CRYPTO: 0.80, FOREX: 0.70 },
    mtph_status: {},
    market_lanes: {},
    last_arbitration: new Date()
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    updateTASData();

    // Update every 30 seconds for real-time tracking
    const interval = setInterval(() => {
      updateTASData();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const updateTASData = async () => {
    try {
      const response = await fetch('/api/engines/triadic-asset-segmentation');
      const data = await response.json();

      if (data.success) {
        setTasData(data.current_status);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('TAS data error:', error);
      setIsLoading(false);
    }
  };

  const executeArbitration = async () => {
    try {
      const response = await fetch('/api/engines/triadic-asset-segmentation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'EXECUTE_ARBITRATION' })
      });

      if (response.ok) {
        updateTASData();
      }
    } catch (error) {
      console.error('Arbitration error:', error);
    }
  };

  const getMarketColor = (market) => {
    const colors = {
      'STOCKS': '#3B82F6', // Blue
      'CRYPTO': '#F59E0B', // Orange
      'FOREX': '#10B981'   // Green
    };
    return colors[market] || '#6B7280';
  };

  const getMarketIcon = (market) => {
    const icons = {
      'STOCKS': '📊',
      'CRYPTO': '🪙',
      'FOREX': '💱'
    };
    return icons[market] || '📈';
  };

  const getAllocationColor = (allocation) => {
    if (allocation >= 0.6) return 'text-green-400'; // Dominant
    if (allocation >= 0.4) return 'text-yellow-400'; // High
    if (allocation >= 0.2) return 'text-blue-400'; // Normal
    return 'text-red-400'; // Low
  };

  const getMTPHStatus = (current, target) => {
    if (current >= target) return { status: 'OPTIMAL', color: 'text-green-400' };
    if (current >= target * 0.7) return { status: 'GOOD', color: 'text-yellow-400' };
    if (current >= target * 0.5) return { status: 'LOW', color: 'text-orange-400' };
    return { status: 'CRITICAL', color: 'text-red-400' };
  };

  // Chart data for allocation visualization
  const chartData = {
    labels: ['Stocks', 'Crypto', 'Forex'],
    datasets: [
      {
        data: [
          (tasData.resource_allocation.STOCKS || 0.33) * 100,
          (tasData.resource_allocation.CRYPTO || 0.33) * 100,
          (tasData.resource_allocation.FOREX || 0.34) * 100
        ],
        backgroundColor: [
          getMarketColor('STOCKS'),
          getMarketColor('CRYPTO'),
          getMarketColor('FOREX')
        ],
        borderColor: [
          getMarketColor('STOCKS'),
          getMarketColor('CRYPTO'),
          getMarketColor('FOREX')
        ],
        borderWidth: 2,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#D1D5DB',
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.label}: ${context.parsed.toFixed(1)}%`;
          }
        }
      }
    }
  };

  if (isLoading) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-600 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-600 rounded w-3/4"></div>
            <div className="h-4 bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-cyan-500/30 p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <ChartPieIcon className="w-5 h-5 text-cyan-400" />
          <h3 className="text-lg font-semibold text-white">
            🔱 Tri-Market Domains (TAS™)
          </h3>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={executeArbitration}
            className="px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white rounded text-sm font-medium transition-colors"
          >
            🧠 Arbitrate
          </button>
          <div className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${
            tasData.segmentation_active ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'
          }`}>
            <div className="w-2 h-2 rounded-full bg-current animate-pulse"></div>
            <span className="font-bold">TAS™</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Allocation Chart */}
        <div className="space-y-4">
          <h4 className="text-md font-semibold text-white">⚖️ Resource Allocation</h4>
          <div className="h-48">
            <Doughnut data={chartData} options={chartOptions} />
          </div>
        </div>

        {/* Market Lanes Status */}
        <div className="space-y-4">
          <h4 className="text-md font-semibold text-white">📊 Consciousness Lanes</h4>
          <div className="space-y-3">
            {Object.entries(tasData.market_lanes).map(([market, lane]) => {
              const mtph_data = tasData.mtph_status[market] || { current_mtph: 0, target_mtph: 0 };
              const mtph_status = getMTPHStatus(mtph_data.current_mtph, mtph_data.target_mtph);
              const allocation = tasData.resource_allocation[market] || 0;
              const coherence = tasData.market_coherence_scores[market] || 0;

              return (
                <div
                  key={market}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    selectedMarket === market
                      ? 'bg-cyan-900/30 border-cyan-500'
                      : 'bg-gray-800/30 border-gray-600 hover:border-gray-500'
                  }`}
                  onClick={() => onMarketSelect && onMarketSelect(market)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getMarketIcon(market)}</span>
                      <span className="font-medium text-white">{market}</span>
                    </div>
                    <span className={`text-sm font-bold ${getAllocationColor(allocation)}`}>
                      {(allocation * 100).toFixed(1)}%
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="text-gray-400">Coherence:</span>
                      <span className="text-cyan-400 ml-1">{(coherence * 100).toFixed(1)}%</span>
                    </div>
                    <div>
                      <span className="text-gray-400">MTPH:</span>
                      <span className={`ml-1 ${mtph_status.color}`}>
                        {mtph_data.current_mtph}/{mtph_data.target_mtph}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* MTPH Summary */}
      <div className="mt-6">
        <h4 className="text-md font-semibold text-white mb-3">⏱️ MTPH (Minimum Trades Per Hour) Status</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {Object.entries(tasData.mtph_status).map(([market, mtph]) => {
            const status = getMTPHStatus(mtph.current_mtph, mtph.target_mtph);
            const efficiency = mtph.target_mtph > 0 ? (mtph.current_mtph / mtph.target_mtph) * 100 : 0;

            return (
              <div key={market} className="p-3 rounded-lg bg-gray-800/30 border border-gray-600 text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <span className="text-sm">{getMarketIcon(market)}</span>
                  <span className="text-sm font-medium text-white">{market}</span>
                </div>

                <div className={`text-lg font-bold ${status.color} mb-1`}>
                  {mtph.current_mtph}/{mtph.target_mtph}
                </div>

                <div className="text-xs text-gray-400">
                  {efficiency.toFixed(0)}% efficiency
                </div>

                {mtph.mtph_deficit > 0 && (
                  <div className="text-xs text-red-400 mt-1">
                    -{mtph.mtph_deficit} trades
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Arbitration Status */}
      <div className="mt-6 p-4 rounded-lg bg-gradient-to-r from-cyan-900/20 to-blue-900/20 border border-cyan-500/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CpuChipIcon className="w-5 h-5 text-cyan-400" />
            <span className="text-white font-medium">Consciousness Arbitration</span>
          </div>

          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <ClockIcon className="w-4 h-4 text-gray-400" />
              <span className="text-gray-400">
                Last: {new Date(tasData.last_arbitration).toLocaleTimeString()}
              </span>
            </div>

            <div className="text-cyan-400 font-bold">
              {Object.values(tasData.resource_allocation).reduce((max, val) => Math.max(max, val), 0) > 0.6 ? 'DOMINANT' : 'BALANCED'}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
