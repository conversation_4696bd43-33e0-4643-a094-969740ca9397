import React, { createContext, useContext, ReactNode } from 'react';

// Define the context type
interface UUICContextType {
  components?: Record<string, React.ComponentType<any>>;
  componentConfigs?: Record<string, any>;
  data?: Record<string, any>;
  [key: string]: any;
}

// Create the context with a default value
const UUICContext = createContext<UUICContextType | undefined>(undefined);

// Define the provider props
interface UUICProviderProps {
  children: ReactNode;
  moduleConfig: UUICContextType;
}

/**
 * UUICProvider - Context provider for UI data injection
 * 
 * This component provides the UI configuration to all child components
 * through React Context.
 */
export const UUICProvider: React.FC<UUICProviderProps> = ({ 
  children, 
  moduleConfig 
}) => {
  return (
    <UUICContext.Provider value={moduleConfig}>
      {children}
    </UUICContext.Provider>
  );
};

/**
 * useUUICContext - Hook to access the UUIC context
 * 
 * @returns UUIC context value
 */
export const useUUICContext = (): UUICContextType => {
  const context = useContext(UUICContext);
  if (context === undefined) {
    throw new Error('useUUICContext must be used within a UUICProvider');
  }
  return context;
};

export default UUICProvider;
