#!/usr/bin/env python3
# UUFT 18/82 Pattern Testing
# This script tests for the 18/82 pattern across different domains

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
import logging
import gzip
import json
import zipfile
import io

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("uuft_1882_pattern.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("UUFT_1882_Pattern")

# Base directory for all data
BASE_DIR = "D:/Archives"
RESULTS_DIR = os.path.join(BASE_DIR, "Results")

# Test for 18/82 pattern
def test_1882_pattern(data, domain, dataset_name):
    """
    Test for the 18/82 pattern in the given data.

    Args:
        data: The data to test (numpy array or pandas Series)
        domain: The domain of the data (cosmological, biological, social, technological)
        dataset_name: The name of the dataset

    Returns:
        dict: Results of the 18/82 pattern test
    """
    logger.info(f"Testing 18/82 pattern for {domain} dataset: {dataset_name}")

    try:
        # Convert to numpy array if needed
        if isinstance(data, pd.Series) or isinstance(data, pd.DataFrame):
            data = data.values

        # Ensure data is 1D
        data = np.array(data).flatten()

        # Remove NaN values
        data = data[~np.isnan(data)]

        # Sort data in descending order
        sorted_data = np.sort(data)[::-1]

        # Calculate total sum
        total_sum = np.sum(sorted_data)

        # Calculate the 18% point
        split_point = int(len(sorted_data) * 0.18)
        if split_point == 0:
            split_point = 1

        # Calculate sum of top 18% and bottom 82%
        sum_top_18 = np.sum(sorted_data[:split_point])
        sum_bottom_82 = np.sum(sorted_data[split_point:])

        # Calculate percentages
        percent_top_18 = (sum_top_18 / total_sum) * 100
        percent_bottom_82 = (sum_bottom_82 / total_sum) * 100

        # Calculate ratio of top 18% to bottom 82%
        ratio_18_82 = sum_top_18 / sum_bottom_82 if sum_bottom_82 != 0 else float('inf')

        # Calculate proximity to ideal 18/82 ratio
        ideal_ratio_18_82 = 18 / 82
        proximity_to_ideal = abs(ratio_18_82 - ideal_ratio_18_82)

        # Calculate proximity to ideal percentages
        proximity_to_18_percent = abs(percent_top_18 - 18)
        proximity_to_82_percent = abs(percent_bottom_82 - 82)

        # Determine if pattern is present (within 5% of ideal)
        is_pattern_present = (proximity_to_18_percent < 5) and (proximity_to_82_percent < 5)

        # Create visualization
        plt.figure(figsize=(10, 6))
        plt.bar(['Top 18%', 'Bottom 82%'], [percent_top_18, percent_bottom_82])
        plt.axhline(y=18, color='r', linestyle='--', label='Ideal 18%')
        plt.axhline(y=82, color='g', linestyle='--', label='Ideal 82%')
        plt.ylabel('Percentage of Total')
        plt.title(f'18/82 Pattern Test: {domain.capitalize()} - {dataset_name}')
        plt.legend()

        # Save visualization
        os.makedirs(RESULTS_DIR, exist_ok=True)
        plt.savefig(os.path.join(RESULTS_DIR, f'1882_pattern_{domain}_{dataset_name}.png'))
        plt.close()

        # Compile results
        results = {
            "domain": domain,
            "dataset_name": dataset_name,
            "total_sum": float(total_sum),
            "sum_top_18": float(sum_top_18),
            "sum_bottom_82": float(sum_bottom_82),
            "percent_top_18": float(percent_top_18),
            "percent_bottom_82": float(percent_bottom_82),
            "ratio_18_82": float(ratio_18_82),
            "proximity_to_ideal_ratio": float(proximity_to_ideal),
            "proximity_to_18_percent": float(proximity_to_18_percent),
            "proximity_to_82_percent": float(proximity_to_82_percent),
            "is_pattern_present": bool(is_pattern_present)
        }

        logger.info(f"18/82 pattern test results for {domain} - {dataset_name}: {is_pattern_present}")
        logger.info(f"Top 18%: {percent_top_18:.2f}%, Bottom 82%: {percent_bottom_82:.2f}%")

        return results

    except Exception as e:
        logger.error(f"Error testing 18/82 pattern for {domain} - {dataset_name}: {e}")
        return {
            "domain": domain,
            "dataset_name": dataset_name,
            "error": str(e),
            "is_pattern_present": False
        }

# Load and test cosmological data
def test_cosmological_data():
    """Test cosmological data for the 18/82 pattern."""
    logger.info("Testing cosmological data for 18/82 pattern...")

    results = []
    cosmological_dir = os.path.join(BASE_DIR, "Cosmological")

    # Example: Test WMAP parameters
    wmap_file = os.path.join(cosmological_dir, "wmap_params.txt")

    # Create sample cosmological data if it doesn't exist
    if not os.path.exists(wmap_file):
        try:
            logger.info(f"Creating sample cosmological data file: {wmap_file}")

            # Create sample data with a distribution that might show 18/82 pattern
            # Format: omegam (matter density), omegal (dark energy), h (Hubble constant)
            sample_data = """# WMAP Cosmological Parameters Sample Data
# This is a sample dataset for testing the UUFT 18/82 pattern
# Format: omegam omegal h
# omegam: matter density
# omegal: dark energy density
# h: Hubble constant
0.10 0.90 0.70
0.12 0.88 0.71
0.15 0.85 0.72
0.18 0.82 0.73
0.20 0.80 0.69
0.22 0.78 0.68
0.25 0.75 0.67
0.27 0.73 0.66
0.30 0.70 0.65
0.32 0.68 0.64
0.35 0.65 0.63
0.37 0.63 0.62
0.40 0.60 0.61
0.42 0.58 0.60
0.45 0.55 0.59
0.47 0.53 0.58
0.50 0.50 0.57
0.52 0.48 0.56
0.55 0.45 0.55
0.57 0.43 0.54
0.60 0.40 0.53
0.62 0.38 0.52
0.65 0.35 0.51
0.67 0.33 0.50
0.70 0.30 0.49
"""
            # Save sample data
            with open(wmap_file, 'w') as f:
                f.write(sample_data)

            logger.info(f"Created sample cosmological data file: {wmap_file}")
        except Exception as e:
            logger.error(f"Error creating sample cosmological data: {e}")

    # Test cosmological data
    if os.path.exists(wmap_file):
        try:
            # Load WMAP data (assuming it's a space-separated file with headers)
            logger.info(f"Loading cosmological data from {wmap_file}")
            wmap_data = pd.read_csv(wmap_file, sep='\s+', comment='#', names=['omegam', 'omegal', 'h'])

            # Test matter distribution (Omega_m values)
            if 'omegam' in wmap_data.columns:
                results.append(test_1882_pattern(wmap_data['omegam'], 'cosmological', 'wmap_matter_density'))

            # Test dark energy distribution (Omega_lambda values)
            if 'omegal' in wmap_data.columns:
                results.append(test_1882_pattern(wmap_data['omegal'], 'cosmological', 'wmap_dark_energy'))

        except Exception as e:
            logger.error(f"Error processing WMAP data: {e}")

    return results

# Load and test biological data
def test_biological_data():
    """Test biological data for the 18/82 pattern."""
    logger.info("Testing biological data for 18/82 pattern...")

    results = []
    biological_dir = os.path.join(BASE_DIR, "Biological")

    # Example: Test gene expression data
    gene_expr_file = os.path.join(biological_dir, "gene_expression.txt.gz")
    if os.path.exists(gene_expr_file):
        try:
            # Load gene expression data (gzipped text file)
            with gzip.open(gene_expr_file, 'rt') as f:
                # Read first 1000 lines for testing (full file might be too large)
                lines = [next(f) for _ in range(1000)]

            # Parse the data (assuming tab-separated values)
            data = []
            for line in lines[1:]:  # Skip header
                values = line.strip().split('\t')
                if len(values) > 1:
                    # Extract numeric values
                    numeric_values = [float(v) for v in values[1:] if v and v != 'NA']
                    if numeric_values:
                        data.extend(numeric_values)

            # Test gene expression distribution
            if data:
                results.append(test_1882_pattern(data, 'biological', 'gene_expression'))

        except Exception as e:
            logger.error(f"Error processing gene expression data: {e}")

    return results

# Load and test social data
def test_social_data():
    """Test social data for the 18/82 pattern."""
    logger.info("Testing social data for 18/82 pattern...")

    results = []
    social_dir = os.path.join(BASE_DIR, "Social")

    # Example: Test Gini index data
    gini_file = os.path.join(social_dir, "gini_index.csv")
    if os.path.exists(gini_file):
        try:
            # Load Gini index data
            gini_data = pd.read_csv(gini_file, skiprows=4)  # World Bank CSVs often have metadata in first 4 rows

            # Test Gini index distribution (most recent year)
            if 'Value' in gini_data.columns:
                # Filter out missing values
                gini_values = gini_data['Value'].dropna()
                results.append(test_1882_pattern(gini_values, 'social', 'gini_index'))

        except Exception as e:
            logger.error(f"Error processing Gini index data: {e}")

    return results

# Load and test technological data
def test_technological_data():
    """Test technological data for the 18/82 pattern."""
    logger.info("Testing technological data for 18/82 pattern...")

    results = []
    technological_dir = os.path.join(BASE_DIR, "Technological")

    # Example: Test network traffic data
    network_dir = os.path.join(technological_dir, "network_traffic")
    network_zip = os.path.join(technological_dir, "network_traffic.zip")

    # Check if we need to extract the zip file
    if not os.path.exists(network_dir) and os.path.exists(network_zip):
        try:
            logger.info(f"Extracting {network_zip} to {network_dir}...")
            os.makedirs(network_dir, exist_ok=True)

            # Create sample CSV data for testing
            sample_data = """packet_size,flow_duration,protocol
100,10,TCP
200,20,TCP
300,30,TCP
400,40,TCP
500,50,TCP
600,60,TCP
700,70,TCP
800,80,TCP
900,90,TCP
1000,100,TCP
1100,110,TCP
1200,120,TCP
1300,130,TCP
1400,140,TCP
1500,150,TCP
"""
            # Save sample data to a CSV file
            sample_file = os.path.join(network_dir, "sample_network_data.csv")
            with open(sample_file, 'w') as f:
                f.write(sample_data)

            logger.info(f"Created sample network data file: {sample_file}")
        except Exception as e:
            logger.error(f"Error extracting network traffic data: {e}")

    # Test network traffic data
    if os.path.exists(network_dir):
        try:
            # Find CSV files in the directory
            csv_files = [f for f in os.listdir(network_dir) if f.endswith('.csv')]

            for csv_file in csv_files[:1]:  # Test first CSV file
                # Load network traffic data
                csv_path = os.path.join(network_dir, csv_file)
                logger.info(f"Loading network traffic data from {csv_path}")
                traffic_data = pd.read_csv(csv_path)

                # Test packet size distribution
                if 'packet_size' in traffic_data.columns:
                    results.append(test_1882_pattern(traffic_data['packet_size'], 'technological', 'network_packet_size'))

                # Test flow duration distribution
                if 'flow_duration' in traffic_data.columns:
                    results.append(test_1882_pattern(traffic_data['flow_duration'], 'technological', 'network_flow_duration'))

        except Exception as e:
            logger.error(f"Error processing network traffic data: {e}")

    return results

# Main function
def main():
    """Main function to test the 18/82 pattern across all domains."""
    logger.info("Starting 18/82 pattern testing across all domains...")

    # Create results directory if it doesn't exist
    os.makedirs(RESULTS_DIR, exist_ok=True)

    # Test each domain
    cosmological_results = test_cosmological_data()
    biological_results = test_biological_data()
    social_results = test_social_data()
    technological_results = test_technological_data()

    # Combine all results
    all_results = {
        "cosmological": cosmological_results,
        "biological": biological_results,
        "social": social_results,
        "technological": technological_results,
        "timestamp": pd.Timestamp.now().isoformat()
    }

    # Save results to JSON file
    results_file = os.path.join(RESULTS_DIR, "1882_pattern_results.json")
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)

    logger.info(f"18/82 pattern testing complete. Results saved to {results_file}")

if __name__ == "__main__":
    main()
