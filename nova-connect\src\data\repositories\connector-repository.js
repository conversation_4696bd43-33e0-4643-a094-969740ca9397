/**
 * NovaFuse Universal API Connector - Connector Repository
 * 
 * This module provides data access methods for connector templates.
 */

const Connector = require('../models/connector');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('connector-repository');

/**
 * Connector Repository
 * 
 * Provides methods for accessing and manipulating connector templates.
 */
class ConnectorRepository {
  /**
   * Get a connector by ID
   * @param {string} id - Connector ID
   * @returns {Promise<Object>} Connector template
   */
  async getConnectorById(id) {
    try {
      logger.debug(`Getting connector by ID: ${id}`);
      return await Connector.findOne({ id, isActive: true });
    } catch (error) {
      logger.error(`Error getting connector by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get connectors by category
   * @param {string} category - Connector category
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Connector templates
   */
  async getConnectorsByCategory(category, options = {}) {
    try {
      const { limit = 100, skip = 0, isPublic = true } = options;
      
      logger.debug(`Getting connectors by category: ${category}`);
      
      const query = { 
        'metadata.category': category, 
        isActive: true 
      };
      
      if (isPublic !== null) {
        query.isPublic = isPublic;
      }
      
      return await Connector.find(query)
        .sort({ 'metadata.name': 1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error getting connectors by category ${category}:`, error);
      throw error;
    }
  }

  /**
   * Search connectors
   * @param {string} searchTerm - Search term
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Connector templates
   */
  async searchConnectors(searchTerm, options = {}) {
    try {
      const { limit = 100, skip = 0, isPublic = true } = options;
      
      logger.debug(`Searching connectors with term: ${searchTerm}`);
      
      const query = { 
        isActive: true,
        $or: [
          { 'metadata.name': { $regex: searchTerm, $options: 'i' } },
          { 'metadata.description': { $regex: searchTerm, $options: 'i' } },
          { 'metadata.tags': { $regex: searchTerm, $options: 'i' } }
        ]
      };
      
      if (isPublic !== null) {
        query.isPublic = isPublic;
      }
      
      return await Connector.find(query)
        .sort({ 'metadata.name': 1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error searching connectors with term ${searchTerm}:`, error);
      throw error;
    }
  }

  /**
   * Get all connectors
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Connector templates
   */
  async getAllConnectors(options = {}) {
    try {
      const { limit = 100, skip = 0, isPublic = true } = options;
      
      logger.debug('Getting all connectors');
      
      const query = { isActive: true };
      
      if (isPublic !== null) {
        query.isPublic = isPublic;
      }
      
      return await Connector.find(query)
        .sort({ 'metadata.name': 1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error('Error getting all connectors:', error);
      throw error;
    }
  }

  /**
   * Get connectors by owner
   * @param {string} ownerId - Owner ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Connector templates
   */
  async getConnectorsByOwner(ownerId, options = {}) {
    try {
      const { limit = 100, skip = 0 } = options;
      
      logger.debug(`Getting connectors by owner: ${ownerId}`);
      
      return await Connector.find({ ownerId, isActive: true })
        .sort({ 'metadata.name': 1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error getting connectors by owner ${ownerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a connector
   * @param {Object} connectorData - Connector template data
   * @returns {Promise<Object>} Created connector template
   */
  async createConnector(connectorData) {
    try {
      logger.debug('Creating connector');
      
      const connector = new Connector(connectorData);
      return await connector.save();
    } catch (error) {
      logger.error('Error creating connector:', error);
      throw error;
    }
  }

  /**
   * Update a connector
   * @param {string} id - Connector ID
   * @param {Object} connectorData - Updated connector template data
   * @returns {Promise<Object>} Updated connector template
   */
  async updateConnector(id, connectorData) {
    try {
      logger.debug(`Updating connector: ${id}`);
      
      // Ensure ID is not changed
      delete connectorData.id;
      
      // Update timestamps
      connectorData.updatedAt = new Date();
      if (connectorData.metadata) {
        connectorData.metadata.updated = new Date();
      }
      
      return await Connector.findOneAndUpdate(
        { id },
        { $set: connectorData },
        { new: true, runValidators: true }
      );
    } catch (error) {
      logger.error(`Error updating connector ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a connector
   * @param {string} id - Connector ID
   * @returns {Promise<boolean>} Whether the connector was deleted
   */
  async deleteConnector(id) {
    try {
      logger.debug(`Deleting connector: ${id}`);
      
      // Soft delete by setting isActive to false
      const result = await Connector.findOneAndUpdate(
        { id },
        { $set: { isActive: false, updatedAt: new Date() } }
      );
      
      return !!result;
    } catch (error) {
      logger.error(`Error deleting connector ${id}:`, error);
      throw error;
    }
  }

  /**
   * Hard delete a connector (for testing and admin purposes)
   * @param {string} id - Connector ID
   * @returns {Promise<boolean>} Whether the connector was deleted
   */
  async hardDeleteConnector(id) {
    try {
      logger.debug(`Hard deleting connector: ${id}`);
      
      const result = await Connector.deleteOne({ id });
      return result.deletedCount > 0;
    } catch (error) {
      logger.error(`Error hard deleting connector ${id}:`, error);
      throw error;
    }
  }

  /**
   * Count connectors
   * @param {Object} filter - Filter criteria
   * @returns {Promise<number>} Connector count
   */
  async countConnectors(filter = {}) {
    try {
      logger.debug('Counting connectors');
      
      // Add isActive filter by default
      const query = { isActive: true, ...filter };
      
      return await Connector.countDocuments(query);
    } catch (error) {
      logger.error('Error counting connectors:', error);
      throw error;
    }
  }

  /**
   * Check if a connector exists
   * @param {string} id - Connector ID
   * @returns {Promise<boolean>} Whether the connector exists
   */
  async connectorExists(id) {
    try {
      logger.debug(`Checking if connector exists: ${id}`);
      
      const count = await Connector.countDocuments({ id, isActive: true });
      return count > 0;
    } catch (error) {
      logger.error(`Error checking if connector exists ${id}:`, error);
      throw error;
    }
  }

  /**
   * Import connectors from JSON files
   * @param {Array<Object>} connectors - Array of connector templates
   * @returns {Promise<Object>} Import results
   */
  async importConnectors(connectors) {
    try {
      logger.debug(`Importing ${connectors.length} connectors`);
      
      const results = {
        created: 0,
        updated: 0,
        failed: 0,
        errors: []
      };
      
      for (const connectorData of connectors) {
        try {
          // Check if connector already exists
          const exists = await this.connectorExists(connectorData.id);
          
          if (exists) {
            // Update existing connector
            await this.updateConnector(connectorData.id, connectorData);
            results.updated++;
          } else {
            // Create new connector
            await this.createConnector(connectorData);
            results.created++;
          }
        } catch (error) {
          logger.error(`Error importing connector ${connectorData.id || 'unknown'}:`, error);
          results.failed++;
          results.errors.push({
            id: connectorData.id || 'unknown',
            error: error.message
          });
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Error importing connectors:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new ConnectorRepository();
