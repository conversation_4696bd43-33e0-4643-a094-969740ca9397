{"version": 3, "names": ["performance", "require", "TransformationEngine", "constructor", "options", "enableMetrics", "enableCaching", "transformers", "lowercase", "value", "toLowerCase", "uppercase", "toUpperCase", "trim", "isoToUnix", "Date", "getTime", "unixToIso", "toISOString", "toNumber", "isNaN", "parseFloat", "to<PERSON><PERSON>", "JSON", "stringify", "fromJson", "parse", "e", "join", "separator", "Array", "isArray", "split", "mask", "pattern", "substring", "cache", "Map", "registerTransformer", "name", "transformer", "Error", "transform", "data", "rules", "startTime", "now", "cache<PERSON>ey", "_generate<PERSON><PERSON><PERSON><PERSON>", "cachedResult", "get", "result", "rule", "_getValueByPath", "source", "transformedValue", "transformParams", "reduce", "currentValue", "transformName", "_setValueByPath", "target", "error", "console", "message", "set", "endTime", "duration", "_recordMetrics", "length", "batchTransform", "arrayPaths", "filter", "includes", "sourceArrayPath", "sourceArray", "items", "item", "itemRules", "map", "replace", "transformedItem", "push", "targetArrayPath", "getMetrics", "metrics", "clearCache", "clear", "rulesCount", "isBatch", "transformations", "totalDuration", "averageDuration", "batchTransformations", "batchTotalDuration", "batchAverageDuration", "rulesApplied", "obj", "path", "normalizedPath", "parts", "current", "part", "undefined", "i", "match", "lastPart", "module", "exports"], "sources": ["transformation-engine.js"], "sourcesContent": ["/**\n * NovaConnect Transformation Engine\n * \n * High-performance data normalization engine capable of transforming data\n * from various sources into a standardized format at sub-millisecond speeds.\n */\n\nconst { performance } = require('perf_hooks');\n\nclass TransformationEngine {\n  constructor(options = {}) {\n    this.options = {\n      enableMetrics: true,\n      enableCaching: true,\n      ...options\n    };\n    \n    // Initialize transformers registry\n    this.transformers = {\n      // String transformations\n      lowercase: (value) => typeof value === 'string' ? value.toLowerCase() : value,\n      uppercase: (value) => typeof value === 'string' ? value.toUpperCase() : value,\n      trim: (value) => typeof value === 'string' ? value.trim() : value,\n      \n      // Date transformations\n      isoToUnix: (value) => typeof value === 'string' ? new Date(value).getTime() : value,\n      unixToIso: (value) => typeof value === 'number' ? new Date(value).toISOString() : value,\n      \n      // Number transformations\n      toNumber: (value) => !isNaN(parseFloat(value)) ? parseFloat(value) : value,\n      \n      // Object transformations\n      toJson: (value) => typeof value === 'object' ? JSON.stringify(value) : value,\n      fromJson: (value) => {\n        if (typeof value !== 'string') return value;\n        try {\n          return JSON.parse(value);\n        } catch (e) {\n          return value;\n        }\n      },\n      \n      // Array transformations\n      join: (value, separator = ',') => Array.isArray(value) ? value.join(separator) : value,\n      split: (value, separator = ',') => typeof value === 'string' ? value.split(separator) : value,\n      \n      // Security transformations\n      mask: (value, pattern = 'xxxx') => {\n        if (typeof value !== 'string') return value;\n        return value.substring(0, 4) + pattern;\n      }\n    };\n    \n    // Initialize transformation cache\n    this.cache = new Map();\n  }\n  \n  /**\n   * Register a custom transformer\n   * @param {string} name - Name of the transformer\n   * @param {Function} transformer - Transformer function\n   */\n  registerTransformer(name, transformer) {\n    if (typeof transformer !== 'function') {\n      throw new Error('Transformer must be a function');\n    }\n    \n    this.transformers[name] = transformer;\n  }\n  \n  /**\n   * Transform data according to the provided rules\n   * @param {Object} data - Source data to transform\n   * @param {Array} rules - Transformation rules\n   * @returns {Object} - Transformed data\n   */\n  transform(data, rules) {\n    const startTime = this.options.enableMetrics ? performance.now() : 0;\n    \n    // Check cache if enabled\n    if (this.options.enableCaching) {\n      const cacheKey = this._generateCacheKey(data, rules);\n      const cachedResult = this.cache.get(cacheKey);\n      \n      if (cachedResult) {\n        return cachedResult;\n      }\n    }\n    \n    const result = {};\n    \n    // Apply each transformation rule\n    for (const rule of rules) {\n      try {\n        // Get value from source path\n        const value = this._getValueByPath(data, rule.source);\n        \n        // Apply transformation if specified\n        let transformedValue = value;\n        if (rule.transform) {\n          if (typeof rule.transform === 'string' && this.transformers[rule.transform]) {\n            // Single transformation\n            transformedValue = this.transformers[rule.transform](value, rule.transformParams);\n          } else if (Array.isArray(rule.transform)) {\n            // Chain of transformations\n            transformedValue = rule.transform.reduce((currentValue, transformName) => {\n              if (this.transformers[transformName]) {\n                return this.transformers[transformName](currentValue, rule.transformParams);\n              }\n              return currentValue;\n            }, value);\n          }\n        }\n        \n        // Set value in target path\n        this._setValueByPath(result, rule.target, transformedValue);\n      } catch (error) {\n        // Log error but continue with other rules\n        console.error(`Error applying transformation rule: ${error.message}`, rule);\n      }\n    }\n    \n    // Cache result if caching is enabled\n    if (this.options.enableCaching) {\n      const cacheKey = this._generateCacheKey(data, rules);\n      this.cache.set(cacheKey, result);\n    }\n    \n    // Record metrics if enabled\n    if (this.options.enableMetrics) {\n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      // Store metrics for this transformation\n      this._recordMetrics(duration, rules.length);\n    }\n    \n    return result;\n  }\n  \n  /**\n   * Transform batch data according to the provided rules\n   * @param {Object} data - Source data containing arrays\n   * @param {Array} rules - Transformation rules with array paths\n   * @returns {Object} - Transformed data\n   */\n  batchTransform(data, rules) {\n    const startTime = this.options.enableMetrics ? performance.now() : 0;\n    \n    // Find array paths in rules\n    const arrayPaths = rules.filter(rule => rule.source.includes('[*]'));\n    \n    // If no array paths, use regular transform\n    if (arrayPaths.length === 0) {\n      return this.transform(data, rules);\n    }\n    \n    // Parse the source array path from the first array rule\n    const sourceArrayPath = arrayPaths[0].source.split('[*]')[0];\n    const sourceArray = this._getValueByPath(data, sourceArrayPath);\n    \n    if (!Array.isArray(sourceArray)) {\n      throw new Error(`Path ${sourceArrayPath} does not point to an array`);\n    }\n    \n    // Create result object with transformed items\n    const result = {};\n    const items = [];\n    \n    // Transform each item in the array\n    for (const item of sourceArray) {\n      // Create item-specific rules by replacing array notation\n      const itemRules = rules.map(rule => {\n        if (!rule.source.includes('[*]')) {\n          return rule;\n        }\n        \n        return {\n          source: rule.source.replace(`${sourceArrayPath}[*]`, '').replace(/^\\./, ''),\n          target: rule.target.replace(/\\[.*\\]/, ''),\n          transform: rule.transform,\n          transformParams: rule.transformParams\n        };\n      });\n      \n      // Transform the item\n      const transformedItem = this.transform({ item }, itemRules);\n      items.push(transformedItem);\n    }\n    \n    // Set the items in the result\n    const targetArrayPath = arrayPaths[0].target.split('[*]')[0];\n    this._setValueByPath(result, targetArrayPath, items);\n    \n    // Record metrics if enabled\n    if (this.options.enableMetrics) {\n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      // Store metrics for this batch transformation\n      this._recordMetrics(duration, rules.length * sourceArray.length, true);\n    }\n    \n    return result;\n  }\n  \n  /**\n   * Get metrics for the transformation engine\n   * @returns {Object} - Metrics object\n   */\n  getMetrics() {\n    return this.metrics;\n  }\n  \n  /**\n   * Clear the transformation cache\n   */\n  clearCache() {\n    this.cache.clear();\n  }\n  \n  /**\n   * Generate a cache key for the data and rules\n   * @private\n   */\n  _generateCacheKey(data, rules) {\n    // Simple implementation - in production would use a more efficient approach\n    return JSON.stringify({ data, rules });\n  }\n  \n  /**\n   * Record metrics for a transformation\n   * @private\n   */\n  _recordMetrics(duration, rulesCount, isBatch = false) {\n    if (!this.metrics) {\n      this.metrics = {\n        transformations: 0,\n        totalDuration: 0,\n        averageDuration: 0,\n        batchTransformations: 0,\n        batchTotalDuration: 0,\n        batchAverageDuration: 0,\n        rulesApplied: 0\n      };\n    }\n    \n    if (isBatch) {\n      this.metrics.batchTransformations++;\n      this.metrics.batchTotalDuration += duration;\n      this.metrics.batchAverageDuration = this.metrics.batchTotalDuration / this.metrics.batchTransformations;\n    } else {\n      this.metrics.transformations++;\n      this.metrics.totalDuration += duration;\n      this.metrics.averageDuration = this.metrics.totalDuration / this.metrics.transformations;\n    }\n    \n    this.metrics.rulesApplied += rulesCount;\n  }\n  \n  /**\n   * Get a value from an object by path\n   * @private\n   */\n  _getValueByPath(obj, path) {\n    if (!path) return obj;\n    \n    // Handle array notation\n    const normalizedPath = path.replace(/\\[(\\w+)\\]/g, '.$1');\n    const parts = normalizedPath.split('.');\n    \n    let current = obj;\n    for (const part of parts) {\n      if (part === '') continue;\n      if (current === null || current === undefined) return undefined;\n      current = current[part];\n    }\n    \n    return current;\n  }\n  \n  /**\n   * Set a value in an object by path\n   * @private\n   */\n  _setValueByPath(obj, path, value) {\n    if (!path) return;\n    \n    // Handle array notation\n    const normalizedPath = path.replace(/\\[(\\w+)\\]/g, '.$1');\n    const parts = normalizedPath.split('.');\n    \n    let current = obj;\n    for (let i = 0; i < parts.length - 1; i++) {\n      const part = parts[i];\n      if (part === '') continue;\n      \n      if (!(part in current)) {\n        // Create object or array based on next part\n        current[part] = parts[i + 1].match(/^\\d+$/) ? [] : {};\n      }\n      current = current[part];\n    }\n    \n    const lastPart = parts[parts.length - 1];\n    if (lastPart !== '') {\n      current[lastPart] = value;\n    }\n  }\n}\n\nmodule.exports = TransformationEngine;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAY,CAAC,GAAGC,OAAO,CAAC,YAAY,CAAC;AAE7C,MAAMC,oBAAoB,CAAC;EACzBC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAACA,OAAO,GAAG;MACbC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE,IAAI;MACnB,GAAGF;IACL,CAAC;;IAED;IACA,IAAI,CAACG,YAAY,GAAG;MAClB;MACAC,SAAS,EAAGC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC,GAAGD,KAAK;MAC7EE,SAAS,EAAGF,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACG,WAAW,CAAC,CAAC,GAAGH,KAAK;MAC7EI,IAAI,EAAGJ,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACI,IAAI,CAAC,CAAC,GAAGJ,KAAK;MAEjE;MACAK,SAAS,EAAGL,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAIM,IAAI,CAACN,KAAK,CAAC,CAACO,OAAO,CAAC,CAAC,GAAGP,KAAK;MACnFQ,SAAS,EAAGR,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAIM,IAAI,CAACN,KAAK,CAAC,CAACS,WAAW,CAAC,CAAC,GAAGT,KAAK;MAEvF;MACAU,QAAQ,EAAGV,KAAK,IAAK,CAACW,KAAK,CAACC,UAAU,CAACZ,KAAK,CAAC,CAAC,GAAGY,UAAU,CAACZ,KAAK,CAAC,GAAGA,KAAK;MAE1E;MACAa,MAAM,EAAGb,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAGc,IAAI,CAACC,SAAS,CAACf,KAAK,CAAC,GAAGA,KAAK;MAC5EgB,QAAQ,EAAGhB,KAAK,IAAK;QACnB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;QAC3C,IAAI;UACF,OAAOc,IAAI,CAACG,KAAK,CAACjB,KAAK,CAAC;QAC1B,CAAC,CAAC,OAAOkB,CAAC,EAAE;UACV,OAAOlB,KAAK;QACd;MACF,CAAC;MAED;MACAmB,IAAI,EAAEA,CAACnB,KAAK,EAAEoB,SAAS,GAAG,GAAG,KAAKC,KAAK,CAACC,OAAO,CAACtB,KAAK,CAAC,GAAGA,KAAK,CAACmB,IAAI,CAACC,SAAS,CAAC,GAAGpB,KAAK;MACtFuB,KAAK,EAAEA,CAACvB,KAAK,EAAEoB,SAAS,GAAG,GAAG,KAAK,OAAOpB,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACuB,KAAK,CAACH,SAAS,CAAC,GAAGpB,KAAK;MAE7F;MACAwB,IAAI,EAAEA,CAACxB,KAAK,EAAEyB,OAAO,GAAG,MAAM,KAAK;QACjC,IAAI,OAAOzB,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;QAC3C,OAAOA,KAAK,CAAC0B,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGD,OAAO;MACxC;IACF,CAAC;;IAED;IACA,IAAI,CAACE,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;EACEC,mBAAmBA,CAACC,IAAI,EAAEC,WAAW,EAAE;IACrC,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;MACrC,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;IACnD;IAEA,IAAI,CAAClC,YAAY,CAACgC,IAAI,CAAC,GAAGC,WAAW;EACvC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEE,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACrB,MAAMC,SAAS,GAAG,IAAI,CAACzC,OAAO,CAACC,aAAa,GAAGL,WAAW,CAAC8C,GAAG,CAAC,CAAC,GAAG,CAAC;;IAEpE;IACA,IAAI,IAAI,CAAC1C,OAAO,CAACE,aAAa,EAAE;MAC9B,MAAMyC,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACL,IAAI,EAAEC,KAAK,CAAC;MACpD,MAAMK,YAAY,GAAG,IAAI,CAACb,KAAK,CAACc,GAAG,CAACH,QAAQ,CAAC;MAE7C,IAAIE,YAAY,EAAE;QAChB,OAAOA,YAAY;MACrB;IACF;IAEA,MAAME,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,KAAK,MAAMC,IAAI,IAAIR,KAAK,EAAE;MACxB,IAAI;QACF;QACA,MAAMnC,KAAK,GAAG,IAAI,CAAC4C,eAAe,CAACV,IAAI,EAAES,IAAI,CAACE,MAAM,CAAC;;QAErD;QACA,IAAIC,gBAAgB,GAAG9C,KAAK;QAC5B,IAAI2C,IAAI,CAACV,SAAS,EAAE;UAClB,IAAI,OAAOU,IAAI,CAACV,SAAS,KAAK,QAAQ,IAAI,IAAI,CAACnC,YAAY,CAAC6C,IAAI,CAACV,SAAS,CAAC,EAAE;YAC3E;YACAa,gBAAgB,GAAG,IAAI,CAAChD,YAAY,CAAC6C,IAAI,CAACV,SAAS,CAAC,CAACjC,KAAK,EAAE2C,IAAI,CAACI,eAAe,CAAC;UACnF,CAAC,MAAM,IAAI1B,KAAK,CAACC,OAAO,CAACqB,IAAI,CAACV,SAAS,CAAC,EAAE;YACxC;YACAa,gBAAgB,GAAGH,IAAI,CAACV,SAAS,CAACe,MAAM,CAAC,CAACC,YAAY,EAAEC,aAAa,KAAK;cACxE,IAAI,IAAI,CAACpD,YAAY,CAACoD,aAAa,CAAC,EAAE;gBACpC,OAAO,IAAI,CAACpD,YAAY,CAACoD,aAAa,CAAC,CAACD,YAAY,EAAEN,IAAI,CAACI,eAAe,CAAC;cAC7E;cACA,OAAOE,YAAY;YACrB,CAAC,EAAEjD,KAAK,CAAC;UACX;QACF;;QAEA;QACA,IAAI,CAACmD,eAAe,CAACT,MAAM,EAAEC,IAAI,CAACS,MAAM,EAAEN,gBAAgB,CAAC;MAC7D,CAAC,CAAC,OAAOO,KAAK,EAAE;QACd;QACAC,OAAO,CAACD,KAAK,CAAC,uCAAuCA,KAAK,CAACE,OAAO,EAAE,EAAEZ,IAAI,CAAC;MAC7E;IACF;;IAEA;IACA,IAAI,IAAI,CAAChD,OAAO,CAACE,aAAa,EAAE;MAC9B,MAAMyC,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACL,IAAI,EAAEC,KAAK,CAAC;MACpD,IAAI,CAACR,KAAK,CAAC6B,GAAG,CAAClB,QAAQ,EAAEI,MAAM,CAAC;IAClC;;IAEA;IACA,IAAI,IAAI,CAAC/C,OAAO,CAACC,aAAa,EAAE;MAC9B,MAAM6D,OAAO,GAAGlE,WAAW,CAAC8C,GAAG,CAAC,CAAC;MACjC,MAAMqB,QAAQ,GAAGD,OAAO,GAAGrB,SAAS;;MAEpC;MACA,IAAI,CAACuB,cAAc,CAACD,QAAQ,EAAEvB,KAAK,CAACyB,MAAM,CAAC;IAC7C;IAEA,OAAOlB,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEmB,cAAcA,CAAC3B,IAAI,EAAEC,KAAK,EAAE;IAC1B,MAAMC,SAAS,GAAG,IAAI,CAACzC,OAAO,CAACC,aAAa,GAAGL,WAAW,CAAC8C,GAAG,CAAC,CAAC,GAAG,CAAC;;IAEpE;IACA,MAAMyB,UAAU,GAAG3B,KAAK,CAAC4B,MAAM,CAACpB,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACmB,QAAQ,CAAC,KAAK,CAAC,CAAC;;IAEpE;IACA,IAAIF,UAAU,CAACF,MAAM,KAAK,CAAC,EAAE;MAC3B,OAAO,IAAI,CAAC3B,SAAS,CAACC,IAAI,EAAEC,KAAK,CAAC;IACpC;;IAEA;IACA,MAAM8B,eAAe,GAAGH,UAAU,CAAC,CAAC,CAAC,CAACjB,MAAM,CAACtB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5D,MAAM2C,WAAW,GAAG,IAAI,CAACtB,eAAe,CAACV,IAAI,EAAE+B,eAAe,CAAC;IAE/D,IAAI,CAAC5C,KAAK,CAACC,OAAO,CAAC4C,WAAW,CAAC,EAAE;MAC/B,MAAM,IAAIlC,KAAK,CAAC,QAAQiC,eAAe,6BAA6B,CAAC;IACvE;;IAEA;IACA,MAAMvB,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMyB,KAAK,GAAG,EAAE;;IAEhB;IACA,KAAK,MAAMC,IAAI,IAAIF,WAAW,EAAE;MAC9B;MACA,MAAMG,SAAS,GAAGlC,KAAK,CAACmC,GAAG,CAAC3B,IAAI,IAAI;QAClC,IAAI,CAACA,IAAI,CAACE,MAAM,CAACmB,QAAQ,CAAC,KAAK,CAAC,EAAE;UAChC,OAAOrB,IAAI;QACb;QAEA,OAAO;UACLE,MAAM,EAAEF,IAAI,CAACE,MAAM,CAAC0B,OAAO,CAAC,GAAGN,eAAe,KAAK,EAAE,EAAE,CAAC,CAACM,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;UAC3EnB,MAAM,EAAET,IAAI,CAACS,MAAM,CAACmB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;UACzCtC,SAAS,EAAEU,IAAI,CAACV,SAAS;UACzBc,eAAe,EAAEJ,IAAI,CAACI;QACxB,CAAC;MACH,CAAC,CAAC;;MAEF;MACA,MAAMyB,eAAe,GAAG,IAAI,CAACvC,SAAS,CAAC;QAAEmC;MAAK,CAAC,EAAEC,SAAS,CAAC;MAC3DF,KAAK,CAACM,IAAI,CAACD,eAAe,CAAC;IAC7B;;IAEA;IACA,MAAME,eAAe,GAAGZ,UAAU,CAAC,CAAC,CAAC,CAACV,MAAM,CAAC7B,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC4B,eAAe,CAACT,MAAM,EAAEgC,eAAe,EAAEP,KAAK,CAAC;;IAEpD;IACA,IAAI,IAAI,CAACxE,OAAO,CAACC,aAAa,EAAE;MAC9B,MAAM6D,OAAO,GAAGlE,WAAW,CAAC8C,GAAG,CAAC,CAAC;MACjC,MAAMqB,QAAQ,GAAGD,OAAO,GAAGrB,SAAS;;MAEpC;MACA,IAAI,CAACuB,cAAc,CAACD,QAAQ,EAAEvB,KAAK,CAACyB,MAAM,GAAGM,WAAW,CAACN,MAAM,EAAE,IAAI,CAAC;IACxE;IAEA,OAAOlB,MAAM;EACf;;EAEA;AACF;AACA;AACA;EACEiC,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,OAAO;EACrB;;EAEA;AACF;AACA;EACEC,UAAUA,CAAA,EAAG;IACX,IAAI,CAAClD,KAAK,CAACmD,KAAK,CAAC,CAAC;EACpB;;EAEA;AACF;AACA;AACA;EACEvC,iBAAiBA,CAACL,IAAI,EAAEC,KAAK,EAAE;IAC7B;IACA,OAAOrB,IAAI,CAACC,SAAS,CAAC;MAAEmB,IAAI;MAAEC;IAAM,CAAC,CAAC;EACxC;;EAEA;AACF;AACA;AACA;EACEwB,cAAcA,CAACD,QAAQ,EAAEqB,UAAU,EAAEC,OAAO,GAAG,KAAK,EAAE;IACpD,IAAI,CAAC,IAAI,CAACJ,OAAO,EAAE;MACjB,IAAI,CAACA,OAAO,GAAG;QACbK,eAAe,EAAE,CAAC;QAClBC,aAAa,EAAE,CAAC;QAChBC,eAAe,EAAE,CAAC;QAClBC,oBAAoB,EAAE,CAAC;QACvBC,kBAAkB,EAAE,CAAC;QACrBC,oBAAoB,EAAE,CAAC;QACvBC,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,OAAO,EAAE;MACX,IAAI,CAACJ,OAAO,CAACQ,oBAAoB,EAAE;MACnC,IAAI,CAACR,OAAO,CAACS,kBAAkB,IAAI3B,QAAQ;MAC3C,IAAI,CAACkB,OAAO,CAACU,oBAAoB,GAAG,IAAI,CAACV,OAAO,CAACS,kBAAkB,GAAG,IAAI,CAACT,OAAO,CAACQ,oBAAoB;IACzG,CAAC,MAAM;MACL,IAAI,CAACR,OAAO,CAACK,eAAe,EAAE;MAC9B,IAAI,CAACL,OAAO,CAACM,aAAa,IAAIxB,QAAQ;MACtC,IAAI,CAACkB,OAAO,CAACO,eAAe,GAAG,IAAI,CAACP,OAAO,CAACM,aAAa,GAAG,IAAI,CAACN,OAAO,CAACK,eAAe;IAC1F;IAEA,IAAI,CAACL,OAAO,CAACW,YAAY,IAAIR,UAAU;EACzC;;EAEA;AACF;AACA;AACA;EACEnC,eAAeA,CAAC4C,GAAG,EAAEC,IAAI,EAAE;IACzB,IAAI,CAACA,IAAI,EAAE,OAAOD,GAAG;;IAErB;IACA,MAAME,cAAc,GAAGD,IAAI,CAAClB,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC;IACxD,MAAMoB,KAAK,GAAGD,cAAc,CAACnE,KAAK,CAAC,GAAG,CAAC;IAEvC,IAAIqE,OAAO,GAAGJ,GAAG;IACjB,KAAK,MAAMK,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAIE,IAAI,KAAK,EAAE,EAAE;MACjB,IAAID,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKE,SAAS,EAAE,OAAOA,SAAS;MAC/DF,OAAO,GAAGA,OAAO,CAACC,IAAI,CAAC;IACzB;IAEA,OAAOD,OAAO;EAChB;;EAEA;AACF;AACA;AACA;EACEzC,eAAeA,CAACqC,GAAG,EAAEC,IAAI,EAAEzF,KAAK,EAAE;IAChC,IAAI,CAACyF,IAAI,EAAE;;IAEX;IACA,MAAMC,cAAc,GAAGD,IAAI,CAAClB,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC;IACxD,MAAMoB,KAAK,GAAGD,cAAc,CAACnE,KAAK,CAAC,GAAG,CAAC;IAEvC,IAAIqE,OAAO,GAAGJ,GAAG;IACjB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAAC/B,MAAM,GAAG,CAAC,EAAEmC,CAAC,EAAE,EAAE;MACzC,MAAMF,IAAI,GAAGF,KAAK,CAACI,CAAC,CAAC;MACrB,IAAIF,IAAI,KAAK,EAAE,EAAE;MAEjB,IAAI,EAAEA,IAAI,IAAID,OAAO,CAAC,EAAE;QACtB;QACAA,OAAO,CAACC,IAAI,CAAC,GAAGF,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MACvD;MACAJ,OAAO,GAAGA,OAAO,CAACC,IAAI,CAAC;IACzB;IAEA,MAAMI,QAAQ,GAAGN,KAAK,CAACA,KAAK,CAAC/B,MAAM,GAAG,CAAC,CAAC;IACxC,IAAIqC,QAAQ,KAAK,EAAE,EAAE;MACnBL,OAAO,CAACK,QAAQ,CAAC,GAAGjG,KAAK;IAC3B;EACF;AACF;AAEAkG,MAAM,CAACC,OAAO,GAAG1G,oBAAoB", "ignoreList": []}