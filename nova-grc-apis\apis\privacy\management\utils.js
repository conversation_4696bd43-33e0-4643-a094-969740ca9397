/**
 * Utility functions for the Privacy Management API
 */

/**
 * Advanced filtering function that can filter an array of objects based on multiple criteria
 * @param {Array} items - The array of items to filter
 * @param {Object} filters - An object containing filter criteria
 * @param {Object} options - Additional options for filtering
 * @returns {Array} - The filtered array
 */
const advancedFilter = (items, filters, options = {}) => {
  if (!filters || Object.keys(filters).length === 0) {
    return items;
  }

  return items.filter(item => {
    // Check each filter criterion
    return Object.entries(filters).every(([key, value]) => {
      // Skip undefined or null values
      if (value === undefined || value === null || value === '') {
        return true;
      }

      // Get the path to the property (supports nested properties like 'user.name')
      const path = key.split('.');
      let itemValue = item;

      // Navigate to the nested property
      for (const segment of path) {
        if (itemValue === undefined || itemValue === null) {
          return false;
        }
        itemValue = itemValue[segment];
      }

      // Handle different types of filters
      if (options.exactMatch && options.exactMatch.includes(key)) {
        // Exact match (case sensitive)
        return itemValue === value;
      } else if (options.exactMatchCaseInsensitive && options.exactMatchCaseInsensitive.includes(key)) {
        // Exact match (case insensitive)
        return String(itemValue).toLowerCase() === String(value).toLowerCase();
      } else if (options.arrayContains && options.arrayContains.includes(key)) {
        // Check if array contains value
        return Array.isArray(itemValue) && itemValue.includes(value);
      } else if (options.arrayIntersects && options.arrayIntersects.includes(key)) {
        // Check if arrays have common elements
        return Array.isArray(itemValue) && Array.isArray(value) &&
               value.some(v => itemValue.includes(v));
      } else if (options.dateRange && options.dateRange[key]) {
        // Date range filter
        const { from, to } = options.dateRange[key];
        const date = new Date(itemValue);

        if (from && to) {
          return date >= new Date(from) && date <= new Date(to);
        } else if (from) {
          return date >= new Date(from);
        } else if (to) {
          return date <= new Date(to);
        }

        return true;
      } else if (options.numberRange && options.numberRange[key]) {
        // Number range filter
        const { min, max } = options.numberRange[key];

        if (min !== undefined && max !== undefined) {
          return itemValue >= min && itemValue <= max;
        } else if (min !== undefined) {
          return itemValue >= min;
        } else if (max !== undefined) {
          return itemValue <= max;
        }

        return true;
      } else if (typeof itemValue === 'string' && typeof value === 'string') {
        // Default: partial string match (case insensitive)
        return itemValue.toLowerCase().includes(value.toLowerCase());
      } else {
        // Default: equality check
        return itemValue === value;
      }
    });
  });
};

/**
 * Search function that can search across multiple fields in an array of objects
 * @param {Array} items - The array of items to search
 * @param {string} query - The search query
 * @param {Array} fields - The fields to search in
 * @returns {Array} - The search results
 */
const search = (items, query, fields) => {
  if (!query || query.trim() === '' || !fields || fields.length === 0) {
    return items;
  }

  const normalizedQuery = query.toLowerCase().trim();

  return items.filter(item => {
    return fields.some(field => {
      // Get the path to the property (supports nested properties)
      const path = field.split('.');
      let value = item;

      // Navigate to the nested property
      for (const segment of path) {
        if (value === undefined || value === null) {
          return false;
        }
        value = value[segment];
      }

      // Check if the value contains the query
      if (typeof value === 'string') {
        return value.toLowerCase().includes(normalizedQuery);
      } else if (Array.isArray(value)) {
        return value.some(v =>
          typeof v === 'string' && v.toLowerCase().includes(normalizedQuery)
        );
      } else if (typeof value === 'number' || typeof value === 'boolean') {
        return String(value).toLowerCase().includes(normalizedQuery);
      }

      return false;
    });
  });
};

/**
 * Sort function that can sort an array of objects based on multiple criteria
 * @param {Array} items - The array of items to sort
 * @param {string} sortBy - The field to sort by
 * @param {string} sortOrder - The sort order ('asc' or 'desc')
 * @returns {Array} - The sorted array
 */
const sort = (items, sortBy = 'createdAt', sortOrder = 'desc') => {
  if (!sortBy) {
    return items;
  }

  const sortedItems = [...items];

  sortedItems.sort((a, b) => {
    // Get the path to the property (supports nested properties)
    const path = sortBy.split('.');
    let valueA = a;
    let valueB = b;

    // Navigate to the nested property
    for (const segment of path) {
      if (valueA === undefined || valueA === null) {
        valueA = undefined;
        break;
      }
      valueA = valueA[segment];

      if (valueB === undefined || valueB === null) {
        valueB = undefined;
        break;
      }
      valueB = valueB[segment];
    }

    // Handle undefined values
    if (valueA === undefined && valueB === undefined) {
      return 0;
    } else if (valueA === undefined) {
      return sortOrder.toLowerCase() === 'desc' ? 1 : -1;
    } else if (valueB === undefined) {
      return sortOrder.toLowerCase() === 'desc' ? -1 : 1;
    }

    // Sort based on value type
    if (typeof valueA === 'string' && typeof valueB === 'string') {
      return sortOrder.toLowerCase() === 'desc'
        ? valueB.localeCompare(valueA)
        : valueA.localeCompare(valueB);
    } else {
      return sortOrder.toLowerCase() === 'desc'
        ? (valueB > valueA ? 1 : -1)
        : (valueA > valueB ? 1 : -1);
    }
  });

  return sortedItems;
};

/**
 * Paginate function that can paginate an array of objects
 * @param {Array} items - The array of items to paginate
 * @param {number} page - The page number
 * @param {number} limit - The number of items per page
 * @returns {Object} - The pagination result
 */
const paginate = (items, page = 1, limit = 10) => {
  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);

  // Validate page and limit
  if (pageNum < 1) {
    throw new Error('Page must be greater than or equal to 1');
  }

  if (limitNum < 1) {
    throw new Error('Limit must be greater than or equal to 1');
  }

  // Apply a maximum limit to prevent performance issues
  const maxLimit = 100;
  const effectiveLimit = Math.min(limitNum, maxLimit);

  const startIndex = (pageNum - 1) * effectiveLimit;
  const endIndex = pageNum * effectiveLimit;

  const paginatedItems = items.slice(startIndex, endIndex);

  // Calculate pagination metadata
  const totalItems = items.length;
  const totalPages = Math.ceil(totalItems / effectiveLimit);

  // Calculate next and previous page numbers
  const nextPage = pageNum < totalPages ? pageNum + 1 : null;
  const prevPage = pageNum > 1 ? pageNum - 1 : null;

  return {
    data: paginatedItems,
    pagination: {
      total: totalItems,
      page: pageNum,
      limit: effectiveLimit,
      pages: totalPages,
      next: nextPage,
      prev: prevPage
    }
  };
};

/**
 * Create a cursor-based pagination function
 * @param {Array} items - The array of items to paginate
 * @param {string} cursor - The cursor (usually an ID or timestamp)
 * @param {number} limit - The number of items per page
 * @param {string} cursorField - The field to use as cursor
 * @param {boolean} reverse - Whether to reverse the order
 * @returns {Object} - The pagination result
 */
const cursorPaginate = (items, cursor = null, limit = 10, cursorField = 'id', reverse = false) => {
  const limitNum = parseInt(limit, 10);

  // Validate limit
  if (limitNum < 1) {
    throw new Error('Limit must be greater than or equal to 1');
  }

  // Apply a maximum limit to prevent performance issues
  const maxLimit = 100;
  const effectiveLimit = Math.min(limitNum, maxLimit);

  // Sort items by cursor field
  const sortedItems = [...items].sort((a, b) => {
    const valueA = a[cursorField];
    const valueB = b[cursorField];

    if (reverse) {
      return valueB > valueA ? 1 : -1;
    } else {
      return valueA > valueB ? 1 : -1;
    }
  });

  // Find the starting index based on cursor
  let startIndex = 0;

  if (cursor) {
    const cursorIndex = sortedItems.findIndex(item => item[cursorField] === cursor);

    if (cursorIndex !== -1) {
      startIndex = cursorIndex + 1; // Start after the cursor
    }
  }

  // Get the paginated items
  const paginatedItems = sortedItems.slice(startIndex, startIndex + effectiveLimit);

  // Get the next cursor
  const nextCursor = paginatedItems.length === effectiveLimit && paginatedItems.length > 0
    ? paginatedItems[paginatedItems.length - 1][cursorField]
    : null;

  return {
    data: paginatedItems,
    pagination: {
      limit: effectiveLimit,
      nextCursor,
      hasPrevious: startIndex > 0,
      hasNext: nextCursor !== null
    }
  };
};

module.exports = {
  advancedFilter,
  search,
  sort,
  paginate,
  cursorPaginate
};
