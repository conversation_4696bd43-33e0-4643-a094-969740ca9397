#!/usr/bin/env python3
"""
Optimized CSDE Core Implementation

This module implements the optimized version of the Cyber-Safety Decision Engine (CSDE)
based on the Universal Unified Field Theory (UUFT) equation: (A ⊗ B ⊕ C) × π10³.

Incorporates Orion's recommendations for:
1. Dynamic φ-weighting
2. Domain-specific π10³ scaling
3. Optimized 18/82 resource allocation
4. Hardware acceleration support
"""

import os
import numpy as np
import tensorflow as tf

# Try to import GPU libraries, fallback gracefully if not available
try:
    import cupy as cp
    HAS_GPU = True
except ImportError:
    HAS_GPU = False

class OptimizedCSDECore:
    """
    Optimized implementation of the CSDE core equation: (A ⊗ B ⊕ C) × π10³
    """

    def __init__(self, use_gpu=None):
        """
        Initialize the CSDE core with optional GPU acceleration

        Args:
            use_gpu: Boolean to force GPU usage, or None to auto-detect
        """
        # Determine if we should use GPU
        if use_gpu is None:
            self.use_gpu = HAS_GPU and os.environ.get('UUFT_USE_GPU', 'False').lower() == 'true'
        else:
            self.use_gpu = use_gpu and HAS_GPU

        # Initialize TensorFlow with GPU support if available
        if self.use_gpu:
            physical_devices = tf.config.list_physical_devices('GPU')
            if physical_devices:
                tf.config.experimental.set_memory_growth(physical_devices[0], True)
                print(f"Using GPU: {physical_devices[0]}")
            else:
                print("No GPU found for TensorFlow, using CPU")

        # Initialize domain-specific scaling factors
        self.scaling_factors = {
            "cybersecurity": 3141.59,  # π × 1000
            "financial": 3141.59,      # π × 1000
            "healthcare": 2718.28,     # e × 1000
            "physics": 2997.92,        # c/100 (speed of light)
            "default": 3141.59         # Default to π × 1000
        }

    def tensor_product(self, A, B):
        """
        Implements the tensor product operator (⊗) with hardware acceleration

        Args:
            A: First tensor (compliance data)
            B: Second tensor (infrastructure data)

        Returns:
            Tensor product A ⊗ B
        """
        if self.use_gpu and HAS_GPU:
            # Use CuPy for GPU acceleration
            A_gpu = cp.asarray(A)
            B_gpu = cp.asarray(B)
            result = cp.tensordot(A_gpu, B_gpu, axes=0)
            return cp.asnumpy(result)
        else:
            # Use TensorFlow
            A_tf = tf.convert_to_tensor(A, dtype=tf.float32)
            B_tf = tf.convert_to_tensor(B, dtype=tf.float32)
            return tf.tensordot(A_tf, B_tf, axes=0)

    def calculate_similarity(self, A, B):
        """
        Calculate cosine similarity between flattened tensors

        Args:
            A: First tensor
            B: Second tensor

        Returns:
            Similarity score between -1 and 1
        """
        # Convert to TensorFlow tensors
        A_tf = tf.convert_to_tensor(A, dtype=tf.float32)
        B_tf = tf.convert_to_tensor(B, dtype=tf.float32)

        # Flatten tensors
        A_flat = tf.reshape(A_tf, [-1])
        B_flat = tf.reshape(B_tf, [-1])

        # Ensure compatible shapes by padding or truncating
        min_dim = min(A_flat.shape[0], B_flat.shape[0])
        A_flat = A_flat[:min_dim]
        B_flat = B_flat[:min_dim]

        # Calculate cosine similarity
        norm_A = tf.norm(A_flat)
        norm_B = tf.norm(B_flat)

        if norm_A == 0 or norm_B == 0:
            return 0.0

        dot_product = tf.reduce_sum(A_flat * B_flat)
        similarity = dot_product / (norm_A * norm_B)

        return similarity.numpy()

    def fusion_operator(self, tensor_result, threat_intelligence, domain="cybersecurity"):
        """
        Implements the fusion operator (⊕) with domain-aware encoding and contrastive loss

        Args:
            tensor_result: Result of tensor product A ⊗ B
            threat_intelligence: Threat intelligence tensor C
            domain: Domain for context-specific fusion

        Returns:
            Fusion result (A ⊗ B) ⊕ C
        """
        # Convert to TensorFlow tensors
        tensor_result_tf = tf.convert_to_tensor(tensor_result, dtype=tf.float32)
        threat_intelligence_tf = tf.convert_to_tensor(threat_intelligence, dtype=tf.float32)

        # Step 1: Apply domain-aware encoding
        # Different domains may require different fusion strategies
        domain_encoders = {
            "cybersecurity": 0.618,  # Golden ratio for cybersecurity
            "financial": 0.667,      # 2/3 for financial (emphasizes patterns)
            "healthcare": 0.6,       # 3/5 for healthcare (balanced approach)
            "physics": 0.577,        # 1/√3 for physics (geometric mean)
            "default": 0.618         # Default to golden ratio
        }

        # Get domain-specific phi value
        base_phi = domain_encoders.get(domain, domain_encoders["default"])

        # Step 2: Calculate similarity for contrastive adjustment
        similarity_score = self.calculate_similarity(tensor_result, threat_intelligence)

        # Step 3: Apply contrastive loss-inspired adjustment
        # When similarity is very low, we want to bring the representations closer
        # When similarity is very high, we can maintain the golden ratio

        # Calculate contrastive adjustment factor
        # This implements a simplified version of contrastive loss principles
        if similarity_score < 0.3:
            # Very dissimilar - adjust phi to bring representations closer
            contrastive_factor = 0.5 + (similarity_score / 0.6)  # Range: 0.5-1.0
            phi = base_phi * contrastive_factor
        elif similarity_score > 0.7:
            # Very similar - use base phi
            phi = base_phi
        else:
            # Moderate similarity - linear interpolation
            normalized_sim = (similarity_score - 0.3) / 0.4  # Range: 0-1
            phi = 0.5 + (base_phi - 0.5) * normalized_sim

        # Step 4: Apply skip connection to preserve original patterns
        # This addresses Carl's suggestion for pattern preservation
        # Skip connection weight is inversely proportional to similarity
        skip_weight = 0.2 * (1.0 - similarity_score)

        # Apply fusion with dynamic weighting and skip connection
        fusion_result = tensor_result_tf * phi + threat_intelligence_tf * (1 - phi)

        # Add skip connection from original tensor result
        fusion_result = fusion_result * (1 - skip_weight) + tensor_result_tf * skip_weight

        # Step 5: Apply layer normalization (Carl's suggestion for accuracy)
        # Calculate mean and variance for normalization
        mean, variance = tf.nn.moments(fusion_result, axes=list(range(fusion_result.ndim)))
        epsilon = 1e-6  # Small constant for numerical stability

        # Apply normalization
        normalized_result = (fusion_result - mean) / tf.sqrt(variance + epsilon)

        # Scale and shift (learnable parameters in real implementation)
        scale = 1.0
        shift = 0.0
        normalized_result = normalized_result * scale + shift

        return normalized_result

    def pi_scaling(self, fusion_result, domain="cybersecurity"):
        """
        Implements parameterized domain-specific π10³ scaling with adaptive adjustment

        Args:
            fusion_result: Result of fusion operation (A ⊗ B) ⊕ C
            domain: Domain for scaling factor selection

        Returns:
            Scaled result (A ⊗ B ⊕ C) × π10³
        """
        # Step 1: Get base scaling factor for the domain
        base_pi_factor = self.scaling_factors.get(domain, self.scaling_factors["default"])

        # Step 2: Analyze fusion result characteristics to determine optimal scaling
        # Calculate statistics of the fusion result
        if isinstance(fusion_result, tf.Tensor):
            # For TensorFlow tensors
            mean = tf.reduce_mean(fusion_result)
            std_dev = tf.math.reduce_std(fusion_result)
            max_val = tf.reduce_max(fusion_result)
            min_val = tf.reduce_min(fusion_result)

            # Convert to numpy for calculations
            mean = mean.numpy()
            std_dev = std_dev.numpy()
            max_val = max_val.numpy()
            min_val = min_val.numpy()
        else:
            # For NumPy arrays
            mean = np.mean(fusion_result)
            std_dev = np.std(fusion_result)
            max_val = np.max(fusion_result)
            min_val = np.min(fusion_result)

        # Step 3: Adjust scaling factor based on data characteristics
        # This implements Carl's suggestion to parameterize π scaling

        # Calculate dynamic range of the data
        data_range = max_val - min_val

        # Calculate coefficient of variation (normalized measure of dispersion)
        cv = std_dev / (mean + 1e-10)  # Add small epsilon to prevent division by zero

        # Determine scaling adjustment factor
        # Higher CV (more variable data) gets lower scaling to prevent explosion
        # Lower CV (more uniform data) gets higher scaling to amplify patterns
        if cv > 1.0:
            # High variability - reduce scaling
            adjustment_factor = 0.5
        elif cv < 0.1:
            # Low variability - increase scaling
            adjustment_factor = 1.5
        else:
            # Moderate variability - linear interpolation
            adjustment_factor = 1.5 - cv

        # Apply adjustment with bounds
        adjusted_pi_factor = base_pi_factor * adjustment_factor

        # Ensure scaling stays within reasonable bounds
        # Don't let it go below 1000 or above 5000
        adjusted_pi_factor = max(1000, min(5000, adjusted_pi_factor))

        # Step 4: Apply optimized scaling
        return fusion_result * adjusted_pi_factor

    def allocate_resources(self, tasks, total_resources):
        """
        Implements entropy-based 18/82 resource allocation with weighted balance penalty

        Args:
            tasks: List of task objects with is_critical attribute
            total_resources: Total available resources

        Returns:
            Tasks with resources allocated
        """
        import math

        critical_tasks = [t for t in tasks if t.is_critical]
        standard_tasks = [t for t in tasks if not t.is_critical]

        # Handle edge cases
        if not critical_tasks:
            # No critical tasks, distribute evenly
            for task in tasks:
                task.resources = total_resources / len(tasks)
            return tasks

        if not standard_tasks:
            # No standard tasks, distribute evenly among critical
            for task in critical_tasks:
                task.resources = total_resources / len(critical_tasks)
            return critical_tasks

        # Calculate initial allocation based on 18/82 principle
        critical_count = len(critical_tasks)
        standard_count = len(standard_tasks)
        total_count = critical_count + standard_count

        # Calculate entropy-based allocation
        # This approach balances the 18/82 principle with task distribution entropy

        # Step 1: Calculate base allocation using 18/82 principle
        critical_percentage = 0.18 * total_count / critical_count
        critical_percentage = min(0.7, critical_percentage)  # Cap at 70%

        # Step 2: Apply entropy weighting
        # Higher entropy = more balanced allocation
        p_critical = critical_count / total_count
        p_standard = standard_count / total_count

        # Calculate Shannon entropy of task distribution
        entropy = -p_critical * math.log2(p_critical) - p_standard * math.log2(p_standard)
        # Normalize entropy (max entropy for binary distribution is 1.0)
        normalized_entropy = entropy / 1.0

        # Apply entropy-based adjustment
        # Higher entropy (more balanced distribution) reduces the skew in resource allocation
        entropy_factor = 1.0 - 0.3 * normalized_entropy

        # Adjust critical percentage based on entropy
        adjusted_critical_percentage = critical_percentage * entropy_factor

        # Step 3: Apply weighted balance penalty
        # This prevents starving standard tasks while prioritizing critical ones

        # Calculate balance penalty based on resource ratio
        critical_per_task = (total_resources * adjusted_critical_percentage) / critical_count
        standard_per_task = (total_resources * (1 - adjusted_critical_percentage)) / standard_count

        ratio = critical_per_task / standard_per_task

        # Apply penalty if ratio is too high (critical tasks get too much)
        if ratio > 10.0:
            # Reduce critical allocation to maintain reasonable balance
            balance_factor = 10.0 / ratio
            adjusted_critical_percentage *= balance_factor

        # Calculate final resource allocation
        critical_resources = total_resources * adjusted_critical_percentage
        standard_resources = total_resources - critical_resources

        # Distribute resources
        for task in tasks:
            if task.is_critical:
                task.resources = critical_resources / critical_count
            else:
                task.resources = standard_resources / standard_count

        return tasks

    def process(self, compliance_data, infrastructure_data, threat_intelligence, domain="cybersecurity", optimize_speed=True):
        """
        Implements the full CSDE equation: (A ⊗ B ⊕ C) × π10³ with optimized operations

        Args:
            compliance_data: Compliance data tensor A
            infrastructure_data: Infrastructure data tensor B
            threat_intelligence: Threat intelligence tensor C
            domain: Domain for scaling factor selection
            optimize_speed: Whether to use optimized operations for speed

        Returns:
            Final CSDE result
        """
        # Profile and optimize operations based on Carl's suggestions

        if optimize_speed:
            # Step 1: Optimize tensor operations by fusing in lower precision
            # Convert inputs to lower precision for faster computation
            if self.use_gpu and HAS_GPU:
                # Use GPU with lower precision
                try:
                    # Convert to float16 for faster GPU computation
                    A_gpu = cp.asarray(compliance_data, dtype=cp.float16)
                    B_gpu = cp.asarray(infrastructure_data, dtype=cp.float16)

                    # Fused tensor product and initial scaling
                    # This combines operations to reduce memory transfers
                    result = cp.tensordot(A_gpu, B_gpu, axes=0)

                    # Convert back to float32 for fusion operation
                    tensor_result = cp.asnumpy(result).astype(np.float32)
                except:
                    # Fallback to standard tensor product if optimization fails
                    tensor_result = self.tensor_product(compliance_data, infrastructure_data)
            else:
                # Use TensorFlow with mixed precision
                try:
                    # Enable mixed precision
                    tf.keras.mixed_precision.set_global_policy('mixed_float16')

                    # Convert to TensorFlow tensors
                    A_tf = tf.convert_to_tensor(compliance_data, dtype=tf.float16)
                    B_tf = tf.convert_to_tensor(infrastructure_data, dtype=tf.float16)

                    # Fused tensor product
                    result = tf.tensordot(A_tf, B_tf, axes=0)

                    # Convert back to float32 for fusion operation
                    tensor_result = tf.cast(result, tf.float32)

                    # Reset precision policy
                    tf.keras.mixed_precision.set_global_policy('float32')
                except:
                    # Fallback to standard tensor product if optimization fails
                    tensor_result = self.tensor_product(compliance_data, infrastructure_data)
        else:
            # Standard tensor product without optimization
            tensor_result = self.tensor_product(compliance_data, infrastructure_data)

        # Step 2: Apply fusion operator with domain-aware encoding and contrastive loss
        fusion_result = self.fusion_operator(tensor_result, threat_intelligence, domain)

        # Step 3: Apply parameterized domain-specific π10³ scaling
        final_result = self.pi_scaling(fusion_result, domain)

        # Step 4: Apply re-injection layer for pattern preservation (Carl's suggestion)
        # This adds a residual connection from the original tensor product
        # The weight is small to maintain the core CSDE equation while enhancing pattern preservation
        if optimize_speed:
            # Simplified re-injection for speed
            pattern_preservation_weight = 0.05
            final_result = final_result * (1 - pattern_preservation_weight) + tensor_result * pattern_preservation_weight
        else:
            # More sophisticated re-injection with pattern-specific weighting
            # Calculate pattern importance
            pattern_importance = tf.reduce_max(tensor_result, axis=-1, keepdims=True)
            pattern_importance = tf.sigmoid(pattern_importance)  # Normalize to 0-1 range

            # Apply pattern-specific re-injection
            pattern_preservation_weight = 0.05 * pattern_importance
            final_result = final_result * (1 - pattern_preservation_weight) + tensor_result * pattern_preservation_weight

        return final_result

# Simple Task class for resource allocation testing
class Task:
    def __init__(self, id, is_critical=False):
        self.id = id
        self.is_critical = is_critical
        self.resources = 0
        self.completion = 0

    def calculate_completion(self):
        """
        Calculate completion percentage based on resources
        Uses sigmoid function to model diminishing returns
        """
        if self.is_critical:
            # Critical tasks need more resources to be effective
            # But have higher maximum completion
            self.completion = 100 * (1 / (1 + np.exp(-0.2 * (self.resources - 20))))
        else:
            # Standard tasks benefit more from small resource amounts
            self.completion = 100 * (1 / (1 + np.exp(-0.3 * (self.resources - 5))))

        return self.completion
