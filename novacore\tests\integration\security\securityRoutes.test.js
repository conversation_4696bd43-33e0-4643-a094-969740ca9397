/**
 * Security Routes Integration Tests
 */

const request = require('supertest');
const { expect } = require('chai');
const sinon = require('sinon');
const app = require('../../../api/app');
const blockchainEvidence = require('../../../api/services/blockchainEvidence');
const jwt = require('jsonwebtoken');
const config = require('../../../config');

describe('Security Routes', () => {
  let authToken;
  
  before(() => {
    // Create a test auth token
    authToken = jwt.sign(
      {
        id: 'user123',
        role: 'admin',
        organizationId: 'org123'
      },
      config.jwt.secretKey,
      { expiresIn: '1h' }
    );
  });
  
  describe('POST /api/v1/security/evidence', () => {
    let createEvidenceRecordStub;
    
    beforeEach(() => {
      // Stub the createEvidenceRecord method
      createEvidenceRecordStub = sinon.stub(blockchainEvidence, 'createEvidenceRecord').resolves({
        id: 'evidence123',
        type: 'test',
        content: 'Test evidence',
        metadata: {
          createdBy: 'user123',
          organizationId: 'org123',
          key: 'value'
        },
        createdAt: new Date().toISOString(),
        blockchain: {
          merkleRoot: 'root'.repeat(16),
          proof: ['proof'.repeat(16)],
          submissionId: '12345',
          blockNumber: 12345678,
          blockHash: '0x' + 'block'.repeat(16),
          timestamp: Math.floor(Date.now() / 1000),
          transactionHash: '0x' + 'tx'.repeat(16)
        }
      });
    });
    
    afterEach(() => {
      // Restore the stub
      createEvidenceRecordStub.restore();
    });
    
    it('should create an evidence record', async () => {
      const response = await request(app)
        .post('/api/v1/security/evidence')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'test',
          content: 'Test evidence',
          metadata: {
            key: 'value'
          }
        });
      
      expect(response.status).to.equal(201);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('id', 'evidence123');
      expect(response.body).to.have.property('type', 'test');
      expect(response.body).to.have.property('content', 'Test evidence');
      expect(response.body).to.have.property('metadata');
      expect(response.body.metadata).to.have.property('key', 'value');
      expect(response.body).to.have.property('createdAt');
      expect(response.body).to.have.property('blockchain');
      
      // Verify that the stub was called
      expect(createEvidenceRecordStub.calledOnce).to.be.true;
    });
    
    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/v1/security/evidence')
        .send({
          type: 'test',
          content: 'Test evidence',
          metadata: {
            key: 'value'
          }
        });
      
      expect(response.status).to.equal(401);
    });
    
    it('should validate request body', async () => {
      const response = await request(app)
        .post('/api/v1/security/evidence')
        .set('Authorization', `Bearer ${authToken}`)
        .send({});
      
      expect(response.status).to.equal(400);
    });
  });
  
  describe('POST /api/v1/security/evidence/verify', () => {
    let verifyEvidenceRecordStub;
    
    beforeEach(() => {
      // Stub the verifyEvidenceRecord method
      verifyEvidenceRecordStub = sinon.stub(blockchainEvidence, 'verifyEvidenceRecord').resolves({
        verified: true,
        evidenceHash: 'hash'.repeat(16),
        merkleRoot: 'root'.repeat(16),
        submissionId: '12345',
        timestamp: Math.floor(Date.now() / 1000)
      });
    });
    
    afterEach(() => {
      // Restore the stub
      verifyEvidenceRecordStub.restore();
    });
    
    it('should verify an evidence record', async () => {
      const response = await request(app)
        .post('/api/v1/security/evidence/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          evidenceRecord: {
            id: 'evidence123',
            type: 'test',
            content: 'Test evidence',
            metadata: {
              createdBy: 'user123',
              organizationId: 'org123',
              key: 'value'
            },
            createdAt: new Date().toISOString(),
            blockchain: {
              merkleRoot: 'root'.repeat(16),
              proof: ['proof'.repeat(16)],
              submissionId: '12345',
              blockNumber: 12345678,
              blockHash: '0x' + 'block'.repeat(16),
              timestamp: Math.floor(Date.now() / 1000),
              transactionHash: '0x' + 'tx'.repeat(16)
            }
          }
        });
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('verified', true);
      expect(response.body).to.have.property('evidenceHash');
      expect(response.body).to.have.property('merkleRoot');
      expect(response.body).to.have.property('submissionId');
      expect(response.body).to.have.property('timestamp');
      
      // Verify that the stub was called
      expect(verifyEvidenceRecordStub.calledOnce).to.be.true;
    });
    
    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/v1/security/evidence/verify')
        .send({
          evidenceRecord: {
            id: 'evidence123',
            type: 'test',
            content: 'Test evidence',
            metadata: {
              createdBy: 'user123',
              organizationId: 'org123',
              key: 'value'
            },
            createdAt: new Date().toISOString(),
            blockchain: {
              merkleRoot: 'root'.repeat(16),
              proof: ['proof'.repeat(16)],
              submissionId: '12345',
              blockNumber: 12345678,
              blockHash: '0x' + 'block'.repeat(16),
              timestamp: Math.floor(Date.now() / 1000),
              transactionHash: '0x' + 'tx'.repeat(16)
            }
          }
        });
      
      expect(response.status).to.equal(401);
    });
    
    it('should validate request body', async () => {
      const response = await request(app)
        .post('/api/v1/security/evidence/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .send({});
      
      expect(response.status).to.equal(400);
    });
  });
  
  describe('GET /api/v1/security/evidence/:id', () => {
    it('should get an evidence record', async () => {
      const response = await request(app)
        .get('/api/v1/security/evidence/evidence123')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('id', 'evidence123');
      expect(response.body).to.have.property('type');
      expect(response.body).to.have.property('content');
      expect(response.body).to.have.property('metadata');
      expect(response.body).to.have.property('createdAt');
      expect(response.body).to.have.property('blockchain');
    });
    
    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/v1/security/evidence/evidence123');
      
      expect(response.status).to.equal(401);
    });
  });
  
  describe('GET /api/v1/security/evidence', () => {
    it('should list evidence records', async () => {
      const response = await request(app)
        .get('/api/v1/security/evidence')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('records');
      expect(response.body.records).to.be.an('array');
      expect(response.body).to.have.property('pagination');
      expect(response.body.pagination).to.have.property('page');
      expect(response.body.pagination).to.have.property('limit');
      expect(response.body.pagination).to.have.property('total');
      expect(response.body.pagination).to.have.property('pages');
    });
    
    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/v1/security/evidence?page=2&limit=5')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('records');
      expect(response.body.records).to.be.an('array');
      expect(response.body.pagination).to.have.property('page', 2);
      expect(response.body.pagination).to.have.property('limit', 5);
    });
    
    it('should support filtering by type', async () => {
      const response = await request(app)
        .get('/api/v1/security/evidence?type=policy')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('records');
      expect(response.body.records).to.be.an('array');
      
      // All records should have the specified type
      for (const record of response.body.records) {
        expect(record.type).to.equal('policy');
      }
    });
    
    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/v1/security/evidence');
      
      expect(response.status).to.equal(401);
    });
  });
  
  describe('GET /api/v1/security/status', () => {
    it('should get security status', async () => {
      const response = await request(app)
        .get('/api/v1/security/status')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('overall');
      expect(response.body).to.have.property('lastUpdated');
      expect(response.body).to.have.property('components');
      expect(response.body.components).to.be.an('object');
      expect(response.body).to.have.property('alerts');
      expect(response.body.alerts).to.be.an('array');
    });
    
    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/v1/security/status');
      
      expect(response.status).to.equal(401);
    });
  });
});
