import React from 'react';
import { 
  <PERSON>, 
  Typography, 
  Paper,
  Grid,
  Divider,
  <PERSON>,
  Button
} from '@mui/material';
import { Editor } from '@monaco-editor/react';
import { 
  Check as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

export default function ReviewForm({ connector }) {
  const validateConnector = () => {
    const issues = [];
    
    // Check metadata
    if (!connector.metadata.name) {
      issues.push({ type: 'error', message: 'Connector name is required' });
    }
    
    if (!connector.metadata.category) {
      issues.push({ type: 'error', message: 'Connector category is required' });
    }
    
    if (!connector.metadata.description || connector.metadata.description.length < 20) {
      issues.push({ type: 'warning', message: 'Connector description should be detailed (at least 20 characters)' });
    }
    
    // Check authentication
    if (!connector.authentication.type) {
      issues.push({ type: 'error', message: 'Authentication type is required' });
    }
    
    if (Object.keys(connector.authentication.fields).length === 0) {
      issues.push({ type: 'error', message: 'At least one authentication field is required' });
    }
    
    if (!connector.authentication.testConnection.endpoint) {
      issues.push({ type: 'warning', message: 'Test connection endpoint is recommended' });
    }
    
    // Check configuration
    if (!connector.configuration.baseUrl) {
      issues.push({ type: 'error', message: 'Base URL is required' });
    }
    
    // Check endpoints
    if (connector.endpoints.length === 0) {
      issues.push({ type: 'error', message: 'At least one endpoint is required' });
    } else {
      connector.endpoints.forEach((endpoint, index) => {
        if (!endpoint.path) {
          issues.push({ type: 'error', message: `Endpoint #${index + 1} (${endpoint.name}) is missing a path` });
        }
      });
    }
    
    // Check mappings
    if (connector.mappings.length === 0) {
      issues.push({ type: 'warning', message: 'No mappings defined. Mappings help transform API responses to NovaGRC format' });
    } else {
      connector.mappings.forEach((mapping, index) => {
        if (mapping.transformations.length === 0) {
          issues.push({ type: 'warning', message: `Mapping #${index + 1} has no transformations defined` });
        }
      });
    }
    
    return issues;
  };

  const issues = validateConnector();
  const errors = issues.filter(issue => issue.type === 'error');
  const warnings = issues.filter(issue => issue.type === 'warning');
  
  const isValid = errors.length === 0;

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3, mb: 3, backgroundColor: 'background.paper' }}>
            <Typography variant="h6" gutterBottom>
              Validation Results
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              {isValid ? (
                <CheckIcon sx={{ color: 'success.main', mr: 1 }} />
              ) : (
                <ErrorIcon sx={{ color: 'error.main', mr: 1 }} />
              )}
              <Typography variant="subtitle1">
                {isValid ? 'Connector is valid' : 'Connector has validation errors'}
              </Typography>
            </Box>
            
            {issues.length > 0 ? (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Issues Found:
                </Typography>
                <Paper variant="outlined" sx={{ p: 2, backgroundColor: 'background.default' }}>
                  {issues.map((issue, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      {issue.type === 'error' ? (
                        <ErrorIcon sx={{ color: 'error.main', mr: 1, fontSize: 20 }} />
                      ) : (
                        <WarningIcon sx={{ color: 'warning.main', mr: 1, fontSize: 20 }} />
                      )}
                      <Typography variant="body2">
                        {issue.message}
                      </Typography>
                    </Box>
                  ))}
                </Paper>
              </Box>
            ) : (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  No issues found. Your connector is ready to use!
                </Typography>
              </Box>
            )}
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Box>
                <Chip 
                  label={`${errors.length} Errors`} 
                  color={errors.length > 0 ? "error" : "default"} 
                  size="small" 
                  sx={{ mr: 1 }}
                />
                <Chip 
                  label={`${warnings.length} Warnings`} 
                  color={warnings.length > 0 ? "warning" : "default"} 
                  size="small" 
                />
              </Box>
              <Button
                variant="contained"
                color="primary"
                disabled={!isValid}
              >
                Publish Connector
              </Button>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
            <Typography variant="h6" gutterBottom>
              Connector JSON Preview
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              This is the JSON representation of your connector that will be saved to the registry.
            </Typography>
            
            <Box sx={{ height: '500px', border: 1, borderColor: 'divider', borderRadius: 1 }}>
              <Editor
                height="100%"
                defaultLanguage="json"
                value={JSON.stringify(connector, null, 2)}
                theme="vs-dark"
                options={{
                  readOnly: true,
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  wordWrap: 'on'
                }}
              />
            </Box>
            
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={() => {
                  navigator.clipboard.writeText(JSON.stringify(connector, null, 2));
                }}
              >
                Copy JSON
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
