/**
 * NovaFuse Universal API Connector - Connector Repository Tests
 */

const { connectorRepository } = require('../../../src/data/repositories');
const Connector = require('../../../src/data/models/connector');

// Mock the Connector model
jest.mock('../../../src/data/models/connector', () => {
  return {
    findOne: jest.fn(),
    find: jest.fn().mockReturnThis(),
    countDocuments: jest.fn(),
    findOneAndUpdate: jest.fn(),
    deleteOne: jest.fn(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis()
  };
});

describe('Connector Repository', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  const mockConnector = {
    id: 'test-connector-1.0',
    metadata: {
      name: 'Test Connector',
      version: '1.0',
      category: 'security',
      description: 'Test connector for unit tests'
    },
    authentication: {
      type: 'API_KEY',
      fields: {
        apiKey: {
          type: 'string',
          description: 'API Key',
          required: true,
          sensitive: true
        }
      }
    },
    configuration: {
      baseUrl: 'https://api.example.com',
      headers: {
        'Content-Type': 'application/json'
      }
    },
    endpoints: [
      {
        id: 'get-data',
        name: 'Get Data',
        description: 'Get data from the API',
        path: '/data',
        method: 'GET'
      }
    ]
  };
  
  it('should get a connector by ID', async () => {
    Connector.findOne.mockResolvedValue(mockConnector);
    
    const connector = await connectorRepository.getConnectorById('test-connector-1.0');
    
    expect(Connector.findOne).toHaveBeenCalledWith({ id: 'test-connector-1.0', isActive: true });
    expect(connector).toEqual(mockConnector);
  });
  
  it('should get connectors by category', async () => {
    Connector.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockConnector])
    }));
    
    const connectors = await connectorRepository.getConnectorsByCategory('security');
    
    expect(Connector.find).toHaveBeenCalledWith({ 'metadata.category': 'security', isActive: true, isPublic: true });
    expect(connectors).toEqual([mockConnector]);
  });
  
  it('should search connectors', async () => {
    Connector.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockConnector])
    }));
    
    const connectors = await connectorRepository.searchConnectors('test');
    
    expect(Connector.find).toHaveBeenCalled();
    expect(connectors).toEqual([mockConnector]);
  });
  
  it('should get all connectors', async () => {
    Connector.find.mockImplementation(() => ({
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockResolvedValue([mockConnector])
    }));
    
    const connectors = await connectorRepository.getAllConnectors();
    
    expect(Connector.find).toHaveBeenCalledWith({ isActive: true, isPublic: true });
    expect(connectors).toEqual([mockConnector]);
  });
  
  it('should create a connector', async () => {
    const mockConnectorModel = {
      save: jest.fn().mockResolvedValue(mockConnector)
    };
    
    jest.spyOn(global, 'Object').mockImplementationOnce(() => mockConnectorModel);
    
    const connector = await connectorRepository.createConnector(mockConnector);
    
    expect(mockConnectorModel.save).toHaveBeenCalled();
    expect(connector).toEqual(mockConnector);
    
    global.Object.mockRestore();
  });
  
  it('should update a connector', async () => {
    Connector.findOneAndUpdate.mockResolvedValue(mockConnector);
    
    const connector = await connectorRepository.updateConnector('test-connector-1.0', mockConnector);
    
    expect(Connector.findOneAndUpdate).toHaveBeenCalled();
    expect(connector).toEqual(mockConnector);
  });
  
  it('should delete a connector', async () => {
    Connector.findOneAndUpdate.mockResolvedValue(mockConnector);
    
    const result = await connectorRepository.deleteConnector('test-connector-1.0');
    
    expect(Connector.findOneAndUpdate).toHaveBeenCalled();
    expect(result).toBe(true);
  });
  
  it('should hard delete a connector', async () => {
    Connector.deleteOne.mockResolvedValue({ deletedCount: 1 });
    
    const result = await connectorRepository.hardDeleteConnector('test-connector-1.0');
    
    expect(Connector.deleteOne).toHaveBeenCalledWith({ id: 'test-connector-1.0' });
    expect(result).toBe(true);
  });
  
  it('should count connectors', async () => {
    Connector.countDocuments.mockResolvedValue(5);
    
    const count = await connectorRepository.countConnectors();
    
    expect(Connector.countDocuments).toHaveBeenCalledWith({ isActive: true });
    expect(count).toBe(5);
  });
  
  it('should check if a connector exists', async () => {
    Connector.countDocuments.mockResolvedValue(1);
    
    const exists = await connectorRepository.connectorExists('test-connector-1.0');
    
    expect(Connector.countDocuments).toHaveBeenCalledWith({ id: 'test-connector-1.0', isActive: true });
    expect(exists).toBe(true);
  });
});
