/**
 * GraphQL Subscription Manager Component
 * 
 * This component provides a UI for managing GraphQL subscriptions.
 */

import React, { useState, useEffect, useRef } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  Divider, 
  Grid, 
  IconButton, 
  List, 
  ListItem, 
  ListItemText, 
  Paper, 
  TextField, 
  Typography 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import RefreshIcon from '@mui/icons-material/Refresh';
import DeleteIcon from '@mui/icons-material/Delete';
import { graphqlApi } from '../../services/api';

// Monaco Editor for code editing
import Editor from '@monaco-editor/react';

const GraphQLSubscriptionManager = ({ endpoint, headers, auth }) => {
  const [query, setQuery] = useState('subscription {\n  # Your subscription query here\n}');
  const [variables, setVariables] = useState('{}');
  const [activeSubscriptions, setActiveSubscriptions] = useState([]);
  const [selectedSubscription, setSelectedSubscription] = useState(null);
  const [messages, setMessages] = useState([]);
  const [errors, setErrors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [clientId, setClientId] = useState(`client-${Date.now()}`);
  
  const pollingInterval = useRef(null);
  
  // Load active subscriptions on component mount
  useEffect(() => {
    loadActiveSubscriptions();
    
    return () => {
      // Clean up polling interval
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
      }
    };
  }, []);
  
  // Poll for messages when a subscription is selected
  useEffect(() => {
    if (selectedSubscription && clientId) {
      // Clear existing interval
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
      }
      
      // Start polling for messages
      pollingInterval.current = setInterval(() => {
        pollSubscriptionMessages(selectedSubscription.id);
        pollSubscriptionErrors(selectedSubscription.id);
      }, 1000);
      
      // Initial poll
      pollSubscriptionMessages(selectedSubscription.id);
      pollSubscriptionErrors(selectedSubscription.id);
    }
    
    return () => {
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
      }
    };
  }, [selectedSubscription, clientId]);
  
  // Load active subscriptions
  const loadActiveSubscriptions = async () => {
    try {
      const response = await graphqlApi.getActiveSubscriptions();
      setActiveSubscriptions(response.data);
    } catch (error) {
      console.error('Error loading subscriptions:', error);
    }
  };
  
  // Poll for subscription messages
  const pollSubscriptionMessages = async (subscriptionId) => {
    try {
      const response = await graphqlApi.getSubscriptionMessages(subscriptionId, clientId);
      
      if (response.data && response.data.length > 0) {
        setMessages(prevMessages => [...prevMessages, ...response.data]);
      }
    } catch (error) {
      console.error('Error polling messages:', error);
    }
  };
  
  // Poll for subscription errors
  const pollSubscriptionErrors = async (subscriptionId) => {
    try {
      const response = await graphqlApi.getSubscriptionErrors(subscriptionId, clientId);
      
      if (response.data && response.data.length > 0) {
        setErrors(prevErrors => [...prevErrors, ...response.data]);
      }
    } catch (error) {
      console.error('Error polling errors:', error);
    }
  };
  
  // Create a new subscription
  const createSubscription = async () => {
    if (!endpoint) {
      alert('GraphQL endpoint URL is required');
      return;
    }
    
    if (!query.includes('subscription')) {
      alert('Query must contain a subscription operation');
      return;
    }
    
    setLoading(true);
    
    try {
      // Parse variables
      let parsedVariables = {};
      try {
        parsedVariables = variables ? JSON.parse(variables) : {};
      } catch (error) {
        alert('Invalid JSON in variables');
        setLoading(false);
        return;
      }
      
      // Create subscription
      const response = await graphqlApi.createSubscription(
        endpoint,
        query,
        parsedVariables,
        headers,
        auth
      );
      
      // Reload subscriptions
      await loadActiveSubscriptions();
      
      // Select the new subscription
      setSelectedSubscription(response.data);
      
      // Clear messages and errors
      setMessages([]);
      setErrors([]);
    } catch (error) {
      console.error('Error creating subscription:', error);
      alert(`Error creating subscription: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Cancel a subscription
  const cancelSubscription = async (subscriptionId) => {
    try {
      await graphqlApi.cancelSubscription(subscriptionId);
      
      // Reload subscriptions
      await loadActiveSubscriptions();
      
      // Clear selection if the cancelled subscription was selected
      if (selectedSubscription && selectedSubscription.id === subscriptionId) {
        setSelectedSubscription(null);
        setMessages([]);
        setErrors([]);
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      alert(`Error cancelling subscription: ${error.response?.data?.message || error.message}`);
    }
  };
  
  // Clear messages
  const clearMessages = () => {
    setMessages([]);
  };
  
  // Clear errors
  const clearErrors = () => {
    setErrors([]);
  };
  
  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Subscription Query
              </Typography>
              
              <Paper variant="outlined" sx={{ height: 300, mb: 2 }}>
                <Editor
                  height="300px"
                  language="graphql"
                  value={query}
                  onChange={setQuery}
                  options={{
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    wordWrap: 'on'
                  }}
                />
              </Paper>
              
              <Typography variant="subtitle2" gutterBottom>
                Variables (JSON)
              </Typography>
              
              <Paper variant="outlined" sx={{ height: 150, mb: 2 }}>
                <Editor
                  height="150px"
                  language="json"
                  value={variables}
                  onChange={setVariables}
                  options={{
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false,
                    fontSize: 14,
                    wordWrap: 'on'
                  }}
                />
              </Paper>
              
              <Button
                variant="contained"
                startIcon={<PlayArrowIcon />}
                onClick={createSubscription}
                disabled={loading || !endpoint}
                fullWidth
              >
                {loading ? 'Creating Subscription...' : 'Create Subscription'}
              </Button>
            </CardContent>
          </Card>
          
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Active Subscriptions
                </Typography>
                
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={loadActiveSubscriptions}
                  size="small"
                >
                  Refresh
                </Button>
              </Box>
              
              {activeSubscriptions.length === 0 ? (
                <Typography variant="body2" color="textSecondary" align="center">
                  No active subscriptions
                </Typography>
              ) : (
                <List>
                  {activeSubscriptions.map(subscription => (
                    <ListItem
                      key={subscription.id}
                      button
                      selected={selectedSubscription?.id === subscription.id}
                      onClick={() => setSelectedSubscription(subscription)}
                      secondaryAction={
                        <IconButton
                          edge="end"
                          aria-label="cancel"
                          onClick={(e) => {
                            e.stopPropagation();
                            cancelSubscription(subscription.id);
                          }}
                        >
                          <StopIcon />
                        </IconButton>
                      }
                    >
                      <ListItemText
                        primary={`Subscription #${subscription.id}`}
                        secondary={
                          <React.Fragment>
                            <Typography variant="body2" component="span" color="textSecondary">
                              Status: {subscription.status}
                            </Typography>
                            <br />
                            <Typography variant="body2" component="span" color="textSecondary">
                              Created: {new Date(subscription.createdAt).toLocaleString()}
                            </Typography>
                          </React.Fragment>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Subscription Messages
                </Typography>
                
                <Button
                  variant="outlined"
                  startIcon={<DeleteIcon />}
                  onClick={clearMessages}
                  size="small"
                  disabled={messages.length === 0}
                >
                  Clear
                </Button>
              </Box>
              
              {!selectedSubscription ? (
                <Typography variant="body2" color="textSecondary" align="center">
                  Select a subscription to view messages
                </Typography>
              ) : messages.length === 0 ? (
                <Typography variant="body2" color="textSecondary" align="center">
                  No messages received yet
                </Typography>
              ) : (
                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {messages.map((message, index) => (
                    <Paper
                      key={index}
                      variant="outlined"
                      sx={{ p: 2, mb: 2, bgcolor: 'success.light' }}
                    >
                      <Typography variant="subtitle2" gutterBottom>
                        Message received at {new Date(message.timestamp).toLocaleTimeString()}
                      </Typography>
                      
                      <Paper variant="outlined" sx={{ p: 1, bgcolor: 'background.paper' }}>
                        <pre style={{ margin: 0, fontSize: 12, whiteSpace: 'pre-wrap' }}>
                          {JSON.stringify(message.data, null, 2)}
                        </pre>
                      </Paper>
                    </Paper>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
          
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Subscription Errors
                </Typography>
                
                <Button
                  variant="outlined"
                  startIcon={<DeleteIcon />}
                  onClick={clearErrors}
                  size="small"
                  disabled={errors.length === 0}
                >
                  Clear
                </Button>
              </Box>
              
              {!selectedSubscription ? (
                <Typography variant="body2" color="textSecondary" align="center">
                  Select a subscription to view errors
                </Typography>
              ) : errors.length === 0 ? (
                <Typography variant="body2" color="textSecondary" align="center">
                  No errors received
                </Typography>
              ) : (
                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {errors.map((error, index) => (
                    <Paper
                      key={index}
                      variant="outlined"
                      sx={{ p: 2, mb: 2, bgcolor: 'error.light' }}
                    >
                      <Typography variant="subtitle2" gutterBottom>
                        Error received at {new Date(error.timestamp).toLocaleTimeString()}
                      </Typography>
                      
                      <Paper variant="outlined" sx={{ p: 1, bgcolor: 'background.paper' }}>
                        <pre style={{ margin: 0, fontSize: 12, whiteSpace: 'pre-wrap' }}>
                          {JSON.stringify(error.errors, null, 2)}
                        </pre>
                      </Paper>
                    </Paper>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GraphQLSubscriptionManager;
