/**
 * Knowledge Manager for the Universal Compliance Tracking Optimizer.
 *
 * This module provides functionality for managing the UCTO Compliance Knowledge Base.
 */

const path = require('path');
const fs = require('fs');
const knowledgeSchema = require('../schema/knowledge-schema');

/**
 * Manager for the UCTO Compliance Knowledge Base.
 */
class KnowledgeManager {
  /**
   * Initialize the Knowledge Manager.
   * @param {Object} options - Options for the Knowledge Manager
   */
  constructor(options = {}) {
    console.log("Initializing Knowledge Manager");
    
    // Set the data directory
    this.dataDir = options.dataDir || path.join(process.cwd(), 'knowledge_data');
    
    // Create the data directory if it doesn't exist
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
    
    // Store the knowledge schema
    this.schema = knowledgeSchema;
    
    // Initialize entity stores
    this.entities = {};
    
    // Initialize relationship stores
    this.relationships = {};
    
    // Initialize search index
    this.searchIndex = {};
    
    // Load data
    this._loadData();
    
    console.log(`Knowledge Manager initialized with data directory: ${this.dataDir}`);
  }
  
  /**
   * Get the knowledge schema.
   * @returns {Object} The knowledge schema
   */
  getSchema() {
    return this.schema;
  }
  
  /**
   * Get entity types.
   * @returns {Array} Entity types
   */
  getEntityTypes() {
    return this.schema.entityTypes;
  }
  
  /**
   * Get relationship types.
   * @returns {Array} Relationship types
   */
  getRelationshipTypes() {
    return this.schema.relationshipTypes;
  }
  
  /**
   * Create an entity.
   * @param {string} entityType - Type of entity
   * @param {Object} entityData - Entity data
   * @returns {Object} Created entity
   */
  createEntity(entityType, entityData) {
    console.log(`Creating entity of type: ${entityType}`);
    
    // Validate entity type
    const entityTypeDefinition = this.schema.entityTypes.find(et => et.id === entityType);
    
    if (!entityTypeDefinition) {
      throw new Error(`Invalid entity type: ${entityType}`);
    }
    
    // Validate entity data
    this._validateEntityData(entityTypeDefinition, entityData);
    
    // Generate ID if not provided
    if (!entityData.id) {
      entityData.id = this._generateId(entityType);
    }
    
    // Initialize entity store if it doesn't exist
    if (!this.entities[entityType]) {
      this.entities[entityType] = {};
    }
    
    // Check if entity already exists
    if (this.entities[entityType][entityData.id]) {
      throw new Error(`Entity already exists: ${entityType}/${entityData.id}`);
    }
    
    // Store the entity
    this.entities[entityType][entityData.id] = entityData;
    
    // Save the entity to disk
    this._saveEntity(entityType, entityData);
    
    // Index the entity for search
    this._indexEntity(entityType, entityData);
    
    return entityData;
  }
  
  /**
   * Get an entity by ID.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @returns {Object} Entity
   */
  getEntity(entityType, entityId) {
    // Check if entity store exists
    if (!this.entities[entityType]) {
      return null;
    }
    
    // Return the entity
    return this.entities[entityType][entityId];
  }
  
  /**
   * Get all entities of a type.
   * @param {string} entityType - Type of entity
   * @returns {Array} Entities
   */
  getEntities(entityType) {
    // Check if entity store exists
    if (!this.entities[entityType]) {
      return [];
    }
    
    // Return all entities
    return Object.values(this.entities[entityType]);
  }
  
  /**
   * Update an entity.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @param {Object} updates - Updates to apply
   * @returns {Object} Updated entity
   */
  updateEntity(entityType, entityId, updates) {
    console.log(`Updating entity: ${entityType}/${entityId}`);
    
    // Check if entity store exists
    if (!this.entities[entityType]) {
      throw new Error(`Entity type not found: ${entityType}`);
    }
    
    // Check if entity exists
    if (!this.entities[entityType][entityId]) {
      throw new Error(`Entity not found: ${entityType}/${entityId}`);
    }
    
    // Get the entity
    const entity = this.entities[entityType][entityId];
    
    // Apply updates
    const updatedEntity = { ...entity, ...updates };
    
    // Validate updated entity
    const entityTypeDefinition = this.schema.entityTypes.find(et => et.id === entityType);
    this._validateEntityData(entityTypeDefinition, updatedEntity);
    
    // Store the updated entity
    this.entities[entityType][entityId] = updatedEntity;
    
    // Save the entity to disk
    this._saveEntity(entityType, updatedEntity);
    
    // Update the entity in the search index
    this._indexEntity(entityType, updatedEntity);
    
    return updatedEntity;
  }
  
  /**
   * Delete an entity.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @returns {boolean} Success
   */
  deleteEntity(entityType, entityId) {
    console.log(`Deleting entity: ${entityType}/${entityId}`);
    
    // Check if entity store exists
    if (!this.entities[entityType]) {
      throw new Error(`Entity type not found: ${entityType}`);
    }
    
    // Check if entity exists
    if (!this.entities[entityType][entityId]) {
      throw new Error(`Entity not found: ${entityType}/${entityId}`);
    }
    
    // Delete the entity
    delete this.entities[entityType][entityId];
    
    // Delete the entity file
    const entityDir = path.join(this.dataDir, entityType);
    const entityPath = path.join(entityDir, `${entityId}.json`);
    
    if (fs.existsSync(entityPath)) {
      fs.unlinkSync(entityPath);
    }
    
    // Remove the entity from the search index
    this._removeEntityFromIndex(entityType, entityId);
    
    // Delete relationships involving the entity
    this._deleteEntityRelationships(entityType, entityId);
    
    return true;
  }
  
  /**
   * Create a relationship between entities.
   * @param {string} relationshipType - Type of relationship
   * @param {string} sourceType - Type of source entity
   * @param {string} sourceId - ID of source entity
   * @param {string} targetType - Type of target entity
   * @param {string} targetId - ID of target entity
   * @param {Object} properties - Relationship properties
   * @returns {Object} Created relationship
   */
  createRelationship(relationshipType, sourceType, sourceId, targetType, targetId, properties = {}) {
    console.log(`Creating relationship: ${relationshipType} from ${sourceType}/${sourceId} to ${targetType}/${targetId}`);
    
    // Validate relationship type
    const relationshipTypeDefinition = this.schema.relationshipTypes.find(rt => rt.id === relationshipType);
    
    if (!relationshipTypeDefinition) {
      throw new Error(`Invalid relationship type: ${relationshipType}`);
    }
    
    // Validate source and target types
    if (relationshipTypeDefinition.sourceType !== sourceType) {
      throw new Error(`Invalid source type for relationship: ${sourceType}`);
    }
    
    if (relationshipTypeDefinition.targetType !== targetType) {
      throw new Error(`Invalid target type for relationship: ${targetType}`);
    }
    
    // Check if source and target entities exist
    if (!this.entities[sourceType] || !this.entities[sourceType][sourceId]) {
      throw new Error(`Source entity not found: ${sourceType}/${sourceId}`);
    }
    
    if (!this.entities[targetType] || !this.entities[targetType][targetId]) {
      throw new Error(`Target entity not found: ${targetType}/${targetId}`);
    }
    
    // Generate relationship ID
    const relationshipId = `${sourceType}_${sourceId}_${targetType}_${targetId}`;
    
    // Initialize relationship store if it doesn't exist
    if (!this.relationships[relationshipType]) {
      this.relationships[relationshipType] = {};
    }
    
    // Check if relationship already exists
    if (this.relationships[relationshipType][relationshipId]) {
      throw new Error(`Relationship already exists: ${relationshipType}/${relationshipId}`);
    }
    
    // Create the relationship
    const relationship = {
      id: relationshipId,
      type: relationshipType,
      sourceType,
      sourceId,
      targetType,
      targetId,
      properties
    };
    
    // Store the relationship
    this.relationships[relationshipType][relationshipId] = relationship;
    
    // Save the relationship to disk
    this._saveRelationship(relationshipType, relationship);
    
    return relationship;
  }
  
  /**
   * Get relationships for an entity.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @param {string} relationshipType - Type of relationship (optional)
   * @returns {Array} Relationships
   */
  getEntityRelationships(entityType, entityId, relationshipType = null) {
    const relationships = [];
    
    // Get relationship types to check
    const relationshipTypes = relationshipType ? [relationshipType] : Object.keys(this.relationships);
    
    // Check each relationship type
    for (const relType of relationshipTypes) {
      // Skip if relationship store doesn't exist
      if (!this.relationships[relType]) {
        continue;
      }
      
      // Get relationships where entity is source or target
      for (const relationship of Object.values(this.relationships[relType])) {
        if ((relationship.sourceType === entityType && relationship.sourceId === entityId) ||
            (relationship.targetType === entityType && relationship.targetId === entityId)) {
          relationships.push(relationship);
        }
      }
    }
    
    return relationships;
  }
  
  /**
   * Delete a relationship.
   * @param {string} relationshipType - Type of relationship
   * @param {string} relationshipId - Relationship ID
   * @returns {boolean} Success
   */
  deleteRelationship(relationshipType, relationshipId) {
    console.log(`Deleting relationship: ${relationshipType}/${relationshipId}`);
    
    // Check if relationship store exists
    if (!this.relationships[relationshipType]) {
      throw new Error(`Relationship type not found: ${relationshipType}`);
    }
    
    // Check if relationship exists
    if (!this.relationships[relationshipType][relationshipId]) {
      throw new Error(`Relationship not found: ${relationshipType}/${relationshipId}`);
    }
    
    // Delete the relationship
    delete this.relationships[relationshipType][relationshipId];
    
    // Delete the relationship file
    const relationshipDir = path.join(this.dataDir, 'relationships', relationshipType);
    const relationshipPath = path.join(relationshipDir, `${relationshipId}.json`);
    
    if (fs.existsSync(relationshipPath)) {
      fs.unlinkSync(relationshipPath);
    }
    
    return true;
  }
  
  /**
   * Search the knowledge base.
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Array} Search results
   */
  search(query, options = {}) {
    console.log(`Searching knowledge base: ${query}`);
    
    // Default options
    const defaultOptions = {
      entityTypes: null, // All entity types
      limit: 10,
      offset: 0,
      sortBy: 'relevance'
    };
    
    // Merge options
    const searchOptions = { ...defaultOptions, ...options };
    
    // Get entity types to search
    const entityTypes = searchOptions.entityTypes || Object.keys(this.entities);
    
    // Search results
    const results = [];
    
    // Search each entity type
    for (const entityType of entityTypes) {
      // Skip if entity store doesn't exist
      if (!this.entities[entityType]) {
        continue;
      }
      
      // Get indexed fields for this entity type
      const indexedFields = this.schema.search.indexedFields.find(i => i.entityType === entityType);
      
      if (!indexedFields) {
        continue;
      }
      
      // Search entities
      for (const entity of Object.values(this.entities[entityType])) {
        // Check if entity matches query
        let matches = false;
        let score = 0;
        
        for (const field of indexedFields.fields) {
          const value = entity[field];
          
          if (!value) {
            continue;
          }
          
          // Check if field value contains query
          if (typeof value === 'string' && value.toLowerCase().includes(query.toLowerCase())) {
            matches = true;
            score += 1;
          } else if (Array.isArray(value)) {
            // Check if array contains query
            for (const item of value) {
              if (typeof item === 'string' && item.toLowerCase().includes(query.toLowerCase())) {
                matches = true;
                score += 1;
              }
            }
          }
        }
        
        if (matches) {
          results.push({
            entity,
            entityType,
            score
          });
        }
      }
    }
    
    // Sort results
    if (searchOptions.sortBy === 'relevance') {
      results.sort((a, b) => b.score - a.score);
    }
    
    // Apply limit and offset
    const paginatedResults = results.slice(searchOptions.offset, searchOptions.offset + searchOptions.limit);
    
    return {
      query,
      total: results.length,
      results: paginatedResults
    };
  }
  
  /**
   * Validate entity data against the entity type definition.
   * @param {Object} entityTypeDefinition - Entity type definition
   * @param {Object} entityData - Entity data
   * @returns {boolean} Valid
   * @private
   */
  _validateEntityData(entityTypeDefinition, entityData) {
    // Check required properties
    for (const property of entityTypeDefinition.properties) {
      if (property.required && !entityData[property.name]) {
        throw new Error(`Required property missing: ${property.name}`);
      }
    }
    
    return true;
  }
  
  /**
   * Generate an ID for an entity.
   * @param {string} entityType - Type of entity
   * @returns {string} Generated ID
   * @private
   */
  _generateId(entityType) {
    return `${entityType}_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }
  
  /**
   * Save an entity to disk.
   * @param {string} entityType - Type of entity
   * @param {Object} entity - Entity data
   * @returns {boolean} Success
   * @private
   */
  _saveEntity(entityType, entity) {
    // Create the entity directory if it doesn't exist
    const entityDir = path.join(this.dataDir, entityType);
    if (!fs.existsSync(entityDir)) {
      fs.mkdirSync(entityDir, { recursive: true });
    }
    
    // Save the entity to a file
    const entityPath = path.join(entityDir, `${entity.id}.json`);
    fs.writeFileSync(entityPath, JSON.stringify(entity, null, 2));
    
    return true;
  }
  
  /**
   * Save a relationship to disk.
   * @param {string} relationshipType - Type of relationship
   * @param {Object} relationship - Relationship data
   * @returns {boolean} Success
   * @private
   */
  _saveRelationship(relationshipType, relationship) {
    // Create the relationship directory if it doesn't exist
    const relationshipDir = path.join(this.dataDir, 'relationships', relationshipType);
    if (!fs.existsSync(relationshipDir)) {
      fs.mkdirSync(relationshipDir, { recursive: true });
    }
    
    // Save the relationship to a file
    const relationshipPath = path.join(relationshipDir, `${relationship.id}.json`);
    fs.writeFileSync(relationshipPath, JSON.stringify(relationship, null, 2));
    
    return true;
  }
  
  /**
   * Load data from disk.
   * @returns {boolean} Success
   * @private
   */
  _loadData() {
    console.log("Loading data from disk");
    
    // Load entities
    for (const entityType of this.schema.entityTypes) {
      this._loadEntities(entityType.id);
    }
    
    // Load relationships
    for (const relationshipType of this.schema.relationshipTypes) {
      this._loadRelationships(relationshipType.id);
    }
    
    return true;
  }
  
  /**
   * Load entities of a type from disk.
   * @param {string} entityType - Type of entity
   * @returns {boolean} Success
   * @private
   */
  _loadEntities(entityType) {
    console.log(`Loading entities of type: ${entityType}`);
    
    // Initialize entity store
    this.entities[entityType] = {};
    
    // Create the entity directory if it doesn't exist
    const entityDir = path.join(this.dataDir, entityType);
    if (!fs.existsSync(entityDir)) {
      fs.mkdirSync(entityDir, { recursive: true });
      return true;
    }
    
    // Get all entity files
    const entityFiles = fs.readdirSync(entityDir).filter(file => file.endsWith('.json'));
    
    // Load each entity
    for (const entityFile of entityFiles) {
      try {
        const entityPath = path.join(entityDir, entityFile);
        const entityData = fs.readFileSync(entityPath, 'utf8');
        const entity = JSON.parse(entityData);
        
        // Store the entity
        this.entities[entityType][entity.id] = entity;
        
        // Index the entity for search
        this._indexEntity(entityType, entity);
        
        console.log(`Loaded entity from disk: ${entityType}/${entity.id}`);
      } catch (error) {
        console.error(`Error loading entity: ${entityFile}`, error);
      }
    }
    
    return true;
  }
  
  /**
   * Load relationships of a type from disk.
   * @param {string} relationshipType - Type of relationship
   * @returns {boolean} Success
   * @private
   */
  _loadRelationships(relationshipType) {
    console.log(`Loading relationships of type: ${relationshipType}`);
    
    // Initialize relationship store
    this.relationships[relationshipType] = {};
    
    // Create the relationship directory if it doesn't exist
    const relationshipDir = path.join(this.dataDir, 'relationships', relationshipType);
    if (!fs.existsSync(relationshipDir)) {
      fs.mkdirSync(relationshipDir, { recursive: true });
      return true;
    }
    
    // Get all relationship files
    const relationshipFiles = fs.readdirSync(relationshipDir).filter(file => file.endsWith('.json'));
    
    // Load each relationship
    for (const relationshipFile of relationshipFiles) {
      try {
        const relationshipPath = path.join(relationshipDir, relationshipFile);
        const relationshipData = fs.readFileSync(relationshipPath, 'utf8');
        const relationship = JSON.parse(relationshipData);
        
        // Store the relationship
        this.relationships[relationshipType][relationship.id] = relationship;
        
        console.log(`Loaded relationship from disk: ${relationshipType}/${relationship.id}`);
      } catch (error) {
        console.error(`Error loading relationship: ${relationshipFile}`, error);
      }
    }
    
    return true;
  }
  
  /**
   * Index an entity for search.
   * @param {string} entityType - Type of entity
   * @param {Object} entity - Entity data
   * @returns {boolean} Success
   * @private
   */
  _indexEntity(entityType, entity) {
    // Get indexed fields for this entity type
    const indexedFields = this.schema.search.indexedFields.find(i => i.entityType === entityType);
    
    if (!indexedFields) {
      return false;
    }
    
    // Initialize search index for this entity type if it doesn't exist
    if (!this.searchIndex[entityType]) {
      this.searchIndex[entityType] = {};
    }
    
    // Index each field
    for (const field of indexedFields.fields) {
      const value = entity[field];
      
      if (!value) {
        continue;
      }
      
      // Index string value
      if (typeof value === 'string') {
        const tokens = this._tokenize(value);
        
        for (const token of tokens) {
          if (!this.searchIndex[entityType][token]) {
            this.searchIndex[entityType][token] = [];
          }
          
          if (!this.searchIndex[entityType][token].includes(entity.id)) {
            this.searchIndex[entityType][token].push(entity.id);
          }
        }
      } else if (Array.isArray(value)) {
        // Index array values
        for (const item of value) {
          if (typeof item === 'string') {
            const tokens = this._tokenize(item);
            
            for (const token of tokens) {
              if (!this.searchIndex[entityType][token]) {
                this.searchIndex[entityType][token] = [];
              }
              
              if (!this.searchIndex[entityType][token].includes(entity.id)) {
                this.searchIndex[entityType][token].push(entity.id);
              }
            }
          }
        }
      }
    }
    
    return true;
  }
  
  /**
   * Remove an entity from the search index.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @returns {boolean} Success
   * @private
   */
  _removeEntityFromIndex(entityType, entityId) {
    // Check if search index exists for this entity type
    if (!this.searchIndex[entityType]) {
      return false;
    }
    
    // Remove entity ID from all tokens
    for (const token in this.searchIndex[entityType]) {
      const index = this.searchIndex[entityType][token].indexOf(entityId);
      
      if (index !== -1) {
        this.searchIndex[entityType][token].splice(index, 1);
      }
      
      // Remove token if no entities are indexed for it
      if (this.searchIndex[entityType][token].length === 0) {
        delete this.searchIndex[entityType][token];
      }
    }
    
    return true;
  }
  
  /**
   * Delete relationships involving an entity.
   * @param {string} entityType - Type of entity
   * @param {string} entityId - Entity ID
   * @returns {boolean} Success
   * @private
   */
  _deleteEntityRelationships(entityType, entityId) {
    // Get all relationships involving the entity
    const relationships = this.getEntityRelationships(entityType, entityId);
    
    // Delete each relationship
    for (const relationship of relationships) {
      this.deleteRelationship(relationship.type, relationship.id);
    }
    
    return true;
  }
  
  /**
   * Tokenize a string for indexing.
   * @param {string} text - Text to tokenize
   * @returns {Array} Tokens
   * @private
   */
  _tokenize(text) {
    // Convert to lowercase
    const lowerText = text.toLowerCase();
    
    // Remove punctuation
    const noPunctuation = lowerText.replace(/[^\w\s]/g, ' ');
    
    // Split into tokens
    const tokens = noPunctuation.split(/\s+/).filter(token => token.length > 0);
    
    // Remove stop words
    const stopWords = this.schema.search.analyzers.find(a => a.name === 'standard').stopWords;
    const filteredTokens = tokens.filter(token => !stopWords.includes(token));
    
    return filteredTokens;
  }
}

module.exports = KnowledgeManager;
