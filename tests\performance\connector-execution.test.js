/**
 * NovaConnect Performance Tests - Connector Execution
 * 
 * These tests validate the performance of the connector execution engine
 * under various load conditions.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  
  // Record response time in global metrics
  if (global.recordResponseTime) {
    global.recordResponseTime(duration);
  }
  
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  connectorId: 'gcp-scc', // Using the GCP Security Command Center connector
  endpointId: 'getFindings',
  credentialId: 'test-credential',
  userId: 'performance-test-user',
  parameters: {}
};

describe('Connector Execution Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(60000);
  
  // Test data
  let executorUrl;
  
  beforeAll(async () => {
    executorUrl = `${config.baseUrl}/api/testing/execute`;
    
    // Create test credential if it doesn't exist
    try {
      await axios.post(`${config.baseUrl}/api/credentials`, {
        id: config.credentialId,
        connectorId: config.connectorId,
        name: 'Test Credential',
        type: 'oauth2',
        data: {
          access_token: 'test-token',
          refresh_token: 'test-refresh-token',
          expires_at: Date.now() + 3600000
        },
        userId: config.userId
      });
    } catch (error) {
      // Ignore if credential already exists
      if (error.response && error.response.status !== 409) {
        console.error('Error creating test credential:', error.message);
      }
    }
  });
  
  // Test single execution performance
  describe('Single Execution Performance', () => {
    it('should execute a connector endpoint within acceptable time', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${executorUrl}/${config.connectorId}/${config.endpointId}`, {
          credentialId: config.credentialId,
          parameters: config.parameters,
          userId: config.userId
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(duration).toBeLessThan(1000); // Should execute in less than 1 second
      
      console.log(`Single execution completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test concurrent execution performance
  describe('Concurrent Execution Performance', () => {
    it('should handle 10 concurrent executions within acceptable time', async () => {
      const concurrentRequests = 10;
      const executionFn = () => axios.post(`${executorUrl}/${config.connectorId}/${config.endpointId}`, {
        credentialId: config.credentialId,
        parameters: config.parameters,
        userId: config.userId
      });
      
      const { result, duration } = await measureExecutionTime(async () => {
        const promises = Array(concurrentRequests).fill(0).map(() => executionFn());
        return await Promise.all(promises);
      });
      
      expect(result).toHaveLength(concurrentRequests);
      result.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('success', true);
      });
      
      // Average time per request should be less than 200ms
      const averageTime = duration / concurrentRequests;
      expect(averageTime).toBeLessThan(200);
      
      console.log(`${concurrentRequests} concurrent executions completed in ${duration.toFixed(2)} ms`);
      console.log(`Average time per request: ${averageTime.toFixed(2)} ms`);
    });
  });
  
  // Test throughput performance
  describe('Throughput Performance', () => {
    it('should achieve acceptable throughput for 50 sequential executions', async () => {
      const totalRequests = 50;
      const executionFn = () => axios.post(`${executorUrl}/${config.connectorId}/${config.endpointId}`, {
        credentialId: config.credentialId,
        parameters: config.parameters,
        userId: config.userId
      });
      
      const { result, duration } = await measureExecutionTime(async () => {
        const results = [];
        for (let i = 0; i < totalRequests; i++) {
          results.push(await executionFn());
        }
        return results;
      });
      
      expect(result).toHaveLength(totalRequests);
      result.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('success', true);
      });
      
      // Calculate throughput (requests per second)
      const throughput = (totalRequests / duration) * 1000;
      
      // Throughput should be at least 10 requests per second
      expect(throughput).toBeGreaterThan(10);
      
      console.log(`${totalRequests} sequential executions completed in ${duration.toFixed(2)} ms`);
      console.log(`Throughput: ${throughput.toFixed(2)} requests/second`);
    });
  });
  
  // Test peak load performance
  describe('Peak Load Performance', () => {
    it('should handle peak load of 50,000 events in 15 minutes', async () => {
      // Simulate peak load by executing a batch of requests
      // Note: We're not actually going to run 50,000 requests in the test
      // Instead, we'll run a smaller batch and extrapolate
      
      const batchSize = 50; // Use a smaller batch for the test
      const targetThroughput = 50000 / (15 * 60); // Events per second for 50K in 15 min
      
      console.log(`Target throughput for 50K events in 15 min: ${targetThroughput.toFixed(2)} events/second`);
      
      const executionFn = () => axios.post(`${executorUrl}/${config.connectorId}/${config.endpointId}`, {
        credentialId: config.credentialId,
        parameters: config.parameters,
        userId: config.userId
      });
      
      const { result, duration } = await measureExecutionTime(async () => {
        const promises = Array(batchSize).fill(0).map(() => executionFn());
        return await Promise.all(promises);
      });
      
      expect(result).toHaveLength(batchSize);
      result.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('success', true);
      });
      
      // Calculate actual throughput (requests per second)
      const actualThroughput = (batchSize / duration) * 1000;
      
      console.log(`Batch of ${batchSize} concurrent executions completed in ${duration.toFixed(2)} ms`);
      console.log(`Actual throughput: ${actualThroughput.toFixed(2)} requests/second`);
      
      // Extrapolate to full load
      const estimatedTimeFor50K = (50000 / actualThroughput) * 1000; // ms
      const estimatedTimeInMinutes = estimatedTimeFor50K / (1000 * 60); // minutes
      
      console.log(`Estimated time to process 50,000 events: ${estimatedTimeInMinutes.toFixed(2)} minutes`);
      
      // Check if we can meet the target of 50K events in 15 minutes
      expect(estimatedTimeInMinutes).toBeLessThanOrEqual(15);
    });
  });
  
  // Test memory usage
  describe('Memory Usage', () => {
    it('should not have memory leaks during repeated executions', async () => {
      const totalRequests = 100;
      const executionFn = () => axios.post(`${executorUrl}/${config.connectorId}/${config.endpointId}`, {
        credentialId: config.credentialId,
        parameters: config.parameters,
        userId: config.userId
      });
      
      // Record initial memory usage
      const initialMemory = process.memoryUsage();
      
      // Execute requests
      for (let i = 0; i < totalRequests; i++) {
        await executionFn();
        
        // Force garbage collection every 25 requests if available
        if (i % 25 === 0 && global.gc) {
          global.gc();
        }
      }
      
      // Record final memory usage
      const finalMemory = process.memoryUsage();
      
      // Calculate memory growth
      const heapGrowth = (finalMemory.heapUsed - initialMemory.heapUsed) / (1024 * 1024); // MB
      
      console.log(`Memory usage after ${totalRequests} executions:`);
      console.log(`Initial heap: ${(initialMemory.heapUsed / (1024 * 1024)).toFixed(2)} MB`);
      console.log(`Final heap: ${(finalMemory.heapUsed / (1024 * 1024)).toFixed(2)} MB`);
      console.log(`Heap growth: ${heapGrowth.toFixed(2)} MB`);
      
      // Memory growth should be reasonable
      // Note: This is a heuristic and may need adjustment based on the actual application
      expect(heapGrowth).toBeLessThan(50); // Less than 50MB growth
    });
  });
});
