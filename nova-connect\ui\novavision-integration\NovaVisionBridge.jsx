/**
 * NovaVision Bridge Component
 * 
 * This component serves as a bridge between NovaConnect UI components and NovaVision.
 * It allows NovaVision to render UI schemas using NovaConnect UI components.
 */

import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UUICProvider, useUUICContext } from '@novafuse/uui-core/react';
import NovaVisionIntegration from './NovaVisionIntegration';

/**
 * NovaVision Bridge component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaVision - NovaVision instance
 * @param {Object} props.schema - UI schema to render
 * @param {Object} [props.data={}] - Data to render
 * @param {Object} [props.options={}] - Rendering options
 * @param {Function} [props.onSubmit] - Submit handler
 * @param {Function} [props.onChange] - Change handler
 * @param {Function} [props.onAction] - Action handler
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} NovaVision Bridge component
 */
const NovaVisionBridge = ({
  novaVision,
  schema,
  data = {},
  options = {},
  onSubmit,
  onChange,
  onAction,
  enableLogging = false
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [integration, setIntegration] = useState(null);
  
  // Initialize NovaVision integration
  useEffect(() => {
    const initializeIntegration = async () => {
      try {
        if (enableLogging) {
          console.log('Initializing NovaVision integration...');
        }
        
        // Create integration instance
        const novaVisionIntegration = new NovaVisionIntegration({
          novaVision,
          enableLogging
        });
        
        // Initialize integration
        await novaVisionIntegration.initialize();
        
        setIntegration(novaVisionIntegration);
        setIsInitialized(true);
        
        if (enableLogging) {
          console.log('NovaVision integration initialized successfully');
        }
      } catch (err) {
        console.error('Error initializing NovaVision integration', err);
        setError(err);
      }
    };
    
    initializeIntegration();
  }, [novaVision, enableLogging]);
  
  // Handle theme changes
  useEffect(() => {
    const handleThemeChange = (event) => {
      if (integration && event.detail && event.detail.theme) {
        const theme = event.detail.theme;
        
        if (enableLogging) {
          console.log(`Theme changed to ${theme}`);
        }
        
        // Apply theme to NovaVision
        integration.applyTheme();
      }
    };
    
    // Listen for theme change events
    window.addEventListener('themeChange', handleThemeChange);
    
    return () => {
      window.removeEventListener('themeChange', handleThemeChange);
    };
  }, [integration, enableLogging]);
  
  if (error) {
    return (
      <div className="nova-vision-bridge-error">
        <h3>Error initializing NovaVision integration</h3>
        <p>{error.message}</p>
      </div>
    );
  }
  
  if (!isInitialized) {
    return (
      <div className="nova-vision-bridge-loading">
        <p>Initializing NovaVision integration...</p>
      </div>
    );
  }
  
  return (
    <UUICProvider>
      <UUICBridge
        schema={schema}
        data={data}
        options={options}
        onSubmit={onSubmit}
        onChange={onChange}
        onAction={onAction}
      />
    </UUICProvider>
  );
};

NovaVisionBridge.propTypes = {
  novaVision: PropTypes.object.isRequired,
  schema: PropTypes.object.isRequired,
  data: PropTypes.object,
  options: PropTypes.object,
  onSubmit: PropTypes.func,
  onChange: PropTypes.func,
  onAction: PropTypes.func,
  enableLogging: PropTypes.bool
};

export default NovaVisionBridge;
