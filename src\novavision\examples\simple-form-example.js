/**
 * NovaVision - Simple Form Example
 *
 * This example demonstrates how to use NovaVision to generate and render a simple form.
 */

const { novaVision } = require('../index');

// Create a simple form configuration
const formConfig = {
  id: 'user-registration',
  title: 'User Registration',
  description: 'Please fill out the form to register a new user',
  submitUrl: '/api/users',
  sections: [
    {
      id: 'personal-info',
      title: 'Personal Information',
      fields: [
        {
          id: 'firstName',
          type: 'text',
          label: 'First Name',
          required: true,
          placeholder: 'Enter your first name'
        },
        {
          id: 'lastName',
          type: 'text',
          label: 'Last Name',
          required: true,
          placeholder: 'Enter your last name'
        },
        {
          id: 'email',
          type: 'email',
          label: 'Email Address',
          required: true,
          placeholder: 'Enter your email address'
        }
      ]
    },
    {
      id: 'account-info',
      title: 'Account Information',
      fields: [
        {
          id: 'username',
          type: 'text',
          label: 'Username',
          required: true,
          placeholder: 'Choose a username',
          minLength: 4,
          maxLength: 20
        },
        {
          id: 'password',
          type: 'password',
          label: 'Password',
          required: true,
          placeholder: 'Choose a password',
          minLength: 8,
          helpText: 'Password must be at least 8 characters long'
        },
        {
          id: 'confirmPassword',
          type: 'password',
          label: 'Confirm Password',
          required: true,
          placeholder: 'Confirm your password',
          displayWhen: {
            field: 'password',
            operator: 'neq',
            value: ''
          }
        }
      ]
    },
    {
      id: 'preferences',
      title: 'Preferences',
      collapsible: true,
      fields: [
        {
          id: 'theme',
          type: 'select',
          label: 'Theme',
          options: [
            { value: 'light', label: 'Light' },
            { value: 'dark', label: 'Dark' },
            { value: 'system', label: 'System Default' }
          ],
          defaultValue: 'system'
        },
        {
          id: 'notifications',
          type: 'checkbox',
          label: 'Enable Notifications',
          defaultValue: true
        },
        {
          id: 'newsletter',
          type: 'checkbox',
          label: 'Subscribe to Newsletter',
          defaultValue: false
        }
      ]
    }
  ],
  actions: [
    {
      id: 'submit',
      type: 'submit',
      label: 'Register',
      primary: true
    },
    {
      id: 'cancel',
      type: 'button',
      label: 'Cancel',
      primary: false
    }
  ]
};

// Generate UI schema for the form
const formSchema = novaVision.generateFormSchema(formConfig);

// Sample form data
const formData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  username: 'johndoe',
  password: 'password123',
  confirmPassword: 'password123',
  theme: 'dark',
  notifications: true,
  newsletter: false
};

// Render UI from schema
const renderedForm = novaVision.renderUiFromSchema(formSchema, formData);

// Export the schema and rendered form
module.exports = {
  formConfig,
  formSchema,
  formData,
  renderedForm
};

// Example of how to use the rendered form in a React application:
/*
import React from 'react';
import { FormRenderer } from 'novavision-react';

function UserRegistrationForm() {
  const [formData, setFormData] = React.useState({});

  const handleSubmit = (data) => {
    console.log('Form submitted:', data);
    // Submit data to API
  };

  return (
    <FormRenderer
      schema={formSchema}
      data={formData}
      onChange={setFormData}
      onSubmit={handleSubmit}
    />
  );
}

export default UserRegistrationForm;
*/
