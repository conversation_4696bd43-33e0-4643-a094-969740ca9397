@echo off
REM Run UUFT/CSDE tests locally

echo === Running UUFT/CSDE Tests ===

REM Check if Python is installed
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python not found. Please install Python 3.8 or higher.
    exit /b 1
)

REM Install required packages
echo Installing required packages...
pip install pyyaml matplotlib numpy

REM Run the tests
echo Running tests...
python config_loader.py

echo Tests completed!
echo Results are available in the results directory.
