<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>1. High-Level System Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 900px;
            height: 600px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>1. High-Level System Architecture</h1>
    
    <div class="diagram-container">
        <!-- NovaFuse Platform -->
        <div class="element" style="top: 50px; left: 300px; width: 300px; font-weight: bold; font-size: 18px;">
            NovaFuse Platform
            <div class="element-number">1</div>
        </div>
        
        <!-- Input Data Sources -->
        <div class="element" style="top: 200px; left: 100px; width: 200px; font-size: 14px;">
            Input Data Sources
            <div class="element-number">2</div>
        </div>
        
        <!-- Output Actions -->
        <div class="element" style="top: 200px; left: 600px; width: 200px; font-size: 14px;">
            Output Actions
            <div class="element-number">3</div>
        </div>
        
        <!-- Comphyology Framework -->
        <div class="element" style="top: 350px; left: 300px; width: 300px; font-weight: bold; font-size: 16px;">
            Comphyology (<span class="bold-formula">Ψᶜ</span>) Framework
            <div class="element-number">4</div>
        </div>
        
        <!-- Connections -->
        <!-- Input to NovaFuse - direct line to box -->
        <div class="connection" style="top: 200px; left: 300px; width: 100px; height: 2px;"></div>
        
        <!-- NovaFuse to Output - direct line to box -->
        <div class="connection" style="top: 200px; left: 600px; width: 100px; height: 2px;"></div>
        
        <!-- NovaFuse to Comphyology - direct line to box -->
        <div class="connection" style="top: 150px; left: 450px; width: 2px; height: 200px;"></div>
    </div>
</body>
</html>
