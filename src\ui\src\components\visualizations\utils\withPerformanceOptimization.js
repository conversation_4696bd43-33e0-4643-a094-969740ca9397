import React, { useState, useEffect, useMemo } from 'react';
import { 
  determineDetailLevel, 
  getOptimizationOptions, 
  optimizeTensorData, 
  optimizeHarmonyData, 
  optimizeComplianceData 
} from './performanceOptimizer';

/**
 * Higher-Order Component (HOC) that adds performance optimization to visualizations
 * @param {React.Component} WrappedComponent - The component to wrap
 * @param {Object} options - Additional options for the HOC
 * @returns {React.Component} - The wrapped component with performance optimization
 */
const withPerformanceOptimization = (WrappedComponent, options = {}) => {
  // Return a new component
  return function WithPerformanceOptimization(props) {
    // Determine the appropriate detail level based on device capabilities
    const [detailLevel, setDetailLevel] = useState(() => {
      // Use provided detail level or determine automatically
      return props.detailLevel || determineDetailLevel();
    });
    
    // Get optimization options based on detail level
    const optimizationOptions = useMemo(() => {
      return {
        ...getOptimizationOptions(detailLevel),
        ...options
      };
    }, [detailLevel]);
    
    // Update detail level when props change
    useEffect(() => {
      if (props.detailLevel && props.detailLevel !== detailLevel) {
        setDetailLevel(props.detailLevel);
      }
    }, [props.detailLevel]);
    
    // Optimize data based on visualization type
    const optimizedProps = useMemo(() => {
      const newProps = { ...props };
      
      // Optimize tensor data
      if (props.domainData) {
        newProps.domainData = optimizeTensorData(props.domainData, optimizationOptions);
      }
      
      // Optimize harmony data
      if (props.harmonyHistory) {
        newProps.harmonyHistory = optimizeHarmonyData(
          { harmonyHistory: props.harmonyHistory },
          optimizationOptions
        )?.harmonyHistory;
      }
      
      // Optimize risk and control data
      if (props.riskData && props.controlData) {
        const optimizedRiskData = optimizeTensorData(props.riskData, optimizationOptions);
        const optimizedControlData = optimizeTensorData(props.controlData, optimizationOptions);
        
        newProps.riskData = optimizedRiskData;
        newProps.controlData = optimizedControlData;
      }
      
      // Optimize compliance data
      if (props.complianceData) {
        newProps.complianceData = optimizeComplianceData(
          { complianceData: props.complianceData },
          optimizationOptions
        )?.complianceData;
      }
      
      // Optimize prediction data
      if (props.predictionData && props.predictionData.criticalPoints) {
        const optimizedPredictionData = { ...props.predictionData };
        
        if (optimizedPredictionData.criticalPoints.length > optimizationOptions.maxHistoryPoints / 5) {
          optimizedPredictionData.criticalPoints = optimizedPredictionData.criticalPoints
            .sort((a, b) => b.severity - a.severity)
            .slice(0, optimizationOptions.maxHistoryPoints / 5);
        }
        
        newProps.predictionData = optimizedPredictionData;
      }
      
      // Add detail level to options
      newProps.options = {
        ...props.options,
        renderMode: detailLevel
      };
      
      return newProps;
    }, [props, detailLevel, optimizationOptions]);
    
    // Render the wrapped component with optimized props
    return <WrappedComponent {...optimizedProps} />;
  };
};

export default withPerformanceOptimization;
