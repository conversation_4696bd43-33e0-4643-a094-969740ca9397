/**
 * Notification Routes
 * 
 * This file defines the routes for notifications.
 */

const express = require('express');
const router = express.Router();
const { notificationController } = require('../controllers');

// Get all notifications
router.get('/', notificationController.getAllNotifications);

// Get a specific notification by ID
router.get('/:id', notificationController.getNotificationById);

// Create a new notification
router.post('/', notificationController.createNotification);

// Mark a notification as read
router.post('/:id/read', notificationController.markNotificationAsRead);

// Send a notification
router.post('/:id/send', notificationController.sendNotification);

// Generate notifications based on criteria
router.post('/generate', notificationController.generateNotifications);

// Send all pending notifications
router.post('/send-all', notificationController.sendAllPendingNotifications);

// Get notifications by recipient
router.get('/recipients/:recipient', notificationController.getNotificationsByRecipient);

// Get notifications by related entity
router.get('/entities/:entityType/:entityId', notificationController.getNotificationsByRelatedEntity);

module.exports = router;
