/**
 * FormattedCurrency Component
 * 
 * A component for formatting currency values according to the current locale.
 */

import React from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n/I18nContext';

/**
 * FormattedCurrency component
 * 
 * @param {Object} props - Component props
 * @param {number} props.value - Currency value
 * @param {string} [props.currency='USD'] - Currency code
 * @param {Object} [props.options] - Currency format options
 * @param {string} [props.component='span'] - Component to render
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} FormattedCurrency component
 */
const FormattedCurrency = ({
  value,
  currency = 'USD',
  options,
  component: Component = 'span',
  className = '',
  style = {},
  ...rest
}) => {
  const { formatCurrency } = useI18n();
  
  // Format currency
  const formattedCurrency = formatCurrency(value, currency, options);
  
  return (
    <Component
      className={className}
      style={style}
      {...rest}
    >
      {formattedCurrency}
    </Component>
  );
};

FormattedCurrency.propTypes = {
  value: PropTypes.number.isRequired,
  currency: PropTypes.string,
  options: PropTypes.object,
  component: PropTypes.elementType,
  className: PropTypes.string,
  style: PropTypes.object
};

export default FormattedCurrency;
