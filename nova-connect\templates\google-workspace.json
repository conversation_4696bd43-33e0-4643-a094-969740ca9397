{"metadata": {"name": "Google Workspace", "version": "1.0.0", "category": "Identity & Access Management", "description": "Connect to Google Workspace Admin SDK for user management, security settings, and compliance monitoring", "author": "NovaGRC", "tags": ["google", "workspace", "identity", "access", "compliance"], "created": "2025-01-01T00:00:00Z", "updated": "2025-01-01T00:00:00Z", "icon": "https://workspace.google.com/static/img/products/png/drive-icon.png"}, "authentication": {"type": "OAUTH2", "fields": {"clientId": {"type": "string", "description": "Google OAuth 2.0 Client ID", "required": true}, "clientSecret": {"type": "string", "description": "Google OAuth 2.0 Client Secret", "required": true, "sensitive": true}, "refreshToken": {"type": "string", "description": "OAuth 2.0 Refresh Token", "required": true, "sensitive": true}}, "oauth2Config": {"tokenUrl": "https://oauth2.googleapis.com/token", "grantType": "refresh_token", "scopes": ["https://www.googleapis.com/auth/admin.directory.user.readonly", "https://www.googleapis.com/auth/admin.directory.group.readonly", "https://www.googleapis.com/auth/admin.reports.audit.readonly", "https://www.googleapis.com/auth/admin.reports.usage.readonly"]}, "testConnection": {"endpoint": "/admin/directory/v1/users?maxResults=1", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "https://www.googleapis.com", "headers": {"Content-Type": "application/json"}, "rateLimit": {"requests": 100, "period": "1m"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getUsers", "name": "Get Users", "description": "Get users from Google Workspace", "path": "/admin/directory/v1/users", "method": "GET", "parameters": {"query": {"maxResults": {"type": "integer", "description": "Maximum number of results to return", "default": 100}, "orderBy": {"type": "string", "description": "Property to use for sorting results", "enum": ["email", "<PERSON><PERSON>ame", "<PERSON><PERSON><PERSON>"], "default": "email"}, "domain": {"type": "string", "description": "The domain name to filter users by", "required": false}}}, "pagination": {"type": "token", "parameters": {"pageToken": "nextPageToken"}}, "response": {"successCode": 200, "schema": {"kind": "string", "etag": "string", "users": "array", "nextPageToken": "string"}}}, {"id": "getGroups", "name": "Get Groups", "description": "Get groups from Google Workspace", "path": "/admin/directory/v1/groups", "method": "GET", "parameters": {"query": {"maxResults": {"type": "integer", "description": "Maximum number of results to return", "default": 100}, "domain": {"type": "string", "description": "The domain name to filter groups by", "required": false}}}, "pagination": {"type": "token", "parameters": {"pageToken": "nextPageToken"}}, "response": {"successCode": 200, "schema": {"kind": "string", "etag": "string", "groups": "array", "nextPageToken": "string"}}}, {"id": "getAuditLogs", "name": "Get Audit Logs", "description": "Get audit logs from Google Workspace", "path": "/admin/reports/v1/activity/users/{userKey}/applications/{applicationName}", "method": "GET", "parameters": {"path": {"userKey": {"type": "string", "description": "User key (email or 'all')", "default": "all"}, "applicationName": {"type": "string", "description": "Application name", "enum": ["admin", "login", "drive", "calendar", "groups", "gcp"], "default": "admin"}}, "query": {"maxResults": {"type": "integer", "description": "Maximum number of results to return", "default": 100}, "startTime": {"type": "string", "description": "Start time (RFC 3339 timestamp)", "required": false}, "endTime": {"type": "string", "description": "End time (RFC 3339 timestamp)", "required": false}}}, "pagination": {"type": "token", "parameters": {"pageToken": "nextPageToken"}}, "response": {"successCode": 200, "schema": {"kind": "string", "items": "array", "nextPageToken": "string"}}}, {"id": "getAlertSettings", "name": "<PERSON> <PERSON><PERSON>", "description": "Get alert settings from Google Workspace", "path": "/admin/reports/v1/alertSettings", "method": "GET", "response": {"successCode": 200, "schema": {"items": "array"}}}], "mappings": [{"sourceEndpoint": "getUsers", "targetSystem": "NovaGRC", "targetEntity": "Users", "transformations": [{"source": "$.users[*].primaryEmail", "target": "email", "transform": "identity"}, {"source": "$.users[*].name.fullName", "target": "name", "transform": "identity"}, {"source": "$.users[*].orgUnitPath", "target": "department", "transform": "extractDepartment"}, {"source": "$.users[*].isAdmin", "target": "isAdmin", "transform": "identity"}, {"source": "$.users[*].suspended", "target": "status", "transform": "mapUserStatus"}]}, {"sourceEndpoint": "getAuditLogs", "targetSystem": "NovaGRC", "targetEntity": "ActivityLogs", "transformations": [{"source": "$.items[*].id.time", "target": "timestamp", "transform": "identity"}, {"source": "$.items[*].id.uniqueQualifier", "target": "eventId", "transform": "identity"}, {"source": "$.items[*].events[0].name", "target": "eventName", "transform": "identity"}, {"source": "$.items[*].events[0].parameters", "target": "eventDetails", "transform": "flattenParameters"}, {"source": "$.items[*].actor.email", "target": "actor", "transform": "identity"}]}], "events": {"polling": [{"endpoint": "getUsers", "interval": "1h", "condition": "hasUserChanges"}, {"endpoint": "getAuditLogs", "interval": "15m", "condition": "hasNewEvents"}]}}