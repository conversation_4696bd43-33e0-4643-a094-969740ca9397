{"version": 3, "names": ["express", "require", "<PERSON><PERSON><PERSON><PERSON>", "cors", "http", "connectorRegistry", "authenticationManager", "connectorExecutor", "createGraphQLServer", "rbacRoutes", "billingRoutes", "marketplaceRoutes", "ConnectorApi", "constructor", "app", "port", "process", "env", "PORT", "use", "json", "registerRoutes", "initialize", "MarketplaceService", "marketplaceService", "BillingService", "billingService", "httpServer", "createServer", "server", "graphqlServer", "listen", "console", "log", "graphqlPath", "error", "get", "req", "res", "status", "getConnectors", "bind", "getConnector", "post", "registerConnector", "put", "updateConnector", "delete", "deleteConnector", "storeCredentials", "deleteCredentials", "testConnection", "executeEndpoint", "category", "query", "connectors", "getConnectorsByCategory", "searchConnectors", "getAllConnectors", "sanitizedConnectors", "map", "connector", "authentication", "fields", "Object", "entries", "reduce", "acc", "key", "field", "default", "sensitive", "undefined", "message", "id", "params", "sanitizedConnector", "body", "connectorId", "credentials", "credentialId", "success", "getCredentials", "result", "endpointId", "parameters", "connectorApi", "module", "exports"], "sources": ["connector-api.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector API\n *\n * This module provides a REST API for managing and executing connectors.\n */\n\nconst express = require('express');\nconst bodyParser = require('body-parser');\nconst cors = require('cors');\nconst http = require('http');\nconst connectorRegistry = require('../registry/connector-registry');\nconst authenticationManager = require('../auth/authentication-manager');\nconst connectorExecutor = require('../executor/connector-executor');\nconst createGraphQLServer = require('./graphql/server');\n\n// Import routes\nconst rbacRoutes = require('./routes/rbacRoutes');\nconst billingRoutes = require('./routes/billingRoutes');\nconst marketplaceRoutes = require('./routes/marketplaceRoutes');\n\nclass ConnectorApi {\n  constructor() {\n    this.app = express();\n    this.port = process.env.PORT || 3010;\n\n    // Configure middleware\n    this.app.use(cors());\n    this.app.use(bodyParser.json());\n\n    // Register routes\n    this.registerRoutes();\n  }\n\n  /**\n   * Initialize the API\n   */\n  async initialize() {\n    try {\n      // Initialize dependencies\n      await connectorRegistry.initialize();\n      await authenticationManager.initialize();\n      await connectorExecutor.initialize();\n\n      // Initialize services\n      const MarketplaceService = require('./services/MarketplaceService');\n      const marketplaceService = new MarketplaceService();\n      await marketplaceService.initialize();\n\n      const BillingService = require('./services/BillingService');\n      const billingService = new BillingService();\n      await billingService.initialize();\n\n      // Create HTTP server\n      const httpServer = http.createServer(this.app);\n\n      // Create GraphQL server\n      const { server: graphqlServer } = await createGraphQLServer(this.app, { httpServer });\n      this.graphqlServer = graphqlServer;\n\n      // Start the server\n      this.server = httpServer.listen(this.port, () => {\n        console.log(`Connector API listening on port ${this.port}`);\n        console.log(`GraphQL endpoint: http://localhost:${this.port}${graphqlServer.graphqlPath}`);\n        console.log(`GraphQL subscriptions: ws://localhost:${this.port}${graphqlServer.graphqlPath}`);\n      });\n\n      return true;\n    } catch (error) {\n      console.error('Failed to initialize Connector API:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Register API routes\n   */\n  registerRoutes() {\n    // Health check\n    this.app.get('/health', (req, res) => {\n      res.status(200).json({ status: 'ok' });\n    });\n\n    // Connector routes\n    this.app.get('/connectors', this.getConnectors.bind(this));\n    this.app.get('/connectors/:id', this.getConnector.bind(this));\n    this.app.post('/connectors', this.registerConnector.bind(this));\n    this.app.put('/connectors/:id', this.updateConnector.bind(this));\n    this.app.delete('/connectors/:id', this.deleteConnector.bind(this));\n\n    // Credential routes\n    this.app.post('/credentials', this.storeCredentials.bind(this));\n    this.app.delete('/credentials/:id', this.deleteCredentials.bind(this));\n    this.app.post('/credentials/:id/test', this.testConnection.bind(this));\n\n    // Execution routes\n    this.app.post('/execute/:connectorId/:endpointId', this.executeEndpoint.bind(this));\n\n    // RBAC routes\n    this.app.use('/rbac', rbacRoutes);\n\n    // Billing routes\n    this.app.use('/billing', billingRoutes);\n\n    // Marketplace routes\n    this.app.use('/marketplace', marketplaceRoutes);\n  }\n\n  /**\n   * Get all connectors\n   */\n  getConnectors(req, res) {\n    try {\n      const { category, query } = req.query;\n\n      let connectors;\n\n      if (category) {\n        connectors = connectorRegistry.getConnectorsByCategory(category);\n      } else if (query) {\n        connectors = connectorRegistry.searchConnectors(query);\n      } else {\n        connectors = connectorRegistry.getAllConnectors();\n      }\n\n      // Remove sensitive information\n      const sanitizedConnectors = connectors.map(connector => ({\n        ...connector,\n        authentication: {\n          ...connector.authentication,\n          fields: Object.entries(connector.authentication.fields).reduce((acc, [key, field]) => {\n            acc[key] = {\n              ...field,\n              default: field.sensitive ? undefined : field.default\n            };\n            return acc;\n          }, {})\n        }\n      }));\n\n      res.status(200).json(sanitizedConnectors);\n    } catch (error) {\n      console.error('Error getting connectors:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n\n  /**\n   * Get a connector by ID\n   */\n  getConnector(req, res) {\n    try {\n      const { id } = req.params;\n      const connector = connectorRegistry.getConnector(id);\n\n      if (!connector) {\n        return res.status(404).json({ error: `Connector '${id}' not found` });\n      }\n\n      // Remove sensitive information\n      const sanitizedConnector = {\n        ...connector,\n        authentication: {\n          ...connector.authentication,\n          fields: Object.entries(connector.authentication.fields).reduce((acc, [key, field]) => {\n            acc[key] = {\n              ...field,\n              default: field.sensitive ? undefined : field.default\n            };\n            return acc;\n          }, {})\n        }\n      };\n\n      res.status(200).json(sanitizedConnector);\n    } catch (error) {\n      console.error('Error getting connector:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n\n  /**\n   * Register a new connector\n   */\n  async registerConnector(req, res) {\n    try {\n      const connector = req.body;\n\n      if (!connector) {\n        return res.status(400).json({ error: 'Connector template is required' });\n      }\n\n      await connectorRegistry.registerConnector(connector);\n\n      res.status(201).json({ message: 'Connector registered successfully' });\n    } catch (error) {\n      console.error('Error registering connector:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n\n  /**\n   * Update an existing connector\n   */\n  async updateConnector(req, res) {\n    try {\n      const { id } = req.params;\n      const connector = req.body;\n\n      if (!connector) {\n        return res.status(400).json({ error: 'Connector template is required' });\n      }\n\n      await connectorRegistry.updateConnector(id, connector);\n\n      res.status(200).json({ message: 'Connector updated successfully' });\n    } catch (error) {\n      console.error('Error updating connector:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n\n  /**\n   * Delete a connector\n   */\n  async deleteConnector(req, res) {\n    try {\n      const { id } = req.params;\n\n      await connectorRegistry.deleteConnector(id);\n\n      res.status(200).json({ message: 'Connector deleted successfully' });\n    } catch (error) {\n      console.error('Error deleting connector:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n\n  /**\n   * Store credentials for a connector\n   */\n  storeCredentials(req, res) {\n    try {\n      const { connectorId, credentials } = req.body;\n\n      if (!connectorId || !credentials) {\n        return res.status(400).json({ error: 'Connector ID and credentials are required' });\n      }\n\n      const credentialId = authenticationManager.storeCredentials(connectorId, credentials);\n\n      res.status(201).json({ credentialId });\n    } catch (error) {\n      console.error('Error storing credentials:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n\n  /**\n   * Delete credentials\n   */\n  deleteCredentials(req, res) {\n    try {\n      const { id } = req.params;\n\n      const success = authenticationManager.deleteCredentials(id);\n\n      if (!success) {\n        return res.status(404).json({ error: `Credentials '${id}' not found` });\n      }\n\n      res.status(200).json({ message: 'Credentials deleted successfully' });\n    } catch (error) {\n      console.error('Error deleting credentials:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n\n  /**\n   * Test a connection using stored credentials\n   */\n  async testConnection(req, res) {\n    try {\n      const { id } = req.params;\n      const { connectorId } = req.body;\n\n      if (!connectorId) {\n        return res.status(400).json({ error: 'Connector ID is required' });\n      }\n\n      const connector = connectorRegistry.getConnector(connectorId);\n\n      if (!connector) {\n        return res.status(404).json({ error: `Connector '${connectorId}' not found` });\n      }\n\n      const credentials = authenticationManager.getCredentials(id);\n\n      if (!credentials) {\n        return res.status(404).json({ error: `Credentials '${id}' not found` });\n      }\n\n      const result = await authenticationManager.testConnection(connector, credentials);\n\n      res.status(200).json(result);\n    } catch (error) {\n      console.error('Error testing connection:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n\n  /**\n   * Execute an endpoint\n   */\n  async executeEndpoint(req, res) {\n    try {\n      const { connectorId, endpointId } = req.params;\n      const { credentialId, parameters } = req.body;\n\n      if (!credentialId) {\n        return res.status(400).json({ error: 'Credential ID is required' });\n      }\n\n      const result = await connectorExecutor.executeEndpoint(connectorId, endpointId, credentialId, parameters);\n\n      res.status(200).json(result);\n    } catch (error) {\n      console.error('Error executing endpoint:', error);\n      res.status(500).json({ error: error.message });\n    }\n  }\n}\n\n// Create and export a singleton instance\nconst connectorApi = new ConnectorApi();\n\nmodule.exports = connectorApi;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,OAAO,GAAGC,OAAO,CAAC,SAAS,CAAC;AAClC,MAAMC,UAAU,GAAGD,OAAO,CAAC,aAAa,CAAC;AACzC,MAAME,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMG,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMI,iBAAiB,GAAGJ,OAAO,CAAC,gCAAgC,CAAC;AACnE,MAAMK,qBAAqB,GAAGL,OAAO,CAAC,gCAAgC,CAAC;AACvE,MAAMM,iBAAiB,GAAGN,OAAO,CAAC,gCAAgC,CAAC;AACnE,MAAMO,mBAAmB,GAAGP,OAAO,CAAC,kBAAkB,CAAC;;AAEvD;AACA,MAAMQ,UAAU,GAAGR,OAAO,CAAC,qBAAqB,CAAC;AACjD,MAAMS,aAAa,GAAGT,OAAO,CAAC,wBAAwB,CAAC;AACvD,MAAMU,iBAAiB,GAAGV,OAAO,CAAC,4BAA4B,CAAC;AAE/D,MAAMW,YAAY,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,GAAG,GAAGd,OAAO,CAAC,CAAC;IACpB,IAAI,CAACe,IAAI,GAAGC,OAAO,CAACC,GAAG,CAACC,IAAI,IAAI,IAAI;;IAEpC;IACA,IAAI,CAACJ,GAAG,CAACK,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC;IACpB,IAAI,CAACW,GAAG,CAACK,GAAG,CAACjB,UAAU,CAACkB,IAAI,CAAC,CAAC,CAAC;;IAE/B;IACA,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;;EAEA;AACF;AACA;EACE,MAAMC,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF;MACA,MAAMjB,iBAAiB,CAACiB,UAAU,CAAC,CAAC;MACpC,MAAMhB,qBAAqB,CAACgB,UAAU,CAAC,CAAC;MACxC,MAAMf,iBAAiB,CAACe,UAAU,CAAC,CAAC;;MAEpC;MACA,MAAMC,kBAAkB,GAAGtB,OAAO,CAAC,+BAA+B,CAAC;MACnE,MAAMuB,kBAAkB,GAAG,IAAID,kBAAkB,CAAC,CAAC;MACnD,MAAMC,kBAAkB,CAACF,UAAU,CAAC,CAAC;MAErC,MAAMG,cAAc,GAAGxB,OAAO,CAAC,2BAA2B,CAAC;MAC3D,MAAMyB,cAAc,GAAG,IAAID,cAAc,CAAC,CAAC;MAC3C,MAAMC,cAAc,CAACJ,UAAU,CAAC,CAAC;;MAEjC;MACA,MAAMK,UAAU,GAAGvB,IAAI,CAACwB,YAAY,CAAC,IAAI,CAACd,GAAG,CAAC;;MAE9C;MACA,MAAM;QAAEe,MAAM,EAAEC;MAAc,CAAC,GAAG,MAAMtB,mBAAmB,CAAC,IAAI,CAACM,GAAG,EAAE;QAAEa;MAAW,CAAC,CAAC;MACrF,IAAI,CAACG,aAAa,GAAGA,aAAa;;MAElC;MACA,IAAI,CAACD,MAAM,GAAGF,UAAU,CAACI,MAAM,CAAC,IAAI,CAAChB,IAAI,EAAE,MAAM;QAC/CiB,OAAO,CAACC,GAAG,CAAC,mCAAmC,IAAI,CAAClB,IAAI,EAAE,CAAC;QAC3DiB,OAAO,CAACC,GAAG,CAAC,sCAAsC,IAAI,CAAClB,IAAI,GAAGe,aAAa,CAACI,WAAW,EAAE,CAAC;QAC1FF,OAAO,CAACC,GAAG,CAAC,yCAAyC,IAAI,CAAClB,IAAI,GAAGe,aAAa,CAACI,WAAW,EAAE,CAAC;MAC/F,CAAC,CAAC;MAEF,OAAO,IAAI;IACb,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACEd,cAAcA,CAAA,EAAG;IACf;IACA,IAAI,CAACP,GAAG,CAACsB,GAAG,CAAC,SAAS,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;MACpCA,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEmB,MAAM,EAAE;MAAK,CAAC,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,IAAI,CAACzB,GAAG,CAACsB,GAAG,CAAC,aAAa,EAAE,IAAI,CAACI,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,IAAI,CAAC3B,GAAG,CAACsB,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACM,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7D,IAAI,CAAC3B,GAAG,CAAC6B,IAAI,CAAC,aAAa,EAAE,IAAI,CAACC,iBAAiB,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAI,CAAC3B,GAAG,CAAC+B,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACC,eAAe,CAACL,IAAI,CAAC,IAAI,CAAC,CAAC;IAChE,IAAI,CAAC3B,GAAG,CAACiC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAACC,eAAe,CAACP,IAAI,CAAC,IAAI,CAAC,CAAC;;IAEnE;IACA,IAAI,CAAC3B,GAAG,CAAC6B,IAAI,CAAC,cAAc,EAAE,IAAI,CAACM,gBAAgB,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAI,CAAC3B,GAAG,CAACiC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAACG,iBAAiB,CAACT,IAAI,CAAC,IAAI,CAAC,CAAC;IACtE,IAAI,CAAC3B,GAAG,CAAC6B,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAACQ,cAAc,CAACV,IAAI,CAAC,IAAI,CAAC,CAAC;;IAEtE;IACA,IAAI,CAAC3B,GAAG,CAAC6B,IAAI,CAAC,mCAAmC,EAAE,IAAI,CAACS,eAAe,CAACX,IAAI,CAAC,IAAI,CAAC,CAAC;;IAEnF;IACA,IAAI,CAAC3B,GAAG,CAACK,GAAG,CAAC,OAAO,EAAEV,UAAU,CAAC;;IAEjC;IACA,IAAI,CAACK,GAAG,CAACK,GAAG,CAAC,UAAU,EAAET,aAAa,CAAC;;IAEvC;IACA,IAAI,CAACI,GAAG,CAACK,GAAG,CAAC,cAAc,EAAER,iBAAiB,CAAC;EACjD;;EAEA;AACF;AACA;EACE6B,aAAaA,CAACH,GAAG,EAAEC,GAAG,EAAE;IACtB,IAAI;MACF,MAAM;QAAEe,QAAQ;QAAEC;MAAM,CAAC,GAAGjB,GAAG,CAACiB,KAAK;MAErC,IAAIC,UAAU;MAEd,IAAIF,QAAQ,EAAE;QACZE,UAAU,GAAGlD,iBAAiB,CAACmD,uBAAuB,CAACH,QAAQ,CAAC;MAClE,CAAC,MAAM,IAAIC,KAAK,EAAE;QAChBC,UAAU,GAAGlD,iBAAiB,CAACoD,gBAAgB,CAACH,KAAK,CAAC;MACxD,CAAC,MAAM;QACLC,UAAU,GAAGlD,iBAAiB,CAACqD,gBAAgB,CAAC,CAAC;MACnD;;MAEA;MACA,MAAMC,mBAAmB,GAAGJ,UAAU,CAACK,GAAG,CAACC,SAAS,KAAK;QACvD,GAAGA,SAAS;QACZC,cAAc,EAAE;UACd,GAAGD,SAAS,CAACC,cAAc;UAC3BC,MAAM,EAAEC,MAAM,CAACC,OAAO,CAACJ,SAAS,CAACC,cAAc,CAACC,MAAM,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;YACpFF,GAAG,CAACC,GAAG,CAAC,GAAG;cACT,GAAGC,KAAK;cACRC,OAAO,EAAED,KAAK,CAACE,SAAS,GAAGC,SAAS,GAAGH,KAAK,CAACC;YAC/C,CAAC;YACD,OAAOH,GAAG;UACZ,CAAC,EAAE,CAAC,CAAC;QACP;MACF,CAAC,CAAC,CAAC;MAEH7B,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAACuC,mBAAmB,CAAC;IAC3C,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACE/B,YAAYA,CAACL,GAAG,EAAEC,GAAG,EAAE;IACrB,IAAI;MACF,MAAM;QAAEoC;MAAG,CAAC,GAAGrC,GAAG,CAACsC,MAAM;MACzB,MAAMd,SAAS,GAAGxD,iBAAiB,CAACqC,YAAY,CAACgC,EAAE,CAAC;MAEpD,IAAI,CAACb,SAAS,EAAE;QACd,OAAOvB,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE,cAAcuC,EAAE;QAAc,CAAC,CAAC;MACvE;;MAEA;MACA,MAAME,kBAAkB,GAAG;QACzB,GAAGf,SAAS;QACZC,cAAc,EAAE;UACd,GAAGD,SAAS,CAACC,cAAc;UAC3BC,MAAM,EAAEC,MAAM,CAACC,OAAO,CAACJ,SAAS,CAACC,cAAc,CAACC,MAAM,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;YACpFF,GAAG,CAACC,GAAG,CAAC,GAAG;cACT,GAAGC,KAAK;cACRC,OAAO,EAAED,KAAK,CAACE,SAAS,GAAGC,SAAS,GAAGH,KAAK,CAACC;YAC/C,CAAC;YACD,OAAOH,GAAG;UACZ,CAAC,EAAE,CAAC,CAAC;QACP;MACF,CAAC;MAED7B,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAACwD,kBAAkB,CAAC;IAC1C,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACE,MAAM7B,iBAAiBA,CAACP,GAAG,EAAEC,GAAG,EAAE;IAChC,IAAI;MACF,MAAMuB,SAAS,GAAGxB,GAAG,CAACwC,IAAI;MAE1B,IAAI,CAAChB,SAAS,EAAE;QACd,OAAOvB,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE;QAAiC,CAAC,CAAC;MAC1E;MAEA,MAAM9B,iBAAiB,CAACuC,iBAAiB,CAACiB,SAAS,CAAC;MAEpDvB,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEqD,OAAO,EAAE;MAAoC,CAAC,CAAC;IACxE,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACE,MAAM3B,eAAeA,CAACT,GAAG,EAAEC,GAAG,EAAE;IAC9B,IAAI;MACF,MAAM;QAAEoC;MAAG,CAAC,GAAGrC,GAAG,CAACsC,MAAM;MACzB,MAAMd,SAAS,GAAGxB,GAAG,CAACwC,IAAI;MAE1B,IAAI,CAAChB,SAAS,EAAE;QACd,OAAOvB,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE;QAAiC,CAAC,CAAC;MAC1E;MAEA,MAAM9B,iBAAiB,CAACyC,eAAe,CAAC4B,EAAE,EAAEb,SAAS,CAAC;MAEtDvB,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEqD,OAAO,EAAE;MAAiC,CAAC,CAAC;IACrE,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACE,MAAMzB,eAAeA,CAACX,GAAG,EAAEC,GAAG,EAAE;IAC9B,IAAI;MACF,MAAM;QAAEoC;MAAG,CAAC,GAAGrC,GAAG,CAACsC,MAAM;MAEzB,MAAMtE,iBAAiB,CAAC2C,eAAe,CAAC0B,EAAE,CAAC;MAE3CpC,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEqD,OAAO,EAAE;MAAiC,CAAC,CAAC;IACrE,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACExB,gBAAgBA,CAACZ,GAAG,EAAEC,GAAG,EAAE;IACzB,IAAI;MACF,MAAM;QAAEwC,WAAW;QAAEC;MAAY,CAAC,GAAG1C,GAAG,CAACwC,IAAI;MAE7C,IAAI,CAACC,WAAW,IAAI,CAACC,WAAW,EAAE;QAChC,OAAOzC,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE;QAA4C,CAAC,CAAC;MACrF;MAEA,MAAM6C,YAAY,GAAG1E,qBAAqB,CAAC2C,gBAAgB,CAAC6B,WAAW,EAAEC,WAAW,CAAC;MAErFzC,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAE4D;MAAa,CAAC,CAAC;IACxC,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACEvB,iBAAiBA,CAACb,GAAG,EAAEC,GAAG,EAAE;IAC1B,IAAI;MACF,MAAM;QAAEoC;MAAG,CAAC,GAAGrC,GAAG,CAACsC,MAAM;MAEzB,MAAMM,OAAO,GAAG3E,qBAAqB,CAAC4C,iBAAiB,CAACwB,EAAE,CAAC;MAE3D,IAAI,CAACO,OAAO,EAAE;QACZ,OAAO3C,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE,gBAAgBuC,EAAE;QAAc,CAAC,CAAC;MACzE;MAEApC,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEqD,OAAO,EAAE;MAAmC,CAAC,CAAC;IACvE,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACE,MAAMtB,cAAcA,CAACd,GAAG,EAAEC,GAAG,EAAE;IAC7B,IAAI;MACF,MAAM;QAAEoC;MAAG,CAAC,GAAGrC,GAAG,CAACsC,MAAM;MACzB,MAAM;QAAEG;MAAY,CAAC,GAAGzC,GAAG,CAACwC,IAAI;MAEhC,IAAI,CAACC,WAAW,EAAE;QAChB,OAAOxC,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE;QAA2B,CAAC,CAAC;MACpE;MAEA,MAAM0B,SAAS,GAAGxD,iBAAiB,CAACqC,YAAY,CAACoC,WAAW,CAAC;MAE7D,IAAI,CAACjB,SAAS,EAAE;QACd,OAAOvB,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE,cAAc2C,WAAW;QAAc,CAAC,CAAC;MAChF;MAEA,MAAMC,WAAW,GAAGzE,qBAAqB,CAAC4E,cAAc,CAACR,EAAE,CAAC;MAE5D,IAAI,CAACK,WAAW,EAAE;QAChB,OAAOzC,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE,gBAAgBuC,EAAE;QAAc,CAAC,CAAC;MACzE;MAEA,MAAMS,MAAM,GAAG,MAAM7E,qBAAqB,CAAC6C,cAAc,CAACU,SAAS,EAAEkB,WAAW,CAAC;MAEjFzC,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC+D,MAAM,CAAC;IAC9B,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;;EAEA;AACF;AACA;EACE,MAAMrB,eAAeA,CAACf,GAAG,EAAEC,GAAG,EAAE;IAC9B,IAAI;MACF,MAAM;QAAEwC,WAAW;QAAEM;MAAW,CAAC,GAAG/C,GAAG,CAACsC,MAAM;MAC9C,MAAM;QAAEK,YAAY;QAAEK;MAAW,CAAC,GAAGhD,GAAG,CAACwC,IAAI;MAE7C,IAAI,CAACG,YAAY,EAAE;QACjB,OAAO1C,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;UAAEe,KAAK,EAAE;QAA4B,CAAC,CAAC;MACrE;MAEA,MAAMgD,MAAM,GAAG,MAAM5E,iBAAiB,CAAC6C,eAAe,CAAC0B,WAAW,EAAEM,UAAU,EAAEJ,YAAY,EAAEK,UAAU,CAAC;MAEzG/C,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC+D,MAAM,CAAC;IAC9B,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDG,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACnB,IAAI,CAAC;QAAEe,KAAK,EAAEA,KAAK,CAACsC;MAAQ,CAAC,CAAC;IAChD;EACF;AACF;;AAEA;AACA,MAAMa,YAAY,GAAG,IAAI1E,YAAY,CAAC,CAAC;AAEvC2E,MAAM,CAACC,OAAO,GAAGF,YAAY", "ignoreList": []}