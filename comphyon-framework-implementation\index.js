/**
 * Comphyon Framework
 * 
 * This module exports all components of the Comphyon Framework.
 */

const TensorOperator = require('./tensor-operator');
const FusionOperator = require('./fusion-operator');
const CircularTrustTopology = require('./circular-trust-topology');
const UUFT = require('./uuft');

/**
 * Create a fully configured Framework system
 * @param {Object} options - Configuration options
 * @returns {Object} - Framework system components
 */
function createFrameworkSystem(options = {}) {
  // Create components
  const tensorOperator = new TensorOperator(options.tensorOptions);
  const fusionOperator = new FusionOperator(options.fusionOptions);
  const circularTrustTopology = new CircularTrustTopology(options.trustOptions);
  
  // Create UUFT
  const uuft = new UUFT({
    tensorOptions: options.tensorOptions,
    fusionOptions: options.fusionOptions,
    trustOptions: options.trustOptions,
    ...options.uuftOptions
  });
  
  return {
    tensorOperator,
    fusionOperator,
    circularTrustTopology,
    uuft
  };
}

/**
 * Calculate Comphyon value
 * @param {Object} domainEnergies - Domain energy values
 * @returns {number} - Comphyon value
 */
function calculateComphyon(domainEnergies) {
  const uuft = new UUFT();
  return uuft.calculateComphyon(domainEnergies);
}

/**
 * Calculate simplified Comphyon value
 * @param {Object} domainData - Domain data
 * @returns {number} - Simplified Comphyon value
 */
function calculateSimplifiedComphyon(domainData) {
  const uuft = new UUFT();
  return uuft.calculateSimplifiedComphyon(domainData);
}

/**
 * Apply UUFT formula
 * @param {Array} a - First vector
 * @param {Array} b - Second vector
 * @param {Array} c - Third vector
 * @returns {Array} - UUFT result
 */
function applyUUFT(a, b, c) {
  const uuft = new UUFT();
  return uuft.apply(a, b, c);
}

/**
 * Apply 18/82 principle
 * @param {Array} positiveFactors - Positive factors
 * @param {Array} negativeFactors - Negative factors
 * @returns {Array} - Result with 18/82 principle applied
 */
function apply1882Principle(positiveFactors, negativeFactors) {
  const fusionOperator = new FusionOperator();
  return fusionOperator.apply1882Principle(positiveFactors, negativeFactors);
}

module.exports = {
  TensorOperator,
  FusionOperator,
  CircularTrustTopology,
  UUFT,
  createFrameworkSystem,
  calculateComphyon,
  calculateSimplifiedComphyon,
  applyUUFT,
  apply1882Principle
};
