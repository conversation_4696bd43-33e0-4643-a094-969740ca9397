/**
 * Authentication Controller
 *
 * This controller handles API requests related to authentication.
 */

const AuthService = require('../services/AuthService');
const ApiKeyService = require('../services/ApiKeyService');
const BruteForceProtectionService = require('../services/BruteForceProtectionService');
const AuthAuditService = require('../services/AuthAuditService');
const { ValidationError, AuthenticationError } = require('../utils/errors');

class AuthController {
  constructor() {
    this.authService = new AuthService();
    this.apiKeyService = new ApiKeyService();
    this.bruteForceService = new BruteForceProtectionService();
    this.authAuditService = new AuthAuditService();
  }

  /**
   * Register a new user
   */
  async register(req, res, next) {
    try {
      const userData = req.body;

      if (!userData) {
        throw new ValidationError('User data is required');
      }

      try {
        const user = await this.authService.register(userData);

        // Log successful registration
        await this.authAuditService.logRegistration({
          userId: user.id,
          username: user.username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          success: true,
          details: {
            email: user.email
          }
        });

        res.status(201).json(user);
      } catch (error) {
        // Log failed registration
        await this.authAuditService.logRegistration({
          username: userData.username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          success: false,
          reason: error.message,
          details: {
            email: userData.email
          }
        });

        throw error;
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Login a user
   */
  async login(req, res, next) {
    try {
      const { username, password } = req.body;

      if (!username) {
        throw new ValidationError('Username is required');
      }

      if (!password) {
        throw new ValidationError('Password is required');
      }

      // Check for brute force protection
      const identifier = username;
      await this.bruteForceService.checkLoginAttempt(identifier);

      try {
        // Attempt to login
        const result = await this.authService.login(username, password);

        // If login successful, reset brute force counter
        await this.bruteForceService.handleSuccessfulLogin(identifier);

        // Log successful login
        await this.authAuditService.logLoginAttempt({
          userId: result.user.id,
          username: result.user.username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          success: true,
          method: result.authMethod || 'password',
          details: {
            requiresTwoFactor: result.requiresTwoFactor || false
          }
        });

        res.json(result);
      } catch (error) {
        // If login failed, record failed attempt
        if (error instanceof AuthenticationError) {
          try {
            await this.bruteForceService.handleFailedLogin(identifier);

            // Log failed login
            await this.authAuditService.logLoginAttempt({
              username: username,
              ip: req.ip || req.connection.remoteAddress,
              userAgent: req.headers['user-agent'],
              success: false,
              reason: error.message,
              method: 'password'
            });
          } catch (bruteForceError) {
            // If brute force protection triggered, return that error
            if (bruteForceError.name === 'BruteForceError') {
              // Log brute force protection
              await this.authAuditService.logLoginAttempt({
                username: username,
                ip: req.ip || req.connection.remoteAddress,
                userAgent: req.headers['user-agent'],
                success: false,
                reason: 'Brute force protection triggered',
                method: 'password',
                details: {
                  bruteForceProtection: true,
                  retryAfter: bruteForceError.retryAfter
                }
              });

              res.setHeader('Retry-After', bruteForceError.retryAfter);
              return res.status(429).json({
                success: false,
                error: {
                  code: 'BRUTE_FORCE_PROTECTION',
                  message: bruteForceError.message
                }
              });
            }
          }
        }

        // Re-throw the original error
        throw error;
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Logout a user
   */
  async logout(req, res, next) {
    try {
      const token = req.headers.authorization?.split(' ')[1];

      if (!token) {
        throw new ValidationError('Token is required');
      }

      const result = await this.authService.logout(token);

      // Log logout
      await this.authAuditService.logLogout({
        userId: req.user.id,
        username: req.user.username,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.headers['user-agent'],
        details: {
          tokenId: result.tokenId
        }
      });

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(req, res, next) {
    try {
      // User is already attached to the request by the auth middleware
      res.json(req.user);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all users
   */
  async getAllUsers(req, res, next) {
    try {
      // Check if user has admin role
      if (!this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }

      const users = await this.authService.getAllUsers();

      res.json(users);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      // Check if user is requesting their own data or has admin role
      if (id !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }

      const user = await this.authService.getUserById(id);

      res.json(user);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update user
   */
  async updateUser(req, res, next) {
    try {
      const { id } = req.params;
      const userData = req.body;

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      if (!userData) {
        throw new ValidationError('User data is required');
      }

      // Check if user is updating their own data or has admin role
      if (id !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }

      // Only admin can update role and permissions
      if (!this.authService.hasRole(req.user, 'admin')) {
        delete userData.role;
        delete userData.permissions;
      }

      const user = await this.authService.updateUser(id, userData);

      res.json(user);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete user
   */
  async deleteUser(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('User ID is required');
      }

      // Check if user is deleting their own account or has admin role
      if (id !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }

      const result = await this.authService.deleteUser(id);

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create API key
   */
  async createApiKey(req, res, next) {
    try {
      const { name, permissions, expiresIn } = req.body;

      if (!name) {
        throw new ValidationError('API key name is required');
      }

      // Create API key for the current user
      const apiKey = await this.apiKeyService.createApiKey(
        name,
        req.user.id,
        permissions || req.user.permissions,
        expiresIn
      );

      res.status(201).json(apiKey);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get API keys
   */
  async getApiKeys(req, res, next) {
    try {
      // Get API keys for the current user
      const apiKeys = await this.apiKeyService.getApiKeysByUserId(req.user.id);

      res.json(apiKeys);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete API key
   */
  async deleteApiKey(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('API key ID is required');
      }

      // Get API key
      const apiKey = await this.apiKeyService.getApiKeyById(id);

      // Check if API key belongs to the current user or user is admin
      if (apiKey.userId !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }

      const result = await this.apiKeyService.deleteApiKey(id);

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Refresh token
   *
   * This endpoint takes a refresh token and returns a new access token
   */
  async refreshToken(req, res, next) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        throw new ValidationError('Refresh token is required');
      }

      try {
        const result = await this.authService.refreshToken(refreshToken);

        // Log successful token refresh
        await this.authAuditService.logTokenRefresh({
          userId: result.user.id,
          username: result.user.username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          success: true
        });

        res.json(result);
      } catch (error) {
        // Log failed token refresh
        await this.authAuditService.logTokenRefresh({
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          success: false,
          reason: error.message
        });

        throw error;
      }
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AuthController();
