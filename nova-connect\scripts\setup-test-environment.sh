#!/bin/bash
# <PERSON><PERSON>t to set up a test environment for Google Cloud Marketplace integration

# Set variables
PROJECT_ID="novafuse-test"
CLUSTER_NAME="novafuse-test-cluster"
REGION="us-central1"
ZONE="us-central1-a"
SERVICE_ACCOUNT="novafuse-test-sa"

# Create a new project
echo "Creating project $PROJECT_ID..."
gcloud projects create $PROJECT_ID

# Set the project as the default
echo "Setting $PROJECT_ID as the default project..."
gcloud config set project $PROJECT_ID

# Enable necessary APIs
echo "Enabling necessary APIs..."
gcloud services enable container.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com
gcloud services enable iam.googleapis.com
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable cloudtrace.googleapis.com
gcloud services enable clouderrorreporting.googleapis.com
gcloud services enable cloudprofiler.googleapis.com

# Create a service account
echo "Creating service account $SERVICE_ACCOUNT..."
gcloud iam service-accounts create $SERVICE_ACCOUNT \
  --display-name="NovaFuse Test Service Account"

# Grant necessary permissions
echo "Granting necessary permissions..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SERVICE_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/container.admin"
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SERVICE_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/storage.admin"
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SERVICE_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/monitoring.admin"
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SERVICE_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/logging.admin"
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SERVICE_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/secretmanager.admin"

# Create a GKE cluster
echo "Creating GKE cluster $CLUSTER_NAME..."
gcloud container clusters create $CLUSTER_NAME \
  --zone $ZONE \
  --num-nodes 3 \
  --machine-type "e2-standard-2" \
  --service-account "$SERVICE_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com"

# Get credentials for the cluster
echo "Getting credentials for the cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE

# Create namespaces
echo "Creating namespaces..."
kubectl create namespace novafuse-test
kubectl create namespace novafuse-prod

# Set up MongoDB and Redis
echo "Setting up MongoDB and Redis..."
kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: mongodb
  namespace: novafuse-test
  labels:
    app: mongodb
spec:
  ports:
  - port: 27017
    targetPort: 27017
  selector:
    app: mongodb
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
  namespace: novafuse-test
  labels:
    app: mongodb
spec:
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:4.4
        ports:
        - containerPort: 27017
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
      volumes:
      - name: mongodb-data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: novafuse-test
  labels:
    app: redis
spec:
  ports:
  - port: 6379
    targetPort: 6379
  selector:
    app: redis
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: novafuse-test
  labels:
    app: redis
spec:
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:6-alpine
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        emptyDir: {}
EOF

echo "Test environment setup complete!"
