/**
 * NovaFuse Universal API Connector - Security Middleware
 * 
 * This module provides middleware for adding security headers.
 */

const { createLogger } = require('../utils/logger');

const logger = createLogger('security');

/**
 * Security headers middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function securityHeaders(req, res, next) {
  try {
    // Set security headers
    
    // Prevent MIME type sniffing
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // Prevent clickjacking
    res.setHeader('X-Frame-Options', 'DENY');
    
    // Enable XSS protection
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // Strict Transport Security (HSTS)
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    
    // Content Security Policy
    res.setHeader(
      'Content-Security-Policy',
      "default-src 'self'; script-src 'self'; object-src 'none'; img-src 'self'; media-src 'self'; frame-src 'none'; font-src 'self'; connect-src 'self'"
    );
    
    // Referrer Policy
    res.setHeader('Referrer-Policy', 'no-referrer-when-downgrade');
    
    // Feature Policy
    res.setHeader(
      'Feature-Policy',
      "camera 'none'; microphone 'none'; geolocation 'none'"
    );
    
    next();
  } catch (error) {
    logger.error('Security headers error:', { error });
    next(error);
  }
}

/**
 * CORS middleware
 * 
 * @param {Object} options - CORS options
 * @returns {Function} - The CORS middleware
 */
function cors(options = {}) {
  const allowedOrigins = options.origins || ['http://localhost:3000'];
  const allowedMethods = options.methods || ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
  const allowedHeaders = options.headers || ['Content-Type', 'Authorization', 'X-API-Key'];
  const maxAge = options.maxAge || 86400; // 24 hours
  
  return (req, res, next) => {
    try {
      const origin = req.headers.origin;
      
      // Check if origin is allowed
      if (origin && (allowedOrigins.includes(origin) || allowedOrigins.includes('*'))) {
        res.setHeader('Access-Control-Allow-Origin', origin);
      }
      
      // Set CORS headers
      res.setHeader('Access-Control-Allow-Methods', allowedMethods.join(', '));
      res.setHeader('Access-Control-Allow-Headers', allowedHeaders.join(', '));
      res.setHeader('Access-Control-Max-Age', maxAge.toString());
      
      // Handle preflight requests
      if (req.method === 'OPTIONS') {
        return res.status(204).end();
      }
      
      next();
    } catch (error) {
      logger.error('CORS error:', { error });
      next(error);
    }
  };
}

module.exports = {
  securityHeaders,
  cors
};
