name: NovaFuse Universal Platform Test Suite

on:
  push:
    branches: [ main, develop, gcp-marketplace-integration ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run linting
        run: npm run lint

  unit-tests:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run unit tests
        run: npm run test:unit
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: unit-test-results
          path: test-results/junit

  component-tests:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run component tests
        run: npm run test:components
      - name: Check coverage
        run: |
          COVERAGE=$(npm run test:coverage | grep -o 'Statements.*%' | grep -o '[0-9.]*')
          if (( $(echo "$COVERAGE < 80" | bc -l) )); then
            echo "Coverage below threshold: $COVERAGE%"
            exit 1
          fi
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: component-test-results
          path: test-results/junit

  api-tests:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run API tests
        run: npm run test:api
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: api-test-results
          path: test-results/junit

  security-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests, component-tests, api-tests]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Create security reports directory
        run: mkdir -p security-reports
      - name: Run npm audit
        run: npm audit --json > security-reports/npm-audit-results.json || true
      - name: Run security tests
        run: npm run test:security
      - name: Run OWASP ZAP scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:3000'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'
      - name: Run Semgrep scan
        uses: returntocorp/semgrep-action@v1
        with:
          config: p/owasp-top-ten
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: security-test-results
          path: |
            test-results/junit
            security-reports

  mutation-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests, component-tests]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run mutation tests on critical modules
        run: npm run test:mutation:compliance
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: mutation-test-results
          path: reports/mutation

  e2e-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests, component-tests, api-tests]
    strategy:
      matrix:
        test-group: [homepage, products, partner-empowerment, nova-concierge, authentication, compliance-audit-flow]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Build application
        run: npm run build
      - name: Start application
        run: npm start &
      - name: Wait for application to start
        run: sleep 10
      - name: Run E2E tests
        run: npx cypress run --spec "cypress/e2e/${{ matrix.test-group }}.cy.js"
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: e2e-test-results-${{ matrix.test-group }}
          path: cypress/videos
      - name: Upload screenshots (if any)
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: e2e-screenshots-${{ matrix.test-group }}
          path: cypress/screenshots

  compliance-tests:
    runs-on: ubuntu-latest
    needs: [security-tests]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Create compliance reports directory
        run: mkdir -p compliance-reports
      - name: Run compliance tests
        run: npm run test:compliance || true
      - name: Upload compliance reports
        uses: actions/upload-artifact@v3
        with:
          name: compliance-reports
          path: compliance-reports

  performance-tests:
    runs-on: ubuntu-latest
    needs: [compliance-tests]
    services:
      mongodb:
        image: mongo:5.0
        ports:
          - 27017:27017
      redis:
        image: redis:6.2-alpine
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Create performance reports directory
        run: mkdir -p performance-reports
      - name: Start API server
        run: npm run start:test &
        env:
          MONGODB_URI: mongodb://localhost:27017/novafuse-test
          REDIS_URL: redis://localhost:6379
          PORT: 3000
      - name: Wait for API server to start
        run: sleep 10
      - name: Run performance tests
        run: npm run test:performance || true
      - name: Upload performance reports
        uses: actions/upload-artifact@v3
        with:
          name: performance-reports
          path: performance-reports

  test-summary:
    runs-on: ubuntu-latest
    needs: [unit-tests, component-tests, api-tests, security-tests, mutation-tests, e2e-tests, compliance-tests, performance-tests]
    steps:
      - uses: actions/checkout@v3
      - name: Download all artifacts
        uses: actions/download-artifact@v3
      - name: Generate test summary
        run: |
          echo "# NovaFuse Universal Platform Test Results" > test-summary.md
          echo "## Unit Tests" >> test-summary.md
          cat unit-test-results/results.xml | grep -o 'tests="[0-9]*"' | head -1 >> test-summary.md
          echo "## Component Tests" >> test-summary.md
          cat component-test-results/results.xml | grep -o 'tests="[0-9]*"' | head -1 >> test-summary.md
          echo "## API Tests" >> test-summary.md
          cat api-test-results/results.xml | grep -o 'tests="[0-9]*"' | head -1 >> test-summary.md
          echo "## Security Tests" >> test-summary.md
          ls -la security-reports >> test-summary.md
          echo "## Compliance Tests" >> test-summary.md
          ls -la compliance-reports >> test-summary.md
          echo "## Performance Tests" >> test-summary.md
          ls -la performance-reports >> test-summary.md
          echo "## E2E Tests" >> test-summary.md
          find e2e-test-results-* -name "*.mp4" | wc -l >> test-summary.md
      - name: Generate master report
        run: node tools/generate-master-report.js || echo "Master report generation failed"
      - name: Upload test summary
        uses: actions/upload-artifact@v3
        with:
          name: test-summary
          path: |
            test-summary.md
            test-reports/master-report.html
