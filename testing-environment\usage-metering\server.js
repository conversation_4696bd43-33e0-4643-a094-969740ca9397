const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bodyParser = require('body-parser');
const winston = require('winston');

const app = express();
const port = process.env.PORT || 3000;

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/usage-metering', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  logger.info('Connected to MongoDB');
})
.catch((err) => {
  logger.error('Error connecting to MongoDB', { error: err.message });
});

// Define usage schema
const usageSchema = new mongoose.Schema({
  userId: { type: String, required: true },
  connectorId: { type: String, required: true },
  endpointId: { type: String, required: true },
  timestamp: { type: Date, default: Date.now },
  status: { type: String, enum: ['success', 'error'], default: 'success' },
  responseTime: { type: Number }
});

const Usage = mongoose.model('Usage', usageSchema);

// Define subscription schema
const subscriptionSchema = new mongoose.Schema({
  userId: { type: String, required: true },
  plan: { type: String, required: true },
  limits: {
    apiCalls: { type: Number, required: true },
    connectors: { type: Number, required: true }
  },
  startDate: { type: Date, default: Date.now },
  endDate: { type: Date },
  status: { type: String, enum: ['active', 'inactive', 'trial'], default: 'active' }
});

const Subscription = mongoose.model('Subscription', subscriptionSchema);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// API endpoints
app.post('/track', async (req, res) => {
  try {
    const { userId, connectorId, endpointId, timestamp, status, responseTime } = req.body;
    
    if (!userId || !connectorId || !endpointId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    const usage = new Usage({
      userId,
      connectorId,
      endpointId,
      timestamp: timestamp || Date.now(),
      status: status || 'success',
      responseTime
    });
    
    await usage.save();
    
    res.status(201).json({ success: true });
  } catch (err) {
    logger.error('Error tracking usage', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/usage', async (req, res) => {
  try {
    const { userId, startDate, endDate } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }
    
    const query = { userId };
    
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) {
        query.timestamp.$gte = new Date(startDate);
      }
      if (endDate) {
        query.timestamp.$lte = new Date(endDate);
      }
    }
    
    const usage = await Usage.find(query);
    
    // Group by connector and endpoint
    const groupedUsage = usage.reduce((acc, item) => {
      const key = `${item.connectorId}:${item.endpointId}`;
      if (!acc[key]) {
        acc[key] = {
          connectorId: item.connectorId,
          endpointId: item.endpointId,
          count: 0,
          successCount: 0,
          errorCount: 0
        };
      }
      
      acc[key].count++;
      if (item.status === 'success') {
        acc[key].successCount++;
      } else {
        acc[key].errorCount++;
      }
      
      return acc;
    }, {});
    
    res.json(Object.values(groupedUsage));
  } catch (err) {
    logger.error('Error fetching usage', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/usage/summary', async (req, res) => {
  try {
    const { userId, period } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }
    
    // Calculate date range based on period
    const endDate = new Date();
    let startDate = new Date();
    
    switch (period) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(startDate.getMonth() - 1); // Default to month
    }
    
    const usage = await Usage.find({
      userId,
      timestamp: { $gte: startDate, $lte: endDate }
    });
    
    // Get subscription
    const subscription = await Subscription.findOne({ userId, status: 'active' });
    
    // Calculate usage metrics
    const totalCalls = usage.length;
    const successCalls = usage.filter(item => item.status === 'success').length;
    const errorCalls = usage.filter(item => item.status === 'error').length;
    
    // Get unique connectors
    const uniqueConnectors = [...new Set(usage.map(item => item.connectorId))];
    
    // Calculate usage percentage
    let usagePercentage = 0;
    let remainingCalls = 0;
    
    if (subscription) {
      usagePercentage = (totalCalls / subscription.limits.apiCalls) * 100;
      remainingCalls = subscription.limits.apiCalls - totalCalls;
    }
    
    res.json({
      totalCalls,
      successCalls,
      errorCalls,
      uniqueConnectors: uniqueConnectors.length,
      subscription: subscription ? {
        plan: subscription.plan,
        apiCallsLimit: subscription.limits.apiCalls,
        connectorsLimit: subscription.limits.connectors,
        usagePercentage,
        remainingCalls
      } : null,
      period
    });
  } catch (err) {
    logger.error('Error fetching usage summary', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/subscriptions', async (req, res) => {
  try {
    const { userId, plan, limits, endDate } = req.body;
    
    if (!userId || !plan || !limits) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Check if user already has an active subscription
    const existingSubscription = await Subscription.findOne({ userId, status: 'active' });
    
    if (existingSubscription) {
      // Update existing subscription
      existingSubscription.plan = plan;
      existingSubscription.limits = limits;
      if (endDate) {
        existingSubscription.endDate = new Date(endDate);
      }
      
      await existingSubscription.save();
      
      res.json(existingSubscription);
    } else {
      // Create new subscription
      const subscription = new Subscription({
        userId,
        plan,
        limits,
        endDate: endDate ? new Date(endDate) : undefined
      });
      
      await subscription.save();
      
      res.status(201).json(subscription);
    }
  } catch (err) {
    logger.error('Error creating subscription', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/subscriptions/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const subscription = await Subscription.findOne({ userId, status: 'active' });
    
    if (!subscription) {
      return res.status(404).json({ error: 'No active subscription found' });
    }
    
    res.json(subscription);
  } catch (err) {
    logger.error('Error fetching subscription', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start the server
app.listen(port, () => {
  logger.info(`Usage Metering service running on port ${port}`);
});
