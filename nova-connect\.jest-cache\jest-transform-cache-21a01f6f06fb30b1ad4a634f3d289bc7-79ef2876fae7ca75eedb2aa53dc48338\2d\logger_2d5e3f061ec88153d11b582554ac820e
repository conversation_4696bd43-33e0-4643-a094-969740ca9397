0c7ebe2fc7ac02583409d840845eb04b
/**
 * NovaFuse Universal API Connector - Logger
 * 
 * This module provides logging functionality for the UAC.
 */

const winston = require('winston');
const {
  format
} = winston;

// Get log level from environment
const LOG_LEVEL = process.env.LOG_LEVEL || 'info';

// Create custom format
const customFormat = format.combine(format.timestamp({
  format: 'YYYY-MM-DD HH:mm:ss.SSS'
}), format.errors({
  stack: true
}), format.splat(), format.json());

// Create console transport
const consoleTransport = new winston.transports.Console({
  format: format.combine(format.colorize(), format.printf(({
    timestamp,
    level,
    message,
    module,
    ...meta
  }) => {
    const moduleStr = module ? `[${module}] ` : '';
    const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
    return `${timestamp} ${level}: ${moduleStr}${message}${metaStr}`;
  }))
});

// Create default logger
const defaultLogger = winston.createLogger({
  level: LOG_LEVEL,
  format: customFormat,
  defaultMeta: {
    service: 'nova-connect'
  },
  transports: [consoleTransport]
});

// Add file transport in production
if (process.env.NODE_ENV === 'production') {
  defaultLogger.add(new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    maxsize: 10485760,
    // 10MB
    maxFiles: 10
  }));
  defaultLogger.add(new winston.transports.File({
    filename: 'logs/combined.log',
    maxsize: 10485760,
    // 10MB
    maxFiles: 10
  }));
}

/**
 * Create a logger for a specific module
 * @param {string} module - Module name
 * @returns {winston.Logger} Logger instance
 */
function createLogger(module) {
  return defaultLogger.child({
    module
  });
}
module.exports = {
  createLogger,
  defaultLogger
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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