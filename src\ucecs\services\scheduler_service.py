"""
Scheduler Service for the Universal Compliance Evidence Collection System.

This module provides a service that runs in the background and executes
scheduled tasks when they are due.
"""

import os
import sys
import time
import logging
import threading
import datetime
from typing import Dict, Any, Optional, Callable

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.schedule_manager import ScheduleManager, TaskType

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SchedulerService:
    """
    Service that runs in the background and executes scheduled tasks.
    
    This class is responsible for checking for due tasks and executing them
    at the appropriate times.
    """
    
    def __init__(self, 
               schedule_manager: ScheduleManager,
               task_executors: Dict[TaskType, Callable[[Dict[str, Any]], Any]],
               check_interval: int = 60):
        """
        Initialize the Scheduler Service.
        
        Args:
            schedule_manager: The Schedule Manager to use
            task_executors: Dictionary mapping task types to executor functions
            check_interval: How often to check for due tasks (in seconds)
        """
        logger.info("Initializing Scheduler Service")
        
        self.schedule_manager = schedule_manager
        self.task_executors = task_executors
        self.check_interval = check_interval
        
        self.running = False
        self.thread = None
        
        logger.info("Scheduler Service initialized")
    
    def start(self) -> None:
        """Start the scheduler service."""
        if self.running:
            logger.warning("Scheduler Service is already running")
            return
        
        logger.info("Starting Scheduler Service")
        
        self.running = True
        self.thread = threading.Thread(target=self._run)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info("Scheduler Service started")
    
    def stop(self) -> None:
        """Stop the scheduler service."""
        if not self.running:
            logger.warning("Scheduler Service is not running")
            return
        
        logger.info("Stopping Scheduler Service")
        
        self.running = False
        if self.thread:
            self.thread.join(timeout=10)
        
        logger.info("Scheduler Service stopped")
    
    def _run(self) -> None:
        """Run the scheduler service."""
        while self.running:
            try:
                # Get due tasks
                due_tasks = self.schedule_manager.get_due_tasks()
                
                if due_tasks:
                    logger.info(f"Found {len(due_tasks)} due tasks")
                    
                    # Execute each due task
                    for task in due_tasks:
                        try:
                            task_id = task['id']
                            task_type = task['type']
                            
                            # Get the appropriate executor for the task type
                            executor = self.task_executors.get(TaskType(task_type))
                            
                            if executor:
                                # Execute the task
                                logger.info(f"Executing task: {task_id}")
                                self.schedule_manager.execute_task(task_id, executor)
                            else:
                                logger.warning(f"No executor found for task type: {task_type}")
                        
                        except Exception as e:
                            logger.error(f"Failed to execute task {task.get('id')}: {e}")
                
                # Sleep until the next check
                time.sleep(self.check_interval)
            
            except Exception as e:
                logger.error(f"Error in scheduler service: {e}")
                time.sleep(self.check_interval)
    
    def is_running(self) -> bool:
        """
        Check if the scheduler service is running.
        
        Returns:
            True if the service is running, False otherwise
        """
        return self.running
