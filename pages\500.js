import React from 'react';
import Link from 'next/link';
import Layout from '../components/Layout';

export default function Custom500() {
  return (
    <Layout title="Server Error">
      <div className="flex flex-col items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 text-center">
          <h1 className="text-4xl font-bold text-red-500">500 - Server Error</h1>
          <p className="mt-2 text-lg text-gray-300">
            An error occurred on the server. Please try again later.
          </p>
          <div className="mt-6">
            <Link
              href="/"
              className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400"
            >
              Return to Home
            </Link>
          </div>
        </div>
      </div>
    </Layout>
  );
}
