/**
 * CSDE Dashboard Routes
 *
 * This module provides API routes for the CSDE dashboard.
 */

const express = require('express');
const { performance } = require('perf_hooks');
const CSEDConnector = require('../connectors/csde-connector');
const CSEDNovaVisionIntegration = require('../integrations/csde-novavision-integration');

// Create router
const router = express.Router();

// Create CSDE connector
let csdeConnector = null;

// Create CSDE NovaVision integration
let csdeNovaVisionIntegration = null;

/**
 * Initialize the CSDE connector and NovaVision integration
 * @param {Object} options - Initialization options
 */
function initialize(options = {}) {
  const logger = options.logger || console;

  logger.info('Initializing CSDE dashboard routes');

  // Create CSDE connector if not already created
  if (!csdeConnector) {
    csdeConnector = new CSEDConnector({
      id: 'csde-connector',
      name: 'CSDE Connector',
      description: 'Connector for the Cyber-Safety Domain Engine (CSDE)',
      csdeApiUrl: process.env.CSDE_API_URL || 'http://localhost:3010',
      enableCaching: process.env.ENABLE_CACHING !== 'false',
      enableMetrics: process.env.ENABLE_METRICS !== 'false',
      cacheSize: parseInt(process.env.CACHE_SIZE || '1000', 10),
      domain: process.env.DOMAIN || 'security',
      logger
    });

    logger.info('CSDE connector created');
  }

  // Create CSDE NovaVision integration if not already created
  if (!csdeNovaVisionIntegration) {
    csdeNovaVisionIntegration = new CSEDNovaVisionIntegration({
      csdeConnector,
      theme: process.env.THEME || 'cyber-safety',
      responsive: process.env.RESPONSIVE !== 'false',
      accessibilityLevel: process.env.ACCESSIBILITY_LEVEL || 'AA',
      logger
    });

    logger.info('CSDE NovaVision integration created');
  }
}

/**
 * Get CSDE dashboard
 */
router.get('/', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeNovaVisionIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Render dashboard
    const renderedDashboard = csdeNovaVisionIntegration.renderCSDEDashboard();

    // Return rendered dashboard
    res.json({
      success: true,
      dashboard: renderedDashboard
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting CSDE dashboard', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get CSDE dashboard schema
 */
router.get('/schema', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeNovaVisionIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Generate dashboard schema
    const dashboardSchema = csdeNovaVisionIntegration.generateCSDEDashboardSchema();

    // Return dashboard schema
    res.json({
      success: true,
      schema: dashboardSchema
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting CSDE dashboard schema', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get CSDE dashboard data
 */
router.get('/data', async (req, res) => {
  try {
    const startTime = performance.now();

    // Initialize if not already initialized
    if (!csdeConnector || !csdeNovaVisionIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get dashboard data
    const dashboardData = csdeNovaVisionIntegration.getDashboardData();

    // Get health
    const health = await csdeConnector.getHealth();

    // Get metrics
    const metrics = await csdeConnector.getMetrics();

    // Calculate response time
    const endTime = performance.now();
    const responseTime = endTime - startTime;

    // Return dashboard data with metrics and health
    res.json({
      success: true,
      data: {
        ...dashboardData,
        metrics,
        health,
        // Add real-time visualization data for Trinity CSDE
        trinityVisualization: {
          csdeValue: dashboardData.csdeValue || 3142.0,
          performanceFactor: dashboardData.performanceFactor || 3142,
          principle1882: true,
          components: [
            {
              name: 'Governance',
              type: 'father',
              score: dashboardData.governanceScore || 0.85,
              qualityScore: dashboardData.governanceQuality || 0.92,
              normalizedScore: dashboardData.governanceNormalized || 0.33
            },
            {
              name: 'Detection',
              type: 'son',
              score: dashboardData.detectionScore || 0.78,
              qualityScore: dashboardData.detectionQuality || 0.88,
              normalizedScore: dashboardData.detectionNormalized || 0.33
            },
            {
              name: 'Response',
              type: 'spirit',
              score: dashboardData.responseScore || 0.82,
              qualityScore: dashboardData.responseQuality || 0.90,
              normalizedScore: dashboardData.responseNormalized || 0.34
            }
          ],
          calculatedAt: dashboardData.calculatedAt || new Date().toISOString()
        },
        // Add offline processing data
        offlineProcessing: {
          queueSize: dashboardData.offlineQueueSize || 0,
          history: dashboardData.offlineHistory || [],
          processingTime: dashboardData.offlineProcessingTime || 0
        },
        // Add cross-domain prediction data
        crossDomainPrediction: {
          totalPredictions: dashboardData.totalPredictions || 0,
          averageConfidence: dashboardData.averageConfidence || 0.85,
          predictions: dashboardData.predictions || [],
          domainMappings: dashboardData.domainMappings || [],
          featureImportance: {
            tensorFusion: 0.35,
            adaptiveLearning: 0.25,
            circularTrust: 0.15,
            patternMatching: 0.15,
            semanticAnalysis: 0.10
          }
        },
        // Add compliance mapping data
        complianceMapping: {
          totalMappings: dashboardData.totalMappings || 0,
          averageConfidence: dashboardData.mappingConfidence || 0.88,
          frameworks: dashboardData.frameworks || [],
          mappingDistribution: {
            predefined: 65,
            semantic: 25,
            machineLearning: 10
          }
        }
      },
      metadata: {
        responseTime,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting CSDE dashboard data', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get CSDE configuration form
 */
router.get('/config', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeNovaVisionIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get current configuration
    const currentConfig = {
      csdeApiUrl: csdeConnector.options.csdeApiUrl,
      domain: csdeConnector.options.domain,
      enableCaching: csdeConnector.options.enableCaching,
      cacheSize: csdeConnector.options.cacheSize,
      enableMetrics: csdeConnector.options.enableMetrics,
      batchSize: csdeConnector.options.batchSize || 10
    };

    // Render configuration form
    const renderedForm = csdeNovaVisionIntegration.renderCSDEConfigForm(currentConfig);

    // Return rendered form
    res.json({
      success: true,
      form: renderedForm
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting CSDE configuration form', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Update CSDE configuration
 */
router.post('/config', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeNovaVisionIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get configuration from request body
    const config = req.body;

    // Validate configuration
    if (!config.csdeApiUrl) {
      throw new Error('CSDE API URL is required');
    }

    // Update connector configuration
    csdeConnector.options.csdeApiUrl = config.csdeApiUrl;
    csdeConnector.options.domain = config.domain || 'security';
    csdeConnector.options.enableCaching = config.enableCaching !== false;
    csdeConnector.options.cacheSize = parseInt(config.cacheSize || '1000', 10);
    csdeConnector.options.enableMetrics = config.enableMetrics !== false;
    csdeConnector.options.batchSize = parseInt(config.batchSize || '10', 10);

    // Update CSDE integration
    if (csdeConnector.csdeIntegration) {
      csdeConnector.csdeIntegration.options.csdeApiUrl = config.csdeApiUrl;
      csdeConnector.csdeIntegration.options.enableCaching = config.enableCaching !== false;
      csdeConnector.csdeIntegration.options.cacheSize = parseInt(config.cacheSize || '1000', 10);
      csdeConnector.csdeIntegration.options.enableMetrics = config.enableMetrics !== false;
    }

    // Return success
    res.json({
      success: true,
      message: 'Configuration updated successfully'
    });
  } catch (error) {
    req.app.locals.logger.error('Error updating CSDE configuration', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Clear CSDE cache
 */
router.post('/cache/clear', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeNovaVisionIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Clear cache
    await csdeConnector.clearCache();

    // Return success
    res.json({
      success: true,
      message: 'Cache cleared successfully'
    });
  } catch (error) {
    req.app.locals.logger.error('Error clearing CSDE cache', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Reset CSDE metrics
 */
router.post('/metrics/reset', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeNovaVisionIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Reset metrics
    await csdeConnector.resetMetrics();

    // Return success
    res.json({
      success: true,
      message: 'Metrics reset successfully'
    });
  } catch (error) {
    req.app.locals.logger.error('Error resetting CSDE metrics', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * Get CSDE health
 */
router.get('/health', async (req, res) => {
  try {
    // Initialize if not already initialized
    if (!csdeConnector || !csdeNovaVisionIntegration) {
      initialize({ logger: req.app.locals.logger });
    }

    // Get health
    const health = await csdeConnector.getHealth();

    // Return health
    res.json({
      success: true,
      health
    });
  } catch (error) {
    req.app.locals.logger.error('Error getting CSDE health', { error: error.message });

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = {
  router,
  initialize
};
