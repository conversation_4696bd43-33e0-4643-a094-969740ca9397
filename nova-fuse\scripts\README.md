# NovaFuse Migration Scripts

This directory contains scripts for migrating code from the existing NovaFuse repositories to the new repository structure.

## Scripts

### migrate-code-v2.ps1

This is the main migration script that automates the process of moving code from the existing repositories to the new structure. It uses the `migration-config.json` file for configuration.

#### Usage

```powershell
.\migrate-code-v2.ps1
```

### verify-migration.ps1

This script verifies that the migration was successful by checking for expected files and directories.

#### Usage

```powershell
.\verify-migration.ps1
```

### migration-config.json

This is the configuration file for the migration scripts. It contains the following settings:

- `sourceRoot`: The root directory of the source code
- `destinationRoot`: The root directory where the new repositories are located
- `logFile`: The path to the log file
- `migrationMappings`: An array of migration mappings, each with the following properties:
  - `name`: The name of the migration
  - `sourcePath`: The path to the source directory (relative to `sourceRoot`)
  - `destinationPath`: The path to the destination directory (relative to `destinationRoot`)
  - `filter`: The file filter to use when copying files
  - `importPathReplacements`: A dictionary of import path replacements to apply to JavaScript/TypeScript files

## Migration Process

The migration process consists of the following steps:

1. Configure the migration by editing the `migration-config.json` file
2. Run the migration script: `.\migrate-code-v2.ps1`
3. Verify the migration: `.\verify-migration.ps1`
4. Fix any issues that were found during verification
5. Run the verification script again to ensure all issues are resolved

## Logs

The migration script creates a log file at the path specified in the `logFile` setting in the configuration file. The verification script creates a log file at `verification-log.txt` in the same directory as the script.

## Troubleshooting

If you encounter any issues during the migration process, check the log files for error messages. Common issues include:

- Source path does not exist
- Destination path does not exist
- No files found in source path
- Missing files in destination path

If you need to modify the migration process, edit the `migration-config.json` file to add, remove, or modify migration mappings.
