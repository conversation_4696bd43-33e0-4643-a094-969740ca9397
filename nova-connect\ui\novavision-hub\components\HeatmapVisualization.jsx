/**
 * HeatmapVisualization Component
 * 
 * A component for visualizing data as a heatmap.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme';

/**
 * HeatmapVisualization component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.data - Heatmap data
 * @param {Array} props.data[].x - X-axis labels
 * @param {Array} props.data[].y - Y-axis labels
 * @param {Array} props.data[].values - 2D array of values
 * @param {Object} [props.options] - Visualization options
 * @param {string} [props.options.title] - Heatmap title
 * @param {string} [props.options.xAxisLabel] - X-axis label
 * @param {string} [props.options.yAxisLabel] - Y-axis label
 * @param {Array} [props.options.colorScale] - Color scale for the heatmap
 * @param {boolean} [props.options.showLegend=true] - Whether to show the legend
 * @param {boolean} [props.options.showTooltip=true] - Whether to show tooltips
 * @param {boolean} [props.options.showGrid=true] - Whether to show grid lines
 * @param {boolean} [props.options.showLabels=true] - Whether to show cell labels
 * @param {Function} [props.options.formatValue] - Function to format cell values
 * @param {Function} [props.onCellClick] - Function to call when a cell is clicked
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} HeatmapVisualization component
 */
const HeatmapVisualization = ({
  data,
  options = {},
  onCellClick,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const containerRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [tooltip, setTooltip] = useState({ visible: false, x: 0, y: 0, content: '' });
  
  // Default options
  const {
    title,
    xAxisLabel,
    yAxisLabel,
    colorScale = [
      theme.colors.primary,
      theme.colors.primaryLight,
      theme.colors.background,
      theme.colors.warningLight,
      theme.colors.warning
    ],
    showLegend = true,
    showTooltip = true,
    showGrid = true,
    showLabels = true,
    formatValue = (value) => value.toFixed(1)
  } = options;
  
  // Update dimensions when container size changes
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateDimensions = () => {
      const { width, height } = containerRef.current.getBoundingClientRect();
      setDimensions({ width, height });
    };
    
    // Initial update
    updateDimensions();
    
    // Add resize observer
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(containerRef.current);
    
    // Cleanup
    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);
  
  // Calculate heatmap dimensions
  const margin = { top: 40, right: 40, bottom: 60, left: 60 };
  const legendHeight = showLegend ? 40 : 0;
  const width = dimensions.width - margin.left - margin.right;
  const height = dimensions.height - margin.top - margin.bottom - legendHeight;
  
  // Check if data is valid
  if (!data || !data.x || !data.y || !data.values) {
    return (
      <div
        ref={containerRef}
        className={`flex items-center justify-center bg-surface border border-divider rounded-md ${className}`}
        style={{ minHeight: '300px', ...style }}
        data-testid="heatmap-visualization-empty"
      >
        <p className="text-textSecondary">No data available</p>
      </div>
    );
  }
  
  // Get min and max values
  const flatValues = data.values.flat();
  const minValue = Math.min(...flatValues);
  const maxValue = Math.max(...flatValues);
  
  // Get color for a value
  const getColor = (value) => {
    if (value === null || value === undefined) {
      return theme.colors.divider;
    }
    
    // Normalize value to 0-1 range
    const normalizedValue = (value - minValue) / (maxValue - minValue);
    
    // Get color from scale
    const colorIndex = Math.min(
      Math.floor(normalizedValue * (colorScale.length - 1)),
      colorScale.length - 2
    );
    
    // Interpolate between colors
    const colorFraction = (normalizedValue * (colorScale.length - 1)) - colorIndex;
    
    // Get colors
    const color1 = colorScale[colorIndex];
    const color2 = colorScale[colorIndex + 1];
    
    // Parse colors
    const parseColor = (color) => {
      const hex = color.replace('#', '');
      return {
        r: parseInt(hex.substring(0, 2), 16),
        g: parseInt(hex.substring(2, 4), 16),
        b: parseInt(hex.substring(4, 6), 16)
      };
    };
    
    const c1 = parseColor(color1);
    const c2 = parseColor(color2);
    
    // Interpolate
    const r = Math.round(c1.r + (c2.r - c1.r) * colorFraction);
    const g = Math.round(c1.g + (c2.g - c1.g) * colorFraction);
    const b = Math.round(c1.b + (c2.b - c1.b) * colorFraction);
    
    return `rgb(${r}, ${g}, ${b})`;
  };
  
  // Calculate cell dimensions
  const cellWidth = width / data.x.length;
  const cellHeight = height / data.y.length;
  
  // Handle cell click
  const handleCellClick = (x, y, value) => {
    if (onCellClick) {
      onCellClick({ x: data.x[x], y: data.y[y], value });
    }
  };
  
  // Handle cell mouse enter
  const handleCellMouseEnter = (event, x, y, value) => {
    if (!showTooltip) return;
    
    const rect = event.target.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    
    setTooltip({
      visible: true,
      x: rect.left - containerRect.left + rect.width / 2,
      y: rect.top - containerRect.top,
      content: `${data.x[x]}, ${data.y[y]}: ${formatValue(value)}`
    });
  };
  
  // Handle cell mouse leave
  const handleCellMouseLeave = () => {
    if (!showTooltip) return;
    
    setTooltip({ ...tooltip, visible: false });
  };
  
  return (
    <div
      ref={containerRef}
      className={`relative bg-background border border-divider rounded-md overflow-hidden ${className}`}
      style={{ minHeight: '300px', ...style }}
      data-testid="heatmap-visualization"
    >
      {/* Title */}
      {title && (
        <div className="absolute top-2 left-0 right-0 text-center">
          <h3 className="text-lg font-medium text-textPrimary">{title}</h3>
        </div>
      )}
      
      {/* SVG Container */}
      <svg
        width={dimensions.width}
        height={dimensions.height}
        className="font-sans text-textPrimary"
      >
        {/* X-axis labels */}
        {data.x.map((label, i) => (
          <text
            key={`x-${i}`}
            x={margin.left + (i + 0.5) * cellWidth}
            y={dimensions.height - margin.bottom + 20}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-xs fill-current text-textSecondary"
          >
            {label}
          </text>
        ))}
        
        {/* Y-axis labels */}
        {data.y.map((label, i) => (
          <text
            key={`y-${i}`}
            x={margin.left - 10}
            y={margin.top + (i + 0.5) * cellHeight}
            textAnchor="end"
            dominantBaseline="middle"
            className="text-xs fill-current text-textSecondary"
          >
            {label}
          </text>
        ))}
        
        {/* X-axis label */}
        {xAxisLabel && (
          <text
            x={margin.left + width / 2}
            y={dimensions.height - 10}
            textAnchor="middle"
            className="text-sm fill-current text-textPrimary font-medium"
          >
            {xAxisLabel}
          </text>
        )}
        
        {/* Y-axis label */}
        {yAxisLabel && (
          <text
            x={10}
            y={margin.top + height / 2}
            textAnchor="middle"
            dominantBaseline="middle"
            transform={`rotate(-90, 10, ${margin.top + height / 2})`}
            className="text-sm fill-current text-textPrimary font-medium"
          >
            {yAxisLabel}
          </text>
        )}
        
        {/* Heatmap cells */}
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          {data.values.map((row, y) => (
            <g key={`row-${y}`}>
              {row.map((value, x) => (
                <rect
                  key={`cell-${x}-${y}`}
                  x={x * cellWidth}
                  y={y * cellHeight}
                  width={cellWidth}
                  height={cellHeight}
                  fill={getColor(value)}
                  stroke={showGrid ? theme.colors.divider : 'none'}
                  strokeWidth={1}
                  onClick={() => handleCellClick(x, y, value)}
                  onMouseEnter={(e) => handleCellMouseEnter(e, x, y, value)}
                  onMouseLeave={handleCellMouseLeave}
                  className="cursor-pointer transition-colors duration-200 hover:opacity-80"
                  data-testid={`heatmap-cell-${x}-${y}`}
                />
              ))}
            </g>
          ))}
          
          {/* Cell labels */}
          {showLabels && data.values.map((row, y) => (
            <g key={`labels-${y}`}>
              {row.map((value, x) => (
                <text
                  key={`label-${x}-${y}`}
                  x={(x + 0.5) * cellWidth}
                  y={(y + 0.5) * cellHeight}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className="text-xs pointer-events-none fill-current"
                  style={{
                    fill: value > (maxValue - minValue) * 0.7 + minValue ? 'white' : 'black'
                  }}
                >
                  {formatValue(value)}
                </text>
              ))}
            </g>
          ))}
        </g>
        
        {/* Legend */}
        {showLegend && (
          <g transform={`translate(${margin.left}, ${dimensions.height - margin.bottom + 40})`}>
            {/* Legend gradient */}
            <defs>
              <linearGradient id="heatmap-legend-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                {colorScale.map((color, i) => (
                  <stop
                    key={`stop-${i}`}
                    offset={`${(i / (colorScale.length - 1)) * 100}%`}
                    stopColor={color}
                  />
                ))}
              </linearGradient>
            </defs>
            
            {/* Legend bar */}
            <rect
              x={0}
              y={0}
              width={width}
              height={10}
              fill="url(#heatmap-legend-gradient)"
              rx={2}
              ry={2}
            />
            
            {/* Legend labels */}
            <text
              x={0}
              y={25}
              textAnchor="start"
              className="text-xs fill-current text-textSecondary"
            >
              {formatValue(minValue)}
            </text>
            
            <text
              x={width / 2}
              y={25}
              textAnchor="middle"
              className="text-xs fill-current text-textSecondary"
            >
              {formatValue((maxValue + minValue) / 2)}
            </text>
            
            <text
              x={width}
              y={25}
              textAnchor="end"
              className="text-xs fill-current text-textSecondary"
            >
              {formatValue(maxValue)}
            </text>
          </g>
        )}
      </svg>
      
      {/* Tooltip */}
      {tooltip.visible && (
        <div
          className="absolute bg-surface text-textPrimary px-2 py-1 rounded shadow-md text-xs pointer-events-none z-10 border border-divider"
          style={{
            left: tooltip.x,
            top: tooltip.y - 30,
            transform: 'translateX(-50%)'
          }}
          data-testid="heatmap-tooltip"
        >
          {tooltip.content}
        </div>
      )}
    </div>
  );
};

HeatmapVisualization.propTypes = {
  data: PropTypes.shape({
    x: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])).isRequired,
    y: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])).isRequired,
    values: PropTypes.arrayOf(PropTypes.arrayOf(PropTypes.number)).isRequired
  }),
  options: PropTypes.shape({
    title: PropTypes.string,
    xAxisLabel: PropTypes.string,
    yAxisLabel: PropTypes.string,
    colorScale: PropTypes.arrayOf(PropTypes.string),
    showLegend: PropTypes.bool,
    showTooltip: PropTypes.bool,
    showGrid: PropTypes.bool,
    showLabels: PropTypes.bool,
    formatValue: PropTypes.func
  }),
  onCellClick: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default HeatmapVisualization;
