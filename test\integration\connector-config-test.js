/**
 * NovaFuse Universal API Connector - Connector Configuration Integration Tests
 * 
 * This module tests the connector configuration service.
 */

const {
  createTestSuite,
  assertTrue,
  assertFalse,
  assertEqual,
  assertNotEqual,
  assertDefined,
  assertNull,
  assertNotNull,
  assertThrows,
  assertDoesNotThrow
} = require('./test-framework');

const connectorRegistryService = require('../../src/connectors/services/connector-registry');
const connectorConfigService = require('../../src/connectors/services/connector-config');

// Create test suite
const suite = createTestSuite('Connector Configuration Integration Tests');

// Test data
let testConnector = null;
let testConfig = null;

// Setup and teardown
suite.beforeAll(async () => {
  // Clear any existing connectors and configs
  connectorRegistryService.connectors.clear();
  connectorConfigService.configs.clear();
  
  // Create a test connector
  testConnector = await connectorRegistryService.createConnector({
    name: 'Test Connector',
    description: 'A test connector for integration testing',
    version: '1.0.0',
    type: 'source',
    status: 'draft',
    configSchema: {
      type: 'object',
      properties: {
        apiKey: { type: 'string' },
        endpoint: { type: 'string' }
      },
      required: ['apiKey']
    }
  });
});

suite.afterAll(async () => {
  // Clean up
  connectorRegistryService.connectors.clear();
  connectorConfigService.configs.clear();
});

// Tests
suite.test('should create a configuration', async () => {
  // Create configuration
  testConfig = await connectorConfigService.createConfiguration({
    name: 'Test Configuration',
    description: 'A test configuration for integration testing',
    connectorId: testConnector.id,
    values: {
      apiKey: 'test-api-key',
      endpoint: 'https://api.example.com'
    }
  });
  
  // Assertions
  assertNotNull(testConfig, 'Configuration should not be null');
  assertDefined(testConfig.id, 'Configuration ID should be defined');
  assertEqual(testConfig.name, 'Test Configuration', 'Configuration name should match');
  assertEqual(testConfig.description, 'A test configuration for integration testing', 'Configuration description should match');
  assertEqual(testConfig.connectorId, testConnector.id, 'Configuration connector ID should match');
  assertEqual(testConfig.values.apiKey, 'test-api-key', 'Configuration API key should match');
  assertEqual(testConfig.values.endpoint, 'https://api.example.com', 'Configuration endpoint should match');
});

suite.test('should get a configuration by ID', async () => {
  // Get configuration
  const config = await connectorConfigService.getConfiguration(testConfig.id);
  
  // Assertions
  assertNotNull(config, 'Configuration should not be null');
  assertEqual(config.id, testConfig.id, 'Configuration ID should match');
  assertEqual(config.name, testConfig.name, 'Configuration name should match');
});

suite.test('should get all configurations', async () => {
  // Get all configurations
  const configs = await connectorConfigService.getAllConfigurations();
  
  // Assertions
  assertNotNull(configs, 'Configurations should not be null');
  assertTrue(Array.isArray(configs), 'Configurations should be an array');
  assertTrue(configs.length > 0, 'Configurations should not be empty');
  
  // Find our test configuration
  const config = configs.find(c => c.id === testConfig.id);
  assertNotNull(config, 'Test configuration should be in the list');
});

suite.test('should get configurations for a connector', async () => {
  // Get configurations for connector
  const configs = await connectorConfigService.getConfigurationsForConnector(testConnector.id);
  
  // Assertions
  assertNotNull(configs, 'Configurations should not be null');
  assertTrue(Array.isArray(configs), 'Configurations should be an array');
  assertTrue(configs.length > 0, 'Configurations should not be empty');
  
  // Find our test configuration
  const config = configs.find(c => c.id === testConfig.id);
  assertNotNull(config, 'Test configuration should be in the list');
});

suite.test('should update a configuration', async () => {
  // Update configuration
  const updatedConfig = await connectorConfigService.updateConfiguration(testConfig.id, {
    description: 'An updated test configuration',
    values: {
      apiKey: 'updated-api-key',
      endpoint: 'https://api.example.com/v2'
    }
  });
  
  // Assertions
  assertNotNull(updatedConfig, 'Updated configuration should not be null');
  assertEqual(updatedConfig.id, testConfig.id, 'Configuration ID should not change');
  assertEqual(updatedConfig.description, 'An updated test configuration', 'Configuration description should be updated');
  assertEqual(updatedConfig.values.apiKey, 'updated-api-key', 'Configuration API key should be updated');
  assertEqual(updatedConfig.values.endpoint, 'https://api.example.com/v2', 'Configuration endpoint should be updated');
  
  // Update our reference
  testConfig = updatedConfig;
});

suite.test('should validate configuration against schema', async () => {
  // Try to create an invalid configuration (missing required field)
  try {
    await connectorConfigService.createConfiguration({
      name: 'Invalid Configuration',
      description: 'An invalid configuration missing required fields',
      connectorId: testConnector.id,
      values: {
        endpoint: 'https://api.example.com'
      }
    });
    throw new Error('Should have thrown a validation error');
  } catch (error) {
    assertTrue(error.message.includes('apiKey is required'), 'Error should indicate missing required field');
  }
});

suite.test('should delete a configuration', async () => {
  // Delete configuration
  const result = await connectorConfigService.deleteConfiguration(testConfig.id);
  
  // Assertions
  assertTrue(result, 'Delete should return true');
  
  // Try to get the deleted configuration
  try {
    await connectorConfigService.getConfiguration(testConfig.id);
    throw new Error('Should have thrown an error');
  } catch (error) {
    assertTrue(error.message.includes('not found'), 'Error should indicate configuration not found');
  }
});

// Run the test suite
async function runTests() {
  const results = await suite.run();
  
  if (results.failed > 0) {
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  suite,
  runTests
};
