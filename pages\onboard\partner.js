import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import AgreementViewer from '../../components/AgreementViewer';
import ConsentForm from '../../components/ConsentForm';

// In a real implementation, this would be fetched from an API or CMS
const PARTNER_AGREEMENT = {
  title: "NovaFuse Pioneer Partner Program Agreement",
  version: "1.0",
  date: "April 23, 2025",
  fileName: "novafuse-pioneer-partner-agreement-v1.0.pdf",
  content: (
    <>
      <h1>NovaFuse Pioneer Partner Program Agreement</h1>
      <h2>Empowering Cyber-Safety Coaches in the Compliance Economy</h2>

      <h3>1. Preamble</h3>
      <p>This Pioneer Partner Program Agreement ("Agreement") is made by and between NovaGRC, Inc. d/b/a NovaFuse ("NovaFuse") and the Partner ("Partner") for participation in the Pioneer Partner Program.</p>

      <p>This Agreement outlines terms governing revenue share, implementation fees, partner obligations, certification requirements, and the shared commitment to the Cultivation Charter, for partners accepted into the first 93-partner cohort of the NovaFuse ecosystem.</p>

      <h3>2. Definitions</h3>
      <p><strong>"Cultivation Charter"</strong>: NovaFuse's guiding principles emphasizing partnership, growth, and ethical practices in compliance.</p>

      <p><strong>"Cyber-Safety Coach"</strong>: The role Partners assume in guiding clients through compliance challenges using NovaFuse's platform.</p>

      <p><strong>"Implementation Fee"</strong>: The value-based fee of 18% of traditional implementation costs charged to end clients.</p>

      <p><strong>"NovaConnect UAC"</strong>: NovaFuse's Universal API Connector technology that enables seamless integration between compliance systems.</p>

      <p><strong>"Partner Portal"</strong>: The dedicated interface through which Partners manage their NovaFuse relationship.</p>

      <p><strong>"Revenue Share"</strong>: The 82% of revenue allocated to Partners from client engagements.</p>

      <h3>3. Pioneer Partner Status & Eligibility</h3>
      <p>To qualify and maintain Pioneer Partner status, Partner must:</p>

      <p><strong>Certification Requirements</strong>: Complete NovaFuse's certification program including:</p>
      <ul>
        <li>Technical proficiency with the NovaConnect UAC</li>
        <li>Mastery of NovaFuse's compliance methodology</li>
        <li>Demonstrated expertise in applicable regulatory frameworks</li>
        <li>Commitment to the Cyber-Safety Coach approach</li>
      </ul>

      <p><strong>Exclusivity Commitment</strong>: Partner agrees to promote NovaFuse as their primary compliance automation platform and not to represent directly competing platforms.</p>

      <p><strong>Minimum Performance Standards</strong>: Achieve and maintain:</p>
      <ul>
        <li>At least 3 successful client implementations within the first 12 months</li>
        <li>Minimum of $100,000 in annual recurring revenue by end of Year 2</li>
        <li>Customer satisfaction rating of 4.5/5 or higher</li>
      </ul>

      <p><strong>Cultivation Charter Adherence</strong>: Demonstrate ongoing commitment to NovaFuse's Cultivation Charter principles in all client engagements.</p>

      <p>NovaFuse reserves the right to review and recertify Partners annually to ensure continued compliance with these requirements.</p>

      {/* This is just a sample - the full agreement would continue here */}
      <p>... [Full agreement content continues] ...</p>
    </>
  )
};

const PartnerOnboarding = () => {
  const router = useRouter();
  const [isAgreementRead, setIsAgreementRead] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    userName: '',
    userEmail: '',
    userTitle: '',
    companyName: ''
  });

  // In a real implementation, this would be fetched from the user's session
  const userId = 'temp-user-id';

  const handleConsent = async () => {
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/consent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userName: formData.userName,
          userEmail: formData.userEmail,
          userTitle: formData.userTitle,
          companyName: formData.companyName,
          agreementType: 'partner',
          agreementVersion: PARTNER_AGREEMENT.version,
          agreementTitle: PARTNER_AGREEMENT.title
        }),
      });

      const data = await response.json();

      if (data.success) {
        // In a real implementation, you would store the consent in the user's session
        // and redirect to the next step in the onboarding process
        router.push('/partner-signup?agreed=true');
      } else {
        // Handle error
        console.error('Failed to record consent:', data.message);
        alert('There was an error recording your consent. Please try again.');
      }
    } catch (error) {
      console.error('Error:', error);
      alert('There was an error recording your consent. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <>
      <Head>
        <title>Pioneer Partner Agreement | NovaFuse</title>
        <meta name="description" content="Review and accept the NovaFuse Pioneer Partner Program Agreement" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        <header className="bg-blue-900 text-white py-4">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center">
              <Link href="/" className="text-2xl font-bold">
                NovaFuse
              </Link>
              <Link href="/partner-signup" className="text-sm hover:underline">
                Back to Signup
              </Link>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-center mb-4">Pioneer Partner Agreement</h1>
            <p className="text-center text-gray-400 mb-8">From 1882 to 18/82: Revolutionizing Partnership Economics</p>

            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">Your Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="userName"
                    value={formData.userName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="userEmail"
                    value={formData.userEmail}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Job Title
                  </label>
                  <input
                    type="text"
                    name="userTitle"
                    value={formData.userTitle}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name
                  </label>
                  <input
                    type="text"
                    name="companyName"
                    value={formData.companyName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="mb-8">
              <AgreementViewer
                agreementTitle={PARTNER_AGREEMENT.title}
                agreementContent={PARTNER_AGREEMENT.content}
                agreementVersion={PARTNER_AGREEMENT.version}
                agreementDate={PARTNER_AGREEMENT.date}
                downloadFileName={PARTNER_AGREEMENT.fileName}
                onFullyScrolled={() => setIsAgreementRead(true)}
              />
            </div>

            <ConsentForm
              agreementTitle={PARTNER_AGREEMENT.title}
              agreementVersion={PARTNER_AGREEMENT.version}
              onConsent={handleConsent}
              isAgreementRead={isAgreementRead}
              isSubmitting={isSubmitting}
            />
          </div>
        </main>

        <footer className="bg-gray-100 py-6 mt-12">
          <div className="container mx-auto px-4 text-center text-sm text-gray-600">
            <p>© {new Date().getFullYear()} NovaGRC, Inc. d/b/a NovaFuse. All rights reserved.</p>
            <p className="mt-2">
              <Link href="/privacy-policy" className="text-blue-600 hover:underline mx-2">
                Privacy Policy
              </Link>
              <Link href="/terms-of-service" className="text-blue-600 hover:underline mx-2">
                Terms of Service
              </Link>
            </p>
          </div>
        </footer>
      </div>
    </>
  );
};

export default PartnerOnboarding;
