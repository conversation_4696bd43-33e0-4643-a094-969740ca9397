"""
Test utilities for the Universal Compliance Testing Framework.

This module provides utility functions for working with compliance tests.
"""

import json
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_test_definition(file_path: str) -> Dict[str, Any]:
    """
    Load a test definition from a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        The test definition
        
    Raises:
        FileNotFoundError: If the file does not exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            test_def = json.load(f)
        
        logger.info(f"Loaded test definition from {file_path}")
        return test_def
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in file: {file_path}")
        raise

def save_test_definition(test_def: Dict[str, Any], file_path: str) -> None:
    """
    Save a test definition to a JSON file.
    
    Args:
        test_def: The test definition
        file_path: Path to the output JSON file
        
    Raises:
        IOError: If the file cannot be written
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(test_def, f, indent=2)
        
        logger.info(f"Saved test definition to {file_path}")
    except IOError:
        logger.error(f"Failed to save test definition to {file_path}")
        raise

def validate_test_definition(test_def: Dict[str, Any]) -> List[str]:
    """
    Validate a test definition.
    
    Args:
        test_def: The test definition to validate
        
    Returns:
        List of validation errors, empty if valid
    """
    errors = []
    
    # Check required fields
    required_fields = ['id', 'name', 'description', 'type', 'parameters', 'checks']
    for field in required_fields:
        if field not in test_def:
            errors.append(f"Missing required field: {field}")
    
    # If any required fields are missing, return early
    if errors:
        return errors
    
    # Check that parameters is a dictionary
    if not isinstance(test_def['parameters'], dict):
        errors.append(f"Parameters must be a dictionary in test definition: {test_def['id']}")
    
    # Check that checks is a list
    if not isinstance(test_def['checks'], list):
        errors.append(f"Checks must be a list in test definition: {test_def['id']}")
    else:
        # Check each check
        for i, check in enumerate(test_def['checks']):
            # Check required fields
            check_required_fields = ['id', 'description', 'type']
            for field in check_required_fields:
                if field not in check:
                    errors.append(f"Missing required field: {field} in check {i}")
    
    return errors

def load_test_suite_definition(file_path: str) -> Dict[str, Any]:
    """
    Load a test suite definition from a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        The test suite definition
        
    Raises:
        FileNotFoundError: If the file does not exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            suite_def = json.load(f)
        
        logger.info(f"Loaded test suite definition from {file_path}")
        return suite_def
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in file: {file_path}")
        raise

def save_test_suite_definition(suite_def: Dict[str, Any], file_path: str) -> None:
    """
    Save a test suite definition to a JSON file.
    
    Args:
        suite_def: The test suite definition
        file_path: Path to the output JSON file
        
    Raises:
        IOError: If the file cannot be written
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(suite_def, f, indent=2)
        
        logger.info(f"Saved test suite definition to {file_path}")
    except IOError:
        logger.error(f"Failed to save test suite definition to {file_path}")
        raise

def validate_test_suite_definition(suite_def: Dict[str, Any]) -> List[str]:
    """
    Validate a test suite definition.
    
    Args:
        suite_def: The test suite definition to validate
        
    Returns:
        List of validation errors, empty if valid
    """
    errors = []
    
    # Check required fields
    required_fields = ['id', 'name', 'description', 'tests']
    for field in required_fields:
        if field not in suite_def:
            errors.append(f"Missing required field: {field}")
    
    # If any required fields are missing, return early
    if errors:
        return errors
    
    # Check that tests is a list
    if not isinstance(suite_def['tests'], list):
        errors.append(f"Tests must be a list in test suite definition: {suite_def['id']}")
    
    # Check that test_parameters is a dictionary if present
    if 'test_parameters' in suite_def and not isinstance(suite_def['test_parameters'], dict):
        errors.append(f"Test parameters must be a dictionary in test suite definition: {suite_def['id']}")
    
    return errors

def merge_test_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Merge multiple test results into a single result.
    
    Args:
        results: List of test results
        
    Returns:
        The merged test result
    """
    if not results:
        return {}
    
    # Calculate overall pass/fail status
    passed = all(result.get('passed', False) for result in results)
    
    # Calculate average score
    total_score = sum(result.get('score', 0) for result in results)
    avg_score = total_score / len(results)
    
    # Merge findings
    findings = []
    for result in results:
        findings.extend(result.get('findings', []))
    
    # Create the merged result
    merged_result = {
        'passed': passed,
        'score': avg_score,
        'findings': findings
    }
    
    return merged_result

def filter_test_results(results: List[Dict[str, Any]], status: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Filter test results by status.
    
    Args:
        results: List of test results
        status: Optional status filter
        
    Returns:
        Filtered list of test results
    """
    if status:
        return [result for result in results if result.get('status') == status]
    else:
        return results

def get_test_result_summary(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get a summary of a test result.
    
    Args:
        result: The test result
        
    Returns:
        The test result summary
    """
    findings = result.get('findings', [])
    
    summary = {
        'passed': result.get('passed', False),
        'score': result.get('score', 0),
        'findings_count': len(findings),
        'passed_findings': len([f for f in findings if f.get('status') == 'passed']),
        'warning_findings': len([f for f in findings if f.get('status') == 'warning']),
        'failed_findings': len([f for f in findings if f.get('status') == 'failed'])
    }
    
    return summary
