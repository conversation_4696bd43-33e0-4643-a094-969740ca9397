/**
 * Alert Configuration Component
 * 
 * This component allows users to configure alert settings for connectors.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Checkbox, 
  Chip, 
  Divider, 
  FormControl, 
  FormControlLabel, 
  Grid, 
  IconButton, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  Switch, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  TextField, 
  Tooltip, 
  Typography 
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import NotificationsIcon from '@mui/icons-material/Notifications';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import NotificationsOffIcon from '@mui/icons-material/NotificationsOff';

const AlertConfiguration = ({ connectors }) => {
  const [globalSettings, setGlobalSettings] = useState({
    emailEnabled: true,
    emailRecipients: '<EMAIL>, <EMAIL>',
    slackEnabled: true,
    slackWebhook: '*****************************************************************************',
    slackChannel: '#api-alerts',
    smsEnabled: false,
    smsRecipients: '',
    webhookEnabled: false,
    webhookUrl: ''
  });
  
  const [thresholds, setThresholds] = useState({
    responseTime: {
      warning: 500,
      error: 1000
    },
    successRate: {
      warning: 90,
      error: 70
    },
    errorRate: {
      warning: 10,
      error: 30
    }
  });
  
  const [rules, setRules] = useState([
    {
      id: 1,
      name: 'High Response Time',
      description: 'Alert when response time exceeds threshold',
      condition: 'responseTime > {{threshold.responseTime.warning}}',
      severity: 'medium',
      enabled: true
    },
    {
      id: 2,
      name: 'Critical Response Time',
      description: 'Alert when response time exceeds critical threshold',
      condition: 'responseTime > {{threshold.responseTime.error}}',
      severity: 'high',
      enabled: true
    },
    {
      id: 3,
      name: 'Low Success Rate',
      description: 'Alert when success rate falls below threshold',
      condition: 'successRate < {{threshold.successRate.warning}}',
      severity: 'medium',
      enabled: true
    },
    {
      id: 4,
      name: 'Critical Success Rate',
      description: 'Alert when success rate falls below critical threshold',
      condition: 'successRate < {{threshold.successRate.error}}',
      severity: 'high',
      enabled: true
    },
    {
      id: 5,
      name: 'Connection Failure',
      description: 'Alert when connection to API fails',
      condition: 'status == "error" && errorType == "connection"',
      severity: 'critical',
      enabled: true
    }
  ]);
  
  const [connectorSettings, setConnectorSettings] = useState(
    connectors.map(connector => ({
      id: connector.id,
      name: connector.name,
      enabled: true,
      checkInterval: 5,
      customRules: []
    }))
  );
  
  const [editingRule, setEditingRule] = useState(null);
  const [showAddRule, setShowAddRule] = useState(false);
  const [newRule, setNewRule] = useState({
    name: '',
    description: '',
    condition: '',
    severity: 'medium',
    enabled: true
  });
  
  const handleGlobalSettingChange = (setting, value) => {
    setGlobalSettings({
      ...globalSettings,
      [setting]: value
    });
  };
  
  const handleThresholdChange = (category, level, value) => {
    setThresholds({
      ...thresholds,
      [category]: {
        ...thresholds[category],
        [level]: value
      }
    });
  };
  
  const handleRuleToggle = (ruleId) => {
    setRules(rules.map(rule => 
      rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
    ));
  };
  
  const handleEditRule = (rule) => {
    setEditingRule(rule);
    setShowAddRule(false);
  };
  
  const handleUpdateRule = () => {
    setRules(rules.map(rule => 
      rule.id === editingRule.id ? editingRule : rule
    ));
    setEditingRule(null);
  };
  
  const handleCancelEditRule = () => {
    setEditingRule(null);
  };
  
  const handleDeleteRule = (ruleId) => {
    setRules(rules.filter(rule => rule.id !== ruleId));
  };
  
  const handleAddRuleClick = () => {
    setShowAddRule(true);
    setEditingRule(null);
  };
  
  const handleNewRuleChange = (field, value) => {
    setNewRule({
      ...newRule,
      [field]: value
    });
  };
  
  const handleSaveNewRule = () => {
    const newRuleWithId = {
      ...newRule,
      id: Math.max(...rules.map(r => r.id), 0) + 1
    };
    
    setRules([...rules, newRuleWithId]);
    setNewRule({
      name: '',
      description: '',
      condition: '',
      severity: 'medium',
      enabled: true
    });
    setShowAddRule(false);
  };
  
  const handleConnectorSettingChange = (connectorId, setting, value) => {
    setConnectorSettings(connectorSettings.map(cs => 
      cs.id === connectorId ? { ...cs, [setting]: value } : cs
    ));
  };
  
  const handleSaveSettings = () => {
    // In a real implementation, this would call an API to save the settings
    alert('Settings saved!');
  };
  
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'error';
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h2">
          Alert Configuration
        </Typography>
        
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSaveSettings}
        >
          Save Settings
        </Button>
      </Box>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Notification Channels
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={globalSettings.emailEnabled}
                        onChange={(e) => handleGlobalSettingChange('emailEnabled', e.target.checked)}
                      />
                    }
                    label="Email Notifications"
                  />
                  
                  {globalSettings.emailEnabled && (
                    <TextField
                      fullWidth
                      label="Email Recipients"
                      value={globalSettings.emailRecipients}
                      onChange={(e) => handleGlobalSettingChange('emailRecipients', e.target.value)}
                      helperText="Comma-separated list of email addresses"
                      sx={{ mt: 1 }}
                    />
                  )}
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={globalSettings.slackEnabled}
                        onChange={(e) => handleGlobalSettingChange('slackEnabled', e.target.checked)}
                      />
                    }
                    label="Slack Notifications"
                  />
                  
                  {globalSettings.slackEnabled && (
                    <>
                      <TextField
                        fullWidth
                        label="Slack Webhook URL"
                        value={globalSettings.slackWebhook}
                        onChange={(e) => handleGlobalSettingChange('slackWebhook', e.target.value)}
                        sx={{ mt: 1, mb: 2 }}
                      />
                      
                      <TextField
                        fullWidth
                        label="Slack Channel"
                        value={globalSettings.slackChannel}
                        onChange={(e) => handleGlobalSettingChange('slackChannel', e.target.value)}
                        helperText="Channel name (e.g., #alerts)"
                      />
                    </>
                  )}
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={globalSettings.smsEnabled}
                        onChange={(e) => handleGlobalSettingChange('smsEnabled', e.target.checked)}
                      />
                    }
                    label="SMS Notifications"
                  />
                  
                  {globalSettings.smsEnabled && (
                    <TextField
                      fullWidth
                      label="SMS Recipients"
                      value={globalSettings.smsRecipients}
                      onChange={(e) => handleGlobalSettingChange('smsRecipients', e.target.value)}
                      helperText="Comma-separated list of phone numbers"
                      sx={{ mt: 1 }}
                    />
                  )}
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={globalSettings.webhookEnabled}
                        onChange={(e) => handleGlobalSettingChange('webhookEnabled', e.target.checked)}
                      />
                    }
                    label="Webhook Notifications"
                  />
                  
                  {globalSettings.webhookEnabled && (
                    <TextField
                      fullWidth
                      label="Webhook URL"
                      value={globalSettings.webhookUrl}
                      onChange={(e) => handleGlobalSettingChange('webhookUrl', e.target.value)}
                      sx={{ mt: 1 }}
                    />
                  )}
                </Grid>
              </Grid>
            </CardContent>
          </Card>
          
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Alert Thresholds
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Response Time (ms)
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Warning Threshold"
                        type="number"
                        value={thresholds.responseTime.warning}
                        onChange={(e) => handleThresholdChange('responseTime', 'warning', parseInt(e.target.value) || 0)}
                        InputProps={{
                          endAdornment: <Typography variant="body2">ms</Typography>
                        }}
                      />
                    </Grid>
                    
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Error Threshold"
                        type="number"
                        value={thresholds.responseTime.error}
                        onChange={(e) => handleThresholdChange('responseTime', 'error', parseInt(e.target.value) || 0)}
                        InputProps={{
                          endAdornment: <Typography variant="body2">ms</Typography>
                        }}
                      />
                    </Grid>
                  </Grid>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Success Rate (%)
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Warning Threshold"
                        type="number"
                        value={thresholds.successRate.warning}
                        onChange={(e) => handleThresholdChange('successRate', 'warning', parseInt(e.target.value) || 0)}
                        InputProps={{
                          endAdornment: <Typography variant="body2">%</Typography>
                        }}
                        helperText="Alert when below this value"
                      />
                    </Grid>
                    
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Error Threshold"
                        type="number"
                        value={thresholds.successRate.error}
                        onChange={(e) => handleThresholdChange('successRate', 'error', parseInt(e.target.value) || 0)}
                        InputProps={{
                          endAdornment: <Typography variant="body2">%</Typography>
                        }}
                        helperText="Alert when below this value"
                      />
                    </Grid>
                  </Grid>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    Error Rate (%)
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Warning Threshold"
                        type="number"
                        value={thresholds.errorRate.warning}
                        onChange={(e) => handleThresholdChange('errorRate', 'warning', parseInt(e.target.value) || 0)}
                        InputProps={{
                          endAdornment: <Typography variant="body2">%</Typography>
                        }}
                        helperText="Alert when above this value"
                      />
                    </Grid>
                    
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Error Threshold"
                        type="number"
                        value={thresholds.errorRate.error}
                        onChange={(e) => handleThresholdChange('errorRate', 'error', parseInt(e.target.value) || 0)}
                        InputProps={{
                          endAdornment: <Typography variant="body2">%</Typography>
                        }}
                        helperText="Alert when above this value"
                      />
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Alert Rules
                </Typography>
                
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleAddRuleClick}
                >
                  Add Rule
                </Button>
              </Box>
              
              {showAddRule && (
                <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    New Alert Rule
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Rule Name"
                        value={newRule.name}
                        onChange={(e) => handleNewRuleChange('name', e.target.value)}
                        required
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel id="new-rule-severity-label">Severity</InputLabel>
                        <Select
                          labelId="new-rule-severity-label"
                          value={newRule.severity}
                          label="Severity"
                          onChange={(e) => handleNewRuleChange('severity', e.target.value)}
                        >
                          <MenuItem value="low">Low</MenuItem>
                          <MenuItem value="medium">Medium</MenuItem>
                          <MenuItem value="high">High</MenuItem>
                          <MenuItem value="critical">Critical</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        value={newRule.description}
                        onChange={(e) => handleNewRuleChange('description', e.target.value)}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Condition"
                        value={newRule.condition}
                        onChange={(e) => handleNewRuleChange('condition', e.target.value)}
                        required
                        helperText="Use {{threshold.category.level}} for dynamic thresholds"
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                        <Button
                          variant="outlined"
                          onClick={() => setShowAddRule(false)}
                        >
                          Cancel
                        </Button>
                        
                        <Button
                          variant="contained"
                          onClick={handleSaveNewRule}
                          disabled={!newRule.name || !newRule.condition}
                        >
                          Save Rule
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              )}
              
              {editingRule && (
                <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Edit Alert Rule
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Rule Name"
                        value={editingRule.name}
                        onChange={(e) => setEditingRule({ ...editingRule, name: e.target.value })}
                        required
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel id="edit-rule-severity-label">Severity</InputLabel>
                        <Select
                          labelId="edit-rule-severity-label"
                          value={editingRule.severity}
                          label="Severity"
                          onChange={(e) => setEditingRule({ ...editingRule, severity: e.target.value })}
                        >
                          <MenuItem value="low">Low</MenuItem>
                          <MenuItem value="medium">Medium</MenuItem>
                          <MenuItem value="high">High</MenuItem>
                          <MenuItem value="critical">Critical</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        value={editingRule.description}
                        onChange={(e) => setEditingRule({ ...editingRule, description: e.target.value })}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Condition"
                        value={editingRule.condition}
                        onChange={(e) => setEditingRule({ ...editingRule, condition: e.target.value })}
                        required
                        helperText="Use {{threshold.category.level}} for dynamic thresholds"
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                        <Button
                          variant="outlined"
                          onClick={handleCancelEditRule}
                        >
                          Cancel
                        </Button>
                        
                        <Button
                          variant="contained"
                          onClick={handleUpdateRule}
                          disabled={!editingRule.name || !editingRule.condition}
                        >
                          Update Rule
                        </Button>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              )}
              
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox"></TableCell>
                      <TableCell>Rule</TableCell>
                      <TableCell>Severity</TableCell>
                      <TableCell>Condition</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {rules.map(rule => (
                      <TableRow key={rule.id} hover>
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={rule.enabled}
                            onChange={() => handleRuleToggle(rule.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {rule.name}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {rule.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={rule.severity.charAt(0).toUpperCase() + rule.severity.slice(1)} 
                            size="small" 
                            color={getSeverityColor(rule.severity)} 
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                            {rule.condition}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => handleEditRule(rule)}
                              sx={{ mr: 1 }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteRule(rule.id)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
          
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Connector-Specific Settings
              </Typography>
              
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Connector</TableCell>
                      <TableCell>Alerts</TableCell>
                      <TableCell>Check Interval</TableCell>
                      <TableCell align="right">Custom Rules</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {connectorSettings.map(cs => (
                      <TableRow key={cs.id} hover>
                        <TableCell>
                          <Typography variant="body2">
                            {cs.name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={cs.enabled}
                            onChange={(e) => handleConnectorSettingChange(cs.id, 'enabled', e.target.checked)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            type="number"
                            value={cs.checkInterval}
                            onChange={(e) => handleConnectorSettingChange(cs.id, 'checkInterval', parseInt(e.target.value) || 1)}
                            InputProps={{
                              endAdornment: <Typography variant="body2">min</Typography>
                            }}
                            size="small"
                            sx={{ width: 100 }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<NotificationsIcon />}
                          >
                            Configure
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AlertConfiguration;
