/**
 * Validation Middleware
 * 
 * This middleware validates request data against Jo<PERSON> schemas.
 */

/**
 * Validate request data against a Joi schema
 * @param {Object} schema - Joi schema
 * @param {string} property - Request property to validate (body, query, params)
 * @returns {Function} Express middleware
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error } = schema.validate(req[property], { abortEarly: false });
    
    if (!error) {
      return next();
    }
    
    const errors = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));
    
    return res.status(400).json({
      error: 'ValidationError',
      message: 'Validation failed',
      errors
    });
  };
};

module.exports = {
  validate
};
