/**
 * 3D Tensor Visualization
 * 
 * This module provides 3D visualization of tensor operations using Three.js.
 * It visualizes tensor product, direct sum, and scaling operations in 3D space.
 */

// Import Three.js (assumed to be included in the HTML)
// const THREE = window.THREE;

/**
 * TensorVisualizer class
 */
class TensorVisualizer {
  /**
   * Constructor
   * @param {HTMLElement} container - Container element
   * @param {Object} options - Configuration options
   */
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      width: container.clientWidth,
      height: container.clientHeight,
      backgroundColor: 0x111133,
      csdeColor: 0x0088ff, // Blue
      csfeColor: 0x22cc44, // Green
      csmeColor: 0x8844ff, // Purple
      fusedColor: 0xff7700, // Orange
      animationDuration: 1000,
      autoRotate: true,
      showAxes: true,
      showLabels: true,
      ...options
    };
    
    // Initialize state
    this.state = {
      isInitialized: false,
      isAnimating: false,
      tensors: {
        csde: null,
        csfe: null,
        csme: null,
        fused: null
      },
      operations: {
        product: null,
        sum: null,
        scale: null
      }
    };
    
    // Initialize scene
    this.initScene();
    
    // Initialize camera
    this.initCamera();
    
    // Initialize renderer
    this.initRenderer();
    
    // Initialize controls
    this.initControls();
    
    // Initialize lights
    this.initLights();
    
    // Initialize axes
    if (this.options.showAxes) {
      this.initAxes();
    }
    
    // Initialize event listeners
    this.initEventListeners();
    
    // Start animation loop
    this.animate();
    
    // Mark as initialized
    this.state.isInitialized = true;
  }
  
  /**
   * Initialize scene
   */
  initScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(this.options.backgroundColor);
    
    // Create groups for tensors and operations
    this.tensorsGroup = new THREE.Group();
    this.operationsGroup = new THREE.Group();
    
    this.scene.add(this.tensorsGroup);
    this.scene.add(this.operationsGroup);
  }
  
  /**
   * Initialize camera
   */
  initCamera() {
    const { width, height } = this.options;
    const aspectRatio = width / height;
    
    this.camera = new THREE.PerspectiveCamera(45, aspectRatio, 0.1, 1000);
    this.camera.position.set(5, 5, 5);
    this.camera.lookAt(0, 0, 0);
  }
  
  /**
   * Initialize renderer
   */
  initRenderer() {
    const { width, height } = this.options;
    
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.renderer.setSize(width, height);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    
    this.container.appendChild(this.renderer.domElement);
  }
  
  /**
   * Initialize controls
   */
  initControls() {
    this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.rotateSpeed = 0.5;
    this.controls.autoRotate = this.options.autoRotate;
    this.controls.autoRotateSpeed = 0.5;
    this.controls.enablePan = true;
    this.controls.enableZoom = true;
    this.controls.minDistance = 2;
    this.controls.maxDistance = 20;
  }
  
  /**
   * Initialize lights
   */
  initLights() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // Directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 10, 7.5);
    directionalLight.castShadow = true;
    
    // Configure shadow
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -10;
    directionalLight.shadow.camera.right = 10;
    directionalLight.shadow.camera.top = 10;
    directionalLight.shadow.camera.bottom = -10;
    
    this.scene.add(directionalLight);
    
    // Point lights for each domain
    const csdeLight = new THREE.PointLight(this.options.csdeColor, 0.5, 10);
    csdeLight.position.set(-3, 2, 2);
    
    const csfeLight = new THREE.PointLight(this.options.csfeColor, 0.5, 10);
    csfeLight.position.set(3, 2, 2);
    
    const csmeLight = new THREE.PointLight(this.options.csmeColor, 0.5, 10);
    csmeLight.position.set(0, 2, -3);
    
    this.scene.add(csdeLight, csfeLight, csmeLight);
  }
  
  /**
   * Initialize axes
   */
  initAxes() {
    const axesHelper = new THREE.AxesHelper(5);
    this.scene.add(axesHelper);
    
    if (this.options.showLabels) {
      // Add axis labels
      const loader = new THREE.FontLoader();
      
      // Use a callback or promise to handle font loading
      // For simplicity, we're using a placeholder approach here
      setTimeout(() => {
        this.addAxisLabels();
      }, 1000);
    }
  }
  
  /**
   * Add axis labels
   */
  addAxisLabels() {
    // This would normally use TextGeometry with loaded font
    // For simplicity, we're using sprites
    
    const createLabel = (text, position, color = 0xffffff) => {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.width = 128;
      canvas.height = 64;
      
      context.fillStyle = '#000000';
      context.fillRect(0, 0, canvas.width, canvas.height);
      
      context.font = 'Bold 24px Arial';
      context.fillStyle = `#${color.toString(16).padStart(6, '0')}`;
      context.textAlign = 'center';
      context.textBaseline = 'middle';
      context.fillText(text, canvas.width / 2, canvas.height / 2);
      
      const texture = new THREE.CanvasTexture(canvas);
      const material = new THREE.SpriteMaterial({ map: texture });
      const sprite = new THREE.Sprite(material);
      
      sprite.position.copy(position);
      sprite.scale.set(1, 0.5, 1);
      
      return sprite;
    };
    
    // X axis label
    const xLabel = createLabel('X', new THREE.Vector3(5.5, 0, 0), 0xff0000);
    this.scene.add(xLabel);
    
    // Y axis label
    const yLabel = createLabel('Y', new THREE.Vector3(0, 5.5, 0), 0x00ff00);
    this.scene.add(yLabel);
    
    // Z axis label
    const zLabel = createLabel('Z', new THREE.Vector3(0, 0, 5.5), 0x0000ff);
    this.scene.add(zLabel);
  }
  
  /**
   * Initialize event listeners
   */
  initEventListeners() {
    // Handle window resize
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // Handle container resize
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.target === this.container) {
          this.onContainerResize();
        }
      }
    });
    
    resizeObserver.observe(this.container);
  }
  
  /**
   * Handle window resize
   */
  onWindowResize() {
    this.onContainerResize();
  }
  
  /**
   * Handle container resize
   */
  onContainerResize() {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;
    
    this.options.width = width;
    this.options.height = height;
    
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    
    this.renderer.setSize(width, height);
  }
  
  /**
   * Animation loop
   */
  animate() {
    requestAnimationFrame(this.animate.bind(this));
    
    // Update controls
    this.controls.update();
    
    // Render scene
    this.renderer.render(this.scene, this.camera);
  }
  
  /**
   * Visualize CSDE tensor
   * @param {Array} tensor - CSDE tensor
   */
  visualizeCSDETensor(tensor) {
    this.visualizeTensor('csde', tensor, this.options.csdeColor, new THREE.Vector3(-3, 0, 0));
  }
  
  /**
   * Visualize CSFE tensor
   * @param {Array} tensor - CSFE tensor
   */
  visualizeCSFETensor(tensor) {
    this.visualizeTensor('csfe', tensor, this.options.csfeColor, new THREE.Vector3(3, 0, 0));
  }
  
  /**
   * Visualize CSME tensor
   * @param {Array} tensor - CSME tensor
   */
  visualizeCSMETensor(tensor) {
    this.visualizeTensor('csme', tensor, this.options.csmeColor, new THREE.Vector3(0, 0, -3));
  }
  
  /**
   * Visualize fused tensor
   * @param {Array} tensor - Fused tensor
   */
  visualizeFusedTensor(tensor) {
    this.visualizeTensor('fused', tensor, this.options.fusedColor, new THREE.Vector3(0, 0, 0));
  }
  
  /**
   * Visualize tensor
   * @param {string} name - Tensor name
   * @param {Array} tensor - Tensor data
   * @param {number} color - Tensor color
   * @param {THREE.Vector3} position - Tensor position
   */
  visualizeTensor(name, tensor, color, position) {
    // Remove existing tensor if any
    if (this.state.tensors[name]) {
      this.tensorsGroup.remove(this.state.tensors[name]);
    }
    
    // Create tensor visualization
    const tensorMesh = this.createTensorMesh(tensor, color);
    
    // Set position
    tensorMesh.position.copy(position);
    
    // Add to group
    this.tensorsGroup.add(tensorMesh);
    
    // Update state
    this.state.tensors[name] = tensorMesh;
    
    // Animate
    this.animateTensor(tensorMesh);
  }
  
  /**
   * Create tensor mesh
   * @param {Array} tensor - Tensor data
   * @param {number} color - Tensor color
   * @returns {THREE.Group} Tensor mesh
   */
  createTensorMesh(tensor, color) {
    const group = new THREE.Group();
    
    // Determine tensor dimensions
    const dimensions = this.getTensorDimensions(tensor);
    
    // Create tensor visualization based on dimensions
    if (dimensions.length === 1) {
      // 1D tensor (vector)
      this.createVectorMesh(group, tensor, color);
    } else if (dimensions.length === 2) {
      // 2D tensor (matrix)
      this.createMatrixMesh(group, tensor, dimensions, color);
    } else {
      // Higher-dimensional tensor
      this.createHigherDimensionalTensorMesh(group, tensor, dimensions, color);
    }
    
    return group;
  }
  
  /**
   * Get tensor dimensions
   * @param {Array} tensor - Tensor data
   * @returns {Array} Tensor dimensions
   */
  getTensorDimensions(tensor) {
    // For simplicity, we're assuming tensor is a flat array
    // In a real implementation, we would determine the actual dimensions
    
    // If tensor is a flat array, treat it as a vector
    if (!Array.isArray(tensor[0])) {
      return [tensor.length];
    }
    
    // If tensor is a 2D array, treat it as a matrix
    if (!Array.isArray(tensor[0][0])) {
      return [tensor.length, tensor[0].length];
    }
    
    // Otherwise, treat it as a higher-dimensional tensor
    // This is a simplified approach
    return [tensor.length, tensor[0].length, tensor[0][0].length];
  }
  
  /**
   * Create vector mesh
   * @param {THREE.Group} group - Group to add mesh to
   * @param {Array} vector - Vector data
   * @param {number} color - Vector color
   */
  createVectorMesh(group, vector, color) {
    const length = vector.length;
    const maxValue = Math.max(...vector.map(Math.abs));
    const scale = 1 / Math.max(1, maxValue);
    
    // Create spheres for each element
    for (let i = 0; i < length; i++) {
      const value = vector[i];
      const normalizedValue = value * scale;
      
      // Create sphere
      const geometry = new THREE.SphereGeometry(Math.abs(normalizedValue) * 0.2 + 0.05, 16, 16);
      const material = new THREE.MeshPhongMaterial({
        color: color,
        opacity: 0.8,
        transparent: true,
        emissive: color,
        emissiveIntensity: 0.2
      });
      
      const sphere = new THREE.Mesh(geometry, material);
      
      // Position sphere
      const x = (i / (length - 1)) * 2 - 1;
      sphere.position.set(x, normalizedValue, 0);
      
      // Add to group
      group.add(sphere);
      
      // Add connecting line to previous sphere
      if (i > 0) {
        const prevValue = vector[i - 1] * scale;
        const prevX = ((i - 1) / (length - 1)) * 2 - 1;
        
        const points = [
          new THREE.Vector3(prevX, prevValue, 0),
          new THREE.Vector3(x, normalizedValue, 0)
        ];
        
        const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
        const lineMaterial = new THREE.LineBasicMaterial({
          color: color,
          opacity: 0.5,
          transparent: true
        });
        
        const line = new THREE.Line(lineGeometry, lineMaterial);
        group.add(line);
      }
    }
  }
  
  /**
   * Create matrix mesh
   * @param {THREE.Group} group - Group to add mesh to
   * @param {Array} matrix - Matrix data
   * @param {Array} dimensions - Matrix dimensions
   * @param {number} color - Matrix color
   */
  createMatrixMesh(group, matrix, dimensions, color) {
    const [rows, cols] = dimensions;
    const maxValue = Math.max(...matrix.flat().map(Math.abs));
    const scale = 1 / Math.max(1, maxValue);
    
    // Create cubes for each element
    for (let i = 0; i < rows; i++) {
      for (let j = 0; j < cols; j++) {
        const value = matrix[i][j];
        const normalizedValue = value * scale;
        
        // Create cube
        const geometry = new THREE.BoxGeometry(
          0.2,
          Math.abs(normalizedValue) * 0.5 + 0.05,
          0.2
        );
        
        const material = new THREE.MeshPhongMaterial({
          color: color,
          opacity: 0.8,
          transparent: true,
          emissive: color,
          emissiveIntensity: 0.2
        });
        
        const cube = new THREE.Mesh(geometry, material);
        
        // Position cube
        const x = (j / (cols - 1)) * 2 - 1;
        const z = (i / (rows - 1)) * 2 - 1;
        cube.position.set(x, normalizedValue / 2, z);
        
        // Add to group
        group.add(cube);
      }
    }
  }
  
  /**
   * Create higher-dimensional tensor mesh
   * @param {THREE.Group} group - Group to add mesh to
   * @param {Array} tensor - Tensor data
   * @param {Array} dimensions - Tensor dimensions
   * @param {number} color - Tensor color
   */
  createHigherDimensionalTensorMesh(group, tensor, dimensions, color) {
    // This is a simplified visualization for higher-dimensional tensors
    // In a real implementation, we would use more sophisticated techniques
    
    const [dim1, dim2, dim3] = dimensions;
    const maxValue = Math.max(...tensor.flat(2).map(Math.abs));
    const scale = 1 / Math.max(1, maxValue);
    
    // Create spheres for each element
    for (let i = 0; i < dim1; i++) {
      for (let j = 0; j < dim2; j++) {
        for (let k = 0; k < dim3; k++) {
          const value = tensor[i][j][k];
          const normalizedValue = value * scale;
          
          // Create sphere
          const geometry = new THREE.SphereGeometry(
            Math.abs(normalizedValue) * 0.1 + 0.02,
            8,
            8
          );
          
          const material = new THREE.MeshPhongMaterial({
            color: color,
            opacity: 0.6,
            transparent: true,
            emissive: color,
            emissiveIntensity: 0.2
          });
          
          const sphere = new THREE.Mesh(geometry, material);
          
          // Position sphere
          const x = (j / (dim2 - 1)) * 2 - 1;
          const y = normalizedValue;
          const z = (i / (dim1 - 1)) * 2 - 1;
          
          // Add depth dimension
          const depthOffset = (k / (dim3 - 1)) * 0.5;
          
          sphere.position.set(x, y, z + depthOffset);
          
          // Add to group
          group.add(sphere);
        }
      }
    }
  }
  
  /**
   * Animate tensor
   * @param {THREE.Object3D} tensorMesh - Tensor mesh
   */
  animateTensor(tensorMesh) {
    // Start with zero scale
    tensorMesh.scale.set(0.01, 0.01, 0.01);
    
    // Animate to full scale
    const duration = this.options.animationDuration;
    const startTime = performance.now();
    
    const animate = (time) => {
      const elapsed = time - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Ease in-out
      const eased = progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;
      
      tensorMesh.scale.set(eased, eased, eased);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }
  
  /**
   * Visualize tensor product operation
   * @param {Array} tensorA - First tensor
   * @param {Array} tensorB - Second tensor
   * @param {Array} result - Result tensor
   */
  visualizeTensorProduct(tensorA, tensorB, result) {
    this.visualizeOperation('product', tensorA, tensorB, result);
  }
  
  /**
   * Visualize direct sum operation
   * @param {Array} tensorA - First tensor
   * @param {Array} tensorB - Second tensor
   * @param {Array} result - Result tensor
   */
  visualizeDirectSum(tensorA, tensorB, result) {
    this.visualizeOperation('sum', tensorA, tensorB, result);
  }
  
  /**
   * Visualize scale operation
   * @param {Array} tensor - Tensor to scale
   * @param {number} scale - Scale factor
   * @param {Array} result - Result tensor
   */
  visualizeScaleTensor(tensor, scale, result) {
    // Implement scale visualization
  }
  
  /**
   * Visualize operation
   * @param {string} name - Operation name
   * @param {Array} tensorA - First tensor
   * @param {Array} tensorB - Second tensor
   * @param {Array} result - Result tensor
   */
  visualizeOperation(name, tensorA, tensorB, result) {
    // Remove existing operation if any
    if (this.state.operations[name]) {
      this.operationsGroup.remove(this.state.operations[name]);
    }
    
    // Create operation visualization
    const operationGroup = new THREE.Group();
    
    // Implement operation visualization
    // This would be specific to each operation type
    
    // Add to group
    this.operationsGroup.add(operationGroup);
    
    // Update state
    this.state.operations[name] = operationGroup;
  }
  
  /**
   * Set auto-rotate
   * @param {boolean} autoRotate - Whether to auto-rotate
   */
  setAutoRotate(autoRotate) {
    this.options.autoRotate = autoRotate;
    this.controls.autoRotate = autoRotate;
  }
  
  /**
   * Set background color
   * @param {number} color - Background color
   */
  setBackgroundColor(color) {
    this.options.backgroundColor = color;
    this.scene.background = new THREE.Color(color);
  }
  
  /**
   * Dispose
   */
  dispose() {
    // Remove event listeners
    window.removeEventListener('resize', this.onWindowResize);
    
    // Dispose renderer
    this.renderer.dispose();
    
    // Remove canvas
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }
    
    // Dispose controls
    this.controls.dispose();
  }
}

// Export
if (typeof module !== 'undefined') {
  module.exports = { TensorVisualizer };
}
