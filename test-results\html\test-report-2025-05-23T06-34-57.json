{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 6, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 6, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1747982093139, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 6, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1747982097018, "runtime": 2835, "slow": false, "start": 1747982094183}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\unit\\novaproof\\blockchain-verification.test.js", "testResults": [{"ancestorTitles": ["NovaProof Blockchain Verification", "Evidence Verification"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Evidence Verification should verify evidence correctly", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should verify evidence correctly"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Evidence Verification"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Evidence Verification should handle evidence with attachments", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should handle evidence with attachments"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Proof Generation"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Proof Generation should generate proof correctly", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "status": "passed", "title": "should generate proof correctly"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Proof Verification"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Proof Verification should verify proof correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should verify proof correctly"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Proof Verification"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Proof Verification should verify actual <PERSON><PERSON>le proofs correctly", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should verify actual <PERSON><PERSON><PERSON> proofs correctly"}, {"ancestorTitles": ["NovaProof Blockchain Verification", "Performance"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof Blockchain Verification Performance should verify evidence efficiently", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should verify evidence efficiently"}], "failureMessage": null}], "wasInterrupted": false}