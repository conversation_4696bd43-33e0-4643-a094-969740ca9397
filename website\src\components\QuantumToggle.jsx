import React from 'react';
import '../styles/QuantumToggle.css';

const QuantumToggle = ({ enabled, onChange, disabled = false }) => {
  return (
    <div className={`quantum-toggle ${enabled ? 'quantum-active' : ''} ${disabled ? 'disabled' : ''}`}>
      <span className="toggle-label">
        {enabled ? 'Quantum Mode' : 'Classical Mode'}
      </span>
      <label className="switch">
        <input 
          type="checkbox" 
          checked={enabled}
          onChange={onChange}
          disabled={disabled}
          aria-label="Toggle quantum mode"
        />
        <span className="slider round"></span>
      </label>
      <div className="status-indicator">
        <div className={`status-dot ${enabled ? 'quantum' : 'classical'}`} />
        <span>{enabled ? 'Quantum Active' : 'Classical Mode'}</span>
      </div>
    </div>
  );
};

export default QuantumToggle;
