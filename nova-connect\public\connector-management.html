<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse UAC Connector Management</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #0066cc;
      color: white;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    header h1 {
      margin: 0;
      font-size: 24px;
    }
    
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .dashboard-header h2 {
      margin: 0;
    }
    
    .action-button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    
    .action-button:hover {
      background-color: #0055aa;
    }
    
    .connector-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    
    .connector-card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .connector-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .connector-card h3 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #0066cc;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    
    .connector-card p {
      margin-bottom: 15px;
      color: #666;
    }
    
    .connector-card .actions {
      display: flex;
      justify-content: space-between;
      margin-top: 15px;
    }
    
    .connector-card .badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      margin-right: 5px;
    }
    
    .badge-draft {
      background-color: #f0f0f0;
      color: #666;
    }
    
    .badge-testing {
      background-color: #e1f5fe;
      color: #0288d1;
    }
    
    .badge-published {
      background-color: #e8f5e9;
      color: #388e3c;
    }
    
    .badge-deprecated {
      background-color: #fff8e1;
      color: #ffa000;
    }
    
    .badge-retired {
      background-color: #ffebee;
      color: #d32f2f;
    }
    
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
      z-index: 100;
      overflow: auto;
    }
    
    .modal-content {
      background-color: #fff;
      margin: 10% auto;
      padding: 20px;
      border-radius: 8px;
      width: 80%;
      max-width: 600px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .modal-header h3 {
      margin: 0;
      color: #0066cc;
    }
    
    .close-button {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    
    .form-group textarea {
      height: 100px;
      resize: vertical;
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }
    
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
    
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-left-color: #0066cc;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1>NovaFuse Universal API Connector - Connector Management</h1>
    </div>
  </header>
  
  <div class="container">
    <div class="dashboard-header">
      <h2>Connectors</h2>
      <button id="create-connector-button" class="action-button">Create Connector</button>
    </div>
    
    <div id="connector-list" class="connector-grid">
      <div class="loading">
        <div class="spinner"></div>
      </div>
    </div>
  </div>
  
  <!-- Create/Edit Connector Modal -->
  <div id="connector-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modal-title">Create Connector</h3>
        <button class="close-button">&times;</button>
      </div>
      <form id="connector-form">
        <input type="hidden" id="connector-id">
        <div class="form-group">
          <label for="connector-name">Name</label>
          <input type="text" id="connector-name" required>
        </div>
        <div class="form-group">
          <label for="connector-description">Description</label>
          <textarea id="connector-description" required></textarea>
        </div>
        <div class="form-group">
          <label for="connector-version">Version</label>
          <input type="text" id="connector-version" value="1.0.0" required>
        </div>
        <div class="form-group">
          <label for="connector-type">Type</label>
          <select id="connector-type" required>
            <option value="source">Source</option>
            <option value="destination">Destination</option>
            <option value="transformation">Transformation</option>
          </select>
        </div>
        <div class="form-group">
          <label for="connector-status">Status</label>
          <select id="connector-status" required>
            <option value="draft">Draft</option>
            <option value="testing">Testing</option>
            <option value="published">Published</option>
            <option value="deprecated">Deprecated</option>
            <option value="retired">Retired</option>
          </select>
        </div>
        <div class="form-actions">
          <button type="button" class="close-button action-button" style="background-color: #ccc;">Cancel</button>
          <button type="submit" class="action-button">Save</button>
        </div>
      </form>
    </div>
  </div>
  
  <!-- View Connector Modal -->
  <div id="view-connector-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="view-modal-title">Connector Details</h3>
        <button class="close-button">&times;</button>
      </div>
      <div id="connector-details">
        <div class="loading">
          <div class="spinner"></div>
        </div>
      </div>
      <div class="form-actions">
        <button type="button" class="close-button action-button" style="background-color: #ccc;">Close</button>
      </div>
    </div>
  </div>
  
  <script>
    // DOM Elements
    const connectorList = document.getElementById('connector-list');
    const createConnectorButton = document.getElementById('create-connector-button');
    const connectorModal = document.getElementById('connector-modal');
    const viewConnectorModal = document.getElementById('view-connector-modal');
    const connectorForm = document.getElementById('connector-form');
    const connectorDetails = document.getElementById('connector-details');
    const modalTitle = document.getElementById('modal-title');
    const viewModalTitle = document.getElementById('view-modal-title');
    
    // Form Elements
    const connectorIdInput = document.getElementById('connector-id');
    const connectorNameInput = document.getElementById('connector-name');
    const connectorDescriptionInput = document.getElementById('connector-description');
    const connectorVersionInput = document.getElementById('connector-version');
    const connectorTypeInput = document.getElementById('connector-type');
    const connectorStatusInput = document.getElementById('connector-status');
    
    // Close Buttons
    const closeButtons = document.querySelectorAll('.close-button');
    
    // Event Listeners
    createConnectorButton.addEventListener('click', () => {
      modalTitle.textContent = 'Create Connector';
      connectorIdInput.value = '';
      connectorNameInput.value = '';
      connectorDescriptionInput.value = '';
      connectorVersionInput.value = '1.0.0';
      connectorTypeInput.value = 'source';
      connectorStatusInput.value = 'draft';
      connectorModal.style.display = 'block';
    });
    
    closeButtons.forEach(button => {
      button.addEventListener('click', () => {
        connectorModal.style.display = 'none';
        viewConnectorModal.style.display = 'none';
      });
    });
    
    connectorForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const connectorData = {
        name: connectorNameInput.value,
        description: connectorDescriptionInput.value,
        version: connectorVersionInput.value,
        type: connectorTypeInput.value,
        status: connectorStatusInput.value
      };
      
      try {
        if (connectorIdInput.value) {
          // Update existing connector
          await updateConnector(connectorIdInput.value, connectorData);
        } else {
          // Create new connector
          await createConnector(connectorData);
        }
        
        // Close modal and refresh list
        connectorModal.style.display = 'none';
        loadConnectors();
      } catch (error) {
        alert(`Error: ${error.message}`);
      }
    });
    
    // API Functions
    async function loadConnectors() {
      try {
        connectorList.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        
        const response = await fetch('/connectors');
        const data = await response.json();
        
        if (data.connectors && data.connectors.length > 0) {
          const connectorHtml = data.connectors.map(connector => `
            <div class="connector-card">
              <h3>${connector.name}</h3>
              <p>${connector.description}</p>
              <div>
                <span class="badge badge-${connector.status}">${connector.status}</span>
                <span>Version: ${connector.version}</span>
              </div>
              <div class="actions">
                <button class="action-button" onclick="viewConnector('${connector.id}')">View</button>
                <button class="action-button" onclick="editConnector('${connector.id}')">Edit</button>
              </div>
            </div>
          `).join('');
          
          connectorList.innerHTML = connectorHtml;
        } else {
          connectorList.innerHTML = '<p>No connectors found. Click "Create Connector" to add one.</p>';
        }
      } catch (error) {
        connectorList.innerHTML = `<p>Error loading connectors: ${error.message}</p>`;
      }
    }
    
    async function createConnector(connectorData) {
      const response = await fetch('/connectors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(connectorData)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || 'Failed to create connector');
      }
      
      return response.json();
    }
    
    async function updateConnector(id, connectorData) {
      const response = await fetch(`/connectors/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(connectorData)
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || 'Failed to update connector');
      }
      
      return response.json();
    }
    
    async function getConnector(id) {
      const response = await fetch(`/connectors/${id}`);
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || 'Failed to get connector');
      }
      
      return response.json();
    }
    
    // View Connector
    window.viewConnector = async (id) => {
      try {
        connectorDetails.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
        viewConnectorModal.style.display = 'block';
        
        const connector = await getConnector(id);
        viewModalTitle.textContent = `Connector: ${connector.name}`;
        
        const detailsHtml = `
          <div>
            <h4>Details</h4>
            <p><strong>ID:</strong> ${connector.id}</p>
            <p><strong>Name:</strong> ${connector.name}</p>
            <p><strong>Description:</strong> ${connector.description}</p>
            <p><strong>Version:</strong> ${connector.version}</p>
            <p><strong>Type:</strong> ${connector.type}</p>
            <p><strong>Status:</strong> <span class="badge badge-${connector.status}">${connector.status}</span></p>
            <p><strong>Created:</strong> ${new Date(connector.createdAt).toLocaleString()}</p>
            <p><strong>Updated:</strong> ${new Date(connector.updatedAt).toLocaleString()}</p>
          </div>
        `;
        
        connectorDetails.innerHTML = detailsHtml;
      } catch (error) {
        connectorDetails.innerHTML = `<p>Error loading connector: ${error.message}</p>`;
      }
    };
    
    // Edit Connector
    window.editConnector = async (id) => {
      try {
        const connector = await getConnector(id);
        
        modalTitle.textContent = 'Edit Connector';
        connectorIdInput.value = connector.id;
        connectorNameInput.value = connector.name;
        connectorDescriptionInput.value = connector.description;
        connectorVersionInput.value = connector.version;
        connectorTypeInput.value = connector.type;
        connectorStatusInput.value = connector.status;
        
        connectorModal.style.display = 'block';
      } catch (error) {
        alert(`Error: ${error.message}`);
      }
    };
    
    // Initialize
    document.addEventListener('DOMContentLoaded', () => {
      loadConnectors();
    });
  </script>
</body>
</html>
