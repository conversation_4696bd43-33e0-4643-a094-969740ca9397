import React from 'react';
import { useU<PERSON><PERSON> } from './useUUIC';
import UUICComponentRegistry from './UUICComponentRegistry';

/**
 * UUICRenderer - Renders specific UI blocks dynamically via config
 * 
 * This component dynamically renders UI components based on the provided
 * component key and props.
 * 
 * @param {Object} props - Component props
 * @param {string} props.componentKey - Key of the component to render
 * @param {Object} props.props - Props to pass to the rendered component
 * @returns {React.ReactElement} Rendered component
 */
const UUICRenderer = ({ componentKey, props = {} }) => {
  // First try to get the component from the context
  let Component = useUUIC(componentKey);
  
  // If not found in context, try the global registry
  if (!Component) {
    Component = UUICComponentRegistry.getComponent(componentKey);
  }
  
  // If still not found, render a fallback
  if (!Component) {
    return (
      <div className="uuic-unknown-component">
        <div className="uuic-unknown-component-message">
          Unknown component: {componentKey}
        </div>
      </div>
    );
  }
  
  // Render the component with the provided props
  return <Component {...props} />;
};

/**
 * UUICSchemaRenderer - Renders UI based on a schema
 * 
 * This component renders a complete UI based on a schema provided by the
 * NovaVision backend.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.schema - UI schema to render
 * @param {Object} props.data - Data to use for rendering
 * @param {Object} props.options - Rendering options
 * @returns {React.ReactElement} Rendered UI
 */
export const UUICSchemaRenderer = ({ schema, data = {}, options = {} }) => {
  if (!schema) {
    return (
      <div className="uuic-no-schema">
        <div className="uuic-no-schema-message">
          No schema provided
        </div>
      </div>
    );
  }
  
  // Determine the type of schema and render accordingly
  switch (schema.type) {
    case 'form':
      return <FormRenderer schema={schema} data={data} options={options} />;
    case 'dashboard':
      return <DashboardRenderer schema={schema} data={data} options={options} />;
    case 'report':
      return <ReportRenderer schema={schema} data={data} options={options} />;
    default:
      return (
        <div className="uuic-unknown-schema-type">
          <div className="uuic-unknown-schema-type-message">
            Unknown schema type: {schema.type}
          </div>
        </div>
      );
  }
};

/**
 * FormRenderer - Renders a form based on a schema
 * 
 * @param {Object} props - Component props
 * @param {Object} props.schema - Form schema
 * @param {Object} props.data - Form data
 * @param {Object} props.options - Rendering options
 * @returns {React.ReactElement} Rendered form
 */
const FormRenderer = ({ schema, data = {}, options = {} }) => {
  const { onSubmit, onChange } = options;
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (onSubmit) {
      onSubmit(data);
    }
  };
  
  const handleChange = (fieldId, value) => {
    if (onChange) {
      const newData = { ...data, [fieldId]: value };
      onChange(newData);
    }
  };
  
  return (
    <div className="uuic-form-container">
      <h2 className="uuic-form-title">{schema.title}</h2>
      {schema.description && (
        <p className="uuic-form-description">{schema.description}</p>
      )}
      
      <form className="uuic-form" onSubmit={handleSubmit}>
        {schema.sections && schema.sections.map((section) => (
          <div key={section.id} className="uuic-form-section">
            <h3 className="uuic-section-title">{section.title}</h3>
            {section.description && (
              <p className="uuic-section-description">{section.description}</p>
            )}
            
            <div className="uuic-section-fields">
              {section.fields && section.fields.map((field) => (
                <UUICRenderer
                  key={field.id}
                  componentKey={`form-field-${field.type}`}
                  props={{
                    field,
                    value: data[field.id],
                    onChange: (value) => handleChange(field.id, value)
                  }}
                />
              ))}
            </div>
          </div>
        ))}
        
        <div className="uuic-form-actions">
          {schema.actions && schema.actions.map((action) => (
            <UUICRenderer
              key={action.id}
              componentKey={`form-action-${action.type}`}
              props={{
                action,
                onAction: action.type === 'submit' ? handleSubmit : null
              }}
            />
          ))}
        </div>
      </form>
    </div>
  );
};

/**
 * DashboardRenderer - Renders a dashboard based on a schema
 * 
 * @param {Object} props - Component props
 * @param {Object} props.schema - Dashboard schema
 * @param {Object} props.data - Dashboard data
 * @param {Object} props.options - Rendering options
 * @returns {React.ReactElement} Rendered dashboard
 */
const DashboardRenderer = ({ schema, data = {}, options = {} }) => {
  const { onFilterChange } = options;
  
  const handleFilterChange = (filterId, value) => {
    if (onFilterChange) {
      const newData = { ...data, [filterId]: value };
      onFilterChange(newData);
    }
  };
  
  return (
    <div className="uuic-dashboard-container">
      <h2 className="uuic-dashboard-title">{schema.title}</h2>
      {schema.description && (
        <p className="uuic-dashboard-description">{schema.description}</p>
      )}
      
      {schema.filters && schema.filters.length > 0 && (
        <div className="uuic-dashboard-filters">
          {schema.filters.map((filter) => (
            <UUICRenderer
              key={filter.id}
              componentKey={`dashboard-filter-${filter.type}`}
              props={{
                filter,
                value: data[filter.id],
                onChange: (value) => handleFilterChange(filter.id, value)
              }}
            />
          ))}
        </div>
      )}
      
      {schema.sections && schema.sections.map((section) => (
        <div key={section.id} className="uuic-dashboard-section">
          <h3 className="uuic-section-title">{section.title}</h3>
          {section.description && (
            <p className="uuic-section-description">{section.description}</p>
          )}
          
          <div className="uuic-section-widgets">
            {section.widgets && section.widgets.map((widget) => (
              <UUICRenderer
                key={widget.id}
                componentKey={`dashboard-widget-${widget.type}`}
                props={{
                  widget,
                  data: data
                }}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * ReportRenderer - Renders a report based on a schema
 * 
 * @param {Object} props - Component props
 * @param {Object} props.schema - Report schema
 * @param {Object} props.data - Report data
 * @param {Object} props.options - Rendering options
 * @returns {React.ReactElement} Rendered report
 */
const ReportRenderer = ({ schema, data = {}, options = {} }) => {
  const { onParameterChange, onExport } = options;
  
  const handleParameterChange = (parameterId, value) => {
    if (onParameterChange) {
      const newData = { ...data, [parameterId]: value };
      onParameterChange(newData);
    }
  };
  
  const handleExport = (format) => {
    if (onExport) {
      onExport(format);
    }
  };
  
  return (
    <div className="uuic-report-container">
      <div className="uuic-report-header">
        <h2 className="uuic-report-title">{schema.title}</h2>
        {schema.description && (
          <p className="uuic-report-description">{schema.description}</p>
        )}
        
        <div className="uuic-report-actions">
          <button
            className="uuic-report-export-button"
            onClick={() => handleExport('pdf')}
          >
            Export as PDF
          </button>
          <button
            className="uuic-report-export-button"
            onClick={() => handleExport('excel')}
          >
            Export as Excel
          </button>
        </div>
      </div>
      
      {schema.parameters && schema.parameters.length > 0 && (
        <div className="uuic-report-parameters">
          {schema.parameters.map((parameter) => (
            <UUICRenderer
              key={parameter.id}
              componentKey={`report-parameter-${parameter.type}`}
              props={{
                parameter,
                value: data[parameter.id],
                onChange: (value) => handleParameterChange(parameter.id, value)
              }}
            />
          ))}
        </div>
      )}
      
      <div className="uuic-report-content">
        {schema.sections && schema.sections.map((section) => (
          <div key={section.id} className="uuic-report-section">
            <h3 className="uuic-section-title">{section.title}</h3>
            
            <div className="uuic-section-elements">
              {section.elements && section.elements.map((element) => (
                <UUICRenderer
                  key={element.id}
                  componentKey={`report-element-${element.type}`}
                  props={{
                    element,
                    data: data
                  }}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export { UUICSchemaRenderer, FormRenderer, DashboardRenderer, ReportRenderer };
export default UUICRenderer;
