{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "promises", "mkdir", "jest", "fn", "mockResolvedValue", "undefined", "readFile", "writeFile", "require", "IpRestrictionService", "fs", "path", "ipRangeCheck", "describe", "ipRestrictionService", "testDataDir", "join", "__dirname", "beforeEach", "clearAllMocks", "mockReset", "it", "expect", "dataDir", "toBe", "ipRestrictionsFile", "toHaveBeenCalledWith", "recursive", "defaultConfig", "toEqual", "enabled", "mode", "allowlist", "blocklist", "rules", "mockRestrictions", "mockResolvedValueOnce", "JSON", "stringify", "restrictions", "loadRestrictions", "error", "Error", "code", "mockRejectedValueOnce", "rejects", "toThrow", "saveRestrictions", "result", "isAllowed", "name", "action", "ipRange", "mockReturnValueOnce", "spyOn", "addToAllowlist", "toHaveBeenCalled", "savedRestrictions", "parse", "calls", "toContain", "not"], "sources": ["IpRestrictionService.test.js"], "sourcesContent": ["/**\n * IP Restriction Service Tests\n */\n\nconst IpRestrictionService = require('../../../api/services/IpRestrictionService');\nconst fs = require('fs').promises;\nconst path = require('path');\n\n// Mock fs.promises\njest.mock('fs', () => ({\n  promises: {\n    mkdir: jest.fn().mockResolvedValue(undefined),\n    readFile: jest.fn(),\n    writeFile: jest.fn().mockResolvedValue(undefined)\n  }\n}));\n\n// Mock ip-range-check\njest.mock('ip-range-check', () => jest.fn());\nconst ipRangeCheck = require('ip-range-check');\n\ndescribe('IpRestrictionService', () => {\n  let ipRestrictionService;\n  const testDataDir = path.join(__dirname, 'test-data');\n  \n  beforeEach(() => {\n    // Reset mocks\n    jest.clearAllMocks();\n    \n    // Create a new instance for each test\n    ipRestrictionService = new IpRestrictionService(testDataDir);\n    \n    // Reset ip-range-check mock\n    ipRangeCheck.mockReset();\n  });\n  \n  describe('constructor', () => {\n    it('should initialize with the correct data directory', () => {\n      expect(ipRestrictionService.dataDir).toBe(testDataDir);\n      expect(ipRestrictionService.ipRestrictionsFile).toBe(path.join(testDataDir, 'ip_restrictions.json'));\n    });\n    \n    it('should call ensureDataDir', () => {\n      expect(fs.mkdir).toHaveBeenCalledWith(testDataDir, { recursive: true });\n    });\n    \n    it('should initialize with default config', () => {\n      expect(ipRestrictionService.defaultConfig).toEqual({\n        enabled: false,\n        mode: 'allowlist',\n        allowlist: [],\n        blocklist: [],\n        rules: []\n      });\n    });\n  });\n  \n  describe('loadRestrictions', () => {\n    it('should load restrictions from file', async () => {\n      const mockRestrictions = {\n        enabled: true,\n        mode: 'allowlist',\n        allowlist: ['***********'],\n        blocklist: [],\n        rules: []\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      \n      const restrictions = await ipRestrictionService.loadRestrictions();\n      \n      expect(fs.readFile).toHaveBeenCalledWith(ipRestrictionService.ipRestrictionsFile, 'utf8');\n      expect(restrictions).toEqual(mockRestrictions);\n    });\n    \n    it('should return default config if file does not exist', async () => {\n      const error = new Error('File not found');\n      error.code = 'ENOENT';\n      fs.readFile.mockRejectedValueOnce(error);\n      \n      const restrictions = await ipRestrictionService.loadRestrictions();\n      \n      expect(restrictions).toEqual(ipRestrictionService.defaultConfig);\n    });\n    \n    it('should throw error if file read fails for other reasons', async () => {\n      const error = new Error('Permission denied');\n      fs.readFile.mockRejectedValueOnce(error);\n      \n      await expect(ipRestrictionService.loadRestrictions()).rejects.toThrow('Permission denied');\n    });\n  });\n  \n  describe('saveRestrictions', () => {\n    it('should save restrictions to file', async () => {\n      const restrictions = {\n        enabled: true,\n        mode: 'allowlist',\n        allowlist: ['***********'],\n        blocklist: [],\n        rules: []\n      };\n      \n      await ipRestrictionService.saveRestrictions(restrictions);\n      \n      expect(fs.writeFile).toHaveBeenCalledWith(\n        ipRestrictionService.ipRestrictionsFile,\n        JSON.stringify(restrictions, null, 2)\n      );\n    });\n    \n    it('should throw error if file write fails', async () => {\n      const error = new Error('Permission denied');\n      fs.writeFile.mockRejectedValueOnce(error);\n      \n      await expect(ipRestrictionService.saveRestrictions({})).rejects.toThrow('Permission denied');\n    });\n  });\n  \n  describe('isAllowed', () => {\n    it('should allow all IPs if restrictions are disabled', async () => {\n      const mockRestrictions = {\n        enabled: false\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      \n      const result = await ipRestrictionService.isAllowed('***********');\n      \n      expect(result).toBe(true);\n    });\n    \n    it('should check rules first', async () => {\n      const mockRestrictions = {\n        enabled: true,\n        mode: 'allowlist',\n        allowlist: [],\n        blocklist: [],\n        rules: [\n          {\n            name: 'Allow Office',\n            action: 'allow',\n            ipRange: '***********/16'\n          }\n        ]\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      ipRangeCheck.mockReturnValueOnce(true); // IP matches rule\n      \n      const result = await ipRestrictionService.isAllowed('***********');\n      \n      expect(ipRangeCheck).toHaveBeenCalledWith('***********', '***********/16');\n      expect(result).toBe(true);\n    });\n    \n    it('should check allowlist in allowlist mode', async () => {\n      const mockRestrictions = {\n        enabled: true,\n        mode: 'allowlist',\n        allowlist: ['***********'],\n        blocklist: [],\n        rules: []\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      \n      const result = await ipRestrictionService.isAllowed('***********');\n      \n      expect(result).toBe(true);\n    });\n    \n    it('should deny IP not in allowlist in allowlist mode', async () => {\n      const mockRestrictions = {\n        enabled: true,\n        mode: 'allowlist',\n        allowlist: ['***********'],\n        blocklist: [],\n        rules: []\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      \n      const result = await ipRestrictionService.isAllowed('***********');\n      \n      expect(result).toBe(false);\n    });\n    \n    it('should check blocklist in blocklist mode', async () => {\n      const mockRestrictions = {\n        enabled: true,\n        mode: 'blocklist',\n        allowlist: [],\n        blocklist: ['***********'],\n        rules: []\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      \n      const result = await ipRestrictionService.isAllowed('***********');\n      \n      expect(result).toBe(false);\n    });\n    \n    it('should allow IP not in blocklist in blocklist mode', async () => {\n      const mockRestrictions = {\n        enabled: true,\n        mode: 'blocklist',\n        allowlist: [],\n        blocklist: ['***********'],\n        rules: []\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      \n      const result = await ipRestrictionService.isAllowed('***********');\n      \n      expect(result).toBe(true);\n    });\n  });\n  \n  describe('addToAllowlist', () => {\n    it('should add IP to allowlist', async () => {\n      const mockRestrictions = {\n        enabled: true,\n        mode: 'allowlist',\n        allowlist: [],\n        blocklist: [],\n        rules: []\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      \n      // Mock isValidIpOrRange\n      jest.spyOn(ipRestrictionService, 'isValidIpOrRange').mockReturnValueOnce(true);\n      \n      await ipRestrictionService.addToAllowlist('***********');\n      \n      expect(fs.writeFile).toHaveBeenCalled();\n      const savedRestrictions = JSON.parse(fs.writeFile.mock.calls[0][1]);\n      expect(savedRestrictions.allowlist).toContain('***********');\n    });\n    \n    it('should remove IP from blocklist if added to allowlist', async () => {\n      const mockRestrictions = {\n        enabled: true,\n        mode: 'allowlist',\n        allowlist: [],\n        blocklist: ['***********'],\n        rules: []\n      };\n      \n      fs.readFile.mockResolvedValueOnce(JSON.stringify(mockRestrictions));\n      \n      // Mock isValidIpOrRange\n      jest.spyOn(ipRestrictionService, 'isValidIpOrRange').mockReturnValueOnce(true);\n      \n      await ipRestrictionService.addToAllowlist('***********');\n      \n      expect(fs.writeFile).toHaveBeenCalled();\n      const savedRestrictions = JSON.parse(fs.writeFile.mock.calls[0][1]);\n      expect(savedRestrictions.allowlist).toContain('***********');\n      expect(savedRestrictions.blocklist).not.toContain('***********');\n    });\n  });\n});\n"], "mappings": "AAQA;AACAA,WAAA,GAAKC,IAAI,CAAC,IAAI,EAAE,OAAO;EACrBC,QAAQ,EAAE;IACRC,KAAK,EAAEC,IAAI,CAACC,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAACC,SAAS,CAAC;IAC7CC,QAAQ,EAAEJ,IAAI,CAACC,EAAE,CAAC,CAAC;IACnBI,SAAS,EAAEL,IAAI,CAACC,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAACC,SAAS;EAClD;AACF,CAAC,CAAC,CAAC;;AAEH;AACAP,WAAA,GAAKC,IAAI,CAAC,gBAAgB,EAAE,MAAMG,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC;AAAC,SAAAL,YAAA;EAAA;IAAAI;EAAA,IAAAM,OAAA;EAAAV,WAAA,GAAAA,CAAA,KAAAI,IAAA;EAAA,OAAAA,IAAA;AAAA;AAlB7C;AACA;AACA;;AAEA,MAAMO,oBAAoB,GAAGD,OAAO,CAAC,4CAA4C,CAAC;AAClF,MAAME,EAAE,GAAGF,OAAO,CAAC,IAAI,CAAC,CAACR,QAAQ;AACjC,MAAMW,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;AAa5B,MAAMI,YAAY,GAAGJ,OAAO,CAAC,gBAAgB,CAAC;AAE9CK,QAAQ,CAAC,sBAAsB,EAAE,MAAM;EACrC,IAAIC,oBAAoB;EACxB,MAAMC,WAAW,GAAGJ,IAAI,CAACK,IAAI,CAACC,SAAS,EAAE,WAAW,CAAC;EAErDC,UAAU,CAAC,MAAM;IACf;IACAhB,IAAI,CAACiB,aAAa,CAAC,CAAC;;IAEpB;IACAL,oBAAoB,GAAG,IAAIL,oBAAoB,CAACM,WAAW,CAAC;;IAE5D;IACAH,YAAY,CAACQ,SAAS,CAAC,CAAC;EAC1B,CAAC,CAAC;EAEFP,QAAQ,CAAC,aAAa,EAAE,MAAM;IAC5BQ,EAAE,CAAC,mDAAmD,EAAE,MAAM;MAC5DC,MAAM,CAACR,oBAAoB,CAACS,OAAO,CAAC,CAACC,IAAI,CAACT,WAAW,CAAC;MACtDO,MAAM,CAACR,oBAAoB,CAACW,kBAAkB,CAAC,CAACD,IAAI,CAACb,IAAI,CAACK,IAAI,CAACD,WAAW,EAAE,sBAAsB,CAAC,CAAC;IACtG,CAAC,CAAC;IAEFM,EAAE,CAAC,2BAA2B,EAAE,MAAM;MACpCC,MAAM,CAACZ,EAAE,CAACT,KAAK,CAAC,CAACyB,oBAAoB,CAACX,WAAW,EAAE;QAAEY,SAAS,EAAE;MAAK,CAAC,CAAC;IACzE,CAAC,CAAC;IAEFN,EAAE,CAAC,uCAAuC,EAAE,MAAM;MAChDC,MAAM,CAACR,oBAAoB,CAACc,aAAa,CAAC,CAACC,OAAO,CAAC;QACjDC,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrB,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCQ,EAAE,CAAC,oCAAoC,EAAE,YAAY;MACnD,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,CAAC,aAAa,CAAC;QAC1BC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE;MACT,CAAC;MAEDxB,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;MAEnE,MAAMI,YAAY,GAAG,MAAMzB,oBAAoB,CAAC0B,gBAAgB,CAAC,CAAC;MAElElB,MAAM,CAACZ,EAAE,CAACJ,QAAQ,CAAC,CAACoB,oBAAoB,CAACZ,oBAAoB,CAACW,kBAAkB,EAAE,MAAM,CAAC;MACzFH,MAAM,CAACiB,YAAY,CAAC,CAACV,OAAO,CAACM,gBAAgB,CAAC;IAChD,CAAC,CAAC;IAEFd,EAAE,CAAC,qDAAqD,EAAE,YAAY;MACpE,MAAMoB,KAAK,GAAG,IAAIC,KAAK,CAAC,gBAAgB,CAAC;MACzCD,KAAK,CAACE,IAAI,GAAG,QAAQ;MACrBjC,EAAE,CAACJ,QAAQ,CAACsC,qBAAqB,CAACH,KAAK,CAAC;MAExC,MAAMF,YAAY,GAAG,MAAMzB,oBAAoB,CAAC0B,gBAAgB,CAAC,CAAC;MAElElB,MAAM,CAACiB,YAAY,CAAC,CAACV,OAAO,CAACf,oBAAoB,CAACc,aAAa,CAAC;IAClE,CAAC,CAAC;IAEFP,EAAE,CAAC,yDAAyD,EAAE,YAAY;MACxE,MAAMoB,KAAK,GAAG,IAAIC,KAAK,CAAC,mBAAmB,CAAC;MAC5ChC,EAAE,CAACJ,QAAQ,CAACsC,qBAAqB,CAACH,KAAK,CAAC;MAExC,MAAMnB,MAAM,CAACR,oBAAoB,CAAC0B,gBAAgB,CAAC,CAAC,CAAC,CAACK,OAAO,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAC5F,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCQ,EAAE,CAAC,kCAAkC,EAAE,YAAY;MACjD,MAAMkB,YAAY,GAAG;QACnBT,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,CAAC,aAAa,CAAC;QAC1BC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE;MACT,CAAC;MAED,MAAMpB,oBAAoB,CAACiC,gBAAgB,CAACR,YAAY,CAAC;MAEzDjB,MAAM,CAACZ,EAAE,CAACH,SAAS,CAAC,CAACmB,oBAAoB,CACvCZ,oBAAoB,CAACW,kBAAkB,EACvCY,IAAI,CAACC,SAAS,CAACC,YAAY,EAAE,IAAI,EAAE,CAAC,CACtC,CAAC;IACH,CAAC,CAAC;IAEFlB,EAAE,CAAC,wCAAwC,EAAE,YAAY;MACvD,MAAMoB,KAAK,GAAG,IAAIC,KAAK,CAAC,mBAAmB,CAAC;MAC5ChC,EAAE,CAACH,SAAS,CAACqC,qBAAqB,CAACH,KAAK,CAAC;MAEzC,MAAMnB,MAAM,CAACR,oBAAoB,CAACiC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAACF,OAAO,CAACC,OAAO,CAAC,mBAAmB,CAAC;IAC9F,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,WAAW,EAAE,MAAM;IAC1BQ,EAAE,CAAC,mDAAmD,EAAE,YAAY;MAClE,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE;MACX,CAAC;MAEDpB,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;MAEnE,MAAMa,MAAM,GAAG,MAAMlC,oBAAoB,CAACmC,SAAS,CAAC,aAAa,CAAC;MAElE3B,MAAM,CAAC0B,MAAM,CAAC,CAACxB,IAAI,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC;IAEFH,EAAE,CAAC,0BAA0B,EAAE,YAAY;MACzC,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,CACL;UACEgB,IAAI,EAAE,cAAc;UACpBC,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE;QACX,CAAC;MAEL,CAAC;MAED1C,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;MACnEvB,YAAY,CAACyC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;MAExC,MAAML,MAAM,GAAG,MAAMlC,oBAAoB,CAACmC,SAAS,CAAC,aAAa,CAAC;MAElE3B,MAAM,CAACV,YAAY,CAAC,CAACc,oBAAoB,CAAC,aAAa,EAAE,gBAAgB,CAAC;MAC1EJ,MAAM,CAAC0B,MAAM,CAAC,CAACxB,IAAI,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC;IAEFH,EAAE,CAAC,0CAA0C,EAAE,YAAY;MACzD,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,CAAC,aAAa,CAAC;QAC1BC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE;MACT,CAAC;MAEDxB,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;MAEnE,MAAMa,MAAM,GAAG,MAAMlC,oBAAoB,CAACmC,SAAS,CAAC,aAAa,CAAC;MAElE3B,MAAM,CAAC0B,MAAM,CAAC,CAACxB,IAAI,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC;IAEFH,EAAE,CAAC,mDAAmD,EAAE,YAAY;MAClE,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,CAAC,aAAa,CAAC;QAC1BC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE;MACT,CAAC;MAEDxB,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;MAEnE,MAAMa,MAAM,GAAG,MAAMlC,oBAAoB,CAACmC,SAAS,CAAC,aAAa,CAAC;MAElE3B,MAAM,CAAC0B,MAAM,CAAC,CAACxB,IAAI,CAAC,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,0CAA0C,EAAE,YAAY;MACzD,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,CAAC,aAAa,CAAC;QAC1BC,KAAK,EAAE;MACT,CAAC;MAEDxB,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;MAEnE,MAAMa,MAAM,GAAG,MAAMlC,oBAAoB,CAACmC,SAAS,CAAC,aAAa,CAAC;MAElE3B,MAAM,CAAC0B,MAAM,CAAC,CAACxB,IAAI,CAAC,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEFH,EAAE,CAAC,oDAAoD,EAAE,YAAY;MACnE,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,CAAC,aAAa,CAAC;QAC1BC,KAAK,EAAE;MACT,CAAC;MAEDxB,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;MAEnE,MAAMa,MAAM,GAAG,MAAMlC,oBAAoB,CAACmC,SAAS,CAAC,aAAa,CAAC;MAElE3B,MAAM,CAAC0B,MAAM,CAAC,CAACxB,IAAI,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFX,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BQ,EAAE,CAAC,4BAA4B,EAAE,YAAY;MAC3C,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE;MACT,CAAC;MAEDxB,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;;MAEnE;MACAjC,IAAI,CAACoD,KAAK,CAACxC,oBAAoB,EAAE,kBAAkB,CAAC,CAACuC,mBAAmB,CAAC,IAAI,CAAC;MAE9E,MAAMvC,oBAAoB,CAACyC,cAAc,CAAC,aAAa,CAAC;MAExDjC,MAAM,CAACZ,EAAE,CAACH,SAAS,CAAC,CAACiD,gBAAgB,CAAC,CAAC;MACvC,MAAMC,iBAAiB,GAAGpB,IAAI,CAACqB,KAAK,CAAChD,EAAE,CAACH,SAAS,CAACR,IAAI,CAAC4D,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnErC,MAAM,CAACmC,iBAAiB,CAACzB,SAAS,CAAC,CAAC4B,SAAS,CAAC,aAAa,CAAC;IAC9D,CAAC,CAAC;IAEFvC,EAAE,CAAC,uDAAuD,EAAE,YAAY;MACtE,MAAMc,gBAAgB,GAAG;QACvBL,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,WAAW;QACjBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,CAAC,aAAa,CAAC;QAC1BC,KAAK,EAAE;MACT,CAAC;MAEDxB,EAAE,CAACJ,QAAQ,CAAC8B,qBAAqB,CAACC,IAAI,CAACC,SAAS,CAACH,gBAAgB,CAAC,CAAC;;MAEnE;MACAjC,IAAI,CAACoD,KAAK,CAACxC,oBAAoB,EAAE,kBAAkB,CAAC,CAACuC,mBAAmB,CAAC,IAAI,CAAC;MAE9E,MAAMvC,oBAAoB,CAACyC,cAAc,CAAC,aAAa,CAAC;MAExDjC,MAAM,CAACZ,EAAE,CAACH,SAAS,CAAC,CAACiD,gBAAgB,CAAC,CAAC;MACvC,MAAMC,iBAAiB,GAAGpB,IAAI,CAACqB,KAAK,CAAChD,EAAE,CAACH,SAAS,CAACR,IAAI,CAAC4D,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnErC,MAAM,CAACmC,iBAAiB,CAACzB,SAAS,CAAC,CAAC4B,SAAS,CAAC,aAAa,CAAC;MAC5DtC,MAAM,CAACmC,iBAAiB,CAACxB,SAAS,CAAC,CAAC4B,GAAG,CAACD,SAAS,CAAC,aAAa,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}