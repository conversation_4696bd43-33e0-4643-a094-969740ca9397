"""
Assessment Manager for the Universal Vendor Risk Management System.

This module provides functionality for managing vendor assessments.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AssessmentManager:
    """
    Manager for vendor assessments.
    
    This class is responsible for creating, retrieving, updating, and managing
    vendor assessments.
    """
    
    def __init__(self, assessments_dir: Optional[str] = None):
        """
        Initialize the Assessment Manager.
        
        Args:
            assessments_dir: Path to a directory for storing assessment information
        """
        logger.info("Initializing Assessment Manager")
        
        # Set the assessments directory
        self.assessments_dir = assessments_dir or os.path.join(os.getcwd(), 'assessment_data')
        
        # Create the assessments directory if it doesn't exist
        os.makedirs(self.assessments_dir, exist_ok=True)
        
        # Dictionary to store assessments in memory
        self.assessments: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store assessment templates
        self.assessment_templates: Dict[str, Dict[str, Any]] = {}
        
        # Load assessments from disk
        self._load_assessments_from_disk()
        
        # Register default assessment templates
        self._register_default_assessment_templates()
        
        logger.info(f"Assessment Manager initialized with {len(self.assessments)} assessments and {len(self.assessment_templates)} templates")
    
    def _register_default_assessment_templates(self) -> None:
        """Register default assessment templates."""
        # Security assessment template
        self.register_assessment_template({
            'id': 'security_assessment',
            'name': 'Security Assessment',
            'description': 'Assessment of vendor security controls and practices',
            'categories': ['security'],
            'sections': [
                {
                    'id': 'security_policies',
                    'name': 'Security Policies',
                    'description': 'Assessment of vendor security policies',
                    'questions': [
                        {
                            'id': 'security_policy_1',
                            'text': 'Does the vendor have a documented information security policy?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'security_policy_2',
                            'text': 'Is the information security policy reviewed and updated regularly?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'security_policy_3',
                            'text': 'Does the vendor have a dedicated security team or officer?',
                            'type': 'yes_no',
                            'required': True
                        }
                    ]
                },
                {
                    'id': 'access_controls',
                    'name': 'Access Controls',
                    'description': 'Assessment of vendor access controls',
                    'questions': [
                        {
                            'id': 'access_control_1',
                            'text': 'Does the vendor implement the principle of least privilege?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'access_control_2',
                            'text': 'Does the vendor use multi-factor authentication?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'access_control_3',
                            'text': 'How often are access rights reviewed?',
                            'type': 'multiple_choice',
                            'options': ['Monthly', 'Quarterly', 'Annually', 'Never'],
                            'required': True
                        }
                    ]
                },
                {
                    'id': 'data_protection',
                    'name': 'Data Protection',
                    'description': 'Assessment of vendor data protection measures',
                    'questions': [
                        {
                            'id': 'data_protection_1',
                            'text': 'Is sensitive data encrypted at rest?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'data_protection_2',
                            'text': 'Is sensitive data encrypted in transit?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'data_protection_3',
                            'text': 'Does the vendor have a data classification policy?',
                            'type': 'yes_no',
                            'required': True
                        }
                    ]
                }
            ]
        })
        
        # Privacy assessment template
        self.register_assessment_template({
            'id': 'privacy_assessment',
            'name': 'Privacy Assessment',
            'description': 'Assessment of vendor privacy practices',
            'categories': ['privacy'],
            'sections': [
                {
                    'id': 'privacy_policies',
                    'name': 'Privacy Policies',
                    'description': 'Assessment of vendor privacy policies',
                    'questions': [
                        {
                            'id': 'privacy_policy_1',
                            'text': 'Does the vendor have a documented privacy policy?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'privacy_policy_2',
                            'text': 'Is the privacy policy compliant with relevant regulations (e.g., GDPR, CCPA)?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'privacy_policy_3',
                            'text': 'Does the vendor have a dedicated privacy officer?',
                            'type': 'yes_no',
                            'required': True
                        }
                    ]
                },
                {
                    'id': 'data_subject_rights',
                    'name': 'Data Subject Rights',
                    'description': 'Assessment of vendor data subject rights handling',
                    'questions': [
                        {
                            'id': 'data_subject_rights_1',
                            'text': 'Does the vendor have processes for handling data subject access requests?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'data_subject_rights_2',
                            'text': 'Does the vendor have processes for handling data subject deletion requests?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'data_subject_rights_3',
                            'text': 'What is the average response time for data subject requests?',
                            'type': 'multiple_choice',
                            'options': ['Less than 1 week', '1-2 weeks', '2-4 weeks', 'More than 4 weeks'],
                            'required': True
                        }
                    ]
                },
                {
                    'id': 'data_processing',
                    'name': 'Data Processing',
                    'description': 'Assessment of vendor data processing practices',
                    'questions': [
                        {
                            'id': 'data_processing_1',
                            'text': 'Does the vendor document the purposes for which personal data is processed?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'data_processing_2',
                            'text': 'Does the vendor implement data minimization principles?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'data_processing_3',
                            'text': 'Does the vendor have data retention policies?',
                            'type': 'yes_no',
                            'required': True
                        }
                    ]
                }
            ]
        })
        
        # Business continuity assessment template
        self.register_assessment_template({
            'id': 'business_continuity_assessment',
            'name': 'Business Continuity Assessment',
            'description': 'Assessment of vendor business continuity and disaster recovery capabilities',
            'categories': ['business_continuity'],
            'sections': [
                {
                    'id': 'business_continuity_planning',
                    'name': 'Business Continuity Planning',
                    'description': 'Assessment of vendor business continuity planning',
                    'questions': [
                        {
                            'id': 'business_continuity_1',
                            'text': 'Does the vendor have a documented business continuity plan?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'business_continuity_2',
                            'text': 'How often is the business continuity plan tested?',
                            'type': 'multiple_choice',
                            'options': ['Monthly', 'Quarterly', 'Annually', 'Never'],
                            'required': True
                        },
                        {
                            'id': 'business_continuity_3',
                            'text': 'Does the vendor have a dedicated business continuity team?',
                            'type': 'yes_no',
                            'required': True
                        }
                    ]
                },
                {
                    'id': 'disaster_recovery',
                    'name': 'Disaster Recovery',
                    'description': 'Assessment of vendor disaster recovery capabilities',
                    'questions': [
                        {
                            'id': 'disaster_recovery_1',
                            'text': 'Does the vendor have a documented disaster recovery plan?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'disaster_recovery_2',
                            'text': 'What is the vendor\'s Recovery Time Objective (RTO)?',
                            'type': 'multiple_choice',
                            'options': ['Less than 1 hour', '1-4 hours', '4-24 hours', 'More than 24 hours'],
                            'required': True
                        },
                        {
                            'id': 'disaster_recovery_3',
                            'text': 'What is the vendor\'s Recovery Point Objective (RPO)?',
                            'type': 'multiple_choice',
                            'options': ['Less than 15 minutes', '15-60 minutes', '1-24 hours', 'More than 24 hours'],
                            'required': True
                        }
                    ]
                },
                {
                    'id': 'incident_response',
                    'name': 'Incident Response',
                    'description': 'Assessment of vendor incident response capabilities',
                    'questions': [
                        {
                            'id': 'incident_response_1',
                            'text': 'Does the vendor have a documented incident response plan?',
                            'type': 'yes_no',
                            'required': True
                        },
                        {
                            'id': 'incident_response_2',
                            'text': 'How often is the incident response plan tested?',
                            'type': 'multiple_choice',
                            'options': ['Monthly', 'Quarterly', 'Annually', 'Never'],
                            'required': True
                        },
                        {
                            'id': 'incident_response_3',
                            'text': 'Does the vendor have a dedicated incident response team?',
                            'type': 'yes_no',
                            'required': True
                        }
                    ]
                }
            ]
        })
    
    def register_assessment_template(self, template: Dict[str, Any]) -> None:
        """
        Register an assessment template.
        
        Args:
            template: The assessment template
            
        Raises:
            ValueError: If the template is invalid
        """
        # Validate the template
        if 'id' not in template:
            raise ValueError("Assessment template ID is required")
        
        if 'name' not in template:
            raise ValueError("Assessment template name is required")
        
        if 'sections' not in template or not isinstance(template['sections'], list):
            raise ValueError("Assessment template must have sections")
        
        # Register the template
        template_id = template['id']
        self.assessment_templates[template_id] = template
        
        logger.info(f"Registered assessment template: {template_id}")
    
    def get_assessment_template(self, template_id: str) -> Dict[str, Any]:
        """
        Get an assessment template.
        
        Args:
            template_id: The ID of the template
            
        Returns:
            The assessment template
            
        Raises:
            ValueError: If the template does not exist
        """
        if template_id not in self.assessment_templates:
            raise ValueError(f"Assessment template not found: {template_id}")
        
        return self.assessment_templates[template_id]
    
    def get_all_assessment_templates(self) -> List[Dict[str, Any]]:
        """
        Get all assessment templates.
        
        Returns:
            List of assessment templates
        """
        return list(self.assessment_templates.values())
    
    def create_assessment(self, 
                         vendor_id: str, 
                         template_id: str, 
                         assessment_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a new assessment.
        
        Args:
            vendor_id: The ID of the vendor
            template_id: The ID of the assessment template
            assessment_data: Additional assessment data
            
        Returns:
            The created assessment
            
        Raises:
            ValueError: If the vendor ID or template ID is invalid
        """
        logger.info(f"Creating new assessment for vendor {vendor_id} using template {template_id}")
        
        # Check if the template exists
        if template_id not in self.assessment_templates:
            raise ValueError(f"Assessment template not found: {template_id}")
        
        # Get the template
        template = self.assessment_templates[template_id]
        
        # Generate a unique assessment ID
        assessment_id = str(uuid.uuid4())
        
        # Create the assessment object
        assessment = {
            'id': assessment_id,
            'vendor_id': vendor_id,
            'template_id': template_id,
            'name': assessment_data.get('name') if assessment_data else template['name'],
            'description': assessment_data.get('description') if assessment_data else template['description'],
            'status': 'pending',
            'sections': [],
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp(),
            'completed_at': None
        }
        
        # Copy sections from the template
        for section in template['sections']:
            assessment_section = {
                'id': section['id'],
                'name': section['name'],
                'description': section['description'],
                'questions': []
            }
            
            # Copy questions from the template
            for question in section['questions']:
                assessment_question = {
                    'id': question['id'],
                    'text': question['text'],
                    'type': question['type'],
                    'required': question.get('required', False),
                    'options': question.get('options', []),
                    'answer': None,
                    'notes': None
                }
                
                assessment_section['questions'].append(assessment_question)
            
            assessment['sections'].append(assessment_section)
        
        # Store the assessment in memory
        self.assessments[assessment_id] = assessment
        
        # Store the assessment on disk
        self._save_assessment_to_disk(assessment)
        
        logger.info(f"Assessment created: {assessment_id}")
        
        return assessment
    
    def get_assessment(self, assessment_id: str) -> Dict[str, Any]:
        """
        Get an assessment.
        
        Args:
            assessment_id: The ID of the assessment
            
        Returns:
            The assessment
            
        Raises:
            ValueError: If the assessment does not exist
        """
        logger.info(f"Getting assessment: {assessment_id}")
        
        if assessment_id not in self.assessments:
            raise ValueError(f"Assessment not found: {assessment_id}")
        
        return self.assessments[assessment_id]
    
    def update_assessment(self, 
                         assessment_id: str, 
                         assessment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an assessment.
        
        Args:
            assessment_id: The ID of the assessment
            assessment_data: The updated assessment data
            
        Returns:
            The updated assessment
            
        Raises:
            ValueError: If the assessment does not exist
        """
        logger.info(f"Updating assessment: {assessment_id}")
        
        # Check if the assessment exists
        if assessment_id not in self.assessments:
            raise ValueError(f"Assessment not found: {assessment_id}")
        
        # Get the existing assessment
        assessment = self.assessments[assessment_id]
        
        # Update the assessment data
        if 'name' in assessment_data:
            assessment['name'] = assessment_data['name']
        
        if 'description' in assessment_data:
            assessment['description'] = assessment_data['description']
        
        if 'status' in assessment_data:
            assessment['status'] = assessment_data['status']
            
            # If the status is changed to 'completed', set the completed_at timestamp
            if assessment_data['status'] == 'completed' and assessment['status'] != 'completed':
                assessment['completed_at'] = self._get_current_timestamp()
        
        # Update the updated_at timestamp
        assessment['updated_at'] = self._get_current_timestamp()
        
        # Store the updated assessment on disk
        self._save_assessment_to_disk(assessment)
        
        logger.info(f"Assessment updated: {assessment_id}")
        
        return assessment
    
    def answer_question(self, 
                       assessment_id: str, 
                       section_id: str, 
                       question_id: str, 
                       answer: Any, 
                       notes: Optional[str] = None) -> Dict[str, Any]:
        """
        Answer a question in an assessment.
        
        Args:
            assessment_id: The ID of the assessment
            section_id: The ID of the section
            question_id: The ID of the question
            answer: The answer to the question
            notes: Optional notes for the answer
            
        Returns:
            The updated assessment
            
        Raises:
            ValueError: If the assessment, section, or question does not exist
        """
        logger.info(f"Answering question {question_id} in section {section_id} of assessment {assessment_id}")
        
        # Check if the assessment exists
        if assessment_id not in self.assessments:
            raise ValueError(f"Assessment not found: {assessment_id}")
        
        # Get the existing assessment
        assessment = self.assessments[assessment_id]
        
        # Find the section
        section = None
        for s in assessment['sections']:
            if s['id'] == section_id:
                section = s
                break
        
        if not section:
            raise ValueError(f"Section not found: {section_id}")
        
        # Find the question
        question = None
        for q in section['questions']:
            if q['id'] == question_id:
                question = q
                break
        
        if not question:
            raise ValueError(f"Question not found: {question_id}")
        
        # Validate the answer
        if question['type'] == 'yes_no' and answer not in ['yes', 'no']:
            raise ValueError(f"Invalid answer for yes/no question: {answer}")
        
        if question['type'] == 'multiple_choice' and answer not in question['options']:
            raise ValueError(f"Invalid answer for multiple choice question: {answer}")
        
        # Update the question
        question['answer'] = answer
        question['notes'] = notes
        
        # Update the updated_at timestamp
        assessment['updated_at'] = self._get_current_timestamp()
        
        # Store the updated assessment on disk
        self._save_assessment_to_disk(assessment)
        
        logger.info(f"Question answered: {question_id}")
        
        return assessment
    
    def delete_assessment(self, assessment_id: str) -> None:
        """
        Delete an assessment.
        
        Args:
            assessment_id: The ID of the assessment
            
        Raises:
            ValueError: If the assessment does not exist
        """
        logger.info(f"Deleting assessment: {assessment_id}")
        
        # Check if the assessment exists
        if assessment_id not in self.assessments:
            raise ValueError(f"Assessment not found: {assessment_id}")
        
        # Remove the assessment from memory
        del self.assessments[assessment_id]
        
        # Remove the assessment from disk
        self._delete_assessment_from_disk(assessment_id)
        
        logger.info(f"Assessment deleted: {assessment_id}")
    
    def get_vendor_assessments(self, vendor_id: str) -> List[Dict[str, Any]]:
        """
        Get all assessments for a vendor.
        
        Args:
            vendor_id: The ID of the vendor
            
        Returns:
            List of assessments for the vendor
        """
        logger.info(f"Getting assessments for vendor: {vendor_id}")
        
        return [a for a in self.assessments.values() if a['vendor_id'] == vendor_id]
    
    def get_assessment_status(self, assessment_id: str) -> Dict[str, Any]:
        """
        Get the status of an assessment.
        
        Args:
            assessment_id: The ID of the assessment
            
        Returns:
            The assessment status
            
        Raises:
            ValueError: If the assessment does not exist
        """
        logger.info(f"Getting status of assessment: {assessment_id}")
        
        # Check if the assessment exists
        if assessment_id not in self.assessments:
            raise ValueError(f"Assessment not found: {assessment_id}")
        
        # Get the assessment
        assessment = self.assessments[assessment_id]
        
        # Calculate the completion status
        total_questions = 0
        answered_questions = 0
        
        for section in assessment['sections']:
            for question in section['questions']:
                total_questions += 1
                
                if question['answer'] is not None:
                    answered_questions += 1
        
        completion_percentage = (answered_questions / total_questions * 100) if total_questions > 0 else 0
        
        # Create the status object
        status = {
            'assessment_id': assessment_id,
            'status': assessment['status'],
            'total_questions': total_questions,
            'answered_questions': answered_questions,
            'completion_percentage': completion_percentage,
            'created_at': assessment['created_at'],
            'updated_at': assessment['updated_at'],
            'completed_at': assessment['completed_at']
        }
        
        return status
    
    def _load_assessments_from_disk(self) -> None:
        """Load assessments from disk."""
        try:
            # Get all JSON files in the assessments directory
            assessment_files = [f for f in os.listdir(self.assessments_dir) if f.endswith('.json')]
            
            for assessment_file in assessment_files:
                try:
                    # Load the assessment from disk
                    file_path = os.path.join(self.assessments_dir, assessment_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        assessment = json.load(f)
                    
                    # Store the assessment in memory
                    assessment_id = assessment.get('id')
                    
                    if assessment_id:
                        self.assessments[assessment_id] = assessment
                        logger.info(f"Loaded assessment from disk: {assessment_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load assessment from {assessment_file}: {e}")
            
            logger.info(f"Loaded {len(self.assessments)} assessments from disk")
        
        except Exception as e:
            logger.error(f"Failed to load assessments from disk: {e}")
    
    def _save_assessment_to_disk(self, assessment: Dict[str, Any]) -> None:
        """
        Save an assessment to disk.
        
        Args:
            assessment: The assessment to save
        """
        try:
            # Get the assessment ID
            assessment_id = assessment.get('id')
            
            if not assessment_id:
                raise ValueError("Assessment ID is missing")
            
            # Create the file path
            file_path = os.path.join(self.assessments_dir, f"{assessment_id}.json")
            
            # Save the assessment to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(assessment, f, indent=2)
            
            logger.info(f"Saved assessment to disk: {assessment_id}")
        
        except Exception as e:
            logger.error(f"Failed to save assessment to disk: {e}")
    
    def _delete_assessment_from_disk(self, assessment_id: str) -> None:
        """
        Delete an assessment from disk.
        
        Args:
            assessment_id: The ID of the assessment
        """
        try:
            # Create the file path
            file_path = os.path.join(self.assessments_dir, f"{assessment_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted assessment from disk: {assessment_id}")
            else:
                logger.warning(f"Assessment file not found on disk: {assessment_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete assessment from disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
