<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Finite Universe Principle Dashboard</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">
          <i class="bi bi-shield-lock-fill me-2"></i>
          Finite Universe Principle Dashboard
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav">
            <li class="nav-item">
              <a class="nav-link active" href="#overview">Overview</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#domains">Domains</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#alerts">Alerts</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#history">History</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="row mt-3">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
              <i class="bi bi-speedometer2 me-2"></i>
              System Status
            </h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <div class="card mb-3">
                  <div class="card-body text-center">
                    <h5 class="card-title">Boundary Violations</h5>
                    <h2 id="boundary-violations" class="text-danger">0</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card mb-3">
                  <div class="card-body text-center">
                    <h5 class="card-title">Boundary Corrections</h5>
                    <h2 id="boundary-corrections" class="text-success">0</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card mb-3">
                  <div class="card-body text-center">
                    <h5 class="card-title">Validation Failures</h5>
                    <h2 id="validation-failures" class="text-warning">0</h2>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="card mb-3">
                  <div class="card-body text-center">
                    <h5 class="card-title">Active Alerts</h5>
                    <h2 id="active-alerts" class="text-info">0</h2>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-success text-white">
            <h5 class="card-title mb-0">
              <i class="bi bi-graph-up me-2"></i>
              Boundary Metrics
            </h5>
          </div>
          <div class="card-body">
            <canvas id="boundary-chart"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
              <i class="bi bi-diagram-3 me-2"></i>
              Domain Metrics
            </h5>
          </div>
          <div class="card-body">
            <canvas id="domain-chart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header bg-warning text-dark">
            <h5 class="card-title mb-0">
              <i class="bi bi-exclamation-triangle me-2"></i>
              Alerts
            </h5>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Time</th>
                    <th>Type</th>
                    <th>Level</th>
                    <th>Message</th>
                  </tr>
                </thead>
                <tbody id="alerts-table-body">
                  <!-- Alerts will be added here dynamically -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Machine Learning Components -->
    <div class="row mt-3">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header bg-purple text-white" style="background-color: #6f42c1;">
            <h5 class="card-title mb-0">
              <i class="bi bi-robot me-2"></i>
              Machine Learning Insights
            </h5>
          </div>
          <div class="card-body">
            <ul class="nav nav-tabs" id="mlTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="anomalies-tab" data-bs-toggle="tab" data-bs-target="#anomalies" type="button" role="tab">Anomaly Detection</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="predictions-tab" data-bs-toggle="tab" data-bs-target="#predictions" type="button" role="tab">Predictive Analytics</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">ML Settings</button>
              </li>
            </ul>
            <div class="tab-content mt-3" id="mlTabContent">
              <!-- Anomaly Detection Tab -->
              <div class="tab-pane fade show active" id="anomalies" role="tabpanel">
                <div class="row">
                  <div class="col-md-6">
                    <div class="card mb-3">
                      <div class="card-body">
                        <h5 class="card-title">Detected Anomalies</h5>
                        <canvas id="anomalies-chart"></canvas>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="card mb-3">
                      <div class="card-body">
                        <h5 class="card-title">Recent Anomalies</h5>
                        <div class="table-responsive">
                          <table class="table table-sm">
                            <thead>
                              <tr>
                                <th>Time</th>
                                <th>Domain</th>
                                <th>Field</th>
                                <th>Value</th>
                                <th>Z-Score</th>
                              </tr>
                            </thead>
                            <tbody id="anomalies-table-body">
                              <!-- Anomalies will be added here dynamically -->
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Predictive Analytics Tab -->
              <div class="tab-pane fade" id="predictions" role="tabpanel">
                <div class="row">
                  <div class="col-md-6">
                    <div class="card mb-3">
                      <div class="card-body">
                        <h5 class="card-title">Violation Forecasts</h5>
                        <canvas id="forecasts-chart"></canvas>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="card mb-3">
                      <div class="card-body">
                        <h5 class="card-title">Prediction Accuracy</h5>
                        <div class="row">
                          <div class="col-md-6">
                            <label class="form-label">Boundary Violations</label>
                            <div class="progress mb-3">
                              <div id="boundary-accuracy-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label class="form-label">Validation Failures</label>
                            <div class="progress mb-3">
                              <div id="validation-accuracy-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%"></div>
                            </div>
                          </div>
                        </div>
                        <div class="row">
                          <div class="col-md-4">
                            <label class="form-label">Cyber Domain</label>
                            <div class="progress mb-3">
                              <div id="cyber-accuracy-progress" class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                            </div>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label">Financial Domain</label>
                            <div class="progress mb-3">
                              <div id="financial-accuracy-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                            </div>
                          </div>
                          <div class="col-md-4">
                            <label class="form-label">Medical Domain</label>
                            <div class="progress mb-3">
                              <div id="medical-accuracy-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- ML Settings Tab -->
              <div class="tab-pane fade" id="settings" role="tabpanel">
                <div class="row">
                  <div class="col-md-6">
                    <div class="card mb-3">
                      <div class="card-body">
                        <h5 class="card-title">Anomaly Detection Settings</h5>
                        <form id="anomaly-settings-form">
                          <div class="mb-3">
                            <label for="anomalyThreshold" class="form-label">Anomaly Threshold (Z-Score)</label>
                            <input type="range" class="form-range" id="anomalyThreshold" min="1" max="5" step="0.1" value="3.0">
                            <div class="text-end">
                              <span id="anomalyThresholdValue">3.0</span>
                            </div>
                          </div>
                          <div class="mb-3">
                            <label for="learningRate" class="form-label">Learning Rate</label>
                            <input type="range" class="form-range" id="learningRate" min="0.01" max="0.5" step="0.01" value="0.1">
                            <div class="text-end">
                              <span id="learningRateValue">0.1</span>
                            </div>
                          </div>
                          <div class="mb-3">
                            <label for="historyLength" class="form-label">History Length</label>
                            <input type="range" class="form-range" id="historyLength" min="10" max="500" step="10" value="100">
                            <div class="text-end">
                              <span id="historyLengthValue">100</span>
                            </div>
                          </div>
                          <button type="submit" class="btn btn-primary">Apply Settings</button>
                        </form>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="card mb-3">
                      <div class="card-body">
                        <h5 class="card-title">Predictive Analytics Settings</h5>
                        <form id="predictive-settings-form">
                          <div class="mb-3">
                            <label for="forecastHorizon" class="form-label">Forecast Horizon</label>
                            <input type="range" class="form-range" id="forecastHorizon" min="1" max="20" step="1" value="10">
                            <div class="text-end">
                              <span id="forecastHorizonValue">10</span>
                            </div>
                          </div>
                          <div class="mb-3">
                            <label for="confidenceLevel" class="form-label">Confidence Level</label>
                            <input type="range" class="form-range" id="confidenceLevel" min="0.8" max="0.99" step="0.01" value="0.95">
                            <div class="text-end">
                              <span id="confidenceLevelValue">0.95</span>
                            </div>
                          </div>
                          <div class="mb-3">
                            <label for="seasonalityPeriod" class="form-label">Seasonality Period</label>
                            <input type="range" class="form-range" id="seasonalityPeriod" min="1" max="48" step="1" value="24">
                            <div class="text-end">
                              <span id="seasonalityPeriodValue">24</span>
                            </div>
                          </div>
                          <button type="submit" class="btn btn-primary">Apply Settings</button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-md-4">
        <div class="card">
          <div class="card-header bg-danger text-white">
            <h5 class="card-title mb-0">
              <i class="bi bi-shield me-2"></i>
              Cyber Domain
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label class="form-label">Security Score</label>
              <div class="progress">
                <div id="security-score-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
              </div>
              <div class="text-end mt-1">
                <span id="security-score-value">0</span>/10
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Threat Level</label>
              <div class="progress">
                <div id="threat-level-progress" class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
              </div>
              <div class="text-end mt-1">
                <span id="threat-level-value">0</span>/10
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Boundary Violations</label>
              <h3 id="cyber-violations" class="text-danger">0</h3>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-header bg-success text-white">
            <h5 class="card-title mb-0">
              <i class="bi bi-currency-dollar me-2"></i>
              Financial Domain
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label class="form-label">Average Interest Rate</label>
              <div class="progress">
                <div id="interest-rate-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%"></div>
              </div>
              <div class="text-end mt-1">
                <span id="interest-rate-value">0</span>%
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Transaction Volume</label>
              <h3 id="transaction-volume" class="text-success">$0</h3>
            </div>
            <div class="mb-3">
              <label class="form-label">Boundary Violations</label>
              <h3 id="financial-violations" class="text-danger">0</h3>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
              <i class="bi bi-heart-pulse me-2"></i>
              Medical Domain
            </h5>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label class="form-label">Average Heart Rate</label>
              <div class="progress">
                <div id="heart-rate-progress" class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
              </div>
              <div class="text-end mt-1">
                <span id="heart-rate-value">0</span> BPM
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Critical Alerts</label>
              <h3 id="critical-alerts" class="text-warning">0</h3>
            </div>
            <div class="mb-3">
              <label class="form-label">Boundary Violations</label>
              <h3 id="medical-violations" class="text-danger">0</h3>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script src="dashboard.js"></script>
</body>
</html>
