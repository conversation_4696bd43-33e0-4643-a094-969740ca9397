/**
 * Error Handling Middleware
 * 
 * This middleware provides centralized error handling for Express applications.
 */

const errorHandlingService = require('../services/ErrorHandlingService');
const logger = require('../utils/logger');

/**
 * Error handler middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  // Create error context
  const context = {
    user: req.user,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    resource: req.originalUrl,
    retryCount: req.retryCount || 0,
    tags: {
      route: req.route ? req.route.path : 'unknown',
      controller: req.controller || 'unknown',
      action: req.action || 'unknown'
    },
    extra: {
      query: req.query,
      params: req.params,
      headers: req.headers
    }
  };
  
  // Handle the error
  const errorResponse = errorHandlingService.handleError(err, context);
  
  // Send error response
  res.status(errorResponse.error.status).json(errorResponse);
};

/**
 * Not found handler middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const notFoundHandler = (req, res, next) => {
  const notFoundError = new Error(`Resource not found: ${req.originalUrl}`);
  notFoundError.name = 'NotFoundError';
  next(notFoundError);
};

/**
 * Async handler wrapper
 * @param {Function} fn - Async function to wrap
 * @returns {Function} - Wrapped function
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Retry handler middleware
 * @param {Object} options - Retry options
 * @returns {Function} - Middleware function
 */
const retryHandler = (options = {}) => {
  return (req, res, next) => {
    // Initialize retry count
    req.retryCount = req.retryCount || 0;
    
    // Add retry-after header if retry count is greater than 0
    if (req.retryCount > 0) {
      const retryAfter = options.retryAfter || 1;
      res.set('Retry-After', retryAfter.toString());
    }
    
    next();
  };
};

/**
 * Circuit breaker middleware
 * @param {Object} options - Circuit breaker options
 * @returns {Function} - Middleware function
 */
const circuitBreakerHandler = (options = {}) => {
  const resource = options.resource || 'default';
  
  // Register circuit breaker
  errorHandlingService.registerCircuitBreaker(resource, options);
  
  return (req, res, next) => {
    const circuitBreaker = errorHandlingService.circuitBreakers.get(resource);
    
    // Check circuit breaker state
    if (circuitBreaker.state === 'open') {
      const circuitBreakerError = new Error(`Circuit breaker for ${resource} is open`);
      circuitBreakerError.name = 'CircuitBreakerError';
      return next(circuitBreakerError);
    }
    
    // Set resource on request
    req.resource = resource;
    
    // Add response listener to update circuit breaker state
    res.on('finish', () => {
      if (res.statusCode >= 500) {
        // Update circuit breaker state
        circuitBreaker.failureCount++;
        circuitBreaker.lastFailureTime = Date.now();
        
        // Check if circuit breaker should trip
        if (circuitBreaker.state === 'closed' && circuitBreaker.failureCount >= circuitBreaker.failureThreshold) {
          circuitBreaker.state = 'open';
          
          // Schedule circuit breaker reset
          setTimeout(() => {
            circuitBreaker.state = 'half-open';
            circuitBreaker.failureCount = 0;
          }, circuitBreaker.resetTimeout);
          
          logger.warn(`Circuit breaker for ${resource} tripped`, {
            failureCount: circuitBreaker.failureCount,
            failureThreshold: circuitBreaker.failureThreshold,
            resetTimeout: circuitBreaker.resetTimeout
          });
        }
      } else if (circuitBreaker.state === 'half-open') {
        // If circuit breaker is half-open and the call succeeded, close it
        circuitBreaker.state = 'closed';
        circuitBreaker.failureCount = 0;
        
        logger.info(`Circuit breaker for ${resource} closed`, {
          state: circuitBreaker.state
        });
      }
    });
    
    next();
  };
};

/**
 * Timeout middleware
 * @param {Object} options - Timeout options
 * @returns {Function} - Middleware function
 */
const timeoutHandler = (options = {}) => {
  const timeoutMs = options.timeoutMs || 30000;
  
  return (req, res, next) => {
    // Set timeout on request
    req.timeout = setTimeout(() => {
      const timeoutError = new Error(`Request timed out after ${timeoutMs}ms`);
      timeoutError.name = 'TimeoutError';
      next(timeoutError);
    }, timeoutMs);
    
    // Clear timeout when response is sent
    res.on('finish', () => {
      clearTimeout(req.timeout);
    });
    
    next();
  };
};

/**
 * Bulkhead middleware
 * @param {Object} options - Bulkhead options
 * @returns {Function} - Middleware function
 */
const bulkheadHandler = (options = {}) => {
  const resource = options.resource || 'default';
  const limit = options.limit || 10;
  let activeCount = 0;
  
  // Register bulkhead limit
  errorHandlingService.registerBulkheadLimit(resource, limit);
  
  return (req, res, next) => {
    // Check if bulkhead limit is reached
    if (activeCount >= limit) {
      const bulkheadError = new Error(`Bulkhead limit reached for ${resource}`);
      bulkheadError.name = 'BulkheadError';
      return next(bulkheadError);
    }
    
    // Increment active count
    activeCount++;
    
    // Decrement active count when response is sent
    res.on('finish', () => {
      activeCount--;
    });
    
    next();
  };
};

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler,
  retryHandler,
  circuitBreakerHandler,
  timeoutHandler,
  bulkheadHandler
};
