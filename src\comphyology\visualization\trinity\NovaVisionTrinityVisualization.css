.nova-vision-trinity-visualization {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  background-color: #1a1a2e;
  transition: all 0.3s ease;
}

.nova-vision-trinity-visualization.fullscreen {
  width: 100% !important;
  height: 100% !important;
  border-radius: 0;
  box-shadow: none;
}

.nova-vision-trinity-visualization-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  background-color: #1a1a2e;
  color: white;
  border-radius: 8px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #2196F3;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.nova-vision-trinity-visualization-error {
  padding: 20px;
  background-color: #f44336;
  color: white;
  border-radius: 8px;
  text-align: center;
}

.nova-vision-trinity-visualization-error h2 {
  margin-top: 0;
  margin-bottom: 10px;
}

.nova-vision-trinity-visualization-error button {
  background-color: white;
  color: #f44336;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  margin-top: 15px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.nova-vision-trinity-visualization-error button:hover {
  background-color: #f5f5f5;
}

.visualization-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.particle-details {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 15px;
  color: white;
  width: 250px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  z-index: 100;
}

.particle-details h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 500;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
}

.particle-details-content {
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.detail-label {
  opacity: 0.7;
}

.detail-value {
  font-weight: 500;
}

.particle-details button {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.particle-details button:hover {
  background-color: #1976D2;
}

.visualization-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
}

.fullscreen-button {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  transition: background-color 0.2s ease;
}

.fullscreen-button:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.nova-vision-badge {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0.7;
  z-index: 100;
}

/* NovaVision theme overrides */
.nova-vision-theme-light .nova-vision-trinity-visualization {
  background-color: #f5f5f5;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nova-vision-theme-light .nova-vision-trinity-visualization-loading {
  background-color: #f5f5f5;
  color: #333;
}

.nova-vision-theme-light .loading-spinner {
  border-color: rgba(0, 0, 0, 0.1);
  border-top-color: #2196F3;
}

.nova-vision-theme-light .nova-vision-badge {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .particle-details {
    width: calc(100% - 40px);
    left: 20px;
    right: 20px;
  }
}
