<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse UI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .feature-card.enabled {
            border-left: 5px solid #27ae60;
            background-color: #f0fff4;
        }
        .feature-card.disabled {
            border-left: 5px solid #e74c3c;
            background-color: #fff5f5;
        }
        .product-switcher {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .product-switcher button {
            background-color: #3498db;
            border: none;
            color: white;
            padding: 10px 20px;
            margin: 0 10px 10px 0;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .product-switcher button.active {
            background-color: #2c3e50;
        }
        .product-switcher button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <header>
        <h1>NovaFuse UI</h1>
        <p>GCP Testing Environment</p>
    </header>
    <div class="container">
        <div class="card">
            <h2>Feature Flag Demo</h2>
            <p>This demo shows how features are enabled or disabled based on the product tier.</p>

            <div class="product-switcher">
                <button class="active" onclick="switchProduct('novaPrime')">NovaPrime</button>
                <button onclick="switchProduct('novaCore')">NovaCore</button>
                <button onclick="switchProduct('novaShield')">NovaShield</button>
                <button onclick="switchProduct('novaLearn')">NovaLearn</button>
                <button onclick="switchProduct('novaAssistAI')">NovaAssistAI</button>
            </div>

            <h3>Dashboard Features</h3>
            <div class="feature-grid" id="dashboard-features">
                <div class="feature-card enabled">
                    <h4>Dashboard Overview</h4>
                    <p>High-level overview of your compliance status</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Analytics Dashboard</h4>
                    <p>Advanced analytics and reporting</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Reports</h4>
                    <p>Customizable compliance reports</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Dashboard Customization</h4>
                    <p>Customize your dashboard layout and widgets</p>
                </div>
            </div>

            <h3>GRC Features</h3>
            <div class="feature-grid" id="grc-features">
                <div class="feature-card enabled">
                    <h4>Privacy Management</h4>
                    <p>Manage privacy compliance requirements</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Security Assessment</h4>
                    <p>Assess security controls and vulnerabilities</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Regulatory Compliance</h4>
                    <p>Manage regulatory frameworks and requirements</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Control Testing</h4>
                    <p>Test and validate security controls</p>
                </div>
                <div class="feature-card enabled">
                    <h4>ESG Management</h4>
                    <p>Manage environmental, social, and governance requirements</p>
                </div>
            </div>

            <h3>Advanced Features</h3>
            <div class="feature-grid" id="advanced-features">
                <div class="feature-card enabled">
                    <h4>AI Assistant</h4>
                    <p>AI-powered compliance assistant</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Predictive Analytics</h4>
                    <p>Predict compliance issues before they occur</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Automated Remediation</h4>
                    <p>Automatically remediate compliance issues</p>
                </div>
                <div class="feature-card enabled">
                    <h4>Custom Integrations</h4>
                    <p>Integrate with custom systems and applications</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>Google Cloud Integration</h2>
            <p>NovaFuse integrates with the following Google Cloud services:</p>
            <ul>
                <li><strong>Security Command Center</strong>: Integrate compliance findings with Security Command Center</li>
                <li><strong>Cloud IAM</strong>: Implement compliance-aware access controls</li>
                <li><strong>BigQuery</strong>: Analyze compliance data with BigQuery</li>
            </ul>
            <button onclick="testIntegration('scc')">Test SCC Integration</button>
            <button onclick="testIntegration('iam')">Test IAM Integration</button>
            <button onclick="testIntegration('bigquery')">Test BigQuery Integration</button>
            <div id="integration-results"></div>
        </div>
    </div>

    <script>
        // Feature flag configuration
        const featureFlags = {
            novaPrime: {
                dashboard: { overview: true, analytics: true, reports: true, customization: true },
                grc: { privacy: true, security: true, compliance: true, control: true, esg: true },
                advanced: { aiAssistant: true, predictiveAnalytics: true, automatedRemediation: true, customIntegrations: true }
            },
            novaCore: {
                dashboard: { overview: true, analytics: false, reports: true, customization: false },
                grc: { privacy: true, security: true, compliance: true, control: false, esg: false },
                advanced: { aiAssistant: false, predictiveAnalytics: false, automatedRemediation: false, customIntegrations: false }
            },
            novaShield: {
                dashboard: { overview: true, analytics: true, reports: true, customization: false },
                grc: { privacy: false, security: true, compliance: true, control: true, esg: false },
                advanced: { aiAssistant: false, predictiveAnalytics: true, automatedRemediation: true, customIntegrations: false }
            },
            novaLearn: {
                dashboard: { overview: true, analytics: false, reports: true, customization: false },
                grc: { privacy: false, security: false, compliance: true, control: false, esg: false },
                advanced: { aiAssistant: true, predictiveAnalytics: false, automatedRemediation: false, customIntegrations: false }
            },
            novaAssistAI: {
                dashboard: { overview: true, analytics: true, reports: true, customization: true },
                grc: { privacy: true, security: true, compliance: true, control: true, esg: true },
                advanced: { aiAssistant: true, predictiveAnalytics: true, automatedRemediation: true, customIntegrations: true }
            }
        };

        // Switch product tier
        function switchProduct(product) {
            // Update active button
            document.querySelectorAll('.product-switcher button').forEach(button => {
                button.classList.remove('active');
            });
            document.querySelector(`button[onclick="switchProduct('${product}')"]`).classList.add('active');

            // Update dashboard features
            const dashboardFeatures = document.querySelectorAll('#dashboard-features .feature-card');
            dashboardFeatures[0].className = featureFlags[product].dashboard.overview ? 'feature-card enabled' : 'feature-card disabled';
            dashboardFeatures[1].className = featureFlags[product].dashboard.analytics ? 'feature-card enabled' : 'feature-card disabled';
            dashboardFeatures[2].className = featureFlags[product].dashboard.reports ? 'feature-card enabled' : 'feature-card disabled';
            dashboardFeatures[3].className = featureFlags[product].dashboard.customization ? 'feature-card enabled' : 'feature-card disabled';

            // Update GRC features
            const grcFeatures = document.querySelectorAll('#grc-features .feature-card');
            grcFeatures[0].className = featureFlags[product].grc.privacy ? 'feature-card enabled' : 'feature-card disabled';
            grcFeatures[1].className = featureFlags[product].grc.security ? 'feature-card enabled' : 'feature-card disabled';
            grcFeatures[2].className = featureFlags[product].grc.compliance ? 'feature-card enabled' : 'feature-card disabled';
            grcFeatures[3].className = featureFlags[product].grc.control ? 'feature-card enabled' : 'feature-card disabled';
            grcFeatures[4].className = featureFlags[product].grc.esg ? 'feature-card enabled' : 'feature-card disabled';

            // Update advanced features
            const advancedFeatures = document.querySelectorAll('#advanced-features .feature-card');
            advancedFeatures[0].className = featureFlags[product].advanced.aiAssistant ? 'feature-card enabled' : 'feature-card disabled';
            advancedFeatures[1].className = featureFlags[product].advanced.predictiveAnalytics ? 'feature-card enabled' : 'feature-card disabled';
            advancedFeatures[2].className = featureFlags[product].advanced.automatedRemediation ? 'feature-card enabled' : 'feature-card disabled';
            advancedFeatures[3].className = featureFlags[product].advanced.customIntegrations ? 'feature-card enabled' : 'feature-card disabled';
        }

        // Test Google Cloud integration
        function testIntegration(service) {
            const resultsDiv = document.getElementById('integration-results');
            resultsDiv.innerHTML = '<p>Testing integration with ' + service + '...</p>';

            let endpoint = '';
            switch(service) {
                case 'scc':
                    endpoint = '/api/scc';
                    break;
                case 'iam':
                    endpoint = '/api/iam';
                    break;
                case 'bigquery':
                    endpoint = '/api/bigquery';
                    break;
            }

            fetch(endpoint)
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = '<p>Integration test successful: ' + JSON.stringify(data) + '</p>';
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<p>Integration test failed: ' + error.message + '</p>';
                });
        }

        // Initialize with NovaPrime
        switchProduct('novaPrime');
    </script>
</body>
</html>
