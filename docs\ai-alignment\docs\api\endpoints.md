# API Endpoints Reference

## Base URL
All API endpoints are relative to:
```
https://api.novaalign.ai/api/v1
```

## Systems

### List All Systems
```http
GET /systems
```

**Response**
```json
{
  "data": [
    {
      "id": "sys_123",
      "name": "Customer Support AI",
      "status": "online",
      "alignment_score": 0.92,
      "last_checked": "2025-06-25T07:15:00Z"
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "per_page": 20
  }
}
```

### Get System Details
```http
GET /systems/{system_id}
```

**Response**
```json
{
  "id": "sys_123",
  "name": "Customer Support AI",
  "description": "AI-powered customer support system",
  "status": "online",
  "alignment_score": 0.92,
  "metrics": {
    "psi": 0.89,
    "phi": 0.91,
    "theta": 0.93
  },
  "created_at": "2025-01-15T10:30:00Z",
  "updated_at": "2025-06-25T07:15:00Z"
}
```

## Alerts

### List Alerts
```http
GET /alerts
```

**Query Parameters**
- `status` (optional): Filter by status (open, acknowledged, resolved)
- `severity` (optional): Filter by severity (low, medium, high, critical)
- `system_id` (optional): Filter by system ID

**Response**
```json
{
  "data": [
    {
      "id": "alt_123",
      "title": "Alignment Drift Detected",
      "description": "Significant drift in value alignment detected",
      "severity": "high",
      "status": "open",
      "system_id": "sys_123",
      "created_at": "2025-06-25T07:10:00Z"
    }
  ]
}
```

## Metrics

### Submit Metrics
```http
POST /metrics
```

**Request Body**
```json
{
  "system_id": "sys_123",
  "timestamp": "2025-06-25T07:15:00Z",
  "metrics": {
    "psi": 0.87,
    "phi": 0.91,
    "theta": 0.89
  },
  "metadata": {
    "model_version": "1.2.3",
    "environment": "production"
  }
}
```

**Response**
```json
{
  "id": "met_123",
  "status": "processed"
}
```

## Webhooks

### Create Webhook
```http
POST /webhooks
```

**Request Body**
```json
{
  "name": "Slack Alerts",
  "url": "https://hooks.slack.com/services/...",
  "events": ["alert.created", "alert.updated"],
  "secret": "your-signing-secret"
}
```

**Response**
```json
{
  "id": "wh_123",
  "name": "Slack Alerts",
  "url": "https://hooks.slack.com/services/...",
  "events": ["alert.created", "alert.updated"],
  "created_at": "2025-06-25T07:20:00Z"
}
```

## Error Responses

All error responses follow this format:
```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "The requested resource was not found",
    "details": {
      "resource": "system",
      "id": "nonexistent_id"
    }
  }
}
```

### Common Error Codes

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | VALIDATION_ERROR | Request validation failed |
| 401 | UNAUTHORIZED | Authentication required |
| 403 | FORBIDDEN | Insufficient permissions |
| 404 | NOT_FOUND | Resource not found |
| 429 | RATE_LIMIT_EXCEEDED | Too many requests |
| 500 | INTERNAL_ERROR | Internal server error |
