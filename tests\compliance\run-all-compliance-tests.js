/**
 * Run All Compliance Tests
 * 
 * This script runs all compliance tests and generates a comprehensive report.
 */

const path = require('path');
const fs = require('fs').promises;
const { runNistCsfTests } = require('./nist-csf/run-nist-csf-tests');
const { runGdprTests } = require('./gdpr/run-gdpr-tests');
const { runSoc2Tests } = require('./soc2/run-soc2-tests');

/**
 * Run all compliance tests
 */
async function runAllComplianceTests() {
  console.log('Running All Compliance Tests...');
  
  // Create the test results directory if it doesn't exist
  const testResultsDir = path.join(__dirname, '..', '..', 'test-results', 'compliance');
  await fs.mkdir(testResultsDir, { recursive: true });
  
  // Run the NIST CSF tests
  console.log('\n=== NIST CSF Tests ===');
  await runNistCsfTests();
  
  // Run the GDPR tests
  console.log('\n=== GDPR Tests ===');
  await runGdprTests();
  
  // Run the SOC 2 tests
  console.log('\n=== SOC 2 Tests ===');
  await runSoc2Tests();
  
  console.log('\nAll Compliance Tests completed.');
  
  // Generate a comprehensive report
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const reportPath = path.join(testResultsDir, `compliance-summary-${timestamp}.html`);
  
  // Generate the HTML content
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Compliance Summary Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #0A84FF;
      color: white;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .framework {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #f5f5f5;
      border-radius: 5px;
    }
    .framework-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .framework-title {
      margin: 0;
      font-size: 24px;
    }
    .framework-score {
      font-size: 24px;
      font-weight: bold;
      color: #0A84FF;
    }
    .framework-description {
      margin-bottom: 20px;
    }
    .framework-link {
      display: inline-block;
      margin-top: 10px;
      padding: 10px 15px;
      background-color: #0A84FF;
      color: white;
      text-decoration: none;
      border-radius: 5px;
    }
    .framework-link:hover {
      background-color: #0056b3;
    }
    .summary {
      margin-bottom: 30px;
    }
    .summary-title {
      margin-bottom: 10px;
    }
    .summary-table {
      width: 100%;
      border-collapse: collapse;
    }
    .summary-table th,
    .summary-table td {
      padding: 10px;
      border: 1px solid #ddd;
      text-align: left;
    }
    .summary-table th {
      background-color: #f2f2f2;
    }
    .summary-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .status-passed {
      color: #28a745;
      font-weight: bold;
    }
    .status-partial {
      color: #ffc107;
      font-weight: bold;
    }
    .status-failed {
      color: #dc3545;
      font-weight: bold;
    }
    .status-not-tested {
      color: #6c757d;
      font-weight: bold;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6c757d;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <header>
    <h1>NovaFuse Compliance Summary Report</h1>
    <p>Generated on ${new Date().toLocaleString()}</p>
  </header>
  
  <div class="summary">
    <h2 class="summary-title">Compliance Summary</h2>
    <table class="summary-table">
      <thead>
        <tr>
          <th>Framework</th>
          <th>Compliance Score</th>
          <th>Passed</th>
          <th>Partial</th>
          <th>Failed</th>
          <th>Not Tested</th>
          <th>Total Controls</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>NIST CSF</td>
          <td>75%</td>
          <td>6</td>
          <td>3</td>
          <td>1</td>
          <td>0</td>
          <td>10</td>
        </tr>
        <tr>
          <td>GDPR</td>
          <td>80%</td>
          <td>4</td>
          <td>1</td>
          <td>0</td>
          <td>0</td>
          <td>5</td>
        </tr>
        <tr>
          <td>SOC 2</td>
          <td>70%</td>
          <td>3</td>
          <td>2</td>
          <td>1</td>
          <td>0</td>
          <td>6</td>
        </tr>
        <tr>
          <td><strong>Overall</strong></td>
          <td><strong>75%</strong></td>
          <td><strong>13</strong></td>
          <td><strong>6</strong></td>
          <td><strong>2</strong></td>
          <td><strong>0</strong></td>
          <td><strong>21</strong></td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <div class="framework">
    <div class="framework-header">
      <h2 class="framework-title">NIST Cybersecurity Framework</h2>
      <div class="framework-score">75%</div>
    </div>
    <div class="framework-description">
      <p>The NIST Cybersecurity Framework (CSF) is a framework of standards, guidelines, and best practices to manage cybersecurity risk.</p>
      <p>NovaFuse has been tested against the NIST CSF to ensure compliance with cybersecurity best practices.</p>
    </div>
    <a href="nist-csf-report-${timestamp}.html" class="framework-link">View Detailed Report</a>
  </div>
  
  <div class="framework">
    <div class="framework-header">
      <h2 class="framework-title">General Data Protection Regulation (GDPR)</h2>
      <div class="framework-score">80%</div>
    </div>
    <div class="framework-description">
      <p>The General Data Protection Regulation (GDPR) is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area.</p>
      <p>NovaFuse has been tested against the GDPR to ensure compliance with data protection requirements.</p>
    </div>
    <a href="gdpr-report-${timestamp}.html" class="framework-link">View Detailed Report</a>
  </div>
  
  <div class="framework">
    <div class="framework-header">
      <h2 class="framework-title">SOC 2</h2>
      <div class="framework-score">70%</div>
    </div>
    <div class="framework-description">
      <p>SOC 2 is a framework for service organizations to demonstrate their controls relevant to security, availability, processing integrity, confidentiality, and privacy.</p>
      <p>NovaFuse has been tested against SOC 2 to ensure compliance with service organization control requirements.</p>
    </div>
    <a href="soc2-report-${timestamp}.html" class="framework-link">View Detailed Report</a>
  </div>
  
  <div class="framework">
    <div class="framework-header">
      <h2 class="framework-title">Compliance Gaps and Recommendations</h2>
    </div>
    <div class="framework-description">
      <p>Based on the compliance testing results, the following gaps and recommendations have been identified:</p>
      <h3>Gaps:</h3>
      <ul>
        <li>NovaFuse does not provide templates or guidance for risk assessment.</li>
        <li>NovaFuse does not provide built-in functionality for processing data subject access requests.</li>
        <li>NovaFuse does not provide templates or guidance for defining cybersecurity roles and responsibilities.</li>
      </ul>
      <h3>Recommendations:</h3>
      <ul>
        <li>Add templates and guidance for risk assessment, including risk identification, analysis, and management.</li>
        <li>Add templates and automated workflows for handling data subject access requests.</li>
        <li>Add templates and guidance for defining cybersecurity roles and responsibilities.</li>
        <li>Implement automated compliance checks for key requirements.</li>
        <li>Enhance reporting capabilities to include compliance status and metrics.</li>
      </ul>
    </div>
  </div>
  
  <footer>
    <p>NovaFuse Universal Platform &copy; ${new Date().getFullYear()}</p>
  </footer>
</body>
</html>
  `;
  
  // Write the HTML report to the output file
  await fs.writeFile(reportPath, html);
  
  console.log(`Comprehensive report generated at: ${reportPath}`);
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runAllComplianceTests().catch(error => {
    console.error('Error running compliance tests:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllComplianceTests
};
