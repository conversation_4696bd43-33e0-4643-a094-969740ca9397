<form id="qml-form" onsubmit="submitQMLForm(event)">
    <input type="hidden" name="type" value="quantum_ml">
    
    <div class="mb-3">
        <label for="qml-name" class="form-label">Experiment Name</label>
        <input type="text" class="form-control" id="qml-name" name="name" value="Quantum ML Protein Folding" required>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="qml-dataset" class="form-label">Dataset</label>
                <select class="form-select" id="qml-dataset" name="dataset" required>
                    <option value="synthetic">Synthetic Protein Sequences</option>
                    <option value="cath">CATH (Small Protein Fragments)</option>
                    <option value="casp">CASP (Critical Assessment of Structure Prediction)</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="qml-backend" class="form-label">Quantum Backend</label>
                <select class="form-select" id="qml-backend" name="quantum_backend" required>
                    <option value="pennylane">PennyLane (default)</option>
                    <option value="qiskit">Qiskit</option>
                    <option value="braket">Amazon Braket</option>
                </select>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-4">
            <div class="mb-3">
                <label for="qml-model" class="form-label">Model Architecture</label>
                <select class="form-select" id="qml-model" name="model" required>
                    <option value="qnn">Quantum Neural Network</option>
                    <option value="qcnn">Quantum Convolutional Neural Network</option>
                    <option value="hybrid">Hybrid Classical-Quantum</option>
                </select>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <label for="qml-epochs" class="form-label">Training Epochs</label>
                <input type="number" class="form-control" id="qml-epochs" name="epochs" min="1" max="1000" value="50" required>
            </div>
        </div>
        <div class="col-md-4">
            <div class="mb-3">
                <label for="qml-batch-size" class="form-label">Batch Size</label>
                <input type="number" class="form-control" id="qml-batch-size" name="batch_size" min="1" max="128" value="16" required>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="qml-optimizer" class="form-label">Optimizer</label>
                <select class="form-select" id="qml-optimizer" name="optimizer" required>
                    <option value="adam">Adam</option>
                    <option value="adagrad">AdaGrad</option>
                    <option value="rmsprop">RMSprop</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="qml-learning-rate" class="form-label">Learning Rate</label>
                <input type="number" class="form-control" id="qml-learning-rate" name="learning_rate" min="0.0001" max="0.1" step="0.0001" value="0.001" required>
            </div>
        </div>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Model Features</label>
        <div class="row">
            <div class="col-md-6">
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="qml-feature-1" name="use_attention" checked>
                    <label class="form-check-label" for="qml-feature-1">Use Attention Mechanism</label>
                </div>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="qml-feature-2" name="use_residual" checked>
                    <label class="form-check-label" for="qml-feature-2">Residual Connections</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="qml-feature-3" name="use_batch_norm" checked>
                    <label class="form-check-label" for="qml-feature-3">Batch Normalization</label>
                </div>
                <div class="form-check form-switch mb-2">
                    <input class="form-check-input" type="checkbox" id="qml-feature-4" name="use_dropout" checked>
                    <label class="form-check-label" for="qml-feature-4">Dropout Regularization</label>
                </div>
            </div>
        </div>
    </div>
    
    <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-outline-secondary me-2" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary" id="qml-submit">
            <span class="spinner-border spinner-border-sm d-none" id="qml-submit-spinner" role="status" aria-hidden="true"></span>
            <span id="qml-submit-text">Start Training</span>
        </button>
    </div>
</form>

<script>
function submitQMLForm(event) {
    event.preventDefault();
    
    const form = event.target;
    const submitButton = form.querySelector('#qml-submit');
    const spinner = form.querySelector('#qml-submit-spinner');
    const buttonText = form.querySelector('#qml-submit-text');
    
    // Show loading state
    submitButton.disabled = true;
    spinner.classList.remove('d-none');
    buttonText.textContent = 'Starting Training...';
    
    // Prepare form data
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Convert numeric fields to numbers and handle checkboxes
    data.epochs = parseInt(data.epochs);
    data.batch_size = parseInt(data.batch_size);
    data.learning_rate = parseFloat(data.learning_rate);
    
    // Add boolean flags
    data.use_attention = form.querySelector('#qml-feature-1').checked;
    data.use_residual = form.querySelector('#qml-feature-2').checked;
    data.use_batch_norm = form.querySelector('#qml-feature-3').checked;
    data.use_dropout = form.querySelector('#qml-feature-4').checked;
    
    // Submit via fetch
    fetch('/api/experiments', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => { throw new Error(err.detail || 'Failed to start training'); });
        }
        return response.json();
    })
    .then(data => {
        // Close modal on success
        const modal = bootstrap.Modal.getInstance(document.getElementById('newExperimentModal'));
        modal.hide();
        
        // Show success message
        showAlert('Quantum ML training started successfully!', 'success');
        
        // Reset form
        form.reset();
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert(`Error: ${error.message}`, 'danger');
    })
    .finally(() => {
        // Reset button state
        submitButton.disabled = false;
        spinner.classList.add('d-none');
        buttonText.textContent = 'Start Training';
    });
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    const container = document.querySelector('.main-content');
    container.prepend(alertDiv);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = bootstrap.Alert.getOrCreateInstance(alertDiv);
        alert.close();
    }, 5000);
}

// Update form based on dataset selection
document.getElementById('qml-dataset').addEventListener('change', function() {
    const dataset = this.value;
    const modelSelect = document.getElementById('qml-model');
    
    // Reset model options
    modelSelect.innerHTML = `
        <option value="qnn">Quantum Neural Network</option>
        <option value="qcnn">Quantum Convolutional Neural Network</option>
        <option value="hybrid">Hybrid Classical-Quantum</option>
    `;
    
    // Add dataset-specific options
    if (dataset === 'casp') {
        modelSelect.innerHTML += `
            <option value="3d-cnn">3D Convolutional Network</option>
            <option value="transformer">Transformer-based</option>
        `;
    } else if (dataset === 'cath') {
        modelSelect.innerHTML += `
            <option value="graph">Graph Neural Network</option>
        `;
    }
});
</script>
