"""
Universal Vendor Risk Management System (UVRMS).

A comprehensive system for assessing, monitoring, and managing vendor risks,
including fourth-party risk management and advanced risk assessment methodologies.
"""

from .core.vendor_manager import Vendor<PERSON>anager
from .core.assessment_manager import AssessmentManager
from .core.risk_manager import RiskManager
from .core.monitoring_manager import MonitoringManager
from .core.relationship_manager import RelationshipManager
from .utils.risk_assessment import (
    calculate_inherent_risk_score,
    calculate_residual_risk_score,
    calculate_impact_likelihood_score,
    calculate_compliance_based_score,
    calculate_industry_specific_score,
    calculate_composite_risk_score,
    get_default_inherent_risk_factors,
    get_default_composite_weights,
    get_default_industry_benchmarks,
    get_default_compliance_requirements
)

__version__ = '0.2.0'
__all__ = [
    'VendorManager',
    'AssessmentManager',
    'RiskManager',
    'MonitoringManager',
    'RelationshipManager',
    'calculate_inherent_risk_score',
    'calculate_residual_risk_score',
    'calculate_impact_likelihood_score',
    'calculate_compliance_based_score',
    'calculate_industry_specific_score',
    'calculate_composite_risk_score',
    'get_default_inherent_risk_factors',
    'get_default_composite_weights',
    'get_default_industry_benchmarks',
    'get_default_compliance_requirements'
]
