// api.test.js
// Basic tests for NovaAscend API endpoints

const request = require('supertest');
const app = require('../novaascend/throne-api');

describe('NovaAscend API', () => {
  it('should align NovaCortex via /decree', async () => {
    const res = await request(app)
      .post('/decree')
      .send({ command: 'align', target: 'test-alignment' });
    expect(res.statusCode).toBe(200);
    expect(res.body.status).toBe('aligned');
    expect(res.body.state.alignment).toBe('test-alignment');
  });

  it('should get vision from NovaCortex via /vision', async () => {
    const res = await request(app).get('/vision');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('qScore');
    expect(res.body).toHaveProperty('alignment');
  });

  it('should update policy via /firewall', async () => {
    const res = await request(app)
      .put('/firewall')
      .send({ newLaw: 'Test policy' });
    expect(res.statusCode).toBe(200);
    expect(res.body.status).toBe('policy updated');
    expect(res.body.policies).toContain('Test policy');
  });

  it('should check system coherence via /coherence/check', async () => {
    const res = await request(app)
      .post('/coherence/check')
      .send({ coherenceScore: 0 });
    expect(res.statusCode).toBe(200);
    expect(res.body.coherence).toBe(true);
  });
});
