# The Cyber-Safety Dominance Framework Diagrams

This React application provides professional diagrams for "The Cyber-Safety Dominance Framework" strategic presentation. The diagrams are designed to be clean, clear, and suitable for inclusion in presentations to Google and NIST.

## Getting Started

### Quick Start

1. Double-click the `start-strategic-framework.bat` file to install dependencies and start the application
2. The application will open in your default web browser
3. Navigate between different diagrams using the navigation at the top
4. Take screenshots of the diagrams using your operating system's screenshot tool
5. View the complete presentation by clicking on "Complete Presentation" in the navigation

### Manual Setup

If the batch file doesn't work, follow these steps:

1. Open a command prompt in this directory
2. Run `npm install` to install dependencies
3. Run `npm start` to start the development server
4. Open your browser to http://localhost:3000

## Diagrams Included

1. **Strategic Trinity** - Visualizes the trinity of Cyber-Safety (Mission), NovaFuse (Engine), and Partner Empowerment (Growth Model)
2. **3,142x Performance Visualization** - Illustrates the dramatic performance improvements of NovaConnect
3. **Partner Empowerment Flywheel** - Shows how the 18/82 model creates exponential value
4. **Google-Only Approach** - Demonstrates why NovaFuse was designed specifically for GCP and how it enhances the Wiz acquisition
5. **Enterprise Case Study** - Shows real-world impact with concrete metrics
6. **Patent Shield & Strategic Moat** - Illustrates the patent protection and strategic advantages

## Taking Screenshots

1. Navigate to the diagram you want to capture
2. Use your operating system's screenshot tool:
   - Windows: Use Windows+Shift+S to open the snipping tool
   - Mac: Use Command+Shift+4 to capture a selected area
3. Save the screenshot with the figure number and name

## Consolidated View

The "Complete Presentation" option in the navigation shows all six diagrams in a single scrollable view, which is useful for:
- Getting an overview of the entire framework
- Printing all diagrams at once
- Saving as a PDF

## Troubleshooting

If you encounter any issues:

- Make sure all dependencies are installed
- Try restarting the development server
- Check the browser console for any errors

## Contact

For any questions or issues, please contact:
- David Nigel Irvin (<EMAIL>)
