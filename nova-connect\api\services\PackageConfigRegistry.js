/**
 * Package Configuration Registry
 * 
 * This service manages the package configuration registry for NovaConnect UAC.
 * It provides functionality for defining feature sets by package and mapping tenants to packages.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const NodeCache = require('node-cache');

class PackageConfigRegistry {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.packagesDir = path.join(this.dataDir, 'packages');
    this.packagesFile = path.join(this.packagesDir, 'packages.json');
    this.tenantMappingFile = path.join(this.packagesDir, 'tenant_package_mapping.json');
    
    // Initialize cache with 5-minute TTL
    this.cache = new NodeCache({ stdTTL: 300, checkperiod: 60 });
    
    // Define package tiers
    this.tiers = {
      CORE: 'core',
      SECURE: 'secure',
      ENTERPRISE: 'enterprise',
      AI_BOOST: 'ai_boost'
    };
    
    this.ensureDataDir();
  }
  
  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.packagesDir, { recursive: true });
      
      // Initialize packages file if it doesn't exist
      try {
        await fs.access(this.packagesFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with default packages
          await fs.writeFile(this.packagesFile, JSON.stringify(this.getDefaultPackages(), null, 2));
        } else {
          throw error;
        }
      }
      
      // Initialize tenant mapping file if it doesn't exist
      try {
        await fs.access(this.tenantMappingFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with empty array
          await fs.writeFile(this.tenantMappingFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error creating packages directory:', error);
      throw error;
    }
  }
  
  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }
  
  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }
  
  /**
   * Get default packages
   */
  getDefaultPackages() {
    return [
      {
        id: 'core',
        name: 'NovaConnect Core',
        description: 'Basic functionality for connecting APIs and automating workflows',
        tier: this.tiers.CORE,
        features: [
          'core.basic_connectors',
          'core.basic_workflows',
          'core.basic_authentication',
          'core.basic_monitoring',
          'core.basic_logging'
        ],
        limits: {
          connections: 10,
          operations_per_day: 1000,
          workflows: 5,
          actions_per_workflow: 10,
          scheduled_workflows: 2,
          alerts: 5
        }
      },
      {
        id: 'secure',
        name: 'NovaConnect Secure',
        description: 'Enhanced security and compliance features',
        tier: this.tiers.SECURE,
        features: [
          'core.basic_connectors',
          'core.basic_workflows',
          'core.basic_authentication',
          'core.basic_monitoring',
          'core.basic_logging',
          'security.encryption',
          'security.audit_logs',
          'security.compliance_checks',
          'security.vulnerability_scanning',
          'security.access_controls'
        ],
        limits: {
          connections: 25,
          operations_per_day: 5000,
          workflows: 15,
          actions_per_workflow: 20,
          scheduled_workflows: 5,
          alerts: 15
        }
      },
      {
        id: 'enterprise',
        name: 'NovaConnect Enterprise',
        description: 'Advanced enterprise features for large-scale deployments',
        tier: this.tiers.ENTERPRISE,
        features: [
          'core.basic_connectors',
          'core.basic_workflows',
          'core.basic_authentication',
          'core.basic_monitoring',
          'core.basic_logging',
          'security.encryption',
          'security.audit_logs',
          'security.compliance_checks',
          'security.vulnerability_scanning',
          'security.access_controls',
          'enterprise.advanced_connectors',
          'enterprise.advanced_workflows',
          'enterprise.advanced_authentication',
          'enterprise.advanced_monitoring',
          'enterprise.advanced_logging',
          'enterprise.sla',
          'enterprise.priority_support'
        ],
        limits: {
          connections: 100,
          operations_per_day: 25000,
          workflows: 50,
          actions_per_workflow: 50,
          scheduled_workflows: 20,
          alerts: 50
        }
      },
      {
        id: 'ai_boost',
        name: 'NovaConnect AI Boost',
        description: 'AI-powered features for intelligent automation',
        tier: this.tiers.AI_BOOST,
        features: [
          'core.basic_connectors',
          'core.basic_workflows',
          'core.basic_authentication',
          'core.basic_monitoring',
          'core.basic_logging',
          'security.encryption',
          'security.audit_logs',
          'security.compliance_checks',
          'security.vulnerability_scanning',
          'security.access_controls',
          'enterprise.advanced_connectors',
          'enterprise.advanced_workflows',
          'enterprise.advanced_authentication',
          'enterprise.advanced_monitoring',
          'enterprise.advanced_logging',
          'enterprise.sla',
          'enterprise.priority_support',
          'ai.predictive_analytics',
          'ai.anomaly_detection',
          'ai.natural_language_processing',
          'ai.automated_remediation',
          'ai.intelligent_recommendations'
        ],
        limits: {
          connections: -1, // Unlimited
          operations_per_day: -1, // Unlimited
          workflows: -1, // Unlimited
          actions_per_workflow: -1, // Unlimited
          scheduled_workflows: -1, // Unlimited
          alerts: -1 // Unlimited
        }
      }
    ];
  }
  
  /**
   * Get all packages
   */
  async getAllPackages() {
    // Check cache first
    const cacheKey = 'all_packages';
    const cachedPackages = this.cache.get(cacheKey);
    
    if (cachedPackages) {
      return cachedPackages;
    }
    
    // Load from file
    const packages = await this.loadData(this.packagesFile);
    
    // Cache the result
    this.cache.set(cacheKey, packages);
    
    return packages;
  }
  
  /**
   * Get package by ID
   */
  async getPackageById(id) {
    // Check cache first
    const cacheKey = `package_${id}`;
    const cachedPackage = this.cache.get(cacheKey);
    
    if (cachedPackage) {
      return cachedPackage;
    }
    
    // Load from file
    const packages = await this.getAllPackages();
    const pkg = packages.find(p => p.id === id);
    
    if (!pkg) {
      throw new Error(`Package with ID ${id} not found`);
    }
    
    // Cache the result
    this.cache.set(cacheKey, pkg);
    
    return pkg;
  }
  
  /**
   * Create a new package
   */
  async createPackage(packageData) {
    const packages = await this.getAllPackages();
    
    // Check if package with the same ID already exists
    if (packages.some(p => p.id === packageData.id)) {
      throw new Error(`Package with ID ${packageData.id} already exists`);
    }
    
    // Add the new package
    packages.push(packageData);
    
    // Save to file
    await this.saveData(this.packagesFile, packages);
    
    // Invalidate cache
    this.cache.del('all_packages');
    
    return packageData;
  }
  
  /**
   * Update a package
   */
  async updatePackage(id, packageData) {
    const packages = await this.getAllPackages();
    
    // Find the package
    const index = packages.findIndex(p => p.id === id);
    
    if (index === -1) {
      throw new Error(`Package with ID ${id} not found`);
    }
    
    // Update the package
    packages[index] = { ...packages[index], ...packageData };
    
    // Save to file
    await this.saveData(this.packagesFile, packages);
    
    // Invalidate cache
    this.cache.del('all_packages');
    this.cache.del(`package_${id}`);
    
    return packages[index];
  }
  
  /**
   * Delete a package
   */
  async deletePackage(id) {
    const packages = await this.getAllPackages();
    
    // Find the package
    const index = packages.findIndex(p => p.id === id);
    
    if (index === -1) {
      throw new Error(`Package with ID ${id} not found`);
    }
    
    // Remove the package
    packages.splice(index, 1);
    
    // Save to file
    await this.saveData(this.packagesFile, packages);
    
    // Invalidate cache
    this.cache.del('all_packages');
    this.cache.del(`package_${id}`);
    
    return true;
  }
  
  /**
   * Get all tenant-to-package mappings
   */
  async getAllTenantMappings() {
    // Check cache first
    const cacheKey = 'all_tenant_mappings';
    const cachedMappings = this.cache.get(cacheKey);
    
    if (cachedMappings) {
      return cachedMappings;
    }
    
    // Load from file
    const mappings = await this.loadData(this.tenantMappingFile);
    
    // Cache the result
    this.cache.set(cacheKey, mappings);
    
    return mappings;
  }
  
  /**
   * Get tenant-to-package mapping by tenant ID
   */
  async getTenantMapping(tenantId) {
    // Check cache first
    const cacheKey = `tenant_mapping_${tenantId}`;
    const cachedMapping = this.cache.get(cacheKey);
    
    if (cachedMapping) {
      return cachedMapping;
    }
    
    // Load from file
    const mappings = await this.getAllTenantMappings();
    const mapping = mappings.find(m => m.tenantId === tenantId);
    
    if (!mapping) {
      // Return default mapping
      return {
        tenantId,
        packageId: 'core', // Default package
        customFeatures: [],
        customLimits: {}
      };
    }
    
    // Cache the result
    this.cache.set(cacheKey, mapping);
    
    return mapping;
  }
  
  /**
   * Create or update tenant-to-package mapping
   */
  async setTenantMapping(tenantId, packageId, customFeatures = [], customLimits = {}) {
    const mappings = await this.getAllTenantMappings();
    
    // Find the mapping
    const index = mappings.findIndex(m => m.tenantId === tenantId);
    
    // Create or update the mapping
    const mapping = {
      tenantId,
      packageId,
      customFeatures,
      customLimits
    };
    
    if (index === -1) {
      // Add new mapping
      mappings.push(mapping);
    } else {
      // Update existing mapping
      mappings[index] = mapping;
    }
    
    // Save to file
    await this.saveData(this.tenantMappingFile, mappings);
    
    // Invalidate cache
    this.cache.del('all_tenant_mappings');
    this.cache.del(`tenant_mapping_${tenantId}`);
    
    return mapping;
  }
  
  /**
   * Delete tenant-to-package mapping
   */
  async deleteTenantMapping(tenantId) {
    const mappings = await this.getAllTenantMappings();
    
    // Find the mapping
    const index = mappings.findIndex(m => m.tenantId === tenantId);
    
    if (index === -1) {
      throw new Error(`Tenant mapping for tenant ${tenantId} not found`);
    }
    
    // Remove the mapping
    mappings.splice(index, 1);
    
    // Save to file
    await this.saveData(this.tenantMappingFile, mappings);
    
    // Invalidate cache
    this.cache.del('all_tenant_mappings');
    this.cache.del(`tenant_mapping_${tenantId}`);
    
    return true;
  }
  
  /**
   * Check if tenant has access to feature
   */
  async hasTenantFeatureAccess(tenantId, featureId) {
    // Get tenant mapping
    const mapping = await this.getTenantMapping(tenantId);
    
    // Check if tenant has custom access to this feature
    if (mapping.customFeatures.includes(featureId)) {
      return true;
    }
    
    // Get tenant's package
    const pkg = await this.getPackageById(mapping.packageId);
    
    // Check if feature is included in the package
    return pkg.features.includes(featureId);
  }
  
  /**
   * Get tenant's feature limit
   */
  async getTenantFeatureLimit(tenantId, limitKey) {
    // Get tenant mapping
    const mapping = await this.getTenantMapping(tenantId);
    
    // Check if tenant has custom limit
    if (mapping.customLimits[limitKey] !== undefined) {
      return mapping.customLimits[limitKey];
    }
    
    // Get tenant's package
    const pkg = await this.getPackageById(mapping.packageId);
    
    // Return package limit
    return pkg.limits[limitKey] !== undefined ? pkg.limits[limitKey] : null;
  }
  
  /**
   * Get tenant's available features
   */
  async getTenantAvailableFeatures(tenantId) {
    // Get tenant mapping
    const mapping = await this.getTenantMapping(tenantId);
    
    // Get tenant's package
    const pkg = await this.getPackageById(mapping.packageId);
    
    // Combine package features and custom features
    const features = [...new Set([...pkg.features, ...mapping.customFeatures])];
    
    return features;
  }
  
  /**
   * Clear cache
   */
  clearCache() {
    this.cache.flushAll();
  }
}

module.exports = PackageConfigRegistry;
