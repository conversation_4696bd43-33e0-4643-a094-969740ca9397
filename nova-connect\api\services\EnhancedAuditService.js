/**
 * Enhanced Audit Service
 *
 * This service extends the base AuditService with advanced functionality:
 * - Advanced filtering and sorting
 * - Export capabilities
 * - Analytics and reporting
 * - Improved BigQuery integration
 */

const AuditService = require('./AuditService');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const csv = require('csv-stringify');
const { promisify } = require('util');
const stringify = promisify(csv.stringify);
const logger = require('../../config/logger');

// Import BigQuery if available
let BigQuery;
try {
  BigQuery = require('@google-cloud/bigquery').BigQuery;
} catch (error) {
  // BigQuery package not available, will use local storage only
  logger.warn('BigQuery package not available, using local storage only');
}

class EnhancedAuditService extends AuditService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    super(dataDir);
    
    // Additional configuration for enhanced features
    this.exportDir = path.join(this.dataDir, 'exports');
    this.ensureExportDir();
    
    // Enhanced BigQuery configuration
    this.bigQueryEnabled = process.env.BIGQUERY_ENABLED === 'true';
    this.projectId = process.env.GCP_PROJECT_ID;
    this.datasetId = process.env.BIGQUERY_DATASET_ID || 'novafuse_audit';
    this.tableId = process.env.BIGQUERY_TABLE_ID || 'events';
    
    if (this.bigQueryEnabled && this.projectId && BigQuery) {
      try {
        this.bigquery = new BigQuery({
          projectId: this.projectId
        });
        logger.info('BigQuery integration enabled for enhanced audit service');
      } catch (error) {
        logger.error('Error initializing BigQuery:', error);
      }
    }
  }
  
  /**
   * Ensure export directory exists
   */
  async ensureExportDir() {
    try {
      await fs.mkdir(this.exportDir, { recursive: true });
    } catch (error) {
      logger.error('Error creating export directory:', error);
    }
  }
  
  /**
   * Get audit logs with enhanced filtering and sorting
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Object} - Filtered and sorted audit logs
   */
  async getAuditLogs(filters = {}) {
    // If BigQuery is enabled and we're not in development mode, use BigQuery
    if (this.bigQueryEnabled && this.bigquery && process.env.NODE_ENV !== 'development') {
      return this.getAuditLogsFromBigQuery(filters);
    }
    
    // Otherwise, use local storage
    return this.getAuditLogsFromLocalStorage(filters);
  }
  
  /**
   * Get audit logs from local storage with enhanced filtering and sorting
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Object} - Filtered and sorted audit logs
   */
  async getAuditLogsFromLocalStorage(filters = {}) {
    const auditLogs = await this.loadAuditLogs();
    
    // Apply filters
    let filteredLogs = auditLogs;
    
    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
    }
    
    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
    }
    
    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }
    
    if (filters.action) {
      filteredLogs = filteredLogs.filter(log => log.action === filters.action);
    }
    
    if (filters.resourceType) {
      filteredLogs = filteredLogs.filter(log => log.resourceType === filters.resourceType);
    }
    
    if (filters.resourceId) {
      filteredLogs = filteredLogs.filter(log => log.resourceId === filters.resourceId);
    }
    
    if (filters.status) {
      filteredLogs = filteredLogs.filter(log => log.status === filters.status);
    }
    
    if (filters.teamId) {
      filteredLogs = filteredLogs.filter(log => log.teamId === filters.teamId);
    }
    
    if (filters.environmentId) {
      filteredLogs = filteredLogs.filter(log => log.environmentId === filters.environmentId);
    }
    
    if (filters.tenantId) {
      filteredLogs = filteredLogs.filter(log => log.tenantId === filters.tenantId);
    }
    
    // Apply search if provided
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredLogs = filteredLogs.filter(log => {
        // Search in various fields
        return (
          (log.userId && log.userId.toLowerCase().includes(searchTerm)) ||
          (log.action && log.action.toLowerCase().includes(searchTerm)) ||
          (log.resourceType && log.resourceType.toLowerCase().includes(searchTerm)) ||
          (log.resourceId && log.resourceId.toLowerCase().includes(searchTerm)) ||
          (log.status && log.status.toLowerCase().includes(searchTerm)) ||
          (log.details && JSON.stringify(log.details).toLowerCase().includes(searchTerm))
        );
      });
    }
    
    // Apply sorting
    const sortBy = filters.sortBy || 'timestamp';
    const sortOrder = filters.sortOrder || 'desc';
    
    filteredLogs.sort((a, b) => {
      if (sortBy === 'timestamp') {
        return sortOrder === 'desc' 
          ? new Date(b.timestamp) - new Date(a.timestamp)
          : new Date(a.timestamp) - new Date(b.timestamp);
      }
      
      if (a[sortBy] === undefined || b[sortBy] === undefined) {
        return 0;
      }
      
      if (typeof a[sortBy] === 'string' && typeof b[sortBy] === 'string') {
        return sortOrder === 'desc'
          ? b[sortBy].localeCompare(a[sortBy])
          : a[sortBy].localeCompare(b[sortBy]);
      }
      
      return sortOrder === 'desc'
        ? b[sortBy] - a[sortBy]
        : a[sortBy] - b[sortBy];
    });
    
    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 100;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);
    
    return {
      logs: paginatedLogs,
      total: filteredLogs.length,
      page,
      limit,
      totalPages: Math.ceil(filteredLogs.length / limit)
    };
  }
  
  /**
   * Get audit logs from BigQuery
   * 
   * @param {Object} filters - Filters to apply
   * @returns {Object} - Filtered and sorted audit logs
   */
  async getAuditLogsFromBigQuery(filters = {}) {
    try {
      // Build SQL query
      let query = `SELECT * FROM \`${this.projectId}.${this.datasetId}.${this.tableId}\` WHERE 1=1`;
      const params = [];
      
      if (filters.startDate) {
        query += ' AND timestamp >= @startDate';
        params.push({
          name: 'startDate',
          type: 'TIMESTAMP',
          value: new Date(filters.startDate).toISOString()
        });
      }
      
      if (filters.endDate) {
        query += ' AND timestamp <= @endDate';
        params.push({
          name: 'endDate',
          type: 'TIMESTAMP',
          value: new Date(filters.endDate).toISOString()
        });
      }
      
      if (filters.userId) {
        query += ' AND userId = @userId';
        params.push({
          name: 'userId',
          type: 'STRING',
          value: filters.userId
        });
      }
      
      if (filters.action) {
        query += ' AND action = @action';
        params.push({
          name: 'action',
          type: 'STRING',
          value: filters.action
        });
      }
      
      if (filters.resourceType) {
        query += ' AND resourceType = @resourceType';
        params.push({
          name: 'resourceType',
          type: 'STRING',
          value: filters.resourceType
        });
      }
      
      if (filters.resourceId) {
        query += ' AND resourceId = @resourceId';
        params.push({
          name: 'resourceId',
          type: 'STRING',
          value: filters.resourceId
        });
      }
      
      if (filters.status) {
        query += ' AND status = @status';
        params.push({
          name: 'status',
          type: 'STRING',
          value: filters.status
        });
      }
      
      if (filters.teamId) {
        query += ' AND teamId = @teamId';
        params.push({
          name: 'teamId',
          type: 'STRING',
          value: filters.teamId
        });
      }
      
      if (filters.environmentId) {
        query += ' AND environmentId = @environmentId';
        params.push({
          name: 'environmentId',
          type: 'STRING',
          value: filters.environmentId
        });
      }
      
      if (filters.tenantId) {
        query += ' AND tenantId = @tenantId';
        params.push({
          name: 'tenantId',
          type: 'STRING',
          value: filters.tenantId
        });
      }
      
      // Add search condition if provided
      if (filters.search) {
        query += ` AND (
          LOWER(CAST(userId AS STRING)) LIKE @search OR
          LOWER(action) LIKE @search OR
          LOWER(resourceType) LIKE @search OR
          LOWER(CAST(resourceId AS STRING)) LIKE @search OR
          LOWER(status) LIKE @search OR
          LOWER(TO_JSON_STRING(details)) LIKE @search
        )`;
        params.push({
          name: 'search',
          type: 'STRING',
          value: `%${filters.search.toLowerCase()}%`
        });
      }
      
      // Add sorting
      const sortBy = filters.sortBy || 'timestamp';
      const sortOrder = filters.sortOrder || 'desc';
      query += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
      
      // Add pagination
      const page = filters.page || 1;
      const limit = filters.limit || 100;
      const offset = (page - 1) * limit;
      query += ` LIMIT @limit OFFSET @offset`;
      params.push(
        {
          name: 'limit',
          type: 'INT64',
          value: limit
        },
        {
          name: 'offset',
          type: 'INT64',
          value: offset
        }
      );
      
      // Count total records (for pagination)
      let countQuery = `SELECT COUNT(*) as total FROM \`${this.projectId}.${this.datasetId}.${this.tableId}\` WHERE 1=1`;
      
      // Add the same filters to count query
      if (filters.startDate) {
        countQuery += ' AND timestamp >= @startDate';
      }
      
      if (filters.endDate) {
        countQuery += ' AND timestamp <= @endDate';
      }
      
      if (filters.userId) {
        countQuery += ' AND userId = @userId';
      }
      
      if (filters.action) {
        countQuery += ' AND action = @action';
      }
      
      if (filters.resourceType) {
        countQuery += ' AND resourceType = @resourceType';
      }
      
      if (filters.resourceId) {
        countQuery += ' AND resourceId = @resourceId';
      }
      
      if (filters.status) {
        countQuery += ' AND status = @status';
      }
      
      if (filters.teamId) {
        countQuery += ' AND teamId = @teamId';
      }
      
      if (filters.environmentId) {
        countQuery += ' AND environmentId = @environmentId';
      }
      
      if (filters.tenantId) {
        countQuery += ' AND tenantId = @tenantId';
      }
      
      if (filters.search) {
        countQuery += ` AND (
          LOWER(CAST(userId AS STRING)) LIKE @search OR
          LOWER(action) LIKE @search OR
          LOWER(resourceType) LIKE @search OR
          LOWER(CAST(resourceId AS STRING)) LIKE @search OR
          LOWER(status) LIKE @search OR
          LOWER(TO_JSON_STRING(details)) LIKE @search
        )`;
      }
      
      // Execute count query
      const [countRows] = await this.bigquery.query({
        query: countQuery,
        params
      });
      
      const total = countRows[0].total;
      
      // Execute main query
      const [rows] = await this.bigquery.query({
        query,
        params
      });
      
      return {
        logs: rows,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error querying BigQuery:', error);
      // Fall back to local storage
      logger.info('Falling back to local storage for audit logs');
      return this.getAuditLogsFromLocalStorage(filters);
    }
  }
}

module.exports = new EnhancedAuditService();
