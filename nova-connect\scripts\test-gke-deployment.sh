#!/bin/bash
# Script to test the GKE deployment of NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
CLUSTER_NAME=${2:-"novafuse-test-cluster"}
ZONE=${3:-"us-central1-a"}
NAMESPACE=${4:-"novafuse-test"}

# Get credentials for the cluster
echo "Getting credentials for the cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE --project $PROJECT_ID

# Get the service URL
echo "Getting service URL..."
SERVICE_IP=$(kubectl get service novafuse-uac -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
if [ -z "$SERVICE_IP" ]; then
  echo "Service is not exposed externally. Creating a port-forward..."
  kubectl port-forward service/novafuse-uac -n $NAMESPACE 8080:80 &
  SERVICE_URL="http://localhost:8080"
  echo "Service available at $SERVICE_URL"
else
  SERVICE_URL="http://$SERVICE_IP"
  echo "Service available at $SERVICE_URL"
fi

# Test the health endpoint
echo "Testing health endpoint..."
curl -f $SERVICE_URL/health || { echo "Health check failed"; exit 1; }

# Test the metrics endpoint
echo "Testing metrics endpoint..."
curl -f $SERVICE_URL/metrics || { echo "Metrics check failed"; exit 1; }

# Test the API endpoints
echo "Testing API endpoints..."
curl -f -H "Authorization: Bearer test-api-key-12345" $SERVICE_URL/api/v1/status || { echo "API check failed"; exit 1; }

# Test different tiers
echo "Testing core tier..."
curl -f -H "Authorization: Bearer test-api-key-12345" -H "X-Tier: core" $SERVICE_URL/api/v1/status || { echo "Core tier check failed"; exit 1; }

echo "Testing secure tier..."
curl -f -H "Authorization: Bearer test-api-key-12345" -H "X-Tier: secure" $SERVICE_URL/api/v1/status || { echo "Secure tier check failed"; exit 1; }

echo "Testing enterprise tier..."
curl -f -H "Authorization: Bearer test-api-key-12345" -H "X-Tier: enterprise" $SERVICE_URL/api/v1/status || { echo "Enterprise tier check failed"; exit 1; }

echo "Testing AI Boost tier..."
curl -f -H "Authorization: Bearer test-api-key-12345" -H "X-Tier: ai_boost" $SERVICE_URL/api/v1/status || { echo "AI Boost tier check failed"; exit 1; }

# Generate some load for monitoring
echo "Generating load for monitoring..."
for i in {1..100}; do
  curl -s -H "Authorization: Bearer test-api-key-12345" $SERVICE_URL/api/v1/status > /dev/null
  echo -n "."
  sleep 0.1
done
echo ""

echo "GKE deployment test complete!"
