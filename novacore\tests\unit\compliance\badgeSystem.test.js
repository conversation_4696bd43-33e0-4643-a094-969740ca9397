/**
 * Badge System Unit Tests
 */

const { expect } = require('chai');
const sinon = require('sinon');
const badgeSystem = require('../../../api/services/badgeSystem');

describe('Badge System Service', () => {
  describe('generateBadge', () => {
    it('should generate a badge with default options', async () => {
      const options = {
        organizationId: 'org123',
        badgeType: 'soc2',
        status: 'compliant'
      };
      
      const badge = await badgeSystem.generateBadge(options);
      
      // Badge should be a Buffer
      expect(badge).to.be.an.instanceof(Buffer);
      
      // Badge should not be empty
      expect(badge.length).to.be.greaterThan(0);
    });
    
    it('should generate badges with different styles', async () => {
      const styles = ['flat', 'gradient', '3d'];
      const badges = [];
      
      for (const style of styles) {
        const options = {
          organizationId: 'org123',
          badgeType: 'soc2',
          status: 'compliant',
          style
        };
        
        const badge = await badgeSystem.generateBadge(options);
        badges.push(badge);
      }
      
      // All badges should be Buffers
      for (const badge of badges) {
        expect(badge).to.be.an.instanceof(Buffer);
        expect(badge.length).to.be.greaterThan(0);
      }
      
      // Badges should be different
      for (let i = 0; i < badges.length; i++) {
        for (let j = i + 1; j < badges.length; j++) {
          expect(badges[i].equals(badges[j])).to.be.false;
        }
      }
    });
    
    it('should generate badges with different sizes', async () => {
      const sizes = ['small', 'medium', 'large'];
      const badges = [];
      
      for (const size of sizes) {
        const options = {
          organizationId: 'org123',
          badgeType: 'soc2',
          status: 'compliant',
          size
        };
        
        const badge = await badgeSystem.generateBadge(options);
        badges.push(badge);
      }
      
      // All badges should be Buffers
      for (const badge of badges) {
        expect(badge).to.be.an.instanceof(Buffer);
        expect(badge.length).to.be.greaterThan(0);
      }
      
      // Badges should be different
      for (let i = 0; i < badges.length; i++) {
        for (let j = i + 1; j < badges.length; j++) {
          expect(badges[i].equals(badges[j])).to.be.false;
        }
      }
    });
    
    it('should generate badges with different statuses', async () => {
      const statuses = ['compliant', 'partial', 'noncompliant', 'unknown'];
      const badges = [];
      
      for (const status of statuses) {
        const options = {
          organizationId: 'org123',
          badgeType: 'soc2',
          status
        };
        
        const badge = await badgeSystem.generateBadge(options);
        badges.push(badge);
      }
      
      // All badges should be Buffers
      for (const badge of badges) {
        expect(badge).to.be.an.instanceof(Buffer);
        expect(badge.length).to.be.greaterThan(0);
      }
      
      // Badges should be different
      for (let i = 0; i < badges.length; i++) {
        for (let j = i + 1; j < badges.length; j++) {
          expect(badges[i].equals(badges[j])).to.be.false;
        }
      }
    });
    
    it('should throw an error for invalid options', async () => {
      try {
        await badgeSystem.generateBadge(null);
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid badge options');
      }
      
      try {
        await badgeSystem.generateBadge({});
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid badge options');
      }
      
      try {
        await badgeSystem.generateBadge({ organizationId: 'org123' });
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid badge options');
      }
      
      try {
        await badgeSystem.generateBadge({ badgeType: 'soc2' });
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid badge options');
      }
    });
  });
  
  describe('verifyBadge', () => {
    it('should verify a valid badge', async () => {
      // Generate a badge
      const options = {
        organizationId: 'org123',
        badgeType: 'soc2',
        status: 'compliant'
      };
      
      const badge = await badgeSystem.generateBadge(options);
      
      // Verify the badge
      const verification = await badgeSystem.verifyBadge(badge);
      
      expect(verification).to.have.property('verified', true);
      expect(verification).to.have.property('organizationId');
      expect(verification).to.have.property('badgeType');
      expect(verification).to.have.property('timestamp');
      expect(verification).to.have.property('status');
    });
    
    it('should reject an invalid badge', async () => {
      // Create an invalid badge
      const invalidBadge = Buffer.from('invalid badge');
      
      // Verify the badge
      const verification = await badgeSystem.verifyBadge(invalidBadge);
      
      expect(verification).to.have.property('verified', false);
    });
  });
  
  describe('getComplianceStatus', () => {
    it('should get compliance status for an organization', async () => {
      const organizationId = 'org123';
      const frameworkId = 'soc2';
      
      const status = await badgeSystem.getComplianceStatus(organizationId, frameworkId);
      
      expect(status).to.be.a('string');
      expect(['compliant', 'partial', 'noncompliant', 'unknown']).to.include(status);
    });
    
    it('should handle different frameworks', async () => {
      const organizationId = 'org123';
      const frameworks = ['soc2', 'gdpr', 'hipaa', 'iso27001', 'nist'];
      
      for (const framework of frameworks) {
        const status = await badgeSystem.getComplianceStatus(organizationId, framework);
        
        expect(status).to.be.a('string');
        expect(['compliant', 'partial', 'noncompliant', 'unknown']).to.include(status);
      }
    });
    
    it('should return unknown for invalid framework', async () => {
      const organizationId = 'org123';
      const frameworkId = 'invalid';
      
      const status = await badgeSystem.getComplianceStatus(organizationId, frameworkId);
      
      expect(status).to.equal('unknown');
    });
  });
  
  describe('createVerificationPage', () => {
    let generateBadgeStub;
    let getOrganizationStub;
    let getComplianceStatusStub;
    
    beforeEach(() => {
      // Stub the generateBadge method
      generateBadgeStub = sinon.stub(badgeSystem, 'generateBadge').resolves(Buffer.from('badge'));
      
      // Stub the getOrganization method
      getOrganizationStub = sinon.stub(badgeSystem, 'getOrganization').resolves({
        id: 'org123',
        name: 'Test Organization',
        website: 'https://example.com',
        industry: 'Technology',
        size: 'Small',
        createdAt: new Date().toISOString()
      });
      
      // Stub the getComplianceStatus method
      getComplianceStatusStub = sinon.stub(badgeSystem, 'getComplianceStatus').resolves('compliant');
    });
    
    afterEach(() => {
      // Restore the stubs
      generateBadgeStub.restore();
      getOrganizationStub.restore();
      getComplianceStatusStub.restore();
    });
    
    it('should create a verification page', async () => {
      const organizationId = 'org123';
      const badgeType = 'soc2';
      
      const html = await badgeSystem.createVerificationPage(organizationId, badgeType);
      
      expect(html).to.be.a('string');
      expect(html).to.include('<!DOCTYPE html>');
      expect(html).to.include('NovaFuse Badge Verification');
      expect(html).to.include('Test Organization');
      expect(html).to.include('SOC2');
      expect(html).to.include('Compliant');
      
      // Verify that the stubs were called
      expect(generateBadgeStub.calledOnce).to.be.true;
      expect(getOrganizationStub.calledOnce).to.be.true;
      expect(getComplianceStatusStub.calledOnce).to.be.true;
    });
    
    it('should handle errors', async () => {
      // Restore the stubs
      generateBadgeStub.restore();
      getOrganizationStub.restore();
      getComplianceStatusStub.restore();
      
      // Create failing stubs
      generateBadgeStub = sinon.stub(badgeSystem, 'generateBadge').rejects(new Error('Badge generation error'));
      getOrganizationStub = sinon.stub(badgeSystem, 'getOrganization').rejects(new Error('Organization error'));
      getComplianceStatusStub = sinon.stub(badgeSystem, 'getComplianceStatus').rejects(new Error('Status error'));
      
      const organizationId = 'org123';
      const badgeType = 'soc2';
      
      const html = await badgeSystem.createVerificationPage(organizationId, badgeType);
      
      expect(html).to.be.a('string');
      expect(html).to.include('<!DOCTYPE html>');
      expect(html).to.include('NovaFuse Badge Verification Error');
      expect(html).to.include('Verification Error');
      expect(html).to.include('An error occurred');
    });
  });
});
