8a05dea66196c1e5ecc76391478c1d85
/**
 * NovaConnect UAC Billing Service
 *
 * This service handles billing-related functionality, including
 * GCP Marketplace entitlements and usage reporting.
 */

const {
  ServiceUsageClient
} = require('@google-cloud/service-usage');
const {
  CloudBillingClient
} = require('@google-cloud/billing');
const {
  BigQuery
} = require('@google-cloud/bigquery');
const {
  v4: uuidv4
} = require('uuid');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../../config/logger');
const FeatureService = require('./FeatureService');
const {
  ValidationError,
  NotFoundError
} = require('../utils/errors');
class BillingService {
  constructor() {
    this.dataDir = path.join(__dirname, '../../data/billing');
    this.entitlementsFile = path.join(this.dataDir, 'entitlements.json');
    this.usageFile = path.join(this.dataDir, 'usage.json');
    this.featureService = new FeatureService();

    // Initialize Google Cloud clients if available
    try {
      if (process.env.GCP_PROJECT_ID) {
        this.serviceUsageClient = new ServiceUsageClient();
        this.cloudBillingClient = new CloudBillingClient();
        this.bigquery = new BigQuery({
          projectId: process.env.GCP_PROJECT_ID
        });
        logger.info('Initialized Google Cloud clients for billing');
      }
    } catch (error) {
      logger.error('Error initializing Google Cloud clients for billing', {
        error: error.message
      });
    }
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, {
        recursive: true
      });

      // Initialize entitlements file if it doesn't exist
      try {
        await fs.access(this.entitlementsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.entitlementsFile, JSON.stringify({}));
        } else {
          throw error;
        }
      }

      // Initialize usage file if it doesn't exist
      try {
        await fs.access(this.usageFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.usageFile, JSON.stringify({}));
        } else {
          throw error;
        }
      }
    } catch (error) {
      logger.error('Error ensuring billing data directory', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Load entitlements from file
   */
  async loadEntitlements() {
    try {
      const data = await fs.readFile(this.entitlementsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Error loading entitlements', {
        error: error.message
      });
      return {};
    }
  }

  /**
   * Save entitlements to file
   */
  async saveEntitlements(entitlements) {
    try {
      await fs.writeFile(this.entitlementsFile, JSON.stringify(entitlements, null, 2));
    } catch (error) {
      logger.error('Error saving entitlements', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Load usage from file
   */
  async loadUsage() {
    try {
      const data = await fs.readFile(this.usageFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Error loading usage', {
        error: error.message
      });
      return {};
    }
  }

  /**
   * Save usage to file
   */
  async saveUsage(usage) {
    try {
      await fs.writeFile(this.usageFile, JSON.stringify(usage, null, 2));
    } catch (error) {
      logger.error('Error saving usage', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Enable features for a customer
   */
  async enableFeatures(customerId, entitlement) {
    try {
      logger.info('Enabling features for customer', {
        customerId,
        entitlement
      });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Add or update entitlement
      entitlements[customerId] = {
        ...entitlement,
        status: 'ACTIVE',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Enable features based on entitlement
      const tier = entitlement.plan || 'core';
      await this.featureService.enableFeaturesForTier(customerId, tier);

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');
        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement.id || `entitlement-${customerId}`,
          plan: tier,
          status: 'ACTIVE',
          created_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_CREATED'
        }]);
      }
      logger.info('Features enabled for customer', {
        customerId,
        tier
      });
    } catch (error) {
      logger.error('Error enabling features', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Update features for a customer
   */
  async updateFeatures(customerId, entitlement) {
    try {
      logger.info('Updating features for customer', {
        customerId,
        entitlement
      });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Check if entitlement exists
      if (!entitlements[customerId]) {
        logger.warn('Entitlement not found for customer', {
          customerId
        });
        return this.enableFeatures(customerId, entitlement);
      }

      // Get current tier
      const currentTier = entitlements[customerId].plan || 'core';

      // Update entitlement
      entitlements[customerId] = {
        ...entitlements[customerId],
        ...entitlement,
        updatedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Update features if tier changed
      const newTier = entitlement.plan || currentTier;
      if (newTier !== currentTier) {
        await this.featureService.enableFeaturesForTier(customerId, newTier);
      }

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');
        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement.id || `entitlement-${customerId}`,
          plan: newTier,
          status: entitlements[customerId].status,
          updated_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_UPDATED'
        }]);
      }
      logger.info('Features updated for customer', {
        customerId,
        tier: newTier
      });
    } catch (error) {
      logger.error('Error updating features', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Disable features for a customer
   */
  async disableFeatures(customerId, entitlement) {
    try {
      logger.info('Disabling features for customer', {
        customerId
      });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Check if entitlement exists
      if (!entitlements[customerId]) {
        logger.warn('Entitlement not found for customer', {
          customerId
        });
        return;
      }

      // Update entitlement status
      entitlements[customerId] = {
        ...entitlements[customerId],
        status: 'DELETED',
        updatedAt: new Date().toISOString(),
        deletedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Disable features
      await this.featureService.disableFeatures(customerId);

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');
        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,
          plan: entitlements[customerId].plan,
          status: 'DELETED',
          updated_at: new Date().toISOString(),
          deleted_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_DELETED'
        }]);
      }
      logger.info('Features disabled for customer', {
        customerId
      });
    } catch (error) {
      logger.error('Error disabling features', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Activate features for a customer
   */
  async activateFeatures(customerId, entitlement) {
    try {
      logger.info('Activating features for customer', {
        customerId
      });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Check if entitlement exists
      if (!entitlements[customerId]) {
        logger.warn('Entitlement not found for customer', {
          customerId
        });
        return this.enableFeatures(customerId, entitlement);
      }

      // Update entitlement status
      entitlements[customerId] = {
        ...entitlements[customerId],
        status: 'ACTIVE',
        updatedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Enable features
      const tier = entitlements[customerId].plan || 'core';
      await this.featureService.enableFeaturesForTier(customerId, tier);

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');
        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,
          plan: tier,
          status: 'ACTIVE',
          updated_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_ACTIVATED'
        }]);
      }
      logger.info('Features activated for customer', {
        customerId,
        tier
      });
    } catch (error) {
      logger.error('Error activating features', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Suspend features for a customer
   */
  async suspendFeatures(customerId, entitlement) {
    try {
      logger.info('Suspending features for customer', {
        customerId
      });

      // Load current entitlements
      const entitlements = await this.loadEntitlements();

      // Check if entitlement exists
      if (!entitlements[customerId]) {
        logger.warn('Entitlement not found for customer', {
          customerId
        });
        return;
      }

      // Update entitlement status
      entitlements[customerId] = {
        ...entitlements[customerId],
        status: 'SUSPENDED',
        updatedAt: new Date().toISOString()
      };

      // Save entitlements
      await this.saveEntitlements(entitlements);

      // Suspend features
      await this.featureService.suspendFeatures(customerId);

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('entitlements');
        await table.insert([{
          customer_id: customerId,
          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,
          plan: entitlements[customerId].plan,
          status: 'SUSPENDED',
          updated_at: new Date().toISOString(),
          event_type: 'ENTITLEMENT_SUSPENDED'
        }]);
      }
      logger.info('Features suspended for customer', {
        customerId
      });
    } catch (error) {
      logger.error('Error suspending features', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Get customer entitlements
   */
  async getCustomerEntitlements(customerId) {
    try {
      // Load entitlements
      const entitlements = await this.loadEntitlements();

      // Return customer entitlement or empty object
      return entitlements[customerId] || {
        status: 'NOT_FOUND'
      };
    } catch (error) {
      logger.error('Error getting customer entitlements', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Get customer usage
   */
  async getCustomerUsage(customerId, startDate, endDate) {
    try {
      // Parse dates
      const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to last 30 days
      const end = endDate ? new Date(endDate) : new Date();

      // Load usage
      const usage = await this.loadUsage();

      // Get customer usage
      const customerUsage = usage[customerId] || {};

      // Filter by date range
      const filteredUsage = Object.entries(customerUsage).filter(([date]) => {
        const usageDate = new Date(date);
        return usageDate >= start && usageDate <= end;
      }).reduce((acc, [date, metrics]) => {
        acc[date] = metrics;
        return acc;
      }, {});

      // Calculate totals
      const totals = {};
      Object.values(filteredUsage).forEach(metrics => {
        Object.entries(metrics).forEach(([metric, value]) => {
          totals[metric] = (totals[metric] || 0) + value;
        });
      });
      return {
        customerId,
        startDate: start.toISOString(),
        endDate: end.toISOString(),
        usage: filteredUsage,
        totals
      };
    } catch (error) {
      logger.error('Error getting customer usage', {
        error: error.message,
        customerId
      });
      throw error;
    }
  }

  /**
   * Report usage
   */
  async reportUsage(customerId, metricName, quantity, timestamp = new Date().toISOString(), tenantId = null) {
    try {
      logger.info('Reporting usage', {
        customerId,
        metricName,
        quantity,
        tenantId
      });

      // Load usage
      const usage = await this.loadUsage();

      // Initialize customer usage if needed
      if (!usage[customerId]) {
        usage[customerId] = {};
      }

      // Get date (YYYY-MM-DD)
      const date = timestamp.split('T')[0];

      // Initialize date usage if needed
      if (!usage[customerId][date]) {
        usage[customerId][date] = {};
      }

      // Update usage
      usage[customerId][date][metricName] = (usage[customerId][date][metricName] || 0) + quantity;

      // Save usage
      await this.saveUsage(usage);

      // Report to GCP Marketplace if available
      if (this.cloudBillingClient && process.env.GCP_BILLING_ACCOUNT) {
        // Implement GCP Marketplace usage reporting for single-tenant architecture
        if (tenantId) {
          try {
            // For single-tenant architecture, we need to include the tenant ID
            const reportRequest = {
              parent: `projects/${process.env.GCP_PROJECT_ID}`,
              resourceName: `projects/${process.env.GCP_PROJECT_ID}/services/novafuse.googleapis.com`,
              usageMetric: metricName,
              usageValue: quantity,
              timestamp: timestamp,
              labels: {
                tenant_id: tenantId
              }
            };

            // Report usage to GCP Marketplace
            await this.cloudBillingClient.reportUsage(reportRequest);
            logger.info('GCP Marketplace usage reported for tenant', {
              tenantId,
              metricName,
              quantity
            });
          } catch (reportError) {
            logger.error('Error reporting usage to GCP Marketplace', {
              error: reportError.message,
              tenantId,
              customerId,
              metricName
            });
          }
        } else {
          logger.warn('Tenant ID not provided for GCP Marketplace usage reporting');
        }
      }

      // Log to BigQuery if available
      if (this.bigquery) {
        const dataset = this.bigquery.dataset('billing');
        const table = dataset.table('usage');
        await table.insert([{
          customer_id: customerId,
          tenant_id: tenantId,
          metric_name: metricName,
          quantity: quantity,
          timestamp: timestamp,
          date: date
        }]);
      }
      logger.info('Usage reported', {
        customerId,
        tenantId,
        metricName,
        quantity
      });
    } catch (error) {
      logger.error('Error reporting usage', {
        error: error.message,
        customerId,
        tenantId,
        metricName
      });
      throw error;
    }
  }

  /**
   * Report tenant-specific usage
   */
  async reportTenantUsage(tenantId, metricName, quantity, timestamp = new Date().toISOString()) {
    try {
      // For tenant-specific usage, we use the tenant ID as the customer ID
      // This ensures proper isolation in our usage tracking
      const customerId = `tenant-${tenantId}`;

      // Report usage with tenant ID
      return this.reportUsage(customerId, metricName, quantity, timestamp, tenantId);
    } catch (error) {
      logger.error('Error reporting tenant usage', {
        error: error.message,
        tenantId,
        metricName
      });
      throw error;
    }
  }
}
module.exports = BillingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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