/**
 * Workflow Service
 *
 * This service handles workflow automation.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AuditService = require('./AuditService');

class WorkflowService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.workflowsDir = path.join(this.dataDir, 'workflows');
    this.workflowsFile = path.join(this.workflowsDir, 'workflows.json');
    this.workflowRunsFile = path.join(this.workflowsDir, 'workflow_runs.json');
    this.workflowTriggersFile = path.join(this.workflowsDir, 'workflow_triggers.json');
    this.auditService = new AuditService(dataDir);

    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.workflowsDir, { recursive: true });

      // Initialize files if they don't exist
      await this.initializeFile(this.workflowsFile, []);
      await this.initializeFile(this.workflowRunsFile, []);
      await this.initializeFile(this.workflowTriggersFile, []);
    } catch (error) {
      console.error('Error creating workflows directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all workflows
   */
  async getAllWorkflows(filters = {}) {
    const workflows = await this.loadData(this.workflowsFile);

    // Apply filters
    let filteredWorkflows = workflows;

    if (filters.createdBy) {
      filteredWorkflows = filteredWorkflows.filter(w => w.createdBy === filters.createdBy);
    }

    if (filters.status) {
      filteredWorkflows = filteredWorkflows.filter(w => w.status === filters.status);
    }

    if (filters.triggerType) {
      filteredWorkflows = filteredWorkflows.filter(w => w.trigger.type === filters.triggerType);
    }

    // Sort by created date (newest first)
    filteredWorkflows.sort((a, b) => new Date(b.created) - new Date(a.created));

    return filteredWorkflows;
  }

  /**
   * Get workflows for a user
   */
  async getWorkflowsForUser(userId, filters = {}) {
    return this.getAllWorkflows({ ...filters, createdBy: userId });
  }

  /**
   * Get workflow by ID
   */
  async getWorkflowById(id) {
    const workflows = await this.loadData(this.workflowsFile);
    const workflow = workflows.find(w => w.id === id);

    if (!workflow) {
      throw new NotFoundError(`Workflow with ID ${id} not found`);
    }

    return workflow;
  }

  /**
   * Create a new workflow
   */
  async createWorkflow(data, userId) {
    if (!data.name) {
      throw new ValidationError('Workflow name is required');
    }

    if (!data.trigger) {
      throw new ValidationError('Workflow trigger is required');
    }

    if (!data.trigger.type) {
      throw new ValidationError('Trigger type is required');
    }

    if (!data.actions || !Array.isArray(data.actions) || data.actions.length === 0) {
      throw new ValidationError('Workflow must have at least one action');
    }

    // Validate trigger type
    const validTriggerTypes = ['manual', 'scheduled', 'event', 'api'];
    if (!validTriggerTypes.includes(data.trigger.type)) {
      throw new ValidationError(`Invalid trigger type: ${data.trigger.type}. Valid types: ${validTriggerTypes.join(', ')}`);
    }

    // Validate actions
    for (const action of data.actions) {
      if (!action.type) {
        throw new ValidationError('Action type is required');
      }

      // Validate action type
      const validActionTypes = ['http', 'connector', 'notification', 'condition', 'delay'];
      if (!validActionTypes.includes(action.type)) {
        throw new ValidationError(`Invalid action type: ${action.type}. Valid types: ${validActionTypes.join(', ')}`);
      }

      // Validate action-specific parameters
      switch (action.type) {
        case 'http':
          if (!action.url) {
            throw new ValidationError('HTTP action requires a URL');
          }
          if (!action.method) {
            throw new ValidationError('HTTP action requires a method');
          }
          break;
        case 'connector':
          if (!action.connectorId) {
            throw new ValidationError('Connector action requires a connector ID');
          }
          if (!action.operation) {
            throw new ValidationError('Connector action requires an operation');
          }
          break;
        case 'notification':
          if (!action.channel) {
            throw new ValidationError('Notification action requires a channel');
          }
          if (!action.message) {
            throw new ValidationError('Notification action requires a message');
          }
          break;
        case 'condition':
          if (!action.condition) {
            throw new ValidationError('Condition action requires a condition');
          }
          if (!action.trueActions || !Array.isArray(action.trueActions)) {
            throw new ValidationError('Condition action requires true actions');
          }
          if (!action.falseActions || !Array.isArray(action.falseActions)) {
            throw new ValidationError('Condition action requires false actions');
          }
          break;
        case 'delay':
          if (!action.duration) {
            throw new ValidationError('Delay action requires a duration');
          }
          break;
      }
    }

    // Create workflow
    const workflow = {
      id: uuidv4(),
      name: data.name,
      description: data.description || '',
      trigger: data.trigger,
      actions: data.actions,
      status: 'active',
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };

    // Save workflow
    const workflows = await this.loadData(this.workflowsFile);
    workflows.push(workflow);
    await this.saveData(this.workflowsFile, workflows);

    // Register workflow trigger if needed
    if (workflow.trigger.type === 'scheduled' || workflow.trigger.type === 'event') {
      await this.registerWorkflowTrigger(workflow);
    }

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'CREATE',
      resourceType: 'workflow',
      resourceId: workflow.id,
      details: {
        name: workflow.name,
        triggerType: workflow.trigger.type
      }
    });

    return workflow;
  }

  /**
   * Update a workflow
   */
  async updateWorkflow(id, data, userId) {
    const workflows = await this.loadData(this.workflowsFile);
    const index = workflows.findIndex(w => w.id === id);

    if (index === -1) {
      throw new NotFoundError(`Workflow with ID ${id} not found`);
    }

    const workflow = workflows[index];

    // Check if user has permission to update
    if (workflow.createdBy !== userId) {
      throw new AuthorizationError('You do not have permission to update this workflow');
    }

    // Validate actions if provided
    if (data.actions) {
      if (!Array.isArray(data.actions) || data.actions.length === 0) {
        throw new ValidationError('Workflow must have at least one action');
      }

      // Validate actions
      for (const action of data.actions) {
        if (!action.type) {
          throw new ValidationError('Action type is required');
        }

        // Validate action type
        const validActionTypes = ['http', 'connector', 'notification', 'condition', 'delay'];
        if (!validActionTypes.includes(action.type)) {
          throw new ValidationError(`Invalid action type: ${action.type}. Valid types: ${validActionTypes.join(', ')}`);
        }

        // Validate action-specific parameters
        switch (action.type) {
          case 'http':
            if (!action.url) {
              throw new ValidationError('HTTP action requires a URL');
            }
            if (!action.method) {
              throw new ValidationError('HTTP action requires a method');
            }
            break;
          case 'connector':
            if (!action.connectorId) {
              throw new ValidationError('Connector action requires a connector ID');
            }
            if (!action.operation) {
              throw new ValidationError('Connector action requires an operation');
            }
            break;
          case 'notification':
            if (!action.channel) {
              throw new ValidationError('Notification action requires a channel');
            }
            if (!action.message) {
              throw new ValidationError('Notification action requires a message');
            }
            break;
          case 'condition':
            if (!action.condition) {
              throw new ValidationError('Condition action requires a condition');
            }
            if (!action.trueActions || !Array.isArray(action.trueActions)) {
              throw new ValidationError('Condition action requires true actions');
            }
            if (!action.falseActions || !Array.isArray(action.falseActions)) {
              throw new ValidationError('Condition action requires false actions');
            }
            break;
          case 'delay':
            if (!action.duration) {
              throw new ValidationError('Delay action requires a duration');
            }
            break;
        }
      }
    }

    // Validate trigger if provided
    if (data.trigger) {
      if (!data.trigger.type) {
        throw new ValidationError('Trigger type is required');
      }

      // Validate trigger type
      const validTriggerTypes = ['manual', 'scheduled', 'event', 'api'];
      if (!validTriggerTypes.includes(data.trigger.type)) {
        throw new ValidationError(`Invalid trigger type: ${data.trigger.type}. Valid types: ${validTriggerTypes.join(', ')}`);
      }
    }

    // Check if trigger type is changing
    const triggerTypeChanged = data.trigger && data.trigger.type !== workflow.trigger.type;

    // Update workflow
    const updatedWorkflow = {
      ...workflow,
      name: data.name || workflow.name,
      description: data.description !== undefined ? data.description : workflow.description,
      trigger: data.trigger || workflow.trigger,
      actions: data.actions || workflow.actions,
      status: data.status || workflow.status,
      updated: new Date().toISOString()
    };

    workflows[index] = updatedWorkflow;
    await this.saveData(this.workflowsFile, workflows);

    // Update workflow trigger if needed
    if (triggerTypeChanged) {
      // Unregister old trigger
      await this.unregisterWorkflowTrigger(workflow);

      // Register new trigger if needed
      if (updatedWorkflow.trigger.type === 'scheduled' || updatedWorkflow.trigger.type === 'event') {
        await this.registerWorkflowTrigger(updatedWorkflow);
      }
    } else if (data.trigger && (updatedWorkflow.trigger.type === 'scheduled' || updatedWorkflow.trigger.type === 'event')) {
      // Update existing trigger
      await this.updateWorkflowTrigger(updatedWorkflow);
    }

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'workflow',
      resourceId: id,
      details: {
        name: updatedWorkflow.name,
        triggerType: updatedWorkflow.trigger.type,
        status: updatedWorkflow.status
      }
    });

    return updatedWorkflow;
  }

  /**
   * Delete a workflow
   */
  async deleteWorkflow(id, userId) {
    const workflows = await this.loadData(this.workflowsFile);
    const index = workflows.findIndex(w => w.id === id);

    if (index === -1) {
      throw new NotFoundError(`Workflow with ID ${id} not found`);
    }

    const workflow = workflows[index];

    // Check if user has permission to delete
    if (workflow.createdBy !== userId) {
      throw new AuthorizationError('You do not have permission to delete this workflow');
    }

    // Remove the workflow
    workflows.splice(index, 1);
    await this.saveData(this.workflowsFile, workflows);

    // Unregister workflow trigger if needed
    if (workflow.trigger.type === 'scheduled' || workflow.trigger.type === 'event') {
      await this.unregisterWorkflowTrigger(workflow);
    }

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'workflow',
      resourceId: id,
      details: {
        name: workflow.name,
        triggerType: workflow.trigger.type
      }
    });

    return { success: true, message: `Workflow ${id} deleted` };
  }

  /**
   * Enable a workflow
   */
  async enableWorkflow(id, userId) {
    const workflows = await this.loadData(this.workflowsFile);
    const index = workflows.findIndex(w => w.id === id);

    if (index === -1) {
      throw new NotFoundError(`Workflow with ID ${id} not found`);
    }

    const workflow = workflows[index];

    // Check if user has permission to update
    if (workflow.createdBy !== userId) {
      throw new AuthorizationError('You do not have permission to update this workflow');
    }

    // Check if workflow is already active
    if (workflow.status === 'active') {
      return workflow;
    }

    // Update workflow status
    workflow.status = 'active';
    workflow.updated = new Date().toISOString();

    workflows[index] = workflow;
    await this.saveData(this.workflowsFile, workflows);

    // Register workflow trigger if needed
    if (workflow.trigger.type === 'scheduled' || workflow.trigger.type === 'event') {
      await this.registerWorkflowTrigger(workflow);
    }

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'ENABLE',
      resourceType: 'workflow',
      resourceId: id,
      details: {
        name: workflow.name,
        triggerType: workflow.trigger.type
      }
    });

    return workflow;
  }

  /**
   * Disable a workflow
   */
  async disableWorkflow(id, userId) {
    const workflows = await this.loadData(this.workflowsFile);
    const index = workflows.findIndex(w => w.id === id);

    if (index === -1) {
      throw new NotFoundError(`Workflow with ID ${id} not found`);
    }

    const workflow = workflows[index];

    // Check if user has permission to update
    if (workflow.createdBy !== userId) {
      throw new AuthorizationError('You do not have permission to update this workflow');
    }

    // Check if workflow is already inactive
    if (workflow.status === 'inactive') {
      return workflow;
    }

    // Update workflow status
    workflow.status = 'inactive';
    workflow.updated = new Date().toISOString();

    workflows[index] = workflow;
    await this.saveData(this.workflowsFile, workflows);

    // Unregister workflow trigger if needed
    if (workflow.trigger.type === 'scheduled' || workflow.trigger.type === 'event') {
      await this.unregisterWorkflowTrigger(workflow);
    }

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DISABLE',
      resourceType: 'workflow',
      resourceId: id,
      details: {
        name: workflow.name,
        triggerType: workflow.trigger.type
      }
    });

    return workflow;
  }

  /**
   * Register workflow trigger
   */
  async registerWorkflowTrigger(workflow) {
    // Only register if workflow is active
    if (workflow.status !== 'active') {
      return;
    }

    const triggers = await this.loadData(this.workflowTriggersFile);

    // Check if trigger already exists
    const existingTriggerIndex = triggers.findIndex(t => t.workflowId === workflow.id);

    if (existingTriggerIndex !== -1) {
      // Update existing trigger
      triggers[existingTriggerIndex] = {
        ...triggers[existingTriggerIndex],
        type: workflow.trigger.type,
        config: workflow.trigger,
        updated: new Date().toISOString()
      };
    } else {
      // Create new trigger
      triggers.push({
        id: uuidv4(),
        workflowId: workflow.id,
        type: workflow.trigger.type,
        config: workflow.trigger,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      });
    }

    await this.saveData(this.workflowTriggersFile, triggers);
  }

  /**
   * Update workflow trigger
   */
  async updateWorkflowTrigger(workflow) {
    // Only update if workflow is active
    if (workflow.status !== 'active') {
      return;
    }

    const triggers = await this.loadData(this.workflowTriggersFile);
    const index = triggers.findIndex(t => t.workflowId === workflow.id);

    if (index === -1) {
      // Trigger doesn't exist, register it
      await this.registerWorkflowTrigger(workflow);
      return;
    }

    // Update trigger
    triggers[index] = {
      ...triggers[index],
      type: workflow.trigger.type,
      config: workflow.trigger,
      updated: new Date().toISOString()
    };

    await this.saveData(this.workflowTriggersFile, triggers);
  }

  /**
   * Unregister workflow trigger
   */
  async unregisterWorkflowTrigger(workflow) {
    const triggers = await this.loadData(this.workflowTriggersFile);
    const index = triggers.findIndex(t => t.workflowId === workflow.id);

    if (index === -1) {
      // Trigger doesn't exist
      return;
    }

    // Remove trigger
    triggers.splice(index, 1);
    await this.saveData(this.workflowTriggersFile, triggers);
  }

  /**
   * Execute a workflow manually
   */
  async executeWorkflow(id, inputs = {}, userId) {
    const workflow = await this.getWorkflowById(id);

    // Check if workflow is active
    if (workflow.status !== 'active') {
      throw new ValidationError(`Workflow ${id} is not active`);
    }

    // Create workflow run
    const run = {
      id: uuidv4(),
      workflowId: workflow.id,
      workflowName: workflow.name,
      triggerType: 'manual',
      status: 'running',
      inputs,
      outputs: {},
      startTime: new Date().toISOString(),
      endTime: null,
      duration: 0,
      steps: [],
      initiatedBy: userId
    };

    // Save workflow run
    const runs = await this.loadData(this.workflowRunsFile);
    runs.push(run);
    await this.saveData(this.workflowRunsFile, runs);

    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'EXECUTE',
      resourceType: 'workflow',
      resourceId: workflow.id,
      details: {
        name: workflow.name,
        runId: run.id
      }
    });

    // Execute workflow asynchronously
    this.executeWorkflowRun(run.id, workflow, inputs).catch(error => {
      console.error(`Error executing workflow ${workflow.id}:`, error);
    });

    return run;
  }

  /**
   * Execute a workflow run
   */
  async executeWorkflowRun(runId, workflow, inputs = {}) {
    const startTime = new Date();
    let status = 'completed';
    let outputs = {};
    const steps = [];

    try {
      // Execute workflow actions
      outputs = await this.executeActions(workflow.actions, inputs, steps);
    } catch (error) {
      status = 'failed';
      steps.push({
        id: uuidv4(),
        type: 'error',
        status: 'failed',
        error: error.message,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString()
      });
    }

    const endTime = new Date();
    const duration = endTime - startTime;

    // Update workflow run
    const runs = await this.loadData(this.workflowRunsFile);
    const index = runs.findIndex(r => r.id === runId);

    if (index !== -1) {
      runs[index] = {
        ...runs[index],
        status,
        outputs,
        endTime: endTime.toISOString(),
        duration,
        steps
      };

      await this.saveData(this.workflowRunsFile, runs);
    }

    return runs[index];
  }

  /**
   * Execute workflow actions
   */
  async executeActions(actions, inputs, steps) {
    let outputs = { ...inputs };

    for (const action of actions) {
      const step = {
        id: uuidv4(),
        type: action.type,
        name: action.name || action.type,
        status: 'running',
        startTime: new Date().toISOString(),
        endTime: null,
        inputs: this.replaceVariables(action, outputs),
        outputs: {}
      };

      steps.push(step);

      try {
        // Execute action based on type
        let actionOutputs;

        switch (action.type) {
          case 'http':
            actionOutputs = await this.executeHttpAction(action, outputs);
            break;
          case 'connector':
            actionOutputs = await this.executeConnectorAction(action, outputs);
            break;
          case 'notification':
            actionOutputs = await this.executeNotificationAction(action, outputs);
            break;
          case 'condition':
            actionOutputs = await this.executeConditionAction(action, outputs, steps);
            break;
          case 'delay':
            actionOutputs = await this.executeDelayAction(action, outputs);
            break;
          default:
            throw new Error(`Unsupported action type: ${action.type}`);
        }

        // Update step
        step.status = 'completed';
        step.endTime = new Date().toISOString();
        step.outputs = actionOutputs;

        // Merge outputs
        outputs = { ...outputs, ...actionOutputs };
      } catch (error) {
        // Update step with error
        step.status = 'failed';
        step.endTime = new Date().toISOString();
        step.error = error.message;

        // Rethrow error to stop workflow execution
        throw error;
      }
    }

    return outputs;
  }

  /**
   * Execute HTTP action
   */
  async executeHttpAction(action, inputs) {
    // In a real implementation, this would make an HTTP request
    // For now, just simulate a successful request

    // Replace variables in URL and body
    const url = this.replaceVariablesInString(action.url, inputs);
    const body = action.body ? this.replaceVariablesInObject(action.body, inputs) : null;

    // Simulate HTTP request
    console.log(`Executing HTTP ${action.method} request to ${url}`);

    // Simulate response
    return {
      statusCode: 200,
      body: { message: 'Success', data: { id: '123', name: 'Test' } },
      headers: { 'content-type': 'application/json' }
    };
  }

  /**
   * Execute connector action
   */
  async executeConnectorAction(action, inputs) {
    // In a real implementation, this would execute a connector operation
    // For now, just simulate a successful operation

    // Replace variables in parameters
    const parameters = action.parameters ? this.replaceVariablesInObject(action.parameters, inputs) : {};

    // Simulate connector operation
    console.log(`Executing connector ${action.connectorId} operation ${action.operation}`);

    // Simulate response
    return {
      success: true,
      data: { id: '456', name: 'Connector Result' }
    };
  }

  /**
   * Execute notification action
   */
  async executeNotificationAction(action, inputs) {
    // In a real implementation, this would send a notification
    // For now, just simulate a successful notification

    // Replace variables in message
    const message = this.replaceVariablesInString(action.message, inputs);

    // Simulate notification
    console.log(`Sending ${action.channel} notification: ${message}`);

    // Simulate response
    return {
      success: true,
      channel: action.channel,
      message
    };
  }

  /**
   * Execute condition action
   */
  async executeConditionAction(action, inputs, steps) {
    // Replace variables in condition
    const condition = this.replaceVariablesInString(action.condition, inputs);

    // Evaluate condition
    let result;
    try {
      // Use Function constructor to evaluate condition
      // This is a simple approach for demo purposes
      // In a real implementation, you would use a proper expression evaluator
      const evalFn = new Function('inputs', `return ${condition};`);
      result = evalFn(inputs);
    } catch (error) {
      throw new Error(`Error evaluating condition: ${error.message}`);
    }

    // Execute true or false actions based on condition result
    if (result) {
      console.log(`Condition ${condition} evaluated to true`);
      return await this.executeActions(action.trueActions, inputs, steps);
    } else {
      console.log(`Condition ${condition} evaluated to false`);
      return await this.executeActions(action.falseActions, inputs, steps);
    }
  }

  /**
   * Execute delay action
   */
  async executeDelayAction(action, inputs) {
    // Replace variables in duration
    const duration = this.replaceVariablesInString(action.duration, inputs);

    // Parse duration in milliseconds
    const durationMs = this.parseDuration(duration);

    // Simulate delay
    console.log(`Delaying for ${durationMs}ms`);
    await new Promise(resolve => setTimeout(resolve, durationMs));

    // Simulate response
    return {
      success: true,
      duration: durationMs
    };
  }

  /**
   * Parse duration string to milliseconds
   */
  parseDuration(duration) {
    // Parse duration string like '5s', '10m', '1h'
    const match = duration.match(/^(\d+)([smh])$/);

    if (!match) {
      // Default to seconds if no unit specified
      return parseInt(duration, 10) * 1000;
    }

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value * 1000; // seconds to milliseconds
      case 'm':
        return value * 60 * 1000; // minutes to milliseconds
      case 'h':
        return value * 60 * 60 * 1000; // hours to milliseconds
      default:
        return value * 1000; // default to seconds
    }
  }

  /**
   * Replace variables in object
   */
  replaceVariablesInObject(obj, inputs) {
    if (!obj) return obj;

    if (typeof obj === 'string') {
      return this.replaceVariablesInString(obj, inputs);
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.replaceVariablesInObject(item, inputs));
    }

    if (typeof obj === 'object' && obj !== null) {
      const result = {};

      for (const key in obj) {
        result[key] = this.replaceVariablesInObject(obj[key], inputs);
      }

      return result;
    }

    return obj;
  }

  /**
   * Replace variables in string
   */
  replaceVariablesInString(str, inputs) {
    if (typeof str !== 'string') return str;

    // Replace variables in format ${variable}
    return str.replace(/\${([^}]+)}/g, (match, variable) => {
      // Split variable path (e.g., 'inputs.user.name')
      const path = variable.split('.');

      // Traverse the inputs object to get the value
      let value = inputs;

      for (const key of path) {
        if (value === undefined || value === null) {
          return match; // Keep original if path doesn't exist
        }

        value = value[key];
      }

      if (value === undefined || value === null) {
        return match; // Keep original if value is undefined or null
      }

      return String(value);
    });
  }

  /**
   * Replace variables in action
   */
  replaceVariables(action, inputs) {
    // Create a copy of the action with variables replaced
    return this.replaceVariablesInObject(action, inputs);
  }

  /**
   * Get all workflow runs
   */
  async getAllWorkflowRuns(filters = {}) {
    const runs = await this.loadData(this.workflowRunsFile);

    // Apply filters
    let filteredRuns = runs;

    if (filters.workflowId) {
      filteredRuns = filteredRuns.filter(r => r.workflowId === filters.workflowId);
    }

    if (filters.status) {
      filteredRuns = filteredRuns.filter(r => r.status === filters.status);
    }

    if (filters.initiatedBy) {
      filteredRuns = filteredRuns.filter(r => r.initiatedBy === filters.initiatedBy);
    }

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredRuns = filteredRuns.filter(r => new Date(r.startTime) >= startDate);
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredRuns = filteredRuns.filter(r => new Date(r.startTime) <= endDate);
    }

    // Sort by start time (newest first)
    filteredRuns.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));

    return filteredRuns;
  }

  /**
   * Get workflow runs for a workflow
   */
  async getWorkflowRunsForWorkflow(workflowId, filters = {}) {
    return this.getAllWorkflowRuns({ ...filters, workflowId });
  }

  /**
   * Get workflow run by ID
   */
  async getWorkflowRunById(id) {
    const runs = await this.loadData(this.workflowRunsFile);
    const run = runs.find(r => r.id === id);

    if (!run) {
      throw new NotFoundError(`Workflow run with ID ${id} not found`);
    }

    return run;
  }

  /**
   * Process scheduled workflows
   */
  async processScheduledWorkflows() {
    const triggers = await this.loadData(this.workflowTriggersFile);
    const scheduledTriggers = triggers.filter(t => t.type === 'scheduled');

    const results = [];

    for (const trigger of scheduledTriggers) {
      try {
        // Get workflow
        const workflow = await this.getWorkflowById(trigger.workflowId);

        // Check if workflow is active
        if (workflow.status !== 'active') {
          continue;
        }

        // Check if it's time to run the workflow
        if (!this.isTimeToRunScheduledWorkflow(trigger.config)) {
          continue;
        }

        // Execute workflow
        const run = await this.executeWorkflow(workflow.id, {}, 'system');

        results.push({
          workflowId: workflow.id,
          workflowName: workflow.name,
          runId: run.id,
          status: 'started'
        });
      } catch (error) {
        results.push({
          workflowId: trigger.workflowId,
          status: 'error',
          error: error.message
        });
      }
    }

    return {
      success: true,
      processed: scheduledTriggers.length,
      executed: results.length,
      results
    };
  }

  /**
   * Check if it's time to run a scheduled workflow
   */
  isTimeToRunScheduledWorkflow(triggerConfig) {
    // In a real implementation, this would check the schedule configuration
    // against the current time to determine if the workflow should run

    // For now, just return true for demo purposes
    return true;
  }

  /**
   * Trigger event-based workflows
   */
  async triggerEventWorkflows(eventType, eventData) {
    const triggers = await this.loadData(this.workflowTriggersFile);
    const eventTriggers = triggers.filter(t =>
      t.type === 'event' &&
      t.config.eventType === eventType
    );

    const results = [];

    for (const trigger of eventTriggers) {
      try {
        // Get workflow
        const workflow = await this.getWorkflowById(trigger.workflowId);

        // Check if workflow is active
        if (workflow.status !== 'active') {
          continue;
        }

        // Execute workflow with event data as input
        const run = await this.executeWorkflow(workflow.id, { event: eventData }, 'system');

        results.push({
          workflowId: workflow.id,
          workflowName: workflow.name,
          runId: run.id,
          status: 'started'
        });
      } catch (error) {
        results.push({
          workflowId: trigger.workflowId,
          status: 'error',
          error: error.message
        });
      }
    }

    return {
      success: true,
      eventType,
      processed: eventTriggers.length,
      executed: results.length,
      results
    };
  }
}

module.exports = WorkflowService;
