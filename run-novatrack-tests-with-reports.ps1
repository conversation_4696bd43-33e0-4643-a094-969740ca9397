# NovaFuse Universal Platform - NovaTrack Tests with HTML Reports

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for test results
Write-ColorOutput "Creating directories for test results..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./test-results" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/html" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/junit" | Out-Null
New-Item -ItemType Directory -Force -Path "./coverage" | Out-Null

# Display welcome message
Write-ColorOutput "NovaFuse Universal Platform - NovaTrack Tests with HTML Reports" -ForegroundColor Cyan
Write-ColorOutput "==========================================================" -ForegroundColor Cyan
Write-ColorOutput "This script will run NovaTrack tests and generate HTML reports." -ForegroundColor Cyan
Write-ColorOutput "Coverage threshold is set to 81% for branches, functions, lines, and statements." -ForegroundColor Cyan
Write-ColorOutput "" -ForegroundColor Cyan

# Run the simple test
Write-ColorOutput "Running simple NovaTrack tests..." -ForegroundColor Green
npx jest tests/unit/novatrack/tracking-manager.simple.test.js --verbose

# Run the comprehensive tests
Write-ColorOutput "`nRunning comprehensive NovaTrack tests..." -ForegroundColor Green
npx jest tests/unit/novatrack --verbose

# Run the tests with coverage
Write-ColorOutput "`nRunning NovaTrack tests with coverage..." -ForegroundColor Green
npx jest tests/unit/novatrack --coverage --coverageThreshold='{"global":{"branches":81,"functions":81,"lines":81,"statements":81}}'

# Run the integration tests
Write-ColorOutput "`nRunning NovaTrack integration tests..." -ForegroundColor Green
npx jest tests/integration/novatrack --verbose

# Display summary
Write-ColorOutput "`nTesting completed!" -ForegroundColor Green
Write-ColorOutput "Test results are available in the console output above." -ForegroundColor Green
Write-ColorOutput "HTML reports are available in ./test-results/html" -ForegroundColor Green
Write-ColorOutput "JUnit reports are available in ./test-results/junit" -ForegroundColor Green
Write-ColorOutput "Coverage report is available in ./coverage/lcov-report/index.html" -ForegroundColor Green

# Open the HTML report
Write-ColorOutput "`nOpening HTML report..." -ForegroundColor Green
$latestReport = Get-ChildItem -Path "./test-results/html" -Filter "*.html" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
if ($latestReport) {
    Start-Process $latestReport.FullName
} else {
    Write-ColorOutput "No HTML report found." -ForegroundColor Red
}

# Open the coverage report
Write-ColorOutput "`nOpening coverage report..." -ForegroundColor Green
Start-Process "./coverage/lcov-report/index.html"
