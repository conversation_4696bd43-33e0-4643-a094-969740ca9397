# Script to assign figure numbers and move diagrams to final location

$sourceDir = "D:\novafuse-api-superstore\Comphyology Diagrams\Mermaid"
$targetDir = "D:\novafuse-api-superstore\coherence-reality-systems\comphyology-patent-diagrams"

# Create target directory if it doesn't exist
if (-not (Test-Path -Path $targetDir)) {
    New-Item -ItemType Directory -Path $targetDir | Out-Null
}

# Mapping of current filenames to figure numbers
$figureMap = @{
    "uuft_core_architecture.mmd" = "FIG1"
    "12_plus_1_novas.mmd" = "FIG2"
    "alignment_architecture.mmd" = "FIG3"
    "application_data_layer.mmd" = "FIG4"
    "cadence_governance_loop.mmd" = "FIG5"
    "consciousness_threshold.mmd" = "FIG6"
    "cross_module_data_processing_pipeline.mmd" = "FIG7"
    "dark_field_classification.mmd" = "FIG8"
    "diagrams-and-figures.mmd" = "FIG9"
    "efficiency_formula.mmd" = "FIG10"
    "entropy_coherence_system.mmd" = "FIG11"
    "finite_universe_paradigm_visualization.mmd" = "FIG12"
    "finite_universe_principle.mmd" = "FIG13"
    "healthcare_implementation.mmd" = "FIG14"
    "nepi_analysis_pipeline.mmd" = "FIG15"
    "nova_align_studio.mmd" = "FIG16"
    "nova_components.mmd" = "FIG17"
    "nova_fuse_universal_stack.mmd" = "FIG18"
    "principle_18_82.mmd" = "FIG19"
    "protein_folding.mmd" = "FIG20"
    "quantum_decoherence_elimination.mmd" = "FIG21"
    "tee_equation.mmd" = "FIG22"
    "three_body_problem_reframing.mmd" = "FIG23"
    "zero_entropy_law.mmd" = "FIG24"
    "ai_alignment_case.mmd" = "FIG25"
}

# Process each file
$mappingTable = @()

foreach ($file in Get-ChildItem -Path $sourceDir -Filter "*.mmd" -File) {
    $originalName = $file.Name
    
    if ($figureMap.ContainsKey($originalName)) {
        $figureNumber = $figureMap[$originalName]
        $newName = "${figureNumber}_$originalName"
        $targetPath = Join-Path -Path $targetDir -ChildPath $newName
        
        # Copy the file
        Copy-Item -Path $file.FullName -Destination $targetPath -Force
        
        # Add to mapping table
        $mappingTable += [PSCustomObject]@{
            Figure = $figureNumber
            OriginalName = $originalName
            NewName = $newName
        }
        
        Write-Host "Copied $originalName -> $newName" -ForegroundColor Green
    } else {
        Write-Warning "No figure number assigned for: $originalName"
    }
}

# Create README.md with mapping
$readmePath = Join-Path -Path $targetDir -ChildPath "README.md"
$readmeContent = @"
# Comphyology Patent Diagrams

This directory contains the final set of diagrams for the Comphyology patent application.

## Figure Index

| Figure | Filename | Description |
|--------|----------|-------------|
"@

# Add table rows for each figure
foreach ($item in $mappingTable | Sort-Object Figure) {
    $description = $item.OriginalName -replace '\.mmd$','' -replace '_',' ' -replace '(\p{Ll})(\p{Lu})','$1 $2'
    $readmeContent += "| $($item.Figure) | $($item.NewName) | $description |`n"
}

# Add footer
$readmeContent += @"

## Notes
- All diagrams are in Mermaid (.mmd) format
- Figure numbers follow the patent application numbering
- Original filenames are preserved with figure number prefixes
"@

# Write README
Set-Content -Path $readmePath -Value $readmeContent

Write-Host "`nCreated README.md in $targetDir" -ForegroundColor Cyan
Write-Host "Processed $($mappingTable.Count) diagram files" -ForegroundColor Green
