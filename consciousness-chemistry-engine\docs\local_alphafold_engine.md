# LocalAlphaFoldEngine

A high-performance, GPU-optimized wrapper for running AlphaFold locally with consciousness-aware optimizations.

## Features

- **GPU Memory Optimization**: Automatic memory management for different GPU configurations
- **Consciousness-Aware Folding**: Ψ-score optimization for biologically relevant structures
- **Fibonacci Constraints**: Ensure structural stability using golden ratio constraints
- **Caching System**: Avoid redundant computations with intelligent caching
- **Real-time Monitoring**: Track GPU usage and prediction progress

## Installation

1. Ensure you have AlphaFold installed locally
2. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Set the `ALPHAFOLD_PATH` environment variable to your AlphaFold installation directory

## Usage

### Basic Usage

```python
from src.folding_engines import LocalAlphaFoldEngine

# Initialize with default settings
engine = LocalAlphaFoldEngine(
    alphafold_path="/path/to/alphafold",
    output_dir="./output"
)

# Run prediction
result = engine.predict("ACDEFGHIKLMNPQRSTVWY")
print(f"Predicted structure saved to: {result['pdb_path']}")
```

### Advanced Configuration

```python
engine = LocalAlphaFoldEngine(
    config={
        'mode': 'therapeutic',  # Standard, therapeutic, or speed
        'alphafold_path': "/path/to/alphafold",
        'output_dir': "./output",
        'gpu_id': 0,
        'psi_optimization': True,  # Enable consciousness optimization
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        },
        'model_preset': 'monomer_ptm',
        'db_preset': 'full_dbs'
    }
)
```

### Using Presets

```python
# Using a preset configuration
from src.folding_engines import create_engine

# Create engine with therapeutic preset
engine = create_engine(
    'local_alphafold',
    mode='therapeutic',
    alphafold_path="/path/to/alphafold",
    output_dir="./output"
)
```

## Configuration Options

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `mode` | str | 'standard' | Operation mode: 'standard', 'therapeutic', or 'speed' |
| `alphafold_path` | str | Required | Path to local AlphaFold installation |
| `output_dir` | str | './output' | Directory to save output files |
| `gpu_id` | int | 0 | ID of the GPU to use |
| `model_preset` | str | 'monomer' | AlphaFold model preset |
| `db_preset` | str | 'reduced_dbs' | Database preset ('reduced_dbs' or 'full_dbs') |
| `psi_optimization` | bool | False | Enable consciousness optimization |
| `fib_constraints` | dict | {'enabled': False} | Fibonacci constraint settings |
| `use_precomputed_msas` | bool | False | Use precomputed MSAs |
| `num_predictions` | int | 1 | Number of predictions to generate |

## Example Script

Run the example script to test different configurations:

```bash
python examples/run_local_alphafold.py \
  --alphafold-path /path/to/alphafold \
  --output-dir ./output \
  --gpu-id 0 \
  --sequence "ACDEFGHIKLMNPQRSTVWY"
```

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Try reducing the batch size or using a smaller model
2. **Missing Dependencies**: Ensure all AlphaFold dependencies are installed
3. **Incorrect Paths**: Verify `ALPHAFOLD_PATH` and other paths are correct

### Logging

Set the log level to `DEBUG` for detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
