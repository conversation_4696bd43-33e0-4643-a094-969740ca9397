// api.test.js
// Basic tests for NovaAscend API endpoints

// Polyfill for TextEncoder/TextDecoder in Node.js test environment
if (typeof TextEncoder === 'undefined') {
  global.TextEncoder = require('util').TextEncoder;
}
if (typeof TextDecoder === 'undefined') {
  global.TextDecoder = require('util').TextDecoder;
}

const request = require('supertest');
const app = require('./throne-api');

describe('NovaAscend API', () => {
  it('should align NovaCortex via /decree', async () => {
    const res = await request(app)
      .post('/decree')
      .send({ command: 'align', target: 'test-alignment' });
    expect(res.statusCode).toBe(200);
    expect(res.body.status).toBe('aligned');
    expect(res.body.state.alignment).toBe('test-alignment');
  });

  it('should get vision from NovaCortex via /vision', async () => {
    const res = await request(app).get('/vision');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('qScore');
    expect(res.body).toHaveProperty('alignment');
  });

  it('should update policy via /firewall', async () => {
    const res = await request(app)
      .put('/firewall')
      .send({ newLaw: 'Test policy' });
    expect(res.statusCode).toBe(200);
    expect(res.body.status).toBe('policy updated');
    expect(res.body.policies).toContain('Test policy');
  });

  it('should check system coherence via /coherence/check', async () => {
    const res = await request(app)
      .post('/coherence/check')
      .send({ coherenceScore: 0 });
    expect(res.statusCode).toBe(200);
    expect(res.body.coherence).toBe(true);
  });

  it('should scale NovaLift via /lift/scale', async () => {
    const res = await request(app)
      .post('/lift/scale')
      .send({ level: 5 });
    expect(res.statusCode).toBe(200);
    expect(res.body.status).toBe('scaling');
    expect(res.body.lastAction).toMatch(/Scaled to 5/);
  });

  it('should get NovaLift status via /lift/status', async () => {
    const res = await request(app).get('/lift/status');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('status');
    expect(res.body).toHaveProperty('lastAction');
  });

  it('should get NovaCaia status via /caia/status', async () => {
    const res = await request(app).get('/caia/status');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('status');
    expect(res.body).toHaveProperty('policies');
    expect(Array.isArray(res.body.policies)).toBe(true);
  });

  it('should evaluate performance vs. compliance via /performance/evaluate', async () => {
    const res = await request(app)
      .post('/performance/evaluate')
      .send({ performance: 10, compliance: 5 });
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('meetsStandard');
    expect(res.body.meetsStandard).toBe(true);
  });

  it('should normalize telemetry data via /telemetry/normalize', async () => {
    const res = await request(app)
      .post('/telemetry/normalize')
      .send({ data: { foo: 'bar' } });
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('normalized');
    expect(res.body.normalized).toEqual({ foo: 'bar' });
  });

  it('should return 400 for invalid /decree request', async () => {
    const res = await request(app)
      .post('/decree')
      .send({ command: 'align' }); // missing target
    expect(res.statusCode).toBe(400);
    expect(res.body).toHaveProperty('error');
  });
});
