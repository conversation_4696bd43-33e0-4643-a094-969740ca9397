{"version": 3, "names": ["FeatureFlagService", "require", "ValidationError", "NotFoundError", "AuthorizationError", "featureFlagService", "getAllPackages", "req", "res", "next", "packages", "json", "error", "getPackageById", "id", "params", "pkg", "message", "includes", "status", "createPackage", "packageData", "body", "name", "tier", "features", "Array", "isArray", "limits", "newPackage", "updatePackage", "updatedPackage", "deletePackage", "end", "getTenantPackage", "tenantId", "setTenantPackage", "packageId", "customFeatures", "customLimits", "mapping", "clearCache", "module", "exports"], "sources": ["PackageController.js"], "sourcesContent": ["/**\n * Package Controller\n * \n * This controller handles package management operations.\n */\n\nconst FeatureFlagService = require('../services/FeatureFlagService');\nconst { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');\n\n// Initialize services\nconst featureFlagService = new FeatureFlagService();\n\n/**\n * Get all packages\n */\nconst getAllPackages = async (req, res, next) => {\n  try {\n    const packages = await featureFlagService.getAllPackages();\n    res.json(packages);\n  } catch (error) {\n    next(error);\n  }\n};\n\n/**\n * Get package by ID\n */\nconst getPackageById = async (req, res, next) => {\n  try {\n    const { id } = req.params;\n    const pkg = await featureFlagService.getPackageById(id);\n    res.json(pkg);\n  } catch (error) {\n    if (error.message.includes('not found')) {\n      return res.status(404).json({\n        error: 'Not Found',\n        message: error.message\n      });\n    }\n    next(error);\n  }\n};\n\n/**\n * Create a new package\n */\nconst createPackage = async (req, res, next) => {\n  try {\n    const packageData = req.body;\n    \n    // Validate required fields\n    if (!packageData.id) {\n      throw new ValidationError('Package ID is required');\n    }\n    \n    if (!packageData.name) {\n      throw new ValidationError('Package name is required');\n    }\n    \n    if (!packageData.tier) {\n      throw new ValidationError('Package tier is required');\n    }\n    \n    if (!packageData.features || !Array.isArray(packageData.features)) {\n      throw new ValidationError('Package features must be an array');\n    }\n    \n    if (!packageData.limits || typeof packageData.limits !== 'object') {\n      throw new ValidationError('Package limits must be an object');\n    }\n    \n    const newPackage = await featureFlagService.createPackage(packageData);\n    res.status(201).json(newPackage);\n  } catch (error) {\n    if (error instanceof ValidationError) {\n      return res.status(400).json({\n        error: 'Validation Error',\n        message: error.message\n      });\n    }\n    next(error);\n  }\n};\n\n/**\n * Update a package\n */\nconst updatePackage = async (req, res, next) => {\n  try {\n    const { id } = req.params;\n    const packageData = req.body;\n    \n    const updatedPackage = await featureFlagService.updatePackage(id, packageData);\n    res.json(updatedPackage);\n  } catch (error) {\n    if (error.message.includes('not found')) {\n      return res.status(404).json({\n        error: 'Not Found',\n        message: error.message\n      });\n    }\n    next(error);\n  }\n};\n\n/**\n * Delete a package\n */\nconst deletePackage = async (req, res, next) => {\n  try {\n    const { id } = req.params;\n    await featureFlagService.deletePackage(id);\n    res.status(204).end();\n  } catch (error) {\n    if (error.message.includes('not found')) {\n      return res.status(404).json({\n        error: 'Not Found',\n        message: error.message\n      });\n    }\n    next(error);\n  }\n};\n\n/**\n * Get tenant package\n */\nconst getTenantPackage = async (req, res, next) => {\n  try {\n    const { tenantId } = req.params;\n    const pkg = await featureFlagService.getTenantPackage(tenantId);\n    res.json(pkg);\n  } catch (error) {\n    next(error);\n  }\n};\n\n/**\n * Set tenant package\n */\nconst setTenantPackage = async (req, res, next) => {\n  try {\n    const { tenantId } = req.params;\n    const { packageId, customFeatures, customLimits } = req.body;\n    \n    // Validate required fields\n    if (!packageId) {\n      throw new ValidationError('Package ID is required');\n    }\n    \n    const mapping = await featureFlagService.setTenantPackage(\n      tenantId,\n      packageId,\n      customFeatures || [],\n      customLimits || {}\n    );\n    \n    res.json(mapping);\n  } catch (error) {\n    if (error instanceof ValidationError) {\n      return res.status(400).json({\n        error: 'Validation Error',\n        message: error.message\n      });\n    }\n    next(error);\n  }\n};\n\n/**\n * Clear cache\n */\nconst clearCache = async (req, res, next) => {\n  try {\n    featureFlagService.clearCache();\n    res.json({ message: 'Cache cleared successfully' });\n  } catch (error) {\n    next(error);\n  }\n};\n\nmodule.exports = {\n  getAllPackages,\n  getPackageById,\n  createPackage,\n  updatePackage,\n  deletePackage,\n  getTenantPackage,\n  setTenantPackage,\n  clearCache\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,kBAAkB,GAAGC,OAAO,CAAC,gCAAgC,CAAC;AACpE,MAAM;EAAEC,eAAe;EAAEC,aAAa;EAAEC;AAAmB,CAAC,GAAGH,OAAO,CAAC,iBAAiB,CAAC;;AAEzF;AACA,MAAMI,kBAAkB,GAAG,IAAIL,kBAAkB,CAAC,CAAC;;AAEnD;AACA;AACA;AACA,MAAMM,cAAc,GAAG,MAAAA,CAAOC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC/C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAML,kBAAkB,CAACC,cAAc,CAAC,CAAC;IAC1DE,GAAG,CAACG,IAAI,CAACD,QAAQ,CAAC;EACpB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdH,IAAI,CAACG,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMC,cAAc,GAAG,MAAAA,CAAON,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC/C,IAAI;IACF,MAAM;MAAEK;IAAG,CAAC,GAAGP,GAAG,CAACQ,MAAM;IACzB,MAAMC,GAAG,GAAG,MAAMX,kBAAkB,CAACQ,cAAc,CAACC,EAAE,CAAC;IACvDN,GAAG,CAACG,IAAI,CAACK,GAAG,CAAC;EACf,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACd,IAAIA,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MACvC,OAAOV,GAAG,CAACW,MAAM,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC;QAC1BC,KAAK,EAAE,WAAW;QAClBK,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC,CAAC;IACJ;IACAR,IAAI,CAACG,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMQ,aAAa,GAAG,MAAAA,CAAOb,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC9C,IAAI;IACF,MAAMY,WAAW,GAAGd,GAAG,CAACe,IAAI;;IAE5B;IACA,IAAI,CAACD,WAAW,CAACP,EAAE,EAAE;MACnB,MAAM,IAAIZ,eAAe,CAAC,wBAAwB,CAAC;IACrD;IAEA,IAAI,CAACmB,WAAW,CAACE,IAAI,EAAE;MACrB,MAAM,IAAIrB,eAAe,CAAC,0BAA0B,CAAC;IACvD;IAEA,IAAI,CAACmB,WAAW,CAACG,IAAI,EAAE;MACrB,MAAM,IAAItB,eAAe,CAAC,0BAA0B,CAAC;IACvD;IAEA,IAAI,CAACmB,WAAW,CAACI,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACN,WAAW,CAACI,QAAQ,CAAC,EAAE;MACjE,MAAM,IAAIvB,eAAe,CAAC,mCAAmC,CAAC;IAChE;IAEA,IAAI,CAACmB,WAAW,CAACO,MAAM,IAAI,OAAOP,WAAW,CAACO,MAAM,KAAK,QAAQ,EAAE;MACjE,MAAM,IAAI1B,eAAe,CAAC,kCAAkC,CAAC;IAC/D;IAEA,MAAM2B,UAAU,GAAG,MAAMxB,kBAAkB,CAACe,aAAa,CAACC,WAAW,CAAC;IACtEb,GAAG,CAACW,MAAM,CAAC,GAAG,CAAC,CAACR,IAAI,CAACkB,UAAU,CAAC;EAClC,CAAC,CAAC,OAAOjB,KAAK,EAAE;IACd,IAAIA,KAAK,YAAYV,eAAe,EAAE;MACpC,OAAOM,GAAG,CAACW,MAAM,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC;QAC1BC,KAAK,EAAE,kBAAkB;QACzBK,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC,CAAC;IACJ;IACAR,IAAI,CAACG,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMkB,aAAa,GAAG,MAAAA,CAAOvB,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC9C,IAAI;IACF,MAAM;MAAEK;IAAG,CAAC,GAAGP,GAAG,CAACQ,MAAM;IACzB,MAAMM,WAAW,GAAGd,GAAG,CAACe,IAAI;IAE5B,MAAMS,cAAc,GAAG,MAAM1B,kBAAkB,CAACyB,aAAa,CAAChB,EAAE,EAAEO,WAAW,CAAC;IAC9Eb,GAAG,CAACG,IAAI,CAACoB,cAAc,CAAC;EAC1B,CAAC,CAAC,OAAOnB,KAAK,EAAE;IACd,IAAIA,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MACvC,OAAOV,GAAG,CAACW,MAAM,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC;QAC1BC,KAAK,EAAE,WAAW;QAClBK,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC,CAAC;IACJ;IACAR,IAAI,CAACG,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMoB,aAAa,GAAG,MAAAA,CAAOzB,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC9C,IAAI;IACF,MAAM;MAAEK;IAAG,CAAC,GAAGP,GAAG,CAACQ,MAAM;IACzB,MAAMV,kBAAkB,CAAC2B,aAAa,CAAClB,EAAE,CAAC;IAC1CN,GAAG,CAACW,MAAM,CAAC,GAAG,CAAC,CAACc,GAAG,CAAC,CAAC;EACvB,CAAC,CAAC,OAAOrB,KAAK,EAAE;IACd,IAAIA,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MACvC,OAAOV,GAAG,CAACW,MAAM,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC;QAC1BC,KAAK,EAAE,WAAW;QAClBK,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC,CAAC;IACJ;IACAR,IAAI,CAACG,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMsB,gBAAgB,GAAG,MAAAA,CAAO3B,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EACjD,IAAI;IACF,MAAM;MAAE0B;IAAS,CAAC,GAAG5B,GAAG,CAACQ,MAAM;IAC/B,MAAMC,GAAG,GAAG,MAAMX,kBAAkB,CAAC6B,gBAAgB,CAACC,QAAQ,CAAC;IAC/D3B,GAAG,CAACG,IAAI,CAACK,GAAG,CAAC;EACf,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdH,IAAI,CAACG,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMwB,gBAAgB,GAAG,MAAAA,CAAO7B,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EACjD,IAAI;IACF,MAAM;MAAE0B;IAAS,CAAC,GAAG5B,GAAG,CAACQ,MAAM;IAC/B,MAAM;MAAEsB,SAAS;MAAEC,cAAc;MAAEC;IAAa,CAAC,GAAGhC,GAAG,CAACe,IAAI;;IAE5D;IACA,IAAI,CAACe,SAAS,EAAE;MACd,MAAM,IAAInC,eAAe,CAAC,wBAAwB,CAAC;IACrD;IAEA,MAAMsC,OAAO,GAAG,MAAMnC,kBAAkB,CAAC+B,gBAAgB,CACvDD,QAAQ,EACRE,SAAS,EACTC,cAAc,IAAI,EAAE,EACpBC,YAAY,IAAI,CAAC,CACnB,CAAC;IAED/B,GAAG,CAACG,IAAI,CAAC6B,OAAO,CAAC;EACnB,CAAC,CAAC,OAAO5B,KAAK,EAAE;IACd,IAAIA,KAAK,YAAYV,eAAe,EAAE;MACpC,OAAOM,GAAG,CAACW,MAAM,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC;QAC1BC,KAAK,EAAE,kBAAkB;QACzBK,OAAO,EAAEL,KAAK,CAACK;MACjB,CAAC,CAAC;IACJ;IACAR,IAAI,CAACG,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAM6B,UAAU,GAAG,MAAAA,CAAOlC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC3C,IAAI;IACFJ,kBAAkB,CAACoC,UAAU,CAAC,CAAC;IAC/BjC,GAAG,CAACG,IAAI,CAAC;MAAEM,OAAO,EAAE;IAA6B,CAAC,CAAC;EACrD,CAAC,CAAC,OAAOL,KAAK,EAAE;IACdH,IAAI,CAACG,KAAK,CAAC;EACb;AACF,CAAC;AAED8B,MAAM,CAACC,OAAO,GAAG;EACfrC,cAAc;EACdO,cAAc;EACdO,aAAa;EACbU,aAAa;EACbE,aAAa;EACbE,gBAAgB;EAChBE,gBAAgB;EAChBK;AACF,CAAC", "ignoreList": []}