/**
 * Simple Role Inheritance Test
 * 
 * This script tests the role inheritance functionality with a simplified approach.
 */

console.log('Starting role inheritance test...');

// Mock the Role model
class Role {
  constructor(data) {
    this._id = Math.random().toString(36).substring(7);
    this.name = data.name;
    this.description = data.description || '';
    this.permissions = data.permissions || [];
    this.inheritsFrom = data.inheritsFrom || [];
  }
  
  hasPermission(permission) {
    // Check direct permissions
    if (this.permissions.includes('*') || this.permissions.includes(permission)) {
      return true;
    }
    
    // Check resource wildcard
    if (permission.includes(':')) {
      const [resource] = permission.split(':');
      const resourceWildcard = `${resource}:*`;
      
      if (this.permissions.includes(resourceWildcard)) {
        return true;
      }
    }
    
    return false;
  }
}

// Create test permissions
const viewPermission = 'resource:view';
const editPermission = 'resource:edit';
const deletePermission = 'resource:delete';

console.log('Created test permissions:');
console.log(`- ${viewPermission}`);
console.log(`- ${editPermission}`);
console.log(`- ${deletePermission}`);

// Create roles without inheritance
const viewerRole = new Role({
  name: 'Viewer',
  description: 'Can view resources',
  permissions: [viewPermission]
});

const editorRole = new Role({
  name: 'Editor',
  description: 'Can view and edit resources',
  permissions: [viewPermission, editPermission]
});

const adminRole = new Role({
  name: 'Admin',
  description: 'Can view, edit, and delete resources',
  permissions: [viewPermission, editPermission, deletePermission]
});

console.log('\nCreated roles without inheritance:');
console.log(`- ${viewerRole.name}: ${viewerRole.permissions.join(', ')}`);
console.log(`- ${editorRole.name}: ${editorRole.permissions.join(', ')}`);
console.log(`- ${adminRole.name}: ${adminRole.permissions.join(', ')}`);

// Test permissions without inheritance
console.log('\nTesting permissions without inheritance:');
console.log(`Viewer has view permission: ${viewerRole.hasPermission(viewPermission)}`);
console.log(`Viewer has edit permission: ${viewerRole.hasPermission(editPermission)}`);
console.log(`Viewer has delete permission: ${viewerRole.hasPermission(deletePermission)}`);

console.log(`Editor has view permission: ${editorRole.hasPermission(viewPermission)}`);
console.log(`Editor has edit permission: ${editorRole.hasPermission(editPermission)}`);
console.log(`Editor has delete permission: ${editorRole.hasPermission(deletePermission)}`);

console.log(`Admin has view permission: ${adminRole.hasPermission(viewPermission)}`);
console.log(`Admin has edit permission: ${adminRole.hasPermission(editPermission)}`);
console.log(`Admin has delete permission: ${adminRole.hasPermission(deletePermission)}`);

// Now let's implement inheritance
class RoleWithInheritance extends Role {
  constructor(data) {
    super(data);
    this.inheritsFrom = data.inheritsFrom || [];
  }
  
  hasPermission(permission) {
    // Check direct permissions
    if (super.hasPermission(permission)) {
      return true;
    }
    
    // Check inherited permissions
    for (const parentRole of this.inheritsFrom) {
      if (parentRole.hasPermission(permission)) {
        return true;
      }
    }
    
    return false;
  }
}

// Create roles with inheritance
const viewerRoleWithInheritance = new RoleWithInheritance({
  name: 'Viewer',
  description: 'Can view resources',
  permissions: [viewPermission]
});

const editorRoleWithInheritance = new RoleWithInheritance({
  name: 'Editor',
  description: 'Can edit resources',
  permissions: [editPermission],
  inheritsFrom: [viewerRoleWithInheritance]
});

const adminRoleWithInheritance = new RoleWithInheritance({
  name: 'Admin',
  description: 'Can delete resources',
  permissions: [deletePermission],
  inheritsFrom: [editorRoleWithInheritance]
});

console.log('\nCreated roles with inheritance:');
console.log(`- ${viewerRoleWithInheritance.name}: ${viewerRoleWithInheritance.permissions.join(', ')}`);
console.log(`- ${editorRoleWithInheritance.name}: ${editorRoleWithInheritance.permissions.join(', ')} (inherits from ${viewerRoleWithInheritance.name})`);
console.log(`- ${adminRoleWithInheritance.name}: ${adminRoleWithInheritance.permissions.join(', ')} (inherits from ${editorRoleWithInheritance.name})`);

// Test permissions with inheritance
console.log('\nTesting permissions with inheritance:');
console.log(`Viewer has view permission: ${viewerRoleWithInheritance.hasPermission(viewPermission)}`);
console.log(`Viewer has edit permission: ${viewerRoleWithInheritance.hasPermission(editPermission)}`);
console.log(`Viewer has delete permission: ${viewerRoleWithInheritance.hasPermission(deletePermission)}`);

console.log(`Editor has view permission: ${editorRoleWithInheritance.hasPermission(viewPermission)}`);
console.log(`Editor has edit permission: ${editorRoleWithInheritance.hasPermission(editPermission)}`);
console.log(`Editor has delete permission: ${editorRoleWithInheritance.hasPermission(deletePermission)}`);

console.log(`Admin has view permission: ${adminRoleWithInheritance.hasPermission(viewPermission)}`);
console.log(`Admin has edit permission: ${adminRoleWithInheritance.hasPermission(editPermission)}`);
console.log(`Admin has delete permission: ${adminRoleWithInheritance.hasPermission(deletePermission)}`);

// Test circular inheritance detection
console.log('\nTesting circular inheritance detection:');

function hasCircularInheritance(role, visited = []) {
  if (visited.includes(role)) {
    return true;
  }
  
  for (const parentRole of role.inheritsFrom) {
    if (hasCircularInheritance(parentRole, [...visited, role])) {
      return true;
    }
  }
  
  return false;
}

// Create a circular inheritance
const roleA = new RoleWithInheritance({ name: 'Role A' });
const roleB = new RoleWithInheritance({ name: 'Role B', inheritsFrom: [roleA] });
const roleC = new RoleWithInheritance({ name: 'Role C', inheritsFrom: [roleB] });

// Create circular reference
roleA.inheritsFrom = [roleC];

console.log(`Role A inherits from Role C`);
console.log(`Role B inherits from Role A`);
console.log(`Role C inherits from Role B`);

console.log(`Circular inheritance detected: ${hasCircularInheritance(roleA)}`);

console.log('\nRole inheritance test completed successfully');
