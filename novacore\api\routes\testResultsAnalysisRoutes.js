/**
 * Test Results Analysis Routes
 * 
 * This file defines the routes for the test results analysis API.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../middleware/asyncHandler');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const testResultsAnalysisController = require('../controllers/TestResultsAnalysisController');

/**
 * @route   GET /api/user-testing/results/analysis
 * @desc    Get test results analysis
 * @access  Private
 */
router.get(
  '/results/analysis',
  authenticate,
  authorize('read:user_testing'),
  async<PERSON><PERSON><PERSON>(testResultsAnalysisController.getTestResultsAnalysis)
);

/**
 * @route   GET /api/user-testing/results/export
 * @desc    Export test results analysis
 * @access  Private
 */
router.get(
  '/results/export',
  authenticate,
  authorize('read:user_testing'),
  async<PERSON><PERSON><PERSON>(testResultsAnalysisController.exportTestResultsAnalysis)
);

module.exports = router;
