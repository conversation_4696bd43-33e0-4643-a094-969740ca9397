node_modules
npm-debug.log
Dockerfile*
.dockerignore
.git
.gitignore
# Large directories to exclude
.next
.nyc_output
.pytest_cache
.venv
.qodo
__pycache__
coverage
reports
node_modules

# Exclude large result directories but keep tests for NovaCortex
*_results
*_output
cmb_results
gini_results*
lbl_tcp_results
proteomics_results
proteomexchange_results
wealth_results
uuft_*_results
comphyology_*_results
adaptive_*_results
trinity_*_results
feedback_results
monitoring_results
novastore_results
resonance_results
test_results
csde_results
uuft_demo_results
uuft_real_world_results

# Large data files
*.csv
*.tar
*.zip
*.pdf
*.png
*.jpg
*.jpeg
*.gif
*.svg

# Documentation but keep essential configs
*.md
*.log
.env

# Keep tests for NovaCortex but not all test outputs
# tests
