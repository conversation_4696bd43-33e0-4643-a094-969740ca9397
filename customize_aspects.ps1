# Script to customize the "3 Critical Aspects" section for each term in the dictionary

$dictionaryPath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = $dictionaryPath -replace '\.md$', '_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').md'

# Create a backup of the original file
Copy-Item -Path $dictionaryPath -Destination $backupPath

# Read the content
$content = Get-Content -Path $dictionaryPath -Raw

# This is a placeholder for the actual customization logic
# The actual implementation would need to analyze each term and generate appropriate aspects
# For now, we'll just show how we would identify terms with the generic template

# Find all terms with the generic template
$terms = [regex]::Matches($content, '(?s)(\*\*[^*]+?\*\*.*?)(?=\n\*\*|$)')

Write-Host "Found $($terms.Count) terms in the dictionary"

# Example of how we would modify a term (this is just a template - actual implementation would be more sophisticated)
$modifiedContent = $content -replace '(?s)(\*\*Adaptation Component \(e\)\*\*.*?)(?=\*\*|$)', 
{
    param($match)
    $term = $match.Groups[1].Value
    
    # This is where we would generate custom aspects based on the term
    # For now, we'll just show an example for the Adaptation Component
    if ($term -match 'Adaptation Component') {
        @"
**Adaptation Component (e)**
🔑 Consciousness in Comphyology has 3 Critical Aspects:

| Dimension | Description |
|-----------|-------------|
| Evolutionary Capacity | The system's ability to adapt and evolve over time |
| Response Mechanism | How the system responds to environmental changes |
| Integration Potential | The ability to incorporate new patterns and behaviors |

"@
    } else {
        # For other terms, we would generate appropriate aspects
        $term
    }
}

# Write the modified content back to the file
# [System.IO.File]::WriteAllText($dictionaryPath, $modifiedContent)

Write-Host "Created backup at: $backupPath"
Write-Host "Note: The actual customization would require specific logic for each term."
Write-Host "Please review the script and uncomment the write operation when ready."
