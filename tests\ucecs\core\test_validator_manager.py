"""
Unit tests for the Validator Manager.

This module contains unit tests for the Validator Manager, including tests for
validation chains, custom validation scripts, and validation result scoring.
"""

import os
import json
import tempfile
import unittest
from unittest.mock import patch, MagicMock

from src.ucecs.core.validator_manager import (
    ValidatorManager,
    ValidationMode,
    ValidationLevel,
    ValidationResult,
    ValidationChain
)

class TestValidatorManager(unittest.TestCase):
    """Test cases for the Validator Manager."""
    
    def setUp(self):
        """Set up the test environment."""
        # Create temporary directories for the test
        self.temp_dir = tempfile.mkdtemp()
        self.chains_dir = os.path.join(self.temp_dir, 'validation_chains')
        self.scripts_dir = os.path.join(self.temp_dir, 'validation_scripts')
        
        # Create the directories
        os.makedirs(self.chains_dir, exist_ok=True)
        os.makedirs(self.scripts_dir, exist_ok=True)
        
        # Create a Validator Manager
        self.validator_manager = ValidatorManager(
            chains_dir=self.chains_dir,
            scripts_dir=self.scripts_dir
        )
        
        # Create a sample evidence item
        self.evidence = {
            'id': '123456',
            'type': 'document',
            'source': 'user_upload',
            'data': {
                'title': 'Sample Document',
                'content': 'This is a sample document for testing the Validator Manager.'
            },
            'metadata': {
                'tags': ['sample', 'document', 'test']
            }
        }
    
    def tearDown(self):
        """Clean up the test environment."""
        # Remove the temporary directories
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_validation_chain_creation(self):
        """Test creating a validation chain."""
        # Create a validation chain
        chain = self.validator_manager.create_validation_chain(
            chain_id='test_chain',
            name='Test Chain',
            description='A test validation chain',
            validators=[
                {'id': 'file_exists', 'name': 'File Exists Validator'},
                {'id': 'file_content', 'name': 'File Content Validator'}
            ],
            mode=ValidationMode.SEQUENTIAL,
            required_score=80.0,
            required_level=ValidationLevel.HIGH
        )
        
        # Check the chain
        self.assertEqual(chain.chain_id, 'test_chain')
        self.assertEqual(chain.name, 'Test Chain')
        self.assertEqual(chain.description, 'A test validation chain')
        self.assertEqual(len(chain.validators), 2)
        self.assertEqual(chain.mode, ValidationMode.SEQUENTIAL)
        self.assertEqual(chain.required_score, 80.0)
        self.assertEqual(chain.required_level, ValidationLevel.HIGH)
        
        # Check that the chain was added to the manager
        self.assertIn('test_chain', self.validator_manager.chains)
        
        # Check that the chain was saved to disk
        chain_file = os.path.join(self.chains_dir, 'test_chain.json')
        self.assertTrue(os.path.exists(chain_file))
    
    def test_validation_chain_update(self):
        """Test updating a validation chain."""
        # Create a validation chain
        chain = self.validator_manager.create_validation_chain(
            chain_id='test_chain',
            name='Test Chain',
            description='A test validation chain',
            validators=[
                {'id': 'file_exists', 'name': 'File Exists Validator'}
            ],
            mode=ValidationMode.SEQUENTIAL,
            required_score=80.0,
            required_level=ValidationLevel.HIGH
        )
        
        # Update the chain
        updated_chain = self.validator_manager.update_validation_chain(
            chain_id='test_chain',
            name='Updated Test Chain',
            validators=[
                {'id': 'file_exists', 'name': 'File Exists Validator'},
                {'id': 'file_content', 'name': 'File Content Validator'}
            ],
            mode=ValidationMode.PARALLEL,
            required_score=90.0
        )
        
        # Check the updated chain
        self.assertEqual(updated_chain.name, 'Updated Test Chain')
        self.assertEqual(len(updated_chain.validators), 2)
        self.assertEqual(updated_chain.mode, ValidationMode.PARALLEL)
        self.assertEqual(updated_chain.required_score, 90.0)
        self.assertEqual(updated_chain.required_level, ValidationLevel.HIGH)  # Unchanged
    
    def test_validation_chain_deletion(self):
        """Test deleting a validation chain."""
        # Create a validation chain
        chain = self.validator_manager.create_validation_chain(
            chain_id='test_chain',
            name='Test Chain',
            description='A test validation chain',
            validators=[
                {'id': 'file_exists', 'name': 'File Exists Validator'}
            ]
        )
        
        # Delete the chain
        self.validator_manager.delete_validation_chain('test_chain')
        
        # Check that the chain was removed from the manager
        self.assertNotIn('test_chain', self.validator_manager.chains)
        
        # Check that the chain file was deleted
        chain_file = os.path.join(self.chains_dir, 'test_chain.json')
        self.assertFalse(os.path.exists(chain_file))
    
    def test_validation_script_creation(self):
        """Test creating a validation script."""
        # Create a validation script
        script_content = """
# Get the evidence data
data = evidence.get('data', {})
title = data.get('title', '')

# Check if the title is not empty
if title:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=True,
        confidence_level=ValidationLevel.HIGH,
        score=100,
        details={'title': title}
    )
else:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=False,
        confidence_level=ValidationLevel.HIGH,
        score=0,
        errors=['Title is empty']
    )
"""
        
        script = self.validator_manager.create_validation_script(
            script_id='title_validator',
            content=script_content,
            name='Title Validator',
            description='Validates that the document has a title',
            author='Test Author',
            version='1.0.0',
            confidence_level='HIGH',
            tags=['document', 'title', 'validation']
        )
        
        # Check the script
        self.assertEqual(script['id'], 'title_validator')
        self.assertEqual(script['metadata']['name'], 'Title Validator')
        self.assertEqual(script['metadata']['description'], 'Validates that the document has a title')
        self.assertEqual(script['metadata']['author'], 'Test Author')
        self.assertEqual(script['metadata']['version'], '1.0.0')
        self.assertEqual(script['metadata']['confidence_level'], 'HIGH')
        self.assertEqual(script['metadata']['tags'], ['document', 'title', 'validation'])
        
        # Check that the script was added to the manager
        self.assertIn('title_validator', self.validator_manager.scripts)
        
        # Check that the script was saved to disk
        script_file = os.path.join(self.scripts_dir, 'title_validator.py')
        self.assertTrue(os.path.exists(script_file))
    
    def test_validation_script_update(self):
        """Test updating a validation script."""
        # Create a validation script
        script_content = """
# Get the evidence data
data = evidence.get('data', {})
title = data.get('title', '')

# Check if the title is not empty
if title:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=True,
        confidence_level=ValidationLevel.HIGH,
        score=100,
        details={'title': title}
    )
else:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=False,
        confidence_level=ValidationLevel.HIGH,
        score=0,
        errors=['Title is empty']
    )
"""
        
        script = self.validator_manager.create_validation_script(
            script_id='title_validator',
            content=script_content,
            name='Title Validator',
            description='Validates that the document has a title',
            author='Test Author',
            version='1.0.0'
        )
        
        # Update the script
        updated_script_content = """
# Get the evidence data
data = evidence.get('data', {})
title = data.get('title', '')

# Check if the title is not empty and has a minimum length
if title and len(title) >= 5:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=True,
        confidence_level=ValidationLevel.HIGH,
        score=100,
        details={'title': title, 'length': len(title)}
    )
else:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=False,
        confidence_level=ValidationLevel.HIGH,
        score=0,
        errors=['Title is empty or too short']
    )
"""
        
        updated_script = self.validator_manager.update_validation_script(
            script_id='title_validator',
            content=updated_script_content,
            name='Updated Title Validator',
            version='1.1.0'
        )
        
        # Check the updated script
        self.assertEqual(updated_script['metadata']['name'], 'Updated Title Validator')
        self.assertEqual(updated_script['metadata']['version'], '1.1.0')
    
    def test_validation_script_deletion(self):
        """Test deleting a validation script."""
        # Create a validation script
        script_content = """
# Simple validation script
result = ValidationResult(
    validator_id='test_validator',
    is_valid=True,
    confidence_level=ValidationLevel.MEDIUM,
    score=100
)
"""
        
        script = self.validator_manager.create_validation_script(
            script_id='test_validator',
            content=script_content,
            name='Test Validator'
        )
        
        # Delete the script
        self.validator_manager.delete_validation_script('test_validator')
        
        # Check that the script was removed from the manager
        self.assertNotIn('test_validator', self.validator_manager.scripts)
        
        # Check that the script file was deleted
        script_file = os.path.join(self.scripts_dir, 'test_validator.py')
        self.assertFalse(os.path.exists(script_file))
    
    def test_validate_with_script(self):
        """Test validating evidence with a script."""
        # Create a validation script
        script_content = """
# Get the evidence data
data = evidence.get('data', {})
title = data.get('title', '')

# Check if the title is not empty
if title:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=True,
        confidence_level=ValidationLevel.HIGH,
        score=100,
        details={'title': title}
    )
else:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=False,
        confidence_level=ValidationLevel.HIGH,
        score=0,
        errors=['Title is empty']
    )
"""
        
        script = self.validator_manager.create_validation_script(
            script_id='title_validator',
            content=script_content,
            name='Title Validator'
        )
        
        # Validate evidence with the script
        result = self.validator_manager.validate('title_validator', self.evidence)
        
        # Check the result
        self.assertTrue(result.is_valid)
        self.assertEqual(result.confidence_level, ValidationLevel.HIGH)
        self.assertEqual(result.score, 100)
        self.assertEqual(result.details['title'], 'Sample Document')
        
        # Validate evidence with an empty title
        invalid_evidence = self.evidence.copy()
        invalid_evidence['data'] = {'title': ''}
        
        result = self.validator_manager.validate('title_validator', invalid_evidence)
        
        # Check the result
        self.assertFalse(result.is_valid)
        self.assertEqual(result.confidence_level, ValidationLevel.HIGH)
        self.assertEqual(result.score, 0)
        self.assertEqual(result.errors, ['Title is empty'])
    
    def test_validate_with_chain(self):
        """Test validating evidence with a chain."""
        # Create validation scripts
        title_script_content = """
# Get the evidence data
data = evidence.get('data', {})
title = data.get('title', '')

# Check if the title is not empty
if title:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=True,
        confidence_level=ValidationLevel.HIGH,
        score=100,
        details={'title': title}
    )
else:
    result = ValidationResult(
        validator_id='title_validator',
        is_valid=False,
        confidence_level=ValidationLevel.HIGH,
        score=0,
        errors=['Title is empty']
    )
"""
        
        content_script_content = """
# Get the evidence data
data = evidence.get('data', {})
content = data.get('content', '')

# Check if the content is not empty
if content:
    result = ValidationResult(
        validator_id='content_validator',
        is_valid=True,
        confidence_level=ValidationLevel.MEDIUM,
        score=100,
        details={'content_length': len(content)}
    )
else:
    result = ValidationResult(
        validator_id='content_validator',
        is_valid=False,
        confidence_level=ValidationLevel.MEDIUM,
        score=0,
        errors=['Content is empty']
    )
"""
        
        self.validator_manager.create_validation_script(
            script_id='title_validator',
            content=title_script_content,
            name='Title Validator'
        )
        
        self.validator_manager.create_validation_script(
            script_id='content_validator',
            content=content_script_content,
            name='Content Validator'
        )
        
        # Create a validation chain
        chain = self.validator_manager.create_validation_chain(
            chain_id='document_chain',
            name='Document Validation Chain',
            description='Validates document title and content',
            validators=[
                {'id': 'title_validator', 'name': 'Title Validator'},
                {'id': 'content_validator', 'name': 'Content Validator'}
            ],
            mode=ValidationMode.SEQUENTIAL,
            required_score=80.0,
            required_level=ValidationLevel.MEDIUM
        )
        
        # Validate evidence with the chain
        result = self.validator_manager.validate('document_chain', self.evidence)
        
        # Check the result
        self.assertTrue(result.is_valid)
        self.assertEqual(result.confidence_level, ValidationLevel.MEDIUM)
        self.assertEqual(result.score, 100.0)  # Average of 100 and 100
        self.assertEqual(len(result.details['validator_results']), 2)
        
        # Validate evidence with an empty title
        invalid_evidence = self.evidence.copy()
        invalid_evidence['data'] = {'title': '', 'content': 'Content'}
        
        result = self.validator_manager.validate('document_chain', invalid_evidence)
        
        # Check the result
        self.assertFalse(result.is_valid)
        self.assertEqual(result.score, 50.0)  # Average of 0 and 100
        self.assertEqual(len(result.errors), 1)
        self.assertEqual(result.errors[0], 'Title is empty')
    
    def test_validation_result_scoring(self):
        """Test validation result scoring."""
        # Create a validation script with scoring
        script_content = """
# Get the evidence data
data = evidence.get('data', {})
title = data.get('title', '')
content = data.get('content', '')

# Calculate score based on title and content
score = 0
errors = []
warnings = []

if title:
    score += 50
else:
    errors.append('Title is empty')

if content:
    if len(content) >= 20:
        score += 50
    else:
        score += 25
        warnings.append('Content is too short')
else:
    errors.append('Content is empty')

# Create the validation result
result = ValidationResult(
    validator_id='document_validator',
    is_valid=score >= 70,
    confidence_level=ValidationLevel.HIGH,
    score=score,
    details={
        'title': title,
        'content_length': len(content) if content else 0
    },
    errors=errors,
    warnings=warnings
)
"""
        
        script = self.validator_manager.create_validation_script(
            script_id='document_validator',
            content=script_content,
            name='Document Validator'
        )
        
        # Validate evidence with a good score
        result = self.validator_manager.validate('document_validator', self.evidence)
        
        # Check the result
        self.assertTrue(result.is_valid)
        self.assertEqual(result.score, 100)
        self.assertEqual(len(result.errors), 0)
        self.assertEqual(len(result.warnings), 0)
        
        # Validate evidence with a medium score
        medium_evidence = self.evidence.copy()
        medium_evidence['data'] = {'title': 'Title', 'content': 'Short'}
        
        result = self.validator_manager.validate('document_validator', medium_evidence)
        
        # Check the result
        self.assertTrue(result.is_valid)
        self.assertEqual(result.score, 75)
        self.assertEqual(len(result.errors), 0)
        self.assertEqual(len(result.warnings), 1)
        
        # Validate evidence with a low score
        low_evidence = self.evidence.copy()
        low_evidence['data'] = {'title': '', 'content': 'Short'}
        
        result = self.validator_manager.validate('document_validator', low_evidence)
        
        # Check the result
        self.assertFalse(result.is_valid)
        self.assertEqual(result.score, 25)
        self.assertEqual(len(result.errors), 1)
        self.assertEqual(len(result.warnings), 1)

if __name__ == '__main__':
    unittest.main()
