/**
 * ESG Reporting API - Routes
 * 
 * This file defines the routes for the ESG Reporting API.
 */

const express = require('express');
const router = express.Router();
const { reportTemplateController, reportController } = require('./controllers');
const validate = require('../../../middleware/validate');
const validationSchemas = require('./validation');
const auth = require('../../../middleware/auth');

/**
 * Report Template Routes
 */
// Get all report templates
router.get('/templates', auth, validate(validationSchemas.query.pagination, 'query'), reportTemplateController.getAllReportTemplates);

// Get report template by ID
router.get('/templates/:id', auth, reportTemplateController.getReportTemplateById);

// Create a new report template
router.post('/templates', auth, validate(validationSchemas.reportTemplate.create), reportTemplateController.createReportTemplate);

// Update a report template
router.put('/templates/:id', auth, validate(validationSchemas.reportTemplate.update), reportTemplateController.updateReportTemplate);

// Delete a report template
router.delete('/templates/:id', auth, reportTemplateController.deleteReportTemplate);

/**
 * Report Routes
 */
// Get all reports
router.get('/reports', auth, validate(validationSchemas.query.pagination, 'query'), reportController.getAllReports);

// Get report by ID
router.get('/reports/:id', auth, reportController.getReportById);

// Create a new report
router.post('/reports', auth, validate(validationSchemas.report.create), reportController.createReport);

// Update a report
router.put('/reports/:id', auth, validate(validationSchemas.report.update), reportController.updateReport);

// Delete a report
router.delete('/reports/:id', auth, reportController.deleteReport);

// Submit a report for approval
router.post('/reports/:id/submit', auth, validate(validationSchemas.report.submitForApproval), reportController.submitReportForApproval);

// Approve or reject a report
router.post('/reports/:id/approve', auth, validate(validationSchemas.report.approveOrReject), reportController.approveOrRejectReport);

// Publish a report
router.post('/reports/:id/publish', auth, reportController.publishReport);

module.exports = router;
