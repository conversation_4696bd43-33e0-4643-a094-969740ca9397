/**
 * Fusion Operator for CSDE Engine
 * 
 * This module implements the fusion operator (⊕) used in the CSDE formula.
 * The fusion operator creates non-linear synergy between components.
 */

class FusionOperator {
  /**
   * Create a new Fusion Operator instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      synergisticFactor: 1.618, // Golden ratio as the default synergistic factor
      nonLinearityFactor: 2.0, // Default non-linearity factor
      ...options
    };
    
    console.log(`Fusion Operator initialized with synergistic factor: ${this.options.synergisticFactor}`);
  }
  
  /**
   * Apply the fusion operator to two components
   * @param {Object} componentA - First component (typically tensor product result)
   * @param {Object} componentB - Second component (typically Cyber-Safety component)
   * @returns {Object} - Fusion result
   */
  apply(componentA, componentB) {
    console.log('Applying fusion operator');
    
    try {
      // Extract values from components
      const valueA = componentA.normalizedValue || componentA.tensorValue || 1;
      const valueB = componentB.processedValue || 1;
      
      // Calculate linear combination
      const linearCombination = valueA + valueB;
      
      // Calculate non-linear synergy
      const nonLinearSynergy = Math.pow(valueA * valueB, this.options.nonLinearityFactor / 10);
      
      // Apply synergistic factor
      const synergisticValue = linearCombination * this.options.synergisticFactor;
      
      // Calculate final fusion value
      const fusionValue = synergisticValue + nonLinearSynergy;
      
      return {
        componentA,
        componentB,
        linearCombination,
        nonLinearSynergy,
        synergisticValue,
        fusionValue
      };
    } catch (error) {
      console.error('Error applying fusion operator:', error);
      throw new Error(`Fusion operation failed: ${error.message}`);
    }
  }
  
  /**
   * Calculate the synergistic effect between two values
   * @param {Number} valueA - First value
   * @param {Number} valueB - Second value
   * @returns {Number} - Synergistic effect
   */
  calculateSynergy(valueA, valueB) {
    // Calculate synergy using the golden ratio and non-linearity factor
    return (valueA + valueB) * this.options.synergisticFactor + 
           Math.pow(valueA * valueB, this.options.nonLinearityFactor / 10);
  }
  
  /**
   * Adjust the synergistic factor
   * @param {Number} newFactor - New synergistic factor
   */
  adjustSynergisticFactor(newFactor) {
    if (newFactor > 0) {
      this.options.synergisticFactor = newFactor;
      console.log(`Adjusted synergistic factor to: ${this.options.synergisticFactor}`);
    } else {
      throw new Error('Synergistic factor must be positive');
    }
  }
  
  /**
   * Adjust the non-linearity factor
   * @param {Number} newFactor - New non-linearity factor
   */
  adjustNonLinearityFactor(newFactor) {
    if (newFactor > 0) {
      this.options.nonLinearityFactor = newFactor;
      console.log(`Adjusted non-linearity factor to: ${this.options.nonLinearityFactor}`);
    } else {
      throw new Error('Non-linearity factor must be positive');
    }
  }
}

module.exports = FusionOperator;
