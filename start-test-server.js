#!/usr/bin/env node

/**
 * NovaFuse Test Server Startup Script
 * Starts the test API server with proper configuration
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting NovaFuse Test API Server...\n');

// Check if required dependencies are installed
const packageJsonPath = path.join(__dirname, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json not found. Please run this from the project root.');
    process.exit(1);
}

// Check if node_modules exists
const nodeModulesPath = path.join(__dirname, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
    console.log('📦 Installing dependencies...');
    
    const npmInstall = spawn('npm', ['install'], {
        stdio: 'inherit',
        shell: true
    });
    
    npmInstall.on('close', (code) => {
        if (code === 0) {
            console.log('✅ Dependencies installed successfully\n');
            startServer();
        } else {
            console.error('❌ Failed to install dependencies');
            process.exit(1);
        }
    });
} else {
    startServer();
}

function startServer() {
    console.log('🔧 Configuration:');
    console.log('   - Port: 3100');
    console.log('   - WebSocket: Enabled');
    console.log('   - Test Root: Current directory');
    console.log('   - CORS: Enabled for all origins\n');
    
    console.log('📊 Dashboards will be available at:');
    console.log('   - http://localhost:3100/dashboard   (Test Dashboard)');
    console.log('   - http://localhost:3100/deployment  (Deployment Dashboard)');
    console.log('   - http://localhost:3100/demos       (Demo Selector)');
    console.log('   - http://localhost:3100/docs        (Documentation Portal)');
    console.log('   - http://localhost:3100/report      (Analytics Report)');
    console.log('   - http://localhost:3100/api         (API endpoints)\n');
    
    console.log('🔗 Integration:');
    console.log('   - NovaConnect: Mock implementation');
    console.log('   - Test Service Connector: Active');
    console.log('   - Deployment Service Connector: Active');
    console.log('   - Demo Execution Connector: Active');
    console.log('   - Documentation Service Connector: Active');
    console.log('   - Real-time updates: WebSocket enabled\n');
    
    console.log('⚡ Starting server...\n');
    
    // Start the test API server
    const server = spawn('node', ['novafuse-test-api-server.js'], {
        stdio: 'inherit',
        shell: true
    });
    
    server.on('close', (code) => {
        if (code === 0) {
            console.log('\n✅ Server stopped gracefully');
        } else {
            console.log(`\n❌ Server exited with code ${code}`);
        }
    });
    
    server.on('error', (error) => {
        console.error('\n❌ Failed to start server:', error);
        process.exit(1);
    });
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n🛑 Shutting down server...');
        server.kill('SIGINT');
    });
    
    process.on('SIGTERM', () => {
        console.log('\n🛑 Shutting down server...');
        server.kill('SIGTERM');
    });
}
