version: '3.8'

services:
  # CSDE API
  csde-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=production
      - PORT=3010
    command: npm start
    networks:
      - csde-network

  # CSDE UI
  csde-ui:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3011:3011"
    environment:
      - NODE_ENV=production
      - PORT=3011
    command: npm run start:ui
    networks:
      - csde-network
    depends_on:
      - csde-api

networks:
  csde-network:
    driver: bridge
