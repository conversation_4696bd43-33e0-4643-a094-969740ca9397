openapi: 3.0.0
info:
  title: Authentication API
  description: API endpoints for authentication
  version: 1.0.0

paths:
  /auth/login:
    post:
      summary: Authenticate a user
      description: Authenticate a user with username and password
      tags:
        - Authentication
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: Username
                  example: admin
                password:
                  type: string
                  description: Password
                  example: password
                  format: password
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT token
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                  expiresIn:
                    type: string
                    description: Token expiration time
                    example: 1h
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: User ID
                        example: user-123
                      username:
                        type: string
                        description: Username
                        example: admin
                      role:
                        type: string
                        description: User role
                        example: admin
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error type
          example: ValidationError
        message:
          type: string
          description: Error message
          example: Validation failed
        errors:
          type: array
          description: Validation errors
          items:
            type: object
            properties:
              field:
                type: string
                description: Field name
                example: username
              message:
                type: string
                description: Error message
                example: Username is required
