import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const TrinityFramework = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel>THE CYBER-SAFETY DOMINANCE FRAMEWORK: A STRATEGIC TRINITY</ContainerLabel>
      </ContainerBox>

      {/* Center Triangle */}
      <svg width="800" height="600" style={{ position: 'absolute', top: 0, left: 0, zIndex: 0 }}>
        <polygon
          points="400,100 200,400 600,400"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
      </svg>

      {/* Top Node - Cyber-Safety */}
      <ComponentBox left="325px" top="70px" width="150px" height="80px">
        <ComponentNumber>101</ComponentNumber>
        <ComponentLabel fontSize="16px">Cyber-Safety</ComponentLabel>
        <span style={{ fontSize: '14px' }}>The Mission</span>
      </ComponentBox>

      {/* Bottom Left Node - NovaFuse */}
      <ComponentBox left="125px" top="420px" width="150px" height="80px">
        <ComponentNumber>102</ComponentNumber>
        <ComponentLabel fontSize="16px">NovaFuse</ComponentLabel>
        <span style={{ fontSize: '14px' }}>The Engine</span>
      </ComponentBox>

      {/* Bottom Right Node - Partner Empowerment */}
      <ComponentBox left="525px" top="420px" width="150px" height="80px">
        <ComponentNumber>103</ComponentNumber>
        <ComponentLabel fontSize="14px">Partner Empowerment</ComponentLabel>
        <span style={{ fontSize: '12px' }}>The Growth Model</span>
      </ComponentBox>

      {/* Cyber-Safety Details */}
      <ComponentBox left="325px" top="180px" width="150px" height="60px" style={{ border: '1px dashed #333' }}>
        <ComponentNumber>104</ComponentNumber>
        <ComponentLabel fontSize="14px" color="#0A84FF">3,142x Performance</ComponentLabel>
        <span style={{ fontSize: '12px' }}>NIST Standards</span>
      </ComponentBox>

      {/* NovaFuse Details */}
      <ComponentBox left="125px" top="520px" width="150px" height="60px" style={{ border: '1px dashed #333' }}>
        <ComponentNumber>105</ComponentNumber>
        <ComponentLabel fontSize="14px" color="#0A84FF">48 Patents</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Universal Architecture</span>
      </ComponentBox>

      {/* Partner Empowerment Details */}
      <ComponentBox left="525px" top="520px" width="150px" height="60px" style={{ border: '1px dashed #333' }}>
        <ComponentNumber>106</ComponentNumber>
        <ComponentLabel fontSize="14px">18/82 Model</ComponentLabel>
        <span style={{ fontSize: '12px', color: '#0A84FF' }}>(0.82 × 2)^n Growth</span>
      </ComponentBox>

      {/* Connecting Arrows */}
      <svg width="800" height="600" style={{ position: 'absolute', top: 0, left: 0, zIndex: 0 }}>
        {/* Cyber-Safety to NovaFuse */}
        <path
          d="M 350,150 L 200,420"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        {/* Cyber-Safety to Partner Empowerment */}
        <path
          d="M 450,150 L 600,420"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        {/* NovaFuse to Partner Empowerment */}
        <path
          d="M 275,460 L 525,460"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />

        {/* Arrow definitions */}
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="0"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
          </marker>
        </defs>
      </svg>

      {/* Key Relationships */}
      <ComponentBox left="250px" top="250px" width="120px" height="40px" style={{ backgroundColor: 'rgba(255,255,255,0.9)' }}>
        <ComponentNumber>107</ComponentNumber>
        <ComponentLabel fontSize="14px">Enables</ComponentLabel>
      </ComponentBox>

      <ComponentBox left="430px" top="250px" width="120px" height="40px" style={{ backgroundColor: 'rgba(255,255,255,0.9)' }}>
        <ComponentNumber>108</ComponentNumber>
        <ComponentLabel fontSize="14px">Accelerates</ComponentLabel>
      </ComponentBox>

      <ComponentBox left="340px" top="430px" width="120px" height="40px" style={{ backgroundColor: 'rgba(255,255,255,0.9)' }}>
        <ComponentNumber>109</ComponentNumber>
        <ComponentLabel fontSize="14px">Amplifies</ComponentLabel>
      </ComponentBox>

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Core Elements of the Strategic Trinity</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" style={{ border: '1px dashed #333' }} />
          <LegendText>Key Attributes</LegendText>
        </LegendItem>
      </DiagramLegend>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default TrinityFramework;
