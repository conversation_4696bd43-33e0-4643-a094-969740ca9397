import React from 'react';

export default function LegacyVsNovaFuse() {
  return (
    <div>
      <h2 className="text-3xl font-bold mb-8 text-center">The GRC Paradigm Shift</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Legacy GRC */}
        <div className="bg-secondary p-6 rounded-lg border-2 border-red-600">
          <h3 className="text-2xl font-bold mb-4 text-red-500">Legacy GRC (Then)</h3>
          
          <div className="space-y-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-red-900 flex items-center justify-center text-2xl">
                ⚠
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Feature Modules 
                  <span className="relative inline-block w-16 h-2 bg-red-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-red-500"></span>
                  Monolithic, Siloed
                </h4>
                <p className="text-gray-400">Rigid, pre-defined modules that can't adapt to unique business needs</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-red-900 flex items-center justify-center text-2xl">
                ⏱
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Long Deployment Cycles
                  <span className="relative inline-block w-16 h-2 bg-red-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-red-500"></span>
                  6-12 Months
                </h4>
                <p className="text-gray-400">Lengthy implementation projects with high failure rates</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-red-900 flex items-center justify-center text-2xl">
                🔒
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Vendor Lock-in
                  <span className="relative inline-block w-16 h-2 bg-red-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-red-500"></span>
                  Trapped
                </h4>
                <p className="text-gray-400">Expensive to switch, difficult to integrate with other systems</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-red-900 flex items-center justify-center text-2xl">
                🚫
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Closed Systems
                  <span className="relative inline-block w-16 h-2 bg-red-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-red-500"></span>
                  Limited Extensibility
                </h4>
                <p className="text-gray-400">Proprietary APIs with minimal third-party integration</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-red-900 flex items-center justify-center text-2xl">
                💰
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Licensing-centric Revenue
                  <span className="relative inline-block w-16 h-2 bg-red-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-red-500"></span>
                  High Upfront Costs
                </h4>
                <p className="text-gray-400">Large capital expenditure with slow time-to-value</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* NovaFuse */}
        <div className="bg-secondary p-6 rounded-lg border-2 border-green-600">
          <h3 className="text-2xl font-bold mb-4 text-green-500">NovaFuse (Now)</h3>
          
          <div className="space-y-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-green-900 flex items-center justify-center text-2xl">
                🔌
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  APIs First
                  <span className="relative inline-block w-16 h-2 bg-green-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-green-500"></span>
                  Composable, Flexible
                </h4>
                <p className="text-gray-400">Mix-and-match capabilities to create custom GRC solutions</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-green-900 flex items-center justify-center text-2xl">
                ⚡
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Rapid Integration
                  <span className="relative inline-block w-16 h-2 bg-green-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-green-500"></span>
                  Days, Not Months
                </h4>
                <p className="text-gray-400">Connect systems in days with pre-built connectors</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-green-900 flex items-center justify-center text-2xl">
                🌐
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Ecosystem Growth
                  <span className="relative inline-block w-16 h-2 bg-green-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-green-500"></span>
                  Open Standards
                </h4>
                <p className="text-gray-400">Growing network of partners and integrations</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-green-900 flex items-center justify-center text-2xl">
                🛠️
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Open SDK + Marketplace
                  <span className="relative inline-block w-16 h-2 bg-green-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-green-500"></span>
                  Viral Adoption
                </h4>
                <p className="text-gray-400">Developers can easily build and share integrations</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-green-900 flex items-center justify-center text-2xl">
                🤝
              </div>
              <div className="ml-4">
                <h4 className="text-xl font-semibold flex items-center">
                  Partner-powered Revenue
                  <span className="relative inline-block w-16 h-2 bg-green-500 mx-2 after:content-[''] after:absolute after:right-0 after:top-[-4px] after:border-t-[5px] after:border-b-[5px] after:border-l-[8px] after:border-transparent after:border-l-green-500"></span>
                  Usage-based Flywheel
                </h4>
                <p className="text-gray-400">85% revenue share creates partner incentives and growth</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Bottom Section: Key Differences */}
      <div className="bg-secondary p-6 rounded-lg mt-8">
        <h3 className="text-2xl font-bold mb-4 text-center">The NovaFuse Advantage</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-xl font-semibold mb-2 text-blue-400">Speed to Value</h4>
            <p className="text-gray-300">Legacy GRC takes 6-12 months to implement. NovaFuse delivers value in days with pre-built connectors and APIs.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-xl font-semibold mb-2 text-blue-400">Ecosystem Network Effects</h4>
            <p className="text-gray-300">Each new partner makes NovaFuse more valuable, creating a network effect that legacy GRC vendors can't match.</p>
          </div>
          
          <div className="p-4 border border-blue-600 rounded-lg">
            <h4 className="text-xl font-semibold mb-2 text-blue-400">Revenue Sharing Model</h4>
            <p className="text-gray-300">NovaFuse's 85% revenue share creates partner incentives that drive growth and innovation across the ecosystem.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
