# Script to list all applications in the codebase by creation date

$outputFile = "$PWD\APPLICATIONS_INVENTORY_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
$baseDir = "d:\novafuse-api-superstore"

# Function to get application information from package.json
function Get-ApplicationInfo {
    param (
        [string]$directory
    )
    
    $packageJson = Join-Path $directory "package.json"
    if (Test-Path $packageJson) {
        try {
            $pkg = Get-Content $packageJson -Raw | ConvertFrom-Json
            $appInfo = @{
                Name = $pkg.name
                Version = $pkg.version
                Description = $pkg.description
                Main = $pkg.main
                Scripts = $pkg.scripts
                Dependencies = $pkg.dependencies
                DevDependencies = $pkg.devDependencies
                Directory = $directory.Replace($baseDir, '').TrimStart('\\')
                Created = (Get-Item $packageJson).CreationTime
                Modified = (Get-Item $packageJson).LastWriteTime
            }
            return $appInfo
        } catch {
            Write-Warning "Error parsing package.json in $directory"
            return $null
        }
    }
    return $null
}

# Function to get directory creation date (earliest file date)
function Get-DirectoryCreationDate {
    param (
        [string]$directory
    )
    
    $files = Get-ChildItem -Path $directory -Recurse -File -ErrorAction SilentlyContinue | 
             Where-Object { $_.Name -ne '.DS_Store' -and $_.Name -ne 'Thumbs.db' }
    
    if ($files) {
        return ($files | Sort-Object CreationTime | Select-Object -First 1).CreationTime
    }
    
    return (Get-Item $directory).CreationTime
}

# Find all package.json files in the codebase
Write-Host "Scanning for applications..."
$packageDirs = Get-ChildItem -Path $baseDir -Recurse -Filter "package.json" -ErrorAction SilentlyContinue | 
               Where-Object { $_.DirectoryName -notmatch '\\node_modules\\' -and 
                            $_.DirectoryName -notmatch '\\.next\\' -and
                            $_.DirectoryName -notmatch '\\dist\\' -and
                            $_.DirectoryName -notmatch '\\build\\' } |
               Select-Object -ExpandProperty DirectoryName

# Get application information
$applications = @()
foreach ($dir in $packageDirs) {
    $appInfo = Get-ApplicationInfo -directory $dir
    if ($appInfo) {
        $applications += $appInfo
    }
}

# Sort applications by creation date
$sortedApps = $applications | Sort-Object -Property Created

# Generate markdown output
$markdown = "# Applications Inventory
*Generated on $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*

## Summary
- **Total Applications Found**: $($sortedApps.Count)
"

# Add applications by date
$currentDate = $null
foreach ($app in $sortedApps) {
    $appDate = $app.Created.ToString('yyyy-MM')
    if ($appDate -ne $currentDate) {
        $markdown += "
## $appDate
"
        $currentDate = $appDate
    }
    
    $markdown += "### $($app.Name) v$($app.Version)"
    $markdown += "
- **Directory**: $($app.Directory)"
    $markdown += "
- **Created**: $($app.Created)"
    $markdown += "
- **Modified**: $($app.Modified)"
    $markdown += "
- **Description**: $($app.Description)"
    $markdown += "
- **Main File**: $($app.Main)"
    
    # List available scripts
    if ($app.Scripts -and ($app.Scripts | Get-Member -MemberType NoteProperty)) {
        $markdown += "
- **Available Scripts**:"
        foreach ($script in $app.Scripts.PSObject.Properties) {
            $markdown += "
  - `$ npm run $($script.Name)`"
        }
    }
    
    $markdown += "
"
}

# Write to file
try {
    $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
    Write-Host "Applications inventory generated: $outputFile"
} catch {
    Write-Error "Error writing to file: $_"
    $outputFile = ".\APPLICATIONS_INVENTORY_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
    Write-Host "Applications inventory generated in current directory: $((Get-Item $outputFile).FullName)"
}

# Also output the location of the file
Write-Host "`nApplications inventory created at: $((Get-Item $outputFile).FullName)"
Write-Host "Total applications found: $($sortedApps.Count)"
