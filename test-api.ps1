# Test script for NovaFuse API Superstore

Write-Host "Testing NovaFuse API Superstore..."
Write-Host ""

# Test Governance API
Write-Host "Testing Governance API..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/governance/board/meetings" -Headers @{apikey = "test-api-key"} -ErrorAction Stop
    Write-Host "? Governance API is working!" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) board meetings"
} catch {
    Write-Host "? Governance API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Security API
Write-Host "Testing Security API..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/security/vulnerabilities" -Headers @{apikey = "test-api-key"} -ErrorAction Stop
    Write-Host "? Security API is working!" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) vulnerabilities"
} catch {
    Write-Host "? Security API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test APIs API
Write-Host "Testing APIs API..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/apis/catalog" -Headers @{apikey = "test-api-key"} -ErrorAction Stop
    Write-Host "? APIs API is working!" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) APIs in the catalog"
} catch {
    Write-Host "? APIs API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test without API key (should fail)
Write-Host "Testing authentication (should fail)..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8000/governance/board/meetings" -ErrorAction Stop
    Write-Host "? Authentication test failed: Request succeeded without API key" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode.value__ -eq 401) {
        Write-Host "? Authentication is working correctly!" -ForegroundColor Green
    } else {
        Write-Host "? Authentication test failed with unexpected error: $_" -ForegroundColor Red
    }
}
Write-Host ""

Write-Host "Tests completed!"
