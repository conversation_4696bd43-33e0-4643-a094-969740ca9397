
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE Fixed ML Model Test Results</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    .chart-container {
      width: 80%;
      margin: 20px auto;
      height: 400px;
    }
    h1, h2 {
      text-align: center;
    }
    .metrics {
      width: 80%;
      margin: 20px auto;
      border-collapse: collapse;
    }
    .metrics th, .metrics td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    .metrics th {
      background-color: #f2f2f2;
    }
    .comparison {
      display: flex;
      justify-content: space-around;
      margin: 20px 0;
    }
    .comparison-item {
      text-align: center;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      width: 30%;
    }
    .comparison-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .improvement {
      color: green;
    }
  </style>
</head>
<body>
  <h1>CSDE Fixed ML Model Test Results</h1>

  <div class="comparison">
    <div class="comparison-item">
      <h3>Original ML Accuracy</h3>
      <div class="comparison-value">6.00%</div>
    </div>
    <div class="comparison-item">
      <h3>CSDE-Enhanced ML Accuracy</h3>
      <div class="comparison-value">0.00%</div>
      <div class="improvement">
        No improvement
      </div>
    </div>
  </div>

  <div class="comparison">
    <div class="comparison-item">
      <h3>Original ML Error</h3>
      <div class="comparison-value">221.55%</div>
    </div>
    <div class="comparison-item">
      <h3>CSDE-Enhanced ML Error</h3>
      <div class="comparison-value">NaN%</div>
      <div class="improvement">
        No improvement
      </div>
    </div>
  </div>

  <h2>Test Metrics</h2>
  <table class="metrics">
    <tr>
      <th>Metric</th>
      <th>Value</th>
    </tr>
    <tr>
      <td>Accuracy</td>
      <td>0.00%</td>
    </tr>
    <tr>
      <td>Average Error</td>
      <td>NaN%</td>
    </tr>
    <tr>
      <td>Max Error</td>
      <td>NaN%</td>
    </tr>
    <tr>
      <td>Min Error</td>
      <td>NaN%</td>
    </tr>
    <tr>
      <td>Test Samples</td>
      <td>100</td>
    </tr>
  </table>

  <div class="chart-container">
    <canvas id="errorDistributionChart"></canvas>
  </div>

  <div class="chart-container">
    <canvas id="predictionScatterChart"></canvas>
  </div>

  <script>
    // Error distribution chart
    const errors = [null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null];
    const errorBins = [0, 1, 2, 5, 10, 20, 50, 100];
    const errorCounts = Array(errorBins.length).fill(0);

    errors.forEach(error => {
      for (let i = 0; i < errorBins.length; i++) {
        if (error <= errorBins[i] || i === errorBins.length - 1) {
          errorCounts[i]++;
          break;
        }
      }
    });

    const errorCtx = document.getElementById('errorDistributionChart').getContext('2d');
    new Chart(errorCtx, {
      type: 'bar',
      data: {
        labels: errorBins.map((bin, index) => {
          if (index === 0) return '0%';
          const prevBin = errorBins[index - 1];
          return `${prevBin}% - ${bin}%`;
        }),
        datasets: [{
          label: 'Number of Predictions',
          data: errorCounts,
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Predictions'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Error Range'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Distribution of Prediction Errors'
          }
        }
      }
    });

    // Prediction scatter chart
    const predictions = [{"expected":214872.88785881526,"predicted":null,"error":null},{"expected":1017598.5153670538,"predicted":null,"error":null},{"expected":748438.6942101007,"predicted":null,"error":null},{"expected":124447.35158383947,"predicted":null,"error":null},{"expected":1250744.4964443925,"predicted":null,"error":null},{"expected":109818.72849831315,"predicted":null,"error":null},{"expected":1460451.8936356942,"predicted":null,"error":null},{"expected":525899.2336143624,"predicted":null,"error":null},{"expected":787253.6289779877,"predicted":null,"error":null},{"expected":128669.83324660736,"predicted":null,"error":null},{"expected":506083.0735548069,"predicted":null,"error":null},{"expected":105678.08860094548,"predicted":null,"error":null},{"expected":93304.2428826062,"predicted":null,"error":null},{"expected":894105.0847396788,"predicted":null,"error":null},{"expected":219985.38001013017,"predicted":null,"error":null},{"expected":301706.1169509222,"predicted":null,"error":null},{"expected":936726.9659487592,"predicted":null,"error":null},{"expected":433900.34607165924,"predicted":null,"error":null},{"expected":88294.0062418251,"predicted":null,"error":null},{"expected":309700.3268319281,"predicted":null,"error":null},{"expected":831036.8319862109,"predicted":null,"error":null},{"expected":631882.7692424482,"predicted":null,"error":null},{"expected":742702.4417813754,"predicted":null,"error":null},{"expected":494766.95258505957,"predicted":null,"error":null},{"expected":248568.61937029357,"predicted":null,"error":null},{"expected":281106.12935924245,"predicted":null,"error":null},{"expected":1569055.643158856,"predicted":null,"error":null},{"expected":240466.68823905912,"predicted":null,"error":null},{"expected":1136678.7350863155,"predicted":null,"error":null},{"expected":215474.6798689118,"predicted":null,"error":null},{"expected":431132.7996444015,"predicted":null,"error":null},{"expected":572008.9173327384,"predicted":null,"error":null},{"expected":707508.5921360267,"predicted":null,"error":null},{"expected":364355.4943758342,"predicted":null,"error":null},{"expected":1243638.9148724084,"predicted":null,"error":null},{"expected":336701.6575196799,"predicted":null,"error":null},{"expected":902004.6961420784,"predicted":null,"error":null},{"expected":206055.21735104857,"predicted":null,"error":null},{"expected":213394.7732606015,"predicted":null,"error":null},{"expected":411842.1224991417,"predicted":null,"error":null},{"expected":310052.49009686784,"predicted":null,"error":null},{"expected":133070.61935932623,"predicted":null,"error":null},{"expected":923952.7991471713,"predicted":null,"error":null},{"expected":155260.934400162,"predicted":null,"error":null},{"expected":639576.6356167124,"predicted":null,"error":null},{"expected":1029607.1688767184,"predicted":null,"error":null},{"expected":1507761.4871446756,"predicted":null,"error":null},{"expected":386340.01419050223,"predicted":null,"error":null},{"expected":302744.81019764906,"predicted":null,"error":null},{"expected":552884.4500223291,"predicted":null,"error":null},{"expected":400574.57903795544,"predicted":null,"error":null},{"expected":1194490.1353112326,"predicted":null,"error":null},{"expected":828719.8639844558,"predicted":null,"error":null},{"expected":456688.2540248471,"predicted":null,"error":null},{"expected":275747.0062484408,"predicted":null,"error":null},{"expected":327260.7301915717,"predicted":null,"error":null},{"expected":1040420.7693542143,"predicted":null,"error":null},{"expected":259679.03172216855,"predicted":null,"error":null},{"expected":133277.4749323491,"predicted":null,"error":null},{"expected":103315.78100244768,"predicted":null,"error":null},{"expected":90750.7920613902,"predicted":null,"error":null},{"expected":503433.3657968519,"predicted":null,"error":null},{"expected":713833.4751075385,"predicted":null,"error":null},{"expected":1016671.5952835021,"predicted":null,"error":null},{"expected":764778.5220786231,"predicted":null,"error":null},{"expected":705558.0038787292,"predicted":null,"error":null},{"expected":799556.7567856146,"predicted":null,"error":null},{"expected":1041998.595652944,"predicted":null,"error":null},{"expected":1102648.5123753285,"predicted":null,"error":null},{"expected":1855712.845986104,"predicted":null,"error":null},{"expected":816267.3339770447,"predicted":null,"error":null},{"expected":415772.33154248324,"predicted":null,"error":null},{"expected":264572.21346345794,"predicted":null,"error":null},{"expected":678908.7168782224,"predicted":null,"error":null},{"expected":1453153.5090953722,"predicted":null,"error":null},{"expected":78303.08121639528,"predicted":null,"error":null},{"expected":1470428.6889114864,"predicted":null,"error":null},{"expected":578324.0772425408,"predicted":null,"error":null},{"expected":625060.9744728088,"predicted":null,"error":null},{"expected":1658993.9147927384,"predicted":null,"error":null},{"expected":88488.19951662105,"predicted":null,"error":null},{"expected":673699.2508798557,"predicted":null,"error":null},{"expected":1704315.5157154251,"predicted":null,"error":null},{"expected":1086918.5393698711,"predicted":null,"error":null},{"expected":216745.2624293537,"predicted":null,"error":null},{"expected":91055.71632158168,"predicted":null,"error":null},{"expected":919146.0024021552,"predicted":null,"error":null},{"expected":353919.72614495293,"predicted":null,"error":null},{"expected":1223650.798308005,"predicted":null,"error":null},{"expected":228007.24022891716,"predicted":null,"error":null},{"expected":389439.80176623206,"predicted":null,"error":null},{"expected":931496.1159279683,"predicted":null,"error":null},{"expected":166823.41851764754,"predicted":null,"error":null},{"expected":803101.5653810218,"predicted":null,"error":null},{"expected":536746.9655161371,"predicted":null,"error":null},{"expected":294691.19282818504,"predicted":null,"error":null},{"expected":1136910.0036966577,"predicted":null,"error":null},{"expected":207022.78269018175,"predicted":null,"error":null},{"expected":483241.4317971092,"predicted":null,"error":null},{"expected":252561.60804819476,"predicted":null,"error":null}];

    const scatterCtx = document.getElementById('predictionScatterChart').getContext('2d');
    new Chart(scatterCtx, {
      type: 'scatter',
      data: {
        datasets: [{
          label: 'Predictions',
          data: predictions.map(p => ({ x: p.expected, y: p.predicted })),
          backgroundColor: predictions.map(p => p.error <= 10 ? 'rgba(75, 192, 192, 0.6)' : 'rgba(255, 99, 132, 0.6)'),
          borderColor: predictions.map(p => p.error <= 10 ? 'rgba(75, 192, 192, 1)' : 'rgba(255, 99, 132, 1)'),
          borderWidth: 1,
          pointRadius: 5
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            type: 'linear',
            position: 'bottom',
            title: {
              display: true,
              text: 'Expected Value'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Predicted Value'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Expected vs. Predicted Values'
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const point = predictions[context.dataIndex];
                return [
                  `Expected: ${point.expected.toFixed(2)}`,
                  `Predicted: ${point.predicted.toFixed(2)}`,
                  `Error: ${point.error.toFixed(2)}%`
                ];
              }
            }
          }
        }
      }
    });
  </script>
</body>
</html>
