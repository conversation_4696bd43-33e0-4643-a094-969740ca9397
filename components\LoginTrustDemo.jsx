import React from 'react';
import { Clock, Shield, Zap } from 'lucide-react';

/**
 * A simplified version of the Trust Automation Demo for the login page
 */
const LoginTrustDemo = () => {
  return (
    <div className="w-full max-w-md mx-auto mb-8">
      {/* Glowing header with animation */}
      <div className="text-center mb-2">
        <h1 
          className="text-4xl font-bold tracking-tight" 
          style={{
            background: "linear-gradient(to right, #0A84FF, #A855F7)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            textShadow: "0 0 15px rgba(10, 132, 255, 0.7), 0 0 30px rgba(168, 85, 247, 0.5)"
          }}
        >
          Trust, Automated.
        </h1>
      </div>
      
      <p className="mb-6 text-lg text-center" style={{
        background: "linear-gradient(to right, rgba(255,255,255,0.9), rgba(168, 85, 247, 0.8))",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        textShadow: "0 0 10px rgba(255, 255, 255, 0.3)"
      }}>From 300-Day Audits → 3-Click Compliance</p>
      
      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-3 mb-6">
        {/* Time saved counter */}
        <div className="flex items-center gap-4 bg-[#0A84FF]/10 p-3 rounded-xl border border-[#0A84FF]/30 transition-all hover:bg-[#0A84FF]/15">
          <Clock className="text-[#0A84FF] flex-shrink-0" />
          <div>
            <p className="text-white/80 text-sm">Time Saved vs. Traditional Audits</p>
            <p className="text-2xl font-bold">117.7 days</p>
          </div>
        </div>
        
        {/* Threat Detection */}
        <div className="flex items-center gap-4 bg-[#0A84FF]/10 p-3 rounded-xl border border-[#0A84FF]/30 transition-all hover:bg-[#0A84FF]/15">
          <Shield className="text-[#0A84FF] flex-shrink-0" />
          <div>
            <p className="text-white/80 text-sm">Threat Detection Rate</p>
            <p className="text-2xl font-bold">99.8%</p>
          </div>
        </div>
        
        {/* Time to Compliance */}
        <div className="flex items-center gap-4 bg-[#0A84FF]/10 p-3 rounded-xl border border-[#0A84FF]/30 transition-all hover:bg-[#0A84FF]/15">
          <Zap className="text-[#0A84FF] flex-shrink-0" />
          <div>
            <p className="text-white/80 text-sm">Time to Compliance</p>
            <p className="text-2xl font-bold">45 sec</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginTrustDemo;
