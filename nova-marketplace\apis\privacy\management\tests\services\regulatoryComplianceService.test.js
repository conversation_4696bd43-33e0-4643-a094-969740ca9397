/**
 * Regulatory Compliance Service Tests
 * 
 * This file contains tests for the regulatory compliance service.
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const regulatoryComplianceService = require('../../services/regulatoryComplianceService');
const { RegulatoryFramework, ComplianceRequirement, ComplianceStatus } = require('../../models');

let mongoServer;

// Setup before tests
beforeAll(async () => {
  // Create in-memory MongoDB server
  mongoServer = await MongoMemoryServer.create();
  const uri = mongoServer.getUri();
  
  // Connect to in-memory database
  await mongoose.connect(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });
});

// Clean up after tests
afterAll(async () => {
  // Disconnect from database
  await mongoose.disconnect();
  
  // Stop in-memory server
  await mongoServer.stop();
});

// Clear database between tests
afterEach(async () => {
  const collections = mongoose.connection.collections;
  
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

describe('Regulatory Compliance Service', () => {
  describe('getAllFrameworks', () => {
    it('should return all active regulatory frameworks', async () => {
      // Create test data
      await RegulatoryFramework.create([
        {
          name: 'General Data Protection Regulation',
          code: 'GDPR',
          version: '2016',
          description: 'EU data protection regulation',
          category: 'privacy',
          regions: ['EU'],
          status: 'active'
        },
        {
          name: 'California Consumer Privacy Act',
          code: 'CCPA',
          version: '2018',
          description: 'California privacy law',
          category: 'privacy',
          regions: ['US-CA'],
          status: 'active'
        },
        {
          name: 'Health Insurance Portability and Accountability Act',
          code: 'HIPAA',
          version: '1996',
          description: 'US healthcare privacy law',
          category: 'privacy',
          regions: ['US'],
          status: 'active'
        },
        {
          name: 'Old Regulation',
          code: 'OLD',
          version: '1.0',
          description: 'Outdated regulation',
          category: 'privacy',
          regions: ['US'],
          status: 'inactive'
        }
      ]);
      
      // Call service method
      const result = await regulatoryComplianceService.getAllFrameworks();
      
      // Assertions
      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(3); // Only active frameworks
      expect(result.pagination).toBeDefined();
      expect(result.pagination.total).toBe(3);
    });
    
    it('should filter frameworks by region', async () => {
      // Create test data
      await RegulatoryFramework.create([
        {
          name: 'General Data Protection Regulation',
          code: 'GDPR',
          version: '2016',
          description: 'EU data protection regulation',
          category: 'privacy',
          regions: ['EU'],
          status: 'active'
        },
        {
          name: 'California Consumer Privacy Act',
          code: 'CCPA',
          version: '2018',
          description: 'California privacy law',
          category: 'privacy',
          regions: ['US-CA'],
          status: 'active'
        }
      ]);
      
      // Call service method with filter
      const result = await regulatoryComplianceService.getAllFrameworks({ region: 'EU' });
      
      // Assertions
      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(1);
      expect(result.data[0].code).toBe('GDPR');
    });
  });
  
  describe('getFrameworkById', () => {
    it('should return a regulatory framework by ID', async () => {
      // Create test data
      const framework = await RegulatoryFramework.create({
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      });
      
      // Call service method
      const result = await regulatoryComplianceService.getFrameworkById(framework._id);
      
      // Assertions
      expect(result).toBeDefined();
      expect(result.code).toBe('GDPR');
      expect(result._id.toString()).toBe(framework._id.toString());
    });
    
    it('should throw an error if framework not found', async () => {
      // Generate a random ObjectId
      const randomId = new mongoose.Types.ObjectId();
      
      // Call service method with non-existent ID
      await expect(regulatoryComplianceService.getFrameworkById(randomId))
        .rejects
        .toThrow(`Regulatory framework with ID ${randomId} not found`);
    });
  });
  
  describe('getFrameworkByCode', () => {
    it('should return a regulatory framework by code', async () => {
      // Create test data
      await RegulatoryFramework.create({
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      });
      
      // Call service method
      const result = await regulatoryComplianceService.getFrameworkByCode('GDPR');
      
      // Assertions
      expect(result).toBeDefined();
      expect(result.name).toBe('General Data Protection Regulation');
      expect(result.code).toBe('GDPR');
    });
    
    it('should throw an error if framework not found', async () => {
      // Call service method with non-existent code
      await expect(regulatoryComplianceService.getFrameworkByCode('NONEXISTENT'))
        .rejects
        .toThrow('Regulatory framework with code NONEXISTENT not found');
    });
  });
  
  describe('getRequirementsByFramework', () => {
    it('should return compliance requirements for a framework', async () => {
      // Create test data
      const framework = await RegulatoryFramework.create({
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      });
      
      await ComplianceRequirement.create([
        {
          framework: framework._id,
          section: 'Art',
          number: '5',
          title: 'Principles relating to processing of personal data',
          description: 'Personal data shall be processed lawfully, fairly and in a transparent manner...',
          category: 'accountability',
          status: 'active'
        },
        {
          framework: framework._id,
          section: 'Art',
          number: '6',
          title: 'Lawfulness of processing',
          description: 'Processing shall be lawful only if and to the extent that at least one of the following applies...',
          category: 'accountability',
          status: 'active'
        },
        {
          framework: framework._id,
          section: 'Art',
          number: '7',
          title: 'Conditions for consent',
          description: 'Where processing is based on consent, the controller shall be able to demonstrate that the data subject has consented...',
          category: 'consent',
          status: 'active'
        }
      ]);
      
      // Call service method
      const result = await regulatoryComplianceService.getRequirementsByFramework(framework._id);
      
      // Assertions
      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(3);
      expect(result.pagination).toBeDefined();
      expect(result.pagination.total).toBe(3);
    });
    
    it('should filter requirements by category', async () => {
      // Create test data
      const framework = await RegulatoryFramework.create({
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      });
      
      await ComplianceRequirement.create([
        {
          framework: framework._id,
          section: 'Art',
          number: '5',
          title: 'Principles relating to processing of personal data',
          description: 'Personal data shall be processed lawfully, fairly and in a transparent manner...',
          category: 'accountability',
          status: 'active'
        },
        {
          framework: framework._id,
          section: 'Art',
          number: '7',
          title: 'Conditions for consent',
          description: 'Where processing is based on consent, the controller shall be able to demonstrate that the data subject has consented...',
          category: 'consent',
          status: 'active'
        }
      ]);
      
      // Call service method with filter
      const result = await regulatoryComplianceService.getRequirementsByFramework(framework._id, { category: 'consent' });
      
      // Assertions
      expect(result.data).toBeDefined();
      expect(result.data.length).toBe(1);
      expect(result.data[0].title).toBe('Conditions for consent');
    });
  });
  
  describe('getComplianceStatus', () => {
    it('should create a new compliance status if one does not exist', async () => {
      // Create test data
      const framework = await RegulatoryFramework.create({
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      });
      
      await ComplianceRequirement.create([
        {
          framework: framework._id,
          section: 'Art',
          number: '5',
          title: 'Principles relating to processing of personal data',
          description: 'Personal data shall be processed lawfully, fairly and in a transparent manner...',
          category: 'accountability',
          status: 'active'
        },
        {
          framework: framework._id,
          section: 'Art',
          number: '6',
          title: 'Lawfulness of processing',
          description: 'Processing shall be lawful only if and to the extent that at least one of the following applies...',
          category: 'accountability',
          status: 'active'
        }
      ]);
      
      // Call service method
      const result = await regulatoryComplianceService.getComplianceStatus('organization', 'org-123', framework._id);
      
      // Assertions
      expect(result).toBeDefined();
      expect(result.entityType).toBe('organization');
      expect(result.entityId).toBe('org-123');
      expect(result.framework.toString()).toBe(framework._id.toString());
      expect(result.frameworkCode).toBe('GDPR');
      expect(result.status).toBe('not-started');
      expect(result.progress).toBe(0);
      expect(result.requirementStatuses).toBeDefined();
      expect(result.requirementStatuses.length).toBe(2);
    });
    
    it('should return existing compliance status if one exists', async () => {
      // Create test data
      const framework = await RegulatoryFramework.create({
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      });
      
      const requirement = await ComplianceRequirement.create({
        framework: framework._id,
        section: 'Art',
        number: '5',
        title: 'Principles relating to processing of personal data',
        description: 'Personal data shall be processed lawfully, fairly and in a transparent manner...',
        category: 'accountability',
        status: 'active'
      });
      
      const status = await ComplianceStatus.create({
        entityType: 'organization',
        entityId: 'org-123',
        framework: framework._id,
        frameworkName: 'General Data Protection Regulation',
        frameworkCode: 'GDPR',
        frameworkVersion: '2016',
        status: 'in-progress',
        progress: 50,
        requirementStatuses: [
          {
            requirement: requirement._id,
            requirementCode: 'Art.5',
            requirementTitle: 'Principles relating to processing of personal data',
            status: 'compliant',
            notes: 'Implemented data processing principles'
          }
        ],
        lastAssessment: new Date()
      });
      
      // Call service method
      const result = await regulatoryComplianceService.getComplianceStatus('organization', 'org-123', framework._id);
      
      // Assertions
      expect(result).toBeDefined();
      expect(result._id.toString()).toBe(status._id.toString());
      expect(result.status).toBe('in-progress');
      expect(result.progress).toBe(50);
      expect(result.requirementStatuses).toBeDefined();
      expect(result.requirementStatuses.length).toBe(1);
    });
  });
  
  describe('updateRequirementStatus', () => {
    it('should update the status of a requirement', async () => {
      // Create test data
      const framework = await RegulatoryFramework.create({
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      });
      
      const requirement = await ComplianceRequirement.create({
        framework: framework._id,
        section: 'Art',
        number: '5',
        title: 'Principles relating to processing of personal data',
        description: 'Personal data shall be processed lawfully, fairly and in a transparent manner...',
        category: 'accountability',
        status: 'active'
      });
      
      const status = await ComplianceStatus.create({
        entityType: 'organization',
        entityId: 'org-123',
        framework: framework._id,
        frameworkName: 'General Data Protection Regulation',
        frameworkCode: 'GDPR',
        frameworkVersion: '2016',
        status: 'not-started',
        progress: 0,
        requirementStatuses: [
          {
            requirement: requirement._id,
            requirementCode: 'Art.5',
            requirementTitle: 'Principles relating to processing of personal data',
            status: 'not-started',
            notes: ''
          }
        ]
      });
      
      // Call service method
      const result = await regulatoryComplianceService.updateRequirementStatus(
        status._id,
        requirement._id,
        {
          status: 'compliant',
          notes: 'Implemented data processing principles',
          evidence: [
            {
              type: 'document',
              title: 'Data Processing Policy',
              description: 'Company policy on data processing principles',
              fileId: 'file-123'
            }
          ]
        }
      );
      
      // Assertions
      expect(result).toBeDefined();
      expect(result.status).toBe('compliant'); // Overall status should be updated
      expect(result.progress).toBe(100); // Progress should be updated
      expect(result.requirementStatuses).toBeDefined();
      expect(result.requirementStatuses[0].status).toBe('compliant');
      expect(result.requirementStatuses[0].notes).toBe('Implemented data processing principles');
      expect(result.requirementStatuses[0].evidence).toBeDefined();
      expect(result.requirementStatuses[0].evidence.length).toBe(1);
      expect(result.requirementStatuses[0].evidence[0].title).toBe('Data Processing Policy');
    });
  });
  
  describe('generateComplianceReport', () => {
    it('should generate a compliance report', async () => {
      // Create test data
      const framework = await RegulatoryFramework.create({
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      });
      
      const requirements = await ComplianceRequirement.create([
        {
          framework: framework._id,
          section: 'Art',
          number: '5',
          title: 'Principles relating to processing of personal data',
          description: 'Personal data shall be processed lawfully, fairly and in a transparent manner...',
          category: 'accountability',
          status: 'active'
        },
        {
          framework: framework._id,
          section: 'Art',
          number: '6',
          title: 'Lawfulness of processing',
          description: 'Processing shall be lawful only if and to the extent that at least one of the following applies...',
          category: 'accountability',
          status: 'active'
        },
        {
          framework: framework._id,
          section: 'Art',
          number: '7',
          title: 'Conditions for consent',
          description: 'Where processing is based on consent, the controller shall be able to demonstrate that the data subject has consented...',
          category: 'consent',
          status: 'active'
        }
      ]);
      
      await ComplianceStatus.create({
        entityType: 'organization',
        entityId: 'org-123',
        framework: framework._id,
        frameworkName: 'General Data Protection Regulation',
        frameworkCode: 'GDPR',
        frameworkVersion: '2016',
        status: 'in-progress',
        progress: 33,
        requirementStatuses: [
          {
            requirement: requirements[0]._id,
            requirementCode: 'Art.5',
            requirementTitle: 'Principles relating to processing of personal data',
            status: 'compliant',
            notes: 'Implemented data processing principles'
          },
          {
            requirement: requirements[1]._id,
            requirementCode: 'Art.6',
            requirementTitle: 'Lawfulness of processing',
            status: 'in-progress',
            notes: 'Working on implementing lawful basis for processing'
          },
          {
            requirement: requirements[2]._id,
            requirementCode: 'Art.7',
            requirementTitle: 'Conditions for consent',
            status: 'not-started',
            notes: ''
          }
        ],
        lastAssessment: new Date()
      });
      
      // Call service method
      const result = await regulatoryComplianceService.generateComplianceReport('organization', 'org-123', framework._id);
      
      // Assertions
      expect(result).toBeDefined();
      expect(result.entityType).toBe('organization');
      expect(result.entityId).toBe('org-123');
      expect(result.framework).toBeDefined();
      expect(result.framework.code).toBe('GDPR');
      expect(result.status).toBe('in-progress');
      expect(result.progress).toBe(33);
      expect(result.sectionCompliance).toBeDefined();
      expect(result.sectionCompliance.Art).toBeDefined();
      expect(result.requirementsBySection).toBeDefined();
      expect(result.requirementsBySection.Art).toBeDefined();
      expect(result.requirementsBySection.Art.length).toBe(3);
      expect(result.summary).toBeDefined();
      expect(result.summary.total).toBe(3);
      expect(result.summary.compliant).toBe(1);
      expect(result.summary.inProgress).toBe(1);
      expect(result.summary.notStarted).toBe(1);
    });
  });
});
