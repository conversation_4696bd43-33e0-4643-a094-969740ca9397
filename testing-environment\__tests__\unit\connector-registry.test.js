const request = require('supertest');
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');

// Mock data
const mockConnectors = [];

// Create a mock implementation of the connector registry service
const app = express();
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Get all connectors
app.get('/connectors', (req, res) => {
  res.json(mockConnectors);
});

// Get connector by ID
app.get('/connectors/:id', (req, res) => {
  const connector = mockConnectors.find(c => c.id === req.params.id);
  if (!connector) {
    return res.status(404).json({ error: 'Connector not found' });
  }
  res.json(connector);
});

// Create a new connector
app.post('/connectors', (req, res) => {
  const connector = {
    id: `connector-${Date.now()}`,
    ...req.body,
    created: new Date().toISOString(),
    updated: new Date().toISOString()
  };
  
  mockConnectors.push(connector);
  res.status(201).json(connector);
});

// Update a connector
app.put('/connectors/:id', (req, res) => {
  const index = mockConnectors.findIndex(c => c.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Connector not found' });
  }
  
  mockConnectors[index] = {
    ...mockConnectors[index],
    ...req.body,
    updated: new Date().toISOString()
  };
  
  res.json(mockConnectors[index]);
});

// Delete a connector
app.delete('/connectors/:id', (req, res) => {
  const index = mockConnectors.findIndex(c => c.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Connector not found' });
  }
  
  mockConnectors.splice(index, 1);
  res.status(204).send();
});

// Test data
const testConnector = {
  metadata: {
    name: 'Test Connector',
    version: '1.0.0',
    category: 'Test',
    description: 'Test connector',
    author: 'NovaGRC',
    tags: ['test']
  },
  authentication: {
    type: 'API_KEY',
    fields: {
      apiKey: {
        type: 'string',
        description: 'API Key',
        required: true,
        sensitive: true
      }
    },
    testConnection: {
      endpoint: '/health',
      method: 'GET',
      expectedResponse: {
        status: 200
      }
    }
  },
  configuration: {
    baseUrl: 'http://localhost:3005',
    headers: {},
    timeout: 30000,
    retryPolicy: {
      maxRetries: 3,
      backoffStrategy: 'exponential'
    }
  },
  endpoints: [
    {
      id: 'getFindings',
      name: 'Get Findings',
      path: '/aws/securityhub/findings',
      method: 'GET',
      parameters: {
        query: {},
        path: {},
        body: {}
      },
      response: {
        successCode: 200
      }
    }
  ],
  mappings: [
    {
      sourceEndpoint: 'getFindings',
      targetSystem: 'NovaGRC',
      targetEntity: 'ComplianceFindings',
      transformations: [
        {
          source: '$.Findings[0].Id',
          target: 'findingId',
          transform: 'identity'
        }
      ]
    }
  ],
  events: {
    webhooks: [],
    polling: []
  }
};

// Unit tests for connector registry service
describe('Connector Registry Service', () => {
  // Clear mock data before each test
  beforeEach(() => {
    mockConnectors.length = 0;
  });
  
  // Test health check endpoint
  describe('GET /health', () => {
    it('should return 200 OK with status', async () => {
      const response = await request(app).get('/health');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'ok');
    });
  });
  
  // Test get all connectors endpoint
  describe('GET /connectors', () => {
    it('should return an empty array when no connectors exist', async () => {
      const response = await request(app).get('/connectors');
      expect(response.status).toBe(200);
      expect(response.body).toEqual([]);
    });
    
    it('should return all connectors', async () => {
      // Add a test connector
      mockConnectors.push({
        id: 'test-connector-1',
        metadata: { name: 'Test Connector 1' }
      });
      
      const response = await request(app).get('/connectors');
      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toHaveProperty('id', 'test-connector-1');
    });
  });
  
  // Test get connector by ID endpoint
  describe('GET /connectors/:id', () => {
    it('should return 404 when connector does not exist', async () => {
      const response = await request(app).get('/connectors/non-existent-id');
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Connector not found');
    });
    
    it('should return the connector when it exists', async () => {
      // Add a test connector
      mockConnectors.push({
        id: 'test-connector-1',
        metadata: { name: 'Test Connector 1' }
      });
      
      const response = await request(app).get('/connectors/test-connector-1');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', 'test-connector-1');
      expect(response.body).toHaveProperty('metadata.name', 'Test Connector 1');
    });
  });
  
  // Test create connector endpoint
  describe('POST /connectors', () => {
    it('should create a new connector', async () => {
      const response = await request(app)
        .post('/connectors')
        .send(testConnector);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('metadata.name', 'Test Connector');
      expect(response.body).toHaveProperty('created');
      expect(response.body).toHaveProperty('updated');
      
      // Check that the connector was added to the mock data
      expect(mockConnectors).toHaveLength(1);
    });
    
    it('should handle missing required fields', async () => {
      const response = await request(app)
        .post('/connectors')
        .send({});
      
      expect(response.status).toBe(201); // In a real implementation, this would be 400
      expect(mockConnectors).toHaveLength(1);
    });
  });
  
  // Test update connector endpoint
  describe('PUT /connectors/:id', () => {
    it('should return 404 when connector does not exist', async () => {
      const response = await request(app)
        .put('/connectors/non-existent-id')
        .send({ metadata: { name: 'Updated Connector' } });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Connector not found');
    });
    
    it('should update an existing connector', async () => {
      // Add a test connector
      mockConnectors.push({
        id: 'test-connector-1',
        metadata: { name: 'Test Connector 1' },
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      });
      
      const response = await request(app)
        .put('/connectors/test-connector-1')
        .send({ metadata: { name: 'Updated Connector' } });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', 'test-connector-1');
      expect(response.body).toHaveProperty('metadata.name', 'Updated Connector');
      expect(response.body).toHaveProperty('updated');
      
      // Check that the connector was updated in the mock data
      expect(mockConnectors[0]).toHaveProperty('metadata.name', 'Updated Connector');
    });
  });
  
  // Test delete connector endpoint
  describe('DELETE /connectors/:id', () => {
    it('should return 404 when connector does not exist', async () => {
      const response = await request(app).delete('/connectors/non-existent-id');
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Connector not found');
    });
    
    it('should delete an existing connector', async () => {
      // Add a test connector
      mockConnectors.push({
        id: 'test-connector-1',
        metadata: { name: 'Test Connector 1' }
      });
      
      const response = await request(app).delete('/connectors/test-connector-1');
      expect(response.status).toBe(204);
      
      // Check that the connector was removed from the mock data
      expect(mockConnectors).toHaveLength(0);
    });
  });
  
  // Test edge cases
  describe('Edge Cases', () => {
    it('should handle large connectors', async () => {
      // Create a large connector with many endpoints
      const largeConnector = {
        ...testConnector,
        endpoints: Array(100).fill(0).map((_, i) => ({
          id: `endpoint-${i}`,
          name: `Endpoint ${i}`,
          path: `/path/${i}`,
          method: 'GET',
          parameters: {
            query: {},
            path: {},
            body: {}
          },
          response: {
            successCode: 200
          }
        }))
      };
      
      const response = await request(app)
        .post('/connectors')
        .send(largeConnector);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('endpoints');
      expect(response.body.endpoints).toHaveLength(100);
    });
    
    it('should handle special characters in connector names', async () => {
      const specialConnector = {
        ...testConnector,
        metadata: {
          ...testConnector.metadata,
          name: 'Special @#$%^&*() Connector'
        }
      };
      
      const response = await request(app)
        .post('/connectors')
        .send(specialConnector);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('metadata.name', 'Special @#$%^&*() Connector');
    });
  });
});
