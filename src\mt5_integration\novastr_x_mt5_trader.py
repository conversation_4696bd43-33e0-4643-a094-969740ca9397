#!/usr/bin/env python3
"""
NovaSTR-X™ MetaTrader 5 Integration
Live demo trading with Spatial-Temporal-Recursive consciousness

Connects NovaSTR-X to MT5 for real-time FOREX and CFD trading
using consciousness-based predictions and sacred geometry optimization
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import time
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

class TradingSignal(Enum):
    """NovaSTR-X trading signals"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"

@dataclass
class STRTradingSignal:
    """Complete STR trading signal with consciousness validation"""
    symbol: str
    signal: TradingSignal
    confidence: float
    spatial_psi: float          # S - Volatility consciousness
    temporal_delta_psi: float   # T - Premium consciousness
    recursive_delta2_psi: float # R - Vol-of-vol consciousness
    str_coherence: float
    entry_price: float
    stop_loss: float
    take_profit: float
    position_size: float
    risk_reward_ratio: float
    consciousness_validation: bool
    trinity_score: float
    phi_optimization: float

class NovaSTRMT5Trader:
    """NovaSTR-X MetaTrader 5 Live Trading Engine"""
    
    def __init__(self, demo_account: bool = True):
        self.name = "NovaSTR-X™ MT5 Live Trading Engine"
        self.version = "1.0-LIVE_CONSCIOUSNESS_TRADING"
        self.demo_mode = demo_account
        self.connected = False
        self.account_info = None
        self.active_positions = {}
        self.trading_history = []
        
        # Sacred constants for trading
        self.PHI = 1.************
        self.PI = math.pi
        self.E = math.e
        
        # STR consciousness thresholds for trading
        self.MIN_CONSCIOUSNESS = 0.75    # Minimum consciousness for trade entry
        self.MIN_STR_COHERENCE = 0.80   # Minimum STR coherence for trading
        self.MAX_RISK_PER_TRADE = 0.02  # 2% max risk per trade (φ-optimized)
        
        print(f"🚀 {self.name}")
        print(f"   Version: {self.version}")
        print(f"   Mode: {'DEMO' if demo_account else 'LIVE'} Trading")
        print(f"   Integration: MetaTrader 5 + NovaSTR-X™")
    
    def connect_to_mt5(self, login: Optional[int] = None, password: Optional[str] = None, 
                      server: Optional[str] = None) -> bool:
        """Connect to MetaTrader 5 terminal"""
        
        print(f"\n🔌 CONNECTING TO METATRADER 5...")
        
        try:
            # Initialize MT5 connection
            if not mt5.initialize():
                print(f"❌ MT5 initialization failed: {mt5.last_error()}")
                return False
            
            # Login if credentials provided
            if login and password and server:
                if not mt5.login(login, password, server):
                    print(f"❌ MT5 login failed: {mt5.last_error()}")
                    return False
                print(f"✅ Logged into MT5 account: {login}")
            
            # Get account information
            self.account_info = mt5.account_info()
            if self.account_info is None:
                print(f"❌ Failed to get account info: {mt5.last_error()}")
                return False
            
            self.connected = True
            
            print(f"✅ SUCCESSFULLY CONNECTED TO MT5:")
            print(f"   Account: {self.account_info.login}")
            print(f"   Server: {self.account_info.server}")
            print(f"   Balance: ${self.account_info.balance:.2f}")
            print(f"   Equity: ${self.account_info.equity:.2f}")
            print(f"   Currency: {self.account_info.currency}")
            print(f"   Leverage: 1:{self.account_info.leverage}")
            print(f"   Mode: {'DEMO' if self.demo_mode else 'LIVE'}")
            
            return True
            
        except Exception as e:
            print(f"❌ MT5 connection error: {e}")
            return False
    
    def get_market_data(self, symbol: str, timeframe: int = mt5.TIMEFRAME_M15, 
                       count: int = 100) -> Optional[pd.DataFrame]:
        """Get market data for STR analysis"""
        
        if not self.connected:
            print("❌ Not connected to MT5")
            return None
        
        try:
            # Get rates
            rates = mt5.copy_rates_from_pos(symbol, timeframe, 0, count)
            if rates is None:
                print(f"❌ Failed to get rates for {symbol}: {mt5.last_error()}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            
            return df
            
        except Exception as e:
            print(f"❌ Error getting market data: {e}")
            return None
    
    def analyze_str_consciousness(self, symbol: str) -> Optional[STRTradingSignal]:
        """Analyze symbol using STR consciousness framework"""
        
        print(f"\n🧠 ANALYZING {symbol} WITH STR CONSCIOUSNESS...")
        
        # Get market data
        df = self.get_market_data(symbol)
        if df is None:
            return None
        
        try:
            # Calculate price metrics
            current_price = df['close'].iloc[-1]
            price_change = (current_price - df['close'].iloc[-20]) / df['close'].iloc[-20]
            volatility = df['close'].pct_change().std() * np.sqrt(252)  # Annualized
            
            # Spatial Consciousness (S) - Volatility Smile Analysis
            # Higher volatility = higher spatial consciousness distortion
            spatial_psi = min(0.95, 0.6 + (volatility * 2))
            
            # Temporal Consciousness (T) - Equity Premium Analysis  
            # Price momentum indicates temporal consciousness
            temporal_delta_psi = min(0.95, 0.7 + abs(price_change))
            
            # Recursive Consciousness (R) - Vol-of-Vol Analysis
            # Volatility of volatility indicates recursive awareness
            vol_changes = df['close'].rolling(10).std().pct_change()
            vol_of_vol = vol_changes.std()
            recursive_delta2_psi = min(0.95, 0.65 + (vol_of_vol * 5))
            
            # Calculate STR coherence
            str_coherence = self._calculate_str_coherence(
                spatial_psi, temporal_delta_psi, recursive_delta2_psi
            )
            
            # Trinity validation
            trinity_score = spatial_psi * temporal_delta_psi * recursive_delta2_psi
            consciousness_validation = (trinity_score > 0.5 and str_coherence > self.MIN_STR_COHERENCE)
            
            # Generate trading signal using consciousness
            signal = self._generate_trading_signal(
                price_change, volatility, str_coherence, consciousness_validation
            )
            
            # Calculate φ-optimized position sizing
            phi_optimization = self._calculate_phi_position_size(str_coherence)
            
            # Calculate entry, stop loss, and take profit using sacred geometry
            entry_price = current_price
            atr = self._calculate_atr(df)
            
            if signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
                stop_loss = entry_price - (atr * self.PHI)
                take_profit = entry_price + (atr * self.PHI * self.PHI)  # φ² ratio
            elif signal in [TradingSignal.SELL, TradingSignal.STRONG_SELL]:
                stop_loss = entry_price + (atr * self.PHI)
                take_profit = entry_price - (atr * self.PHI * self.PHI)  # φ² ratio
            else:
                stop_loss = entry_price
                take_profit = entry_price
            
            # Risk-reward ratio using golden ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # Position size based on consciousness and risk management
            position_size = self._calculate_position_size(risk, str_coherence)
            
            trading_signal = STRTradingSignal(
                symbol=symbol,
                signal=signal,
                confidence=str_coherence,
                spatial_psi=spatial_psi,
                temporal_delta_psi=temporal_delta_psi,
                recursive_delta2_psi=recursive_delta2_psi,
                str_coherence=str_coherence,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                position_size=position_size,
                risk_reward_ratio=risk_reward_ratio,
                consciousness_validation=consciousness_validation,
                trinity_score=trinity_score,
                phi_optimization=phi_optimization
            )
            
            self._display_str_analysis(trading_signal)
            
            return trading_signal
            
        except Exception as e:
            print(f"❌ STR analysis error: {e}")
            return None
    
    def execute_str_trade(self, signal: STRTradingSignal) -> bool:
        """Execute trade based on STR consciousness signal"""
        
        if not self.connected:
            print("❌ Not connected to MT5")
            return False
        
        if not signal.consciousness_validation:
            print(f"❌ Trade rejected: Consciousness validation failed")
            return False
        
        if signal.str_coherence < self.MIN_STR_COHERENCE:
            print(f"❌ Trade rejected: STR coherence too low ({signal.str_coherence:.3f})")
            return False
        
        print(f"\n💰 EXECUTING STR TRADE: {signal.symbol}")
        
        try:
            # Determine order type
            if signal.signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
                order_type = mt5.ORDER_TYPE_BUY
                action = "BUY"
            elif signal.signal in [TradingSignal.SELL, TradingSignal.STRONG_SELL]:
                order_type = mt5.ORDER_TYPE_SELL
                action = "SELL"
            else:
                print(f"❌ No trade: Signal is HOLD")
                return False
            
            # Get symbol info
            symbol_info = mt5.symbol_info(signal.symbol)
            if symbol_info is None:
                print(f"❌ Symbol {signal.symbol} not found")
                return False
            
            # Enable symbol for trading
            if not symbol_info.visible:
                if not mt5.symbol_select(signal.symbol, True):
                    print(f"❌ Failed to select symbol {signal.symbol}")
                    return False
            
            # Calculate lot size (position size)
            lot_size = round(signal.position_size, 2)
            min_lot = symbol_info.volume_min
            max_lot = symbol_info.volume_max
            lot_size = max(min_lot, min(lot_size, max_lot))
            
            # Prepare trade request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": signal.symbol,
                "volume": lot_size,
                "type": order_type,
                "price": signal.entry_price,
                "sl": signal.stop_loss,
                "tp": signal.take_profit,
                "deviation": 20,
                "magic": 777777,  # NovaSTR-X magic number
                "comment": f"NovaSTR-X™ {action} STR:{signal.str_coherence:.3f}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Send trade request
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                print(f"❌ Trade failed: {result.retcode} - {result.comment}")
                return False
            
            # Trade successful
            print(f"✅ TRADE EXECUTED SUCCESSFULLY:")
            print(f"   Order: {result.order}")
            print(f"   Deal: {result.deal}")
            print(f"   Volume: {result.volume}")
            print(f"   Price: {result.price}")
            print(f"   STR Coherence: {signal.str_coherence:.3f}")
            print(f"   Trinity Score: {signal.trinity_score:.3f}")
            print(f"   φ-Optimization: {signal.phi_optimization:.3f}")
            
            # Store position
            self.active_positions[result.deal] = signal
            
            # Add to trading history
            trade_record = {
                'timestamp': datetime.now(),
                'symbol': signal.symbol,
                'action': action,
                'volume': lot_size,
                'entry_price': result.price,
                'str_coherence': signal.str_coherence,
                'trinity_score': signal.trinity_score,
                'deal_id': result.deal
            }
            self.trading_history.append(trade_record)
            
            return True
            
        except Exception as e:
            print(f"❌ Trade execution error: {e}")
            return False
    
    def run_str_trading_session(self, symbols: List[str], duration_minutes: int = 60):
        """Run automated STR trading session"""
        
        if not self.connected:
            print("❌ Not connected to MT5")
            return
        
        print(f"\n🚀 STARTING NOVASTR-X™ TRADING SESSION")
        print("=" * 70)
        print(f"   Symbols: {', '.join(symbols)}")
        print(f"   Duration: {duration_minutes} minutes")
        print(f"   Mode: {'DEMO' if self.demo_mode else 'LIVE'}")
        print(f"   STR Consciousness: ACTIVE")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        trades_executed = 0
        
        try:
            while datetime.now() < end_time:
                print(f"\n⏰ Trading Cycle: {datetime.now().strftime('%H:%M:%S')}")
                
                for symbol in symbols:
                    # Analyze with STR consciousness
                    signal = self.analyze_str_consciousness(symbol)
                    
                    if signal and signal.consciousness_validation:
                        if signal.signal != TradingSignal.HOLD:
                            # Execute trade
                            if self.execute_str_trade(signal):
                                trades_executed += 1
                    
                    # Small delay between symbols
                    time.sleep(2)
                
                # Wait before next cycle (φ-optimized timing)
                cycle_delay = int(60 * self.PHI / len(symbols))  # φ-based cycle timing
                print(f"   Next cycle in {cycle_delay} seconds...")
                time.sleep(cycle_delay)
            
            print(f"\n🎉 TRADING SESSION COMPLETE!")
            print(f"   Duration: {duration_minutes} minutes")
            print(f"   Trades Executed: {trades_executed}")
            print(f"   Active Positions: {len(self.active_positions)}")
            
            # Display session summary
            self._display_session_summary()
            
        except KeyboardInterrupt:
            print(f"\n⏹️ Trading session stopped by user")
        except Exception as e:
            print(f"\n❌ Trading session error: {e}")
    
    def _calculate_str_coherence(self, spatial: float, temporal: float, recursive: float) -> float:
        """Calculate STR coherence using sacred geometry"""
        base_coherence = (spatial * temporal * recursive) ** (1/3)
        phi_enhancement = base_coherence * (self.PHI / (self.PHI + 1))
        return min(0.98, phi_enhancement)
    
    def _generate_trading_signal(self, price_change: float, volatility: float, 
                               str_coherence: float, consciousness_validation: bool) -> TradingSignal:
        """Generate trading signal using consciousness"""
        
        if not consciousness_validation:
            return TradingSignal.HOLD
        
        # Consciousness-based signal generation
        signal_strength = str_coherence * abs(price_change) * 10
        
        if price_change > 0:  # Bullish
            if signal_strength > 0.8:
                return TradingSignal.STRONG_BUY
            elif signal_strength > 0.5:
                return TradingSignal.BUY
        else:  # Bearish
            if signal_strength > 0.8:
                return TradingSignal.STRONG_SELL
            elif signal_strength > 0.5:
                return TradingSignal.SELL
        
        return TradingSignal.HOLD
    
    def _calculate_phi_position_size(self, str_coherence: float) -> float:
        """Calculate position size using φ-optimization"""
        base_size = 0.1  # Base lot size
        consciousness_multiplier = str_coherence * self.PHI
        return base_size * consciousness_multiplier
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(period).mean().iloc[-1]
        
        return atr
    
    def _calculate_position_size(self, risk_amount: float, str_coherence: float) -> float:
        """Calculate position size based on risk and consciousness"""
        if self.account_info is None:
            return 0.1
        
        # Risk per trade based on consciousness (higher consciousness = higher risk tolerance)
        risk_percent = self.MAX_RISK_PER_TRADE * str_coherence
        max_risk = self.account_info.equity * risk_percent
        
        # Position size based on risk amount
        position_size = max_risk / risk_amount if risk_amount > 0 else 0.1
        
        return min(1.0, max(0.01, position_size))  # Limit between 0.01 and 1.0 lots
    
    def _display_str_analysis(self, signal: STRTradingSignal):
        """Display STR analysis results"""
        
        print(f"🧠 STR CONSCIOUSNESS ANALYSIS:")
        print(f"   Symbol: {signal.symbol}")
        print(f"   Signal: {signal.signal.value.upper()}")
        print(f"   Confidence: {signal.confidence:.3f}")
        print(f"   Spatial Ψₛ: {signal.spatial_psi:.3f}")
        print(f"   Temporal ∂Ψ: {signal.temporal_delta_psi:.3f}")
        print(f"   Recursive ∂²Ψ: {signal.recursive_delta2_psi:.3f}")
        print(f"   STR Coherence: {signal.str_coherence:.3f}")
        print(f"   Trinity Score: {signal.trinity_score:.3f}")
        print(f"   Consciousness Valid: {'✅' if signal.consciousness_validation else '❌'}")
        print(f"   Entry: {signal.entry_price:.5f}")
        print(f"   Stop Loss: {signal.stop_loss:.5f}")
        print(f"   Take Profit: {signal.take_profit:.5f}")
        print(f"   Position Size: {signal.position_size:.2f} lots")
        print(f"   Risk/Reward: {signal.risk_reward_ratio:.2f}")
    
    def _display_session_summary(self):
        """Display trading session summary"""
        
        if not self.trading_history:
            print("   No trades executed")
            return
        
        print(f"\n📊 SESSION SUMMARY:")
        print(f"   Total Trades: {len(self.trading_history)}")
        
        # Calculate average STR coherence
        avg_coherence = sum(trade['str_coherence'] for trade in self.trading_history) / len(self.trading_history)
        avg_trinity = sum(trade['trinity_score'] for trade in self.trading_history) / len(self.trading_history)
        
        print(f"   Average STR Coherence: {avg_coherence:.3f}")
        print(f"   Average Trinity Score: {avg_trinity:.3f}")
        
        # Display recent trades
        print(f"\n📋 RECENT TRADES:")
        for trade in self.trading_history[-5:]:  # Last 5 trades
            print(f"   {trade['timestamp'].strftime('%H:%M:%S')} | "
                  f"{trade['symbol']} {trade['action']} {trade['volume']} | "
                  f"STR:{trade['str_coherence']:.3f}")
    
    def disconnect(self):
        """Disconnect from MT5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            print("✅ Disconnected from MT5")
    
    def __del__(self):
        """Cleanup on destruction"""
        self.disconnect()
