<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber-Safety Incident Response Flow Diagram</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }
        
        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 700px; /* Minimum height, will expand with content */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }
        
        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }
        
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }
        
        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }
        
        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }
        
        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }
        
        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }
        
        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 0;
        }
        
        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }
        
        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }
        
        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
    </style>
</head>
<body>

    <h1>FIG. 8: Cyber-Safety Incident Response Flow Diagram</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="overflow: visible; min-height: 100px; min-width: 200px;" style="width: 750px; height: 580px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">CYBER-SAFETY INCIDENT RESPONSE FLOW</div>
        </div>

        <!-- Threat Detection -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 80px; width: 200px; height: 100px;">
            <div class="component-number-inside">1001</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Threat Detection</div>
            <div style="font-size: 12px; text-align: center;">
                NovaShield<br>
                Multi-source Anomaly Detection
            </div>
        </div>
        <div class="formula-box" style="left: 510px; top: 100px;">Trinity Equation</div>

        <!-- Initial Analysis -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 180px; width: 200px; height: 100px;">
            <div class="component-number-inside">1002</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Initial Analysis</div>
            <div style="font-size: 12px; text-align: center;">
                NovaCore<br>
                UUFT-based Pattern Recognition
            </div>
        </div>
        <div class="formula-box" style="left: 510px; top: 200px;">(A⊗B⊕C)×π10³</div>

        <!-- Risk Assessment -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 280px; width: 200px; height: 100px;">
            <div class="component-number-inside">1003</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Risk Assessment</div>
            <div style="font-size: 12px; text-align: center;">
                NovaTrack<br>
                Impact & Probability Calculation
            </div>
        </div>
        <div class="formula-box" style="left: 510px; top: 300px;">Data Purity Score</div>

        <!-- Response Selection -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 150px; top: 380px; width: 200px; height: 100px;">
            <div class="component-number-inside">1004</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Automated Response</div>
            <div style="font-size: 12px; text-align: center;">
                NovaFlowX<br>
                Predefined Response Workflows
            </div>
        </div>
        <div class="formula-box" style="left: 150px; top: 470px;">18/82 Principle</div>

        <!-- Response Selection -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 450px; top: 380px; width: 200px; height: 100px;">
            <div class="component-number-inside">1005</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Adaptive Response</div>
            <div style="font-size: 12px; text-align: center;">
                NovaLearn<br>
                AI-driven Custom Response
            </div>
        </div>
        <div class="formula-box" style="left: 450px; top: 470px;">Adaptive Coherence</div>

        <!-- Verification & Documentation -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 300px; top: 500px; width: 200px; height: 100px;">
            <div class="component-number-inside">1006</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Verification & Documentation</div>
            <div style="font-size: 12px; text-align: center;">
                NovaProof<br>
                Compliance & Audit Trail
            </div>
        </div>

        <!-- Decision Points -->
        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 50px; top: 280px; width: 150px; height: 100px; border: 2px dashed #333;">
            <div class="component-number-inside">1007</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Decision Point</div>
            <div style="font-size: 12px; text-align: center;">
                Severity Assessment<br>
                High/Medium/Low
            </div>
        </div>

        <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="overflow: visible; min-height: 50px; min-width: 100px;" style="left: 600px; top: 280px; width: 150px; height: 100px; border: 2px dashed #333;">
            <div class="component-number-inside">1008</div>
            <div class="component-label" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;" style="overflow: visible; font-size: 12px; margin-top: 10px; margin-bottom: 2px;">Decision Point</div>
            <div style="font-size: 12px; text-align: center;">
                Response Type<br>
                Auto/Adaptive/Manual
            </div>
        </div>

        <!-- Arrows -->
        <div class="arrow-line" style="left: 400px; top: 160px; width: 2px; height: 580px;"></div>
        <div class="arrow-head" style="left: 396px; top: 180px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 400px; top: 260px; width: 2px; height: 580px;"></div>
        <div class="arrow-head" style="left: 396px; top: 280px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 300px; top: 320px; width: 150px; height: 580px;"></div>
        <div class="arrow-head" style="left: 150px; top: 316px; border-width: 4px 8px 4px 0; border-color: transparent #555555 transparent transparent;"></div>

        <div class="arrow-line" style="left: 500px; top: 320px; width: 100px; height: 580px;"></div>
        <div class="arrow-head" style="left: 600px; top: 316px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>

        <div class="arrow-line" style="left: 125px; top: 360px; width: 2px; height: 580px;"></div>
        <div class="arrow-head" style="left: 121px; top: 380px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 675px; top: 360px; width: 2px; height: 580px;"></div>
        <div class="arrow-head" style="left: 671px; top: 380px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 250px; top: 420px; width: 50px; height: 580px; transform: translate(0, 0) rotate(45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 300px; top: 500px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>

        <div class="arrow-line" style="left: 550px; top: 420px; width: 50px; height: 580px; transform: translate(0, 0) rotate(-45deg); transform-origin: top left;"></div>
        <div class="arrow-head" style="left: 500px; top: 500px; border-width: 4px 8px 4px 0; border-color: transparent #555555 transparent transparent;"></div>

        <!-- Legend -->
        <div class="legend" style="position: absolute; right: 10px; bottom: 10px; background-color: white; border: 1px solid #ddd; border-radius: 4px; padding: 5px; z-index: 10; width: 200px;">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Process Steps</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff; border: 2px dashed #333;"></div>
                <div>Decision Points</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f9f9f9;"></div>
                <div>Mathematical Formulas</div>
            </div>
        </div>

        <div class="inventor-label" style="position: absolute; left: 10px; bottom: 10px; font-size: 12px; font-style: italic; color: #333;">Inventor: David Nigel Irvin</div>
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
</div>

</body>
</html>
