'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiPlus, FiServer, FiCloud, FiGithub, FiDatabase, FiSlack, FiCheckCircle, FiAlertCircle, FiPlay, FiPause, FiTrash2, FiSettings } from 'react-icons/fi';
import { getConnectors, activateConnector, deactivateConnector, deleteConnector } from '@/services/novaCoreApi';
import { useFeatureFlags } from '@/contexts/FeatureFlagContext';
import { FeatureFlag } from '@/utils/features/featureFlags';
import Link from 'next/link';

// Mock connector types
const connectorTypes = {
  aws: { name: 'AWS', icon: FiCloud, color: 'text-yellow-500' },
  azure: { name: 'Azure', icon: FiCloud, color: 'text-blue-500' },
  gcp: { name: 'Google Cloud', icon: FiCloud, color: 'text-green-500' },
  github: { name: '<PERSON><PERSON><PERSON><PERSON>', icon: FiGithub, color: 'text-gray-800 dark:text-white' },
  jira: { name: '<PERSON><PERSON>', icon: FiServer, color: 'text-blue-600' },
  slack: { name: 'Slack', icon: FiSlack, color: 'text-purple-500' },
  rest_api: { name: 'REST API', icon: FiServer, color: 'text-indigo-500' },
  database: { name: 'Database', icon: FiDatabase, color: 'text-orange-500' },
};

// Mock connectors data
const mockConnectors = [
  {
    id: 'conn-001',
    name: 'AWS Security Hub',
    type: 'aws',
    status: 'active',
    lastCollection: '2023-10-15',
    evidenceCount: 128,
  },
  {
    id: 'conn-002',
    name: 'Azure Security Center',
    type: 'azure',
    status: 'active',
    lastCollection: '2023-10-14',
    evidenceCount: 95,
  },
  {
    id: 'conn-003',
    name: 'GitHub Repository',
    type: 'github',
    status: 'inactive',
    lastCollection: '2023-10-10',
    evidenceCount: 42,
  },
  {
    id: 'conn-004',
    name: 'Jira Projects',
    type: 'jira',
    status: 'error',
    lastCollection: '2023-10-05',
    evidenceCount: 18,
  },
  {
    id: 'conn-005',
    name: 'Custom API',
    type: 'rest_api',
    status: 'configuring',
    lastCollection: '-',
    evidenceCount: 0,
  },
];

export default function ConnectorsPage() {
  const [connectors, setConnectors] = useState(mockConnectors);
  const [loading, setLoading] = useState(true);
  const [selectedConnector, setSelectedConnector] = useState<string | null>(null);
  const { isEnabled } = useFeatureFlags();

  // Load connectors from API
  useEffect(() => {
    const loadConnectors = async () => {
      try {
        // In a real implementation, this would fetch connectors from the API
        // const result = await getConnectors();
        // setConnectors(result.items);
        
        // For now, use mock data
        setConnectors(mockConnectors);
        setLoading(false);
      } catch (error) {
        console.error('Error loading connectors:', error);
        setLoading(false);
      }
    };
    
    loadConnectors();
  }, []);

  // Handle connector activation
  const handleActivate = async (id: string) => {
    try {
      // In a real implementation, this would activate the connector via API
      // await activateConnector(id);
      
      // For now, update local state
      setConnectors(connectors.map(c => 
        c.id === id ? { ...c, status: 'active' } : c
      ));
    } catch (error) {
      console.error('Error activating connector:', error);
    }
  };

  // Handle connector deactivation
  const handleDeactivate = async (id: string) => {
    try {
      // In a real implementation, this would deactivate the connector via API
      // await deactivateConnector(id);
      
      // For now, update local state
      setConnectors(connectors.map(c => 
        c.id === id ? { ...c, status: 'inactive' } : c
      ));
    } catch (error) {
      console.error('Error deactivating connector:', error);
    }
  };

  // Handle connector deletion
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this connector?')) {
      return;
    }
    
    try {
      // In a real implementation, this would delete the connector via API
      // await deleteConnector(id);
      
      // For now, update local state
      setConnectors(connectors.filter(c => c.id !== id));
    } catch (error) {
      console.error('Error deleting connector:', error);
    }
  };

  // Get connector type details
  const getConnectorTypeDetails = (type: string) => {
    return connectorTypes[type] || { name: 'Unknown', icon: FiServer, color: 'text-gray-500' };
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'configuring':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <MainLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Data Connectors</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage connections to evidence sources</p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link href="/connectors/new" className="btn btn-primary flex items-center">
            <FiPlus className="mr-2" />
            New Connector
          </Link>
        </div>
      </div>

      {loading ? (
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Loading connectors...</p>
        </div>
      ) : (
        <div className="card">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Last Collection
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Evidence Count
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-secondary-light divide-y divide-gray-200 dark:divide-gray-700">
                {connectors.map((connector) => {
                  const typeDetails = getConnectorTypeDetails(connector.type);
                  const TypeIcon = typeDetails.icon;
                  
                  return (
                    <tr 
                      key={connector.id}
                      className={`${selectedConnector === connector.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                      onClick={() => setSelectedConnector(connector.id)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-md bg-gray-100 dark:bg-gray-700">
                            <TypeIcon className={`h-6 w-6 ${typeDetails.color}`} />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {connector.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              ID: {connector.id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {typeDetails.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(connector.status)}`}
                        >
                          {connector.status.charAt(0).toUpperCase() + connector.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {connector.lastCollection}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {connector.evidenceCount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          {connector.status === 'active' ? (
                            <button
                              className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                              title="Deactivate"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeactivate(connector.id);
                              }}
                            >
                              <FiPause />
                            </button>
                          ) : (
                            <button
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              title="Activate"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleActivate(connector.id);
                              }}
                            >
                              <FiPlay />
                            </button>
                          )}
                          <Link
                            href={`/connectors/${connector.id}`}
                            className="text-primary hover:text-primary-dark"
                            title="Settings"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <FiSettings />
                          </Link>
                          <button
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                            title="Delete"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(connector.id);
                            }}
                          >
                            <FiTrash2 />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {isEnabled(FeatureFlag.VENDOR_RISK_INTEGRATION) && (
        <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-blue-700 dark:text-blue-200">
                <strong>BridgeCore Integration Available:</strong> Connect to vendor risk management data sources through BridgeCore integration.
              </p>
              <p className="text-sm text-blue-700 dark:text-blue-200 mt-1">
                <Link href="/settings/integrations" className="text-blue-600 dark:text-blue-300 underline">
                  Configure BridgeCore Integration
                </Link>
              </p>
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
