<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE Performance Monitoring</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
  <link rel="stylesheet" href="css/common.css">
  <style>
    .card {
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }
    
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .metric-value {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 0;
    }
    
    .metric-label {
      font-size: 0.9rem;
      color: #6c757d;
    }
    
    .chart-container {
      height: 300px;
      width: 100%;
    }
    
    .alert-badge {
      position: absolute;
      top: -10px;
      right: -10px;
      background-color: #dc3545;
      color: white;
      border-radius: 50%;
      width: 25px;
      height: 25px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      font-size: 0.8rem;
    }
    
    .status-indicator {
      width: 15px;
      height: 15px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 5px;
    }
    
    .status-healthy {
      background-color: #28a745;
    }
    
    .status-degraded {
      background-color: #ffc107;
    }
    
    .status-unhealthy {
      background-color: #dc3545;
    }
    
    .refresh-button {
      cursor: pointer;
      transition: transform 0.3s ease;
    }
    
    .refresh-button:hover {
      transform: rotate(180deg);
    }
    
    .spinner-border {
      width: 1rem;
      height: 1rem;
      border-width: 0.2em;
    }
    
    .alert-list {
      max-height: 300px;
      overflow-y: auto;
    }
    
    .alert-item {
      padding: 10px;
      border-bottom: 1px solid #dee2e6;
    }
    
    .alert-item:last-child {
      border-bottom: none;
    }
    
    .alert-timestamp {
      font-size: 0.8rem;
      color: #6c757d;
    }
    
    .alert-message {
      font-weight: bold;
    }
    
    .alert-type {
      font-size: 0.8rem;
      padding: 2px 5px;
      border-radius: 3px;
      background-color: #f8f9fa;
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
      <a class="navbar-brand" href="/">NovaConnect</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/csde/dashboard">CSDE Dashboard</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/csde/batch">Batch Processing</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/csde/advanced">Advanced Features</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/csde/performance">Performance</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container-fluid mt-4">
    <div class="row mb-4">
      <div class="col">
        <h1>CSDE Performance Monitoring</h1>
        <p class="lead">Real-time performance metrics for the CSDE engine</p>
      </div>
      <div class="col-auto d-flex align-items-center">
        <div class="d-flex align-items-center me-3">
          <span id="status-indicator" class="status-indicator status-healthy"></span>
          <span id="status-text">Healthy</span>
        </div>
        <div class="d-flex align-items-center me-3">
          <span>Last updated: </span>
          <span id="last-updated" class="ms-1">Never</span>
        </div>
        <button id="refresh-button" class="btn btn-outline-primary">
          <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
      </div>
    </div>

    <div class="row">
      <!-- Engine Metrics -->
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-header">
            Engine Metrics
            <i class="bi bi-gear"></i>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-6 mb-3">
                <p class="metric-label">Total Operations</p>
                <p id="total-operations" class="metric-value">0</p>
              </div>
              <div class="col-6 mb-3">
                <p class="metric-label">Success Rate</p>
                <p id="success-rate" class="metric-value">100%</p>
              </div>
              <div class="col-6">
                <p class="metric-label">Avg. Latency</p>
                <p id="avg-latency" class="metric-value">0ms</p>
              </div>
              <div class="col-6">
                <p class="metric-label">Performance Factor</p>
                <p id="performance-factor" class="metric-value">3,142x</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cache Metrics -->
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-header">
            Cache Metrics
            <i class="bi bi-speedometer"></i>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-6 mb-3">
                <p class="metric-label">Hit Rate</p>
                <p id="cache-hit-rate" class="metric-value">0%</p>
              </div>
              <div class="col-6 mb-3">
                <p class="metric-label">Size</p>
                <p id="cache-size" class="metric-value">0</p>
              </div>
              <div class="col-6">
                <p class="metric-label">Hits</p>
                <p id="cache-hits" class="metric-value">0</p>
              </div>
              <div class="col-6">
                <p class="metric-label">Misses</p>
                <p id="cache-misses" class="metric-value">0</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Metrics -->
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-header">
            System Metrics
            <i class="bi bi-cpu"></i>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-6 mb-3">
                <p class="metric-label">CPU Usage</p>
                <p id="cpu-usage" class="metric-value">0%</p>
              </div>
              <div class="col-6 mb-3">
                <p class="metric-label">Memory Usage</p>
                <p id="memory-usage" class="metric-value">0%</p>
              </div>
              <div class="col-6">
                <p class="metric-label">Event Loop Lag</p>
                <p id="event-loop-lag" class="metric-value">0ms</p>
              </div>
              <div class="col-6">
                <p class="metric-label">Uptime</p>
                <p id="uptime" class="metric-value">0m</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Alerts -->
      <div class="col-md-6 col-lg-3">
        <div class="card">
          <div class="card-header">
            Alerts
            <i class="bi bi-exclamation-triangle"></i>
            <span id="alert-badge" class="alert-badge d-none">0</span>
          </div>
          <div class="card-body p-0">
            <div id="alert-list" class="alert-list">
              <div class="alert-item text-center py-4">
                <p class="text-muted">No alerts</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <!-- Operations Chart -->
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            Operations Per Second
            <i class="bi bi-graph-up"></i>
          </div>
          <div class="card-body">
            <div id="operations-chart" class="chart-container"></div>
          </div>
        </div>
      </div>

      <!-- Latency Chart -->
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            Average Latency (ms)
            <i class="bi bi-clock"></i>
          </div>
          <div class="card-body">
            <div id="latency-chart" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <!-- Performance Factor Chart -->
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            Performance Factor
            <i class="bi bi-lightning"></i>
          </div>
          <div class="card-body">
            <div id="performance-factor-chart" class="chart-container"></div>
          </div>
        </div>
      </div>

      <!-- Cache Hit Rate Chart -->
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            Cache Hit Rate
            <i class="bi bi-speedometer2"></i>
          </div>
          <div class="card-body">
            <div id="cache-hit-rate-chart" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-4">
      <!-- Component Metrics -->
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            Component Metrics
            <i class="bi bi-puzzle"></i>
          </div>
          <div class="card-body">
            <div id="component-chart" class="chart-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
  <script>
    // Initialize charts
    const charts = {};
    
    // Fetch metrics data
    async function fetchMetrics() {
      try {
        const response = await fetch('/csde/performance/metrics');
        const data = await response.json();
        
        if (data.success) {
          updateDashboard(data.metrics);
        } else {
          console.error('Failed to fetch metrics:', data.error);
        }
      } catch (error) {
        console.error('Error fetching metrics:', error);
      }
    }
    
    // Fetch visualization data
    async function fetchVisualizationData() {
      try {
        const response = await fetch('/csde/performance/visualization');
        const data = await response.json();
        
        if (data.success) {
          updateCharts(data.data);
        } else {
          console.error('Failed to fetch visualization data:', data.error);
        }
      } catch (error) {
        console.error('Error fetching visualization data:', error);
      }
    }
    
    // Fetch alerts
    async function fetchAlerts() {
      try {
        const response = await fetch('/csde/performance/alerts');
        const data = await response.json();
        
        if (data.success) {
          updateAlerts(data.alerts);
        } else {
          console.error('Failed to fetch alerts:', data.error);
        }
      } catch (error) {
        console.error('Error fetching alerts:', error);
      }
    }
    
    // Fetch health status
    async function fetchHealth() {
      try {
        const response = await fetch('/csde/performance/health');
        const data = await response.json();
        
        if (data.success) {
          updateHealth(data.health);
        } else {
          console.error('Failed to fetch health status:', data.error);
        }
      } catch (error) {
        console.error('Error fetching health status:', error);
      }
    }
    
    // Update dashboard with metrics
    function updateDashboard(metrics) {
      // Update engine metrics
      document.getElementById('total-operations').textContent = metrics.engine.totalOperations.toLocaleString();
      document.getElementById('success-rate').textContent = metrics.engine.totalOperations > 0 
        ? `${((metrics.engine.successfulOperations / metrics.engine.totalOperations) * 100).toFixed(1)}%` 
        : '100%';
      document.getElementById('avg-latency').textContent = `${metrics.engine.averageLatency.toFixed(2)}ms`;
      document.getElementById('performance-factor').textContent = `${metrics.engine.performanceFactor.toLocaleString()}x`;
      
      // Update cache metrics
      document.getElementById('cache-hit-rate').textContent = `${(metrics.cache.hitRate * 100).toFixed(1)}%`;
      document.getElementById('cache-size').textContent = metrics.cache.size.toLocaleString();
      document.getElementById('cache-hits').textContent = metrics.cache.hits.toLocaleString();
      document.getElementById('cache-misses').textContent = metrics.cache.misses.toLocaleString();
      
      // Update system metrics
      const latestCpuSample = metrics.system.cpu.length > 0 ? metrics.system.cpu[metrics.system.cpu.length - 1] : { value: 0 };
      const latestMemorySample = metrics.system.memory.length > 0 ? metrics.system.memory[metrics.system.memory.length - 1] : { value: 0 };
      const latestEventLoopSample = metrics.system.eventLoop.length > 0 ? metrics.system.eventLoop[metrics.system.eventLoop.length - 1] : { value: 0 };
      
      document.getElementById('cpu-usage').textContent = `${(latestCpuSample.value * 100).toFixed(1)}%`;
      document.getElementById('memory-usage').textContent = `${(latestMemorySample.value * 100).toFixed(1)}%`;
      document.getElementById('event-loop-lag').textContent = `${latestEventLoopSample.value.toFixed(2)}ms`;
      
      // Calculate uptime
      const uptimeMinutes = Math.floor((Date.now() - metrics.startTime) / 60000);
      document.getElementById('uptime').textContent = uptimeMinutes < 60 
        ? `${uptimeMinutes}m` 
        : `${Math.floor(uptimeMinutes / 60)}h ${uptimeMinutes % 60}m`;
      
      // Update last updated time
      document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
    }
    
    // Update charts with visualization data
    function updateCharts(data) {
      // Initialize charts if not already initialized
      if (!charts.operations) {
        initializeCharts(data);
      } else {
        // Update existing charts
        charts.operations.data.labels = data.timeLabels;
        charts.operations.data.datasets[0].data = data.operations.data;
        charts.operations.update();
        
        charts.latency.data.labels = data.timeLabels;
        charts.latency.data.datasets[0].data = data.latency.data;
        charts.latency.update();
        
        charts.performanceFactor.data.labels = data.timeLabels;
        charts.performanceFactor.data.datasets[0].data = data.performanceFactor.data;
        charts.performanceFactor.update();
        
        charts.cacheHitRate.data.labels = data.timeLabels;
        charts.cacheHitRate.data.datasets[0].data = data.cacheHitRate.data;
        charts.cacheHitRate.update();
        
        charts.components.data.labels = data.components.labels;
        charts.components.data.datasets[0].data = data.components.operations;
        charts.components.data.datasets[1].data = data.components.latency;
        charts.components.update();
      }
    }
    
    // Initialize charts
    function initializeCharts(data) {
      const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        animation: {
          duration: 500
        },
        plugins: {
          legend: {
            position: 'top'
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      };
      
      // Operations chart
      charts.operations = new Chart(
        document.getElementById('operations-chart'),
        {
          type: 'line',
          data: {
            labels: data.timeLabels,
            datasets: [{
              label: 'Operations Per Second',
              data: data.operations.data,
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              tension: 0.4,
              fill: true
            }]
          },
          options: chartOptions
        }
      );
      
      // Latency chart
      charts.latency = new Chart(
        document.getElementById('latency-chart'),
        {
          type: 'line',
          data: {
            labels: data.timeLabels,
            datasets: [{
              label: 'Average Latency (ms)',
              data: data.latency.data,
              borderColor: 'rgb(255, 99, 132)',
              backgroundColor: 'rgba(255, 99, 132, 0.2)',
              tension: 0.4,
              fill: true
            }]
          },
          options: chartOptions
        }
      );
      
      // Performance factor chart
      charts.performanceFactor = new Chart(
        document.getElementById('performance-factor-chart'),
        {
          type: 'line',
          data: {
            labels: data.timeLabels,
            datasets: [{
              label: 'Performance Factor',
              data: data.performanceFactor.data,
              borderColor: 'rgb(54, 162, 235)',
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              tension: 0.4,
              fill: true
            }]
          },
          options: chartOptions
        }
      );
      
      // Cache hit rate chart
      charts.cacheHitRate = new Chart(
        document.getElementById('cache-hit-rate-chart'),
        {
          type: 'line',
          data: {
            labels: data.timeLabels,
            datasets: [{
              label: 'Cache Hit Rate',
              data: data.cacheHitRate.data,
              borderColor: 'rgb(255, 159, 64)',
              backgroundColor: 'rgba(255, 159, 64, 0.2)',
              tension: 0.4,
              fill: true
            }]
          },
          options: {
            ...chartOptions,
            scales: {
              y: {
                beginAtZero: true,
                max: 1,
                ticks: {
                  callback: function(value) {
                    return (value * 100) + '%';
                  }
                }
              }
            }
          }
        }
      );
      
      // Component metrics chart
      charts.components = new Chart(
        document.getElementById('component-chart'),
        {
          type: 'bar',
          data: {
            labels: data.components.labels,
            datasets: [
              {
                label: 'Operations',
                data: data.components.operations,
                backgroundColor: 'rgba(75, 192, 192, 0.7)',
                borderColor: 'rgb(75, 192, 192)',
                borderWidth: 1,
                yAxisID: 'y'
              },
              {
                label: 'Avg. Latency (ms)',
                data: data.components.latency,
                backgroundColor: 'rgba(255, 99, 132, 0.7)',
                borderColor: 'rgb(255, 99, 132)',
                borderWidth: 1,
                yAxisID: 'y1'
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top'
              }
            },
            scales: {
              y: {
                type: 'linear',
                position: 'left',
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'Operations'
                }
              },
              y1: {
                type: 'linear',
                position: 'right',
                beginAtZero: true,
                grid: {
                  drawOnChartArea: false
                },
                title: {
                  display: true,
                  text: 'Latency (ms)'
                }
              }
            }
          }
        }
      );
    }
    
    // Update alerts
    function updateAlerts(alerts) {
      const alertList = document.getElementById('alert-list');
      const alertBadge = document.getElementById('alert-badge');
      
      // Update alert badge
      if (alerts.length > 0) {
        alertBadge.textContent = alerts.length;
        alertBadge.classList.remove('d-none');
      } else {
        alertBadge.classList.add('d-none');
      }
      
      // Update alert list
      if (alerts.length === 0) {
        alertList.innerHTML = `
          <div class="alert-item text-center py-4">
            <p class="text-muted">No alerts</p>
          </div>
        `;
        return;
      }
      
      // Sort alerts by timestamp (newest first)
      alerts.sort((a, b) => b.timestamp - a.timestamp);
      
      // Generate alert items
      alertList.innerHTML = alerts.map(alert => {
        const date = new Date(alert.timestamp);
        const formattedTime = date.toLocaleTimeString();
        
        return `
          <div class="alert-item">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <span class="alert-timestamp">${formattedTime}</span>
              <span class="alert-type">${alert.type}</span>
            </div>
            <div class="alert-message">${alert.message}</div>
          </div>
        `;
      }).join('');
    }
    
    // Update health status
    function updateHealth(health) {
      const statusIndicator = document.getElementById('status-indicator');
      const statusText = document.getElementById('status-text');
      
      // Update status indicator
      statusIndicator.className = 'status-indicator';
      statusText.textContent = health.status.charAt(0).toUpperCase() + health.status.slice(1);
      
      if (health.status === 'healthy') {
        statusIndicator.classList.add('status-healthy');
      } else if (health.status === 'degraded') {
        statusIndicator.classList.add('status-degraded');
      } else {
        statusIndicator.classList.add('status-unhealthy');
      }
    }
    
    // Refresh button click handler
    document.getElementById('refresh-button').addEventListener('click', () => {
      fetchMetrics();
      fetchVisualizationData();
      fetchAlerts();
      fetchHealth();
    });
    
    // Initial data fetch
    fetchMetrics();
    fetchVisualizationData();
    fetchAlerts();
    fetchHealth();
    
    // Set up automatic refresh
    setInterval(() => {
      fetchMetrics();
      fetchVisualizationData();
      fetchAlerts();
      fetchHealth();
    }, 30000); // Refresh every 30 seconds
  </script>
</body>
</html>
