/**
 * Seed Compliance Knowledge
 * 
 * This script seeds the ComplianceKnowledge collection with initial data
 * for NovaAssistAI to use when responding to user queries.
 */

const mongoose = require('mongoose');
const ComplianceKnowledge = require('../models/complianceKnowledge');
const logger = require('../utils/logger');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  logger.info('MongoDB connected');
  seedDatabase();
})
.catch(err => {
  logger.error('MongoDB connection error:', err);
  process.exit(1);
});

// Seed database with initial compliance knowledge
async function seedDatabase() {
  try {
    // Check if collection already has data
    const count = await ComplianceKnowledge.countDocuments();
    if (count > 0) {
      logger.info(`ComplianceKnowledge collection already has ${count} documents. Skipping seed.`);
      process.exit(0);
    }

    // Seed data
    const knowledgeData = [
      // SOC 2 Knowledge
      {
        title: 'SOC 2 Overview',
        content: 'SOC 2 (System and Organization Controls 2) is a framework developed by the American Institute of CPAs (AICPA) that specifies how organizations should manage customer data. The framework is based on five Trust Services Criteria: Security, Availability, Processing Integrity, Confidentiality, and Privacy. Organizations can choose which criteria to include in their SOC 2 audit based on their business needs.',
        source: 'framework',
        sourceDetails: {
          name: 'AICPA SOC 2',
          version: '2017',
          url: 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/sorhome.html',
          publishedDate: new Date('2017-01-01')
        },
        category: 'policy_management',
        frameworks: ['soc2'],
        controls: [],
        keywords: ['soc2', 'aicpa', 'trust', 'services', 'criteria', 'security', 'availability', 'processing', 'integrity', 'confidentiality', 'privacy', 'audit', 'compliance'],
        relevantPages: ['dashboard', 'frameworks', 'novaassure'],
        priority: 10
      },
      {
        title: 'SOC 2 Trust Services Criteria',
        content: 'The five Trust Services Criteria in SOC 2 are:\n1. Security: Protection against unauthorized access (both physical and logical).\n2. Availability: System availability for operation and use as committed or agreed.\n3. Processing Integrity: System processing is complete, valid, accurate, timely, and authorized.\n4. Confidentiality: Information designated as confidential is protected as committed or agreed.\n5. Privacy: Personal information is collected, used, retained, disclosed, and disposed of in conformity with the commitments in the entity\'s privacy notice and with criteria set forth in Generally Accepted Privacy Principles (GAPP).',
        source: 'framework',
        sourceDetails: {
          name: 'AICPA SOC 2',
          version: '2017',
          url: 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/sorhome.html',
          publishedDate: new Date('2017-01-01')
        },
        category: 'policy_management',
        frameworks: ['soc2'],
        controls: [],
        keywords: ['soc2', 'trust', 'services', 'criteria', 'security', 'availability', 'processing', 'integrity', 'confidentiality', 'privacy'],
        relevantPages: ['frameworks', 'novaassure'],
        priority: 9
      },
      {
        title: 'SOC 2 Compliance Process',
        content: 'The SOC 2 compliance process typically involves the following steps:\n1. Determine which Trust Services Criteria are applicable to your organization.\n2. Perform a readiness assessment to identify gaps in controls.\n3. Implement necessary controls and remediate gaps.\n4. Document policies and procedures.\n5. Collect evidence of control effectiveness.\n6. Engage a CPA firm to perform the audit.\n7. Remediate any issues identified during the audit.\n8. Receive the SOC 2 report.\n9. Maintain compliance through continuous monitoring and periodic reassessment.',
        source: 'best_practice',
        sourceDetails: {
          name: 'NovaFuse Best Practices',
          version: '1.0',
          publishedDate: new Date('2023-01-01')
        },
        category: 'policy_management',
        frameworks: ['soc2'],
        controls: [],
        keywords: ['soc2', 'compliance', 'process', 'audit', 'assessment', 'controls', 'policies', 'procedures', 'evidence', 'report'],
        relevantPages: ['frameworks', 'novaassure'],
        priority: 8
      },

      // HIPAA Knowledge
      {
        title: 'HIPAA Overview',
        content: 'The Health Insurance Portability and Accountability Act (HIPAA) is a US federal law enacted in 1996 that establishes standards for the privacy and security of protected health information (PHI). HIPAA consists of several rules, including the Privacy Rule, Security Rule, Breach Notification Rule, and Omnibus Rule. These rules apply to covered entities (healthcare providers, health plans, and healthcare clearinghouses) and their business associates.',
        source: 'regulation',
        sourceDetails: {
          name: 'HIPAA',
          version: '1996',
          url: 'https://www.hhs.gov/hipaa/index.html',
          publishedDate: new Date('1996-08-21')
        },
        category: 'data_protection',
        frameworks: ['hipaa'],
        controls: [],
        keywords: ['hipaa', 'health', 'insurance', 'portability', 'accountability', 'act', 'phi', 'protected', 'health', 'information', 'privacy', 'security', 'breach', 'notification', 'omnibus', 'covered', 'entities', 'business', 'associates'],
        relevantPages: ['frameworks', 'regulations', 'novaassure'],
        priority: 9
      },
      {
        title: 'HIPAA Security Rule',
        content: 'The HIPAA Security Rule establishes national standards to protect electronic protected health information (ePHI). The Security Rule requires appropriate administrative, physical, and technical safeguards to ensure the confidentiality, integrity, and security of ePHI. The Security Rule is organized into three categories of safeguards: Administrative Safeguards, Physical Safeguards, and Technical Safeguards. Each category contains both required (addressable) and required (required) implementation specifications.',
        source: 'regulation',
        sourceDetails: {
          name: 'HIPAA Security Rule',
          version: '2003',
          url: 'https://www.hhs.gov/hipaa/for-professionals/security/index.html',
          publishedDate: new Date('2003-02-20')
        },
        category: 'data_protection',
        frameworks: ['hipaa'],
        controls: [],
        keywords: ['hipaa', 'security', 'rule', 'ephi', 'electronic', 'protected', 'health', 'information', 'safeguards', 'administrative', 'physical', 'technical', 'confidentiality', 'integrity', 'security'],
        relevantPages: ['frameworks', 'regulations', 'novaassure'],
        priority: 8
      },

      // GDPR Knowledge
      {
        title: 'GDPR Overview',
        content: 'The General Data Protection Regulation (GDPR) is a regulation in EU law on data protection and privacy for all individuals within the European Union and the European Economic Area. It also addresses the export of personal data outside the EU and EEA. The GDPR aims primarily to give control to individuals over their personal data and to simplify the regulatory environment for international business by unifying the regulation within the EU.',
        source: 'regulation',
        sourceDetails: {
          name: 'GDPR',
          version: '2016',
          url: 'https://gdpr.eu/',
          publishedDate: new Date('2016-04-27')
        },
        category: 'data_protection',
        frameworks: ['gdpr'],
        controls: [],
        keywords: ['gdpr', 'general', 'data', 'protection', 'regulation', 'eu', 'european', 'union', 'privacy', 'personal', 'data', 'control', 'individuals', 'international', 'business'],
        relevantPages: ['frameworks', 'regulations', 'novaassure'],
        priority: 9
      },

      // NovaFuse Platform Knowledge
      {
        title: 'NovaFuse Platform Overview',
        content: 'NovaFuse is a modular GRC solution positioned as a "Cyber-Safety" platform that represents the fusion of GRC, IT, and Cybersecurity. The platform consists of 9 Universal components: NovaEdge, NovaConnect, NovaFlow, NovaPulse, NovaSphere, NovaAssure (UCTF), NovaAssistAI, NovaConnect-UAC, and Nova\'s. These components integrate through a unified data model, consistent API layer, shared user context, and cross-component analytics.',
        source: 'internal',
        sourceDetails: {
          name: 'NovaFuse Documentation',
          version: '1.0',
          publishedDate: new Date('2023-01-01')
        },
        category: 'other',
        frameworks: [],
        controls: [],
        keywords: ['novafuse', 'platform', 'overview', 'cyber-safety', 'grc', 'it', 'cybersecurity', 'components', 'universal', 'novaedge', 'novaconnect', 'novaflow', 'novapulse', 'novasphere', 'novaassure', 'uctf', 'novaassistai', 'novaconnect-uac', 'nova'],
        relevantPages: ['dashboard', 'novaassure', 'frameworks', 'regulations'],
        priority: 10
      },
      {
        title: 'NovaAssure (UCTF) Overview',
        content: 'NovaAssure, also known as the Universal Control Testing Framework (UCTF), is a component of the NovaFuse platform that enables continuous validation of controls across different compliance frameworks. It provides capabilities for creating, scheduling, and executing control tests, collecting and verifying evidence, and generating compliance attestations. NovaAssure helps organizations maintain continuous compliance by regularly testing controls and collecting evidence of their effectiveness.',
        source: 'internal',
        sourceDetails: {
          name: 'NovaFuse Documentation',
          version: '1.0',
          publishedDate: new Date('2023-01-01')
        },
        category: 'other',
        frameworks: [],
        controls: [],
        keywords: ['novaassure', 'uctf', 'universal', 'control', 'testing', 'framework', 'continuous', 'validation', 'controls', 'compliance', 'frameworks', 'tests', 'evidence', 'attestations'],
        relevantPages: ['novaassure', 'dashboard'],
        priority: 9
      },
      {
        title: 'Creating a Control Test in NovaAssure',
        content: 'To create a control test in NovaAssure:\n1. Navigate to the NovaAssure dashboard.\n2. Click on "Create Test" button.\n3. Select the compliance framework and control.\n4. Enter a name and description for the test.\n5. Choose the test type (manual, automated, or hybrid).\n6. Set the test frequency (daily, weekly, monthly, quarterly, or annually).\n7. Add test steps that describe how to validate the control.\n8. Define evidence requirements for the test.\n9. If the test is automated or hybrid, configure the automation settings.\n10. Save the test.\n\nOnce created, the test can be executed manually or scheduled to run automatically based on the defined frequency.',
        source: 'internal',
        sourceDetails: {
          name: 'NovaFuse Documentation',
          version: '1.0',
          publishedDate: new Date('2023-01-01')
        },
        category: 'other',
        frameworks: [],
        controls: [],
        keywords: ['novaassure', 'create', 'control', 'test', 'steps', 'framework', 'control', 'name', 'description', 'type', 'frequency', 'steps', 'evidence', 'automation', 'execute', 'schedule'],
        relevantPages: ['novaassure'],
        priority: 8
      },
      {
        title: 'Running a Test in NovaAssure',
        content: 'To run a test in NovaAssure:\n1. Navigate to the NovaAssure dashboard or Tests page.\n2. Find the test you want to run and click "Run Test".\n3. The test will start and show the test steps.\n4. For each step, mark it as Pass, Fail, or Inconclusive based on the results.\n5. Add notes to document your findings.\n6. Upload evidence as required (screenshots, logs, documents, etc.).\n7. Once all steps are completed, click "Complete Test Run".\n8. Review the test results and add any final notes.\n9. Submit the test run.\n\nThe test results and evidence will be stored and can be used for compliance reporting and attestations.',
        source: 'internal',
        sourceDetails: {
          name: 'NovaFuse Documentation',
          version: '1.0',
          publishedDate: new Date('2023-01-01')
        },
        category: 'other',
        frameworks: [],
        controls: [],
        keywords: ['novaassure', 'run', 'test', 'steps', 'pass', 'fail', 'inconclusive', 'notes', 'evidence', 'upload', 'complete', 'results', 'submit', 'reporting', 'attestations'],
        relevantPages: ['novaassure'],
        priority: 8
      },
      {
        title: 'Evidence Collection in NovaAssure',
        content: 'Evidence collection is a critical part of the compliance process. In NovaAssure, evidence can be collected during test execution or uploaded separately. Types of evidence include:\n1. Documents (policies, procedures, contracts, etc.)\n2. Screenshots (of system configurations, settings, etc.)\n3. Logs (system logs, audit logs, etc.)\n4. Data exports (from systems, databases, etc.)\n5. Attestations (signed statements from responsible parties)\n\nEvidence is securely stored and linked to the relevant tests and controls. It can be verified by authorized users and used to demonstrate compliance during audits. NovaAssure also tracks evidence metadata, including who uploaded it, when it was uploaded, and its verification status.',
        source: 'internal',
        sourceDetails: {
          name: 'NovaFuse Documentation',
          version: '1.0',
          publishedDate: new Date('2023-01-01')
        },
        category: 'other',
        frameworks: [],
        controls: [],
        keywords: ['novaassure', 'evidence', 'collection', 'compliance', 'documents', 'screenshots', 'logs', 'data', 'exports', 'attestations', 'stored', 'linked', 'tests', 'controls', 'verified', 'audits', 'metadata'],
        relevantPages: ['novaassure'],
        priority: 7
      },
      {
        title: 'Compliance Attestations in NovaAssure',
        content: 'Compliance attestations in NovaAssure are formal declarations that specific controls have been tested and are operating effectively. To create an attestation:\n1. Navigate to the Attestations page in NovaAssure.\n2. Click "Create Attestation".\n3. Select the framework and controls to include in the attestation.\n4. Add a name and description for the attestation.\n5. Select the evidence to include as support for the attestation.\n6. Add any notes or context.\n7. Submit the attestation for approval.\n8. An authorized approver reviews and approves or rejects the attestation.\n\nApproved attestations can be used to demonstrate compliance to auditors, regulators, or business partners. They include details about who attested, who approved, when it was approved, and links to the supporting evidence.',
        source: 'internal',
        sourceDetails: {
          name: 'NovaFuse Documentation',
          version: '1.0',
          publishedDate: new Date('2023-01-01')
        },
        category: 'other',
        frameworks: [],
        controls: [],
        keywords: ['novaassure', 'compliance', 'attestations', 'declarations', 'controls', 'tested', 'operating', 'effectively', 'create', 'framework', 'controls', 'evidence', 'notes', 'context', 'approval', 'approver', 'auditors', 'regulators', 'business', 'partners'],
        relevantPages: ['novaassure'],
        priority: 7
      },

      // NovaAssistAI Knowledge
      {
        title: 'Using NovaAssistAI',
        content: 'NovaAssistAI is an intelligent assistant for the NovaFuse platform that helps users with compliance-related questions and tasks. You can use NovaAssistAI to:\n1. Get information about compliance frameworks, controls, and regulations.\n2. Navigate the NovaFuse platform.\n3. Get help with compliance testing and evidence collection.\n4. Receive guidance on regulatory requirements.\n5. Execute actions like creating tests, running tests, and generating reports.\n\nTo use NovaAssistAI, simply type your question or request in the chat widget at the bottom right of the screen. NovaAssistAI will provide a response and may suggest follow-up actions or questions.',
        source: 'internal',
        sourceDetails: {
          name: 'NovaFuse Documentation',
          version: '1.0',
          publishedDate: new Date('2023-01-01')
        },
        category: 'other',
        frameworks: [],
        controls: [],
        keywords: ['novaassistai', 'assistant', 'novafuse', 'platform', 'compliance', 'questions', 'tasks', 'frameworks', 'controls', 'regulations', 'navigate', 'testing', 'evidence', 'collection', 'guidance', 'regulatory', 'requirements', 'actions', 'creating', 'running', 'tests', 'generating', 'reports', 'chat', 'widget'],
        relevantPages: ['dashboard', 'novaassure', 'frameworks', 'regulations'],
        priority: 8
      }
    ];

    // Insert data
    await ComplianceKnowledge.insertMany(knowledgeData);
    logger.info(`Successfully seeded ${knowledgeData.length} compliance knowledge documents`);
    process.exit(0);
  } catch (error) {
    logger.error('Error seeding compliance knowledge:', error);
    process.exit(1);
  }
}
