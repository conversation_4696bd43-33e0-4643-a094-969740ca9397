/**
 * Train and Test CSDE-Enhanced ML Model
 *
 * This script trains and tests the CSDE-Enhanced ML model.
 */

const fs = require('fs');
const path = require('path');
const CSDEEnhancedML = require('./csde_enhanced_ml');

// Configuration
const config = {
  dataSize: 100, // Generate 100 samples
  frameworks: ['NIST', 'GDPR', 'HIPAA'],
  outputDir: path.join(__dirname, 'data'),
  modelDir: path.join(__dirname, 'models'),
  epochs: 100,
  learningRate: 0.01,
  validationSplit: 0.2,
  testSplit: 0.1
};

// Create output directories if they don't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

if (!fs.existsSync(config.modelDir)) {
  fs.mkdirSync(config.modelDir, { recursive: true });
}

// Initialize CSDE-Enhanced ML
const csdeML = new CSDEEnhancedML({
  epochs: config.epochs,
  learningRate: config.learningRate,
  validationSplit: config.validationSplit
});

console.log(`Generating ${config.dataSize} enhanced training samples...`);

// Generate enhanced training data
const allData = csdeML.generateEnhancedData(config.dataSize, config.frameworks);

// Split data into training and test sets
const testSize = Math.floor(allData.length * config.testSplit);
const testData = allData.slice(0, testSize);
const trainingData = allData.slice(testSize);

console.log(`Split data into ${trainingData.length} training and ${testData.length} test samples.`);

// Save data
fs.writeFileSync(
  path.join(config.outputDir, 'training_data.json'),
  JSON.stringify(trainingData, null, 2)
);

fs.writeFileSync(
  path.join(config.outputDir, 'test_data.json'),
  JSON.stringify(testData, null, 2)
);

console.log('Training CSDE-Enhanced ML model...');

// Train model
const trainingResult = csdeML.train(trainingData);

console.log(`Training completed with final accuracy: ${(trainingResult.finalAccuracy * 100).toFixed(2)}%`);

// Save model
fs.writeFileSync(
  path.join(config.modelDir, 'csde_ml_model.json'),
  JSON.stringify({
    model: trainingResult.model,
    metrics: trainingResult.metrics,
    config: {
      epochs: config.epochs,
      learningRate: config.learningRate,
      validationSplit: config.validationSplit,
      frameworks: config.frameworks
    },
    trainedAt: new Date().toISOString()
  }, null, 2)
);

console.log(`Model saved to ${path.join(config.modelDir, 'csde_ml_model.json')}`);

// Test model
console.log('Testing model on test data...');

const testResults = {
  predictions: [],
  metrics: {
    accuracy: 0,
    averageError: 0,
    maxError: 0,
    minError: Number.MAX_VALUE
  }
};

let totalCorrect = 0;
let totalError = 0;

// Test on each sample
for (const sample of testData) {
  // Make prediction
  const result = csdeML.predict(sample.input);

  // Calculate error
  const expectedValue = sample.output.csdeValue;
  const predictedValue = result.prediction;
  const error = Math.abs(expectedValue - predictedValue) / expectedValue;

  // Update metrics
  totalError += error;
  testResults.metrics.maxError = Math.max(testResults.metrics.maxError, error);
  testResults.metrics.minError = Math.min(testResults.metrics.minError, error);

  // Check if prediction is within 10% of actual value
  const isCorrect = error < 0.1;
  if (isCorrect) {
    totalCorrect++;
  }

  // Store prediction
  testResults.predictions.push({
    expected: expectedValue,
    predicted: predictedValue,
    error: error * 100, // Convert to percentage
    isCorrect,
    confidence: result.confidence,
    explanation: result.explanation
  });
}

// Calculate final metrics
testResults.metrics.accuracy = totalCorrect / testData.length;
testResults.metrics.averageError = totalError / testData.length;

console.log(`Test results: Accuracy: ${(testResults.metrics.accuracy * 100).toFixed(2)}%, Average Error: ${(testResults.metrics.averageError * 100).toFixed(2)}%`);

// Save test results
fs.writeFileSync(
  path.join(config.outputDir, 'test_results.json'),
  JSON.stringify(testResults, null, 2)
);

console.log(`Test results saved to ${path.join(config.outputDir, 'test_results.json')}`);

// Generate HTML report
const reportHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE-Enhanced ML Test Results</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    .chart-container {
      width: 80%;
      margin: 20px auto;
      height: 400px;
    }
    h1, h2 {
      text-align: center;
    }
    .metrics {
      width: 80%;
      margin: 20px auto;
      border-collapse: collapse;
    }
    .metrics th, .metrics td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    .metrics th {
      background-color: #f2f2f2;
    }
    .comparison {
      display: flex;
      justify-content: space-around;
      margin: 20px 0;
    }
    .comparison-item {
      text-align: center;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      width: 30%;
    }
    .comparison-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .improvement {
      color: green;
    }
  </style>
</head>
<body>
  <h1>CSDE-Enhanced ML Test Results</h1>

  <div class="comparison">
    <div class="comparison-item">
      <h3>Original ML Accuracy</h3>
      <div class="comparison-value">6.00%</div>
    </div>
    <div class="comparison-item">
      <h3>CSDE-Enhanced ML Accuracy</h3>
      <div class="comparison-value">${(testResults.metrics.accuracy * 100).toFixed(2)}%</div>
      <div class="improvement">
        ${(testResults.metrics.accuracy * 100 > 6) ?
          `+${((testResults.metrics.accuracy * 100 - 6) / 6 * 100).toFixed(2)}% improvement` :
          'No improvement'}
      </div>
    </div>
  </div>

  <div class="comparison">
    <div class="comparison-item">
      <h3>Original ML Error</h3>
      <div class="comparison-value">221.55%</div>
    </div>
    <div class="comparison-item">
      <h3>CSDE-Enhanced ML Error</h3>
      <div class="comparison-value">${(testResults.metrics.averageError * 100).toFixed(2)}%</div>
      <div class="improvement">
        ${(testResults.metrics.averageError * 100 < 221.55) ?
          `-${((221.55 - testResults.metrics.averageError * 100) / 221.55 * 100).toFixed(2)}% reduction` :
          'No improvement'}
      </div>
    </div>
  </div>

  <h2>Test Metrics</h2>
  <table class="metrics">
    <tr>
      <th>Metric</th>
      <th>Value</th>
    </tr>
    <tr>
      <td>Accuracy</td>
      <td>${(testResults.metrics.accuracy * 100).toFixed(2)}%</td>
    </tr>
    <tr>
      <td>Average Error</td>
      <td>${(testResults.metrics.averageError * 100).toFixed(2)}%</td>
    </tr>
    <tr>
      <td>Max Error</td>
      <td>${(testResults.metrics.maxError * 100).toFixed(2)}%</td>
    </tr>
    <tr>
      <td>Min Error</td>
      <td>${(testResults.metrics.minError * 100).toFixed(2)}%</td>
    </tr>
    <tr>
      <td>Test Samples</td>
      <td>${testData.length}</td>
    </tr>
  </table>

  <div class="chart-container">
    <canvas id="accuracyChart"></canvas>
  </div>

  <div class="chart-container">
    <canvas id="errorDistributionChart"></canvas>
  </div>

  <script>
    // Accuracy chart
    const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
    new Chart(accuracyCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(trainingResult.metrics.epochs)},
        datasets: [
          {
            label: 'Training Accuracy',
            data: ${JSON.stringify(trainingResult.metrics.trainAccuracy.map(a => a * 100))},
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1,
            fill: false
          },
          {
            label: 'Validation Accuracy',
            data: ${JSON.stringify(trainingResult.metrics.validationAccuracy.map(a => a * 100))},
            borderColor: 'rgb(255, 99, 132)',
            tension: 0.1,
            fill: false
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Accuracy (%)'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Epoch'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Training and Validation Accuracy'
          }
        }
      }
    });

    // Error distribution chart
    const errors = ${JSON.stringify(testResults.predictions.map(p => p.error))};
    const errorBins = [0, 1, 2, 5, 10, 20, 50, 100];
    const errorCounts = Array(errorBins.length).fill(0);

    errors.forEach(error => {
      for (let i = 0; i < errorBins.length; i++) {
        if (error <= errorBins[i] || i === errorBins.length - 1) {
          errorCounts[i]++;
          break;
        }
      }
    });

    const errorCtx = document.getElementById('errorDistributionChart').getContext('2d');
    new Chart(errorCtx, {
      type: 'bar',
      data: {
        labels: errorBins.map((bin, index) => {
          if (index === 0) return '0%';
          const prevBin = errorBins[index - 1];
          return \`\${prevBin}% - \${bin}%\`;
        }),
        datasets: [{
          label: 'Number of Predictions',
          data: errorCounts,
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Predictions'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Error Range'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Distribution of Prediction Errors'
          }
        }
      }
    });
  </script>
</body>
</html>
`;

fs.writeFileSync(
  path.join(config.outputDir, 'test_report.html'),
  reportHtml
);

console.log(`Test report saved to ${path.join(config.outputDir, 'test_report.html')}`);
