/**
 * Privacy Notice Service
 * 
 * This service handles business logic for privacy notices.
 */

const { PrivacyNotice } = require('../models');
const { logger } = require('../utils/logger');

/**
 * Get all privacy notices with pagination and filtering
 * 
 * @param {Object} options - Query options
 * @param {number} options.page - Page number
 * @param {number} options.limit - Items per page
 * @param {Object} options.filter - Filter criteria
 * @param {Object} options.sort - Sort criteria
 * @returns {Promise<Object>} - Paginated results
 */
async function getAllPrivacyNotices(options) {
  try {
    const { page = 1, limit = 10, filter = {}, sort = { effectiveDate: -1 } } = options;
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const privacyNotices = await PrivacyNotice
      .find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await PrivacyNotice.countDocuments(filter);
    
    return {
      data: privacyNotices,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error(`Error getting privacy notices: ${error.message}`);
    throw error;
  }
}

/**
 * Get a privacy notice by ID
 * 
 * @param {string} id - Privacy notice ID
 * @returns {Promise<Object>} - Privacy notice
 */
async function getPrivacyNoticeById(id) {
  try {
    const privacyNotice = await PrivacyNotice.findById(id);
    
    if (!privacyNotice) {
      const error = new Error('Privacy notice not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    return privacyNotice;
  } catch (error) {
    logger.error(`Error getting privacy notice by ID: ${error.message}`);
    throw error;
  }
}

/**
 * Get the latest version of a privacy notice by type and audience
 * 
 * @param {string} type - Privacy notice type
 * @param {string} audience - Target audience
 * @param {string} language - Language code
 * @returns {Promise<Object>} - Latest privacy notice
 */
async function getLatestPrivacyNotice(type, audience, language) {
  try {
    const filter = { type, audience };
    
    if (language) {
      filter.language = language;
    }
    
    const privacyNotice = await PrivacyNotice
      .findOne(filter)
      .sort({ version: -1 })
      .limit(1);
    
    if (!privacyNotice) {
      const error = new Error('Privacy notice not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    return privacyNotice;
  } catch (error) {
    logger.error(`Error getting latest privacy notice: ${error.message}`);
    throw error;
  }
}

/**
 * Create a new privacy notice
 * 
 * @param {Object} noticeData - Privacy notice data
 * @returns {Promise<Object>} - Created privacy notice
 */
async function createPrivacyNotice(noticeData) {
  try {
    // Check if we need to auto-increment the version
    if (noticeData.type && noticeData.audience && !noticeData.version) {
      const latestVersion = await getLatestVersionNumber(noticeData.type, noticeData.audience);
      noticeData.version = latestVersion + 1;
    }
    
    // Set default status if not provided
    if (!noticeData.status) {
      noticeData.status = 'draft';
    }
    
    // Create the privacy notice
    const privacyNotice = new PrivacyNotice(noticeData);
    await privacyNotice.save();
    
    logger.info(`Created new privacy notice with ID: ${privacyNotice._id}`);
    return privacyNotice;
  } catch (error) {
    logger.error(`Error creating privacy notice: ${error.message}`);
    throw error;
  }
}

/**
 * Update a privacy notice
 * 
 * @param {string} id - Privacy notice ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} - Updated privacy notice
 */
async function updatePrivacyNotice(id, updateData) {
  try {
    const privacyNotice = await getPrivacyNoticeById(id);
    
    // Prevent updating published notices
    if (privacyNotice.status === 'published' && !updateData.status) {
      const error = new Error('Cannot update a published privacy notice');
      error.name = 'ValidationError';
      throw error;
    }
    
    // Update status-related dates
    if (updateData.status && updateData.status !== privacyNotice.status) {
      if (updateData.status === 'published' && !updateData.effectiveDate) {
        updateData.effectiveDate = new Date();
      }
    }
    
    // Update only the fields that are provided in the update data
    Object.keys(updateData).forEach(key => {
      privacyNotice[key] = updateData[key];
    });
    
    await privacyNotice.save();
    
    logger.info(`Updated privacy notice with ID: ${id}`);
    return privacyNotice;
  } catch (error) {
    logger.error(`Error updating privacy notice: ${error.message}`);
    throw error;
  }
}

/**
 * Delete a privacy notice
 * 
 * @param {string} id - Privacy notice ID
 * @returns {Promise<Object>} - Deleted privacy notice
 */
async function deletePrivacyNotice(id) {
  try {
    const privacyNotice = await getPrivacyNoticeById(id);
    
    // Prevent deleting published notices
    if (privacyNotice.status === 'published') {
      const error = new Error('Cannot delete a published privacy notice');
      error.name = 'ValidationError';
      throw error;
    }
    
    await privacyNotice.remove();
    
    logger.info(`Deleted privacy notice with ID: ${id}`);
    return privacyNotice;
  } catch (error) {
    logger.error(`Error deleting privacy notice: ${error.message}`);
    throw error;
  }
}

/**
 * Publish a privacy notice
 * 
 * @param {string} id - Privacy notice ID
 * @param {Date} effectiveDate - Date when the notice becomes effective
 * @returns {Promise<Object>} - Published privacy notice
 */
async function publishPrivacyNotice(id, effectiveDate) {
  try {
    const privacyNotice = await getPrivacyNoticeById(id);
    
    // Prevent publishing already published notices
    if (privacyNotice.status === 'published') {
      const error = new Error('Privacy notice is already published');
      error.name = 'ValidationError';
      throw error;
    }
    
    // Update status and effective date
    privacyNotice.status = 'published';
    privacyNotice.effectiveDate = effectiveDate || new Date();
    privacyNotice.publishedDate = new Date();
    
    await privacyNotice.save();
    
    logger.info(`Published privacy notice with ID: ${id}`);
    return privacyNotice;
  } catch (error) {
    logger.error(`Error publishing privacy notice: ${error.message}`);
    throw error;
  }
}

/**
 * Archive a privacy notice
 * 
 * @param {string} id - Privacy notice ID
 * @returns {Promise<Object>} - Archived privacy notice
 */
async function archivePrivacyNotice(id) {
  try {
    const privacyNotice = await getPrivacyNoticeById(id);
    
    // Only published notices can be archived
    if (privacyNotice.status !== 'published') {
      const error = new Error('Only published privacy notices can be archived');
      error.name = 'ValidationError';
      throw error;
    }
    
    // Update status
    privacyNotice.status = 'archived';
    privacyNotice.archivedDate = new Date();
    
    await privacyNotice.save();
    
    logger.info(`Archived privacy notice with ID: ${id}`);
    return privacyNotice;
  } catch (error) {
    logger.error(`Error archiving privacy notice: ${error.message}`);
    throw error;
  }
}

/**
 * Get the latest version number for a privacy notice type and audience
 * 
 * @param {string} type - Privacy notice type
 * @param {string} audience - Target audience
 * @returns {Promise<number>} - Latest version number
 */
async function getLatestVersionNumber(type, audience) {
  try {
    const latestNotice = await PrivacyNotice
      .findOne({ type, audience })
      .sort({ version: -1 })
      .limit(1);
    
    return latestNotice ? latestNotice.version : 0;
  } catch (error) {
    logger.error(`Error getting latest version number: ${error.message}`);
    throw error;
  }
}

/**
 * Compare two privacy notice versions
 * 
 * @param {string} id1 - First privacy notice ID
 * @param {string} id2 - Second privacy notice ID
 * @returns {Promise<Object>} - Comparison result
 */
async function comparePrivacyNotices(id1, id2) {
  try {
    const notice1 = await getPrivacyNoticeById(id1);
    const notice2 = await getPrivacyNoticeById(id2);
    
    // Ensure notices are of the same type and audience
    if (notice1.type !== notice2.type || notice1.audience !== notice2.audience) {
      const error = new Error('Cannot compare privacy notices of different types or audiences');
      error.name = 'ValidationError';
      throw error;
    }
    
    // Compare content and identify differences
    const differences = [];
    
    // Compare sections
    if (notice1.sections && notice2.sections) {
      notice1.sections.forEach((section1, index) => {
        const section2 = notice2.sections[index];
        
        if (!section2 || section1.title !== section2.title || section1.content !== section2.content) {
          differences.push({
            type: 'section',
            index,
            old: section2 ? section2 : null,
            new: section1
          });
        }
      });
      
      // Check for removed sections
      if (notice2.sections.length > notice1.sections.length) {
        for (let i = notice1.sections.length; i < notice2.sections.length; i++) {
          differences.push({
            type: 'section',
            index: i,
            old: notice2.sections[i],
            new: null
          });
        }
      }
    }
    
    // Compare metadata
    const metadataFields = ['title', 'description', 'language', 'effectiveDate', 'expirationDate'];
    
    metadataFields.forEach(field => {
      if (notice1[field] !== notice2[field]) {
        differences.push({
          type: 'metadata',
          field,
          old: notice2[field],
          new: notice1[field]
        });
      }
    });
    
    return {
      notice1: {
        id: notice1._id,
        version: notice1.version,
        status: notice1.status
      },
      notice2: {
        id: notice2._id,
        version: notice2.version,
        status: notice2.status
      },
      differences
    };
  } catch (error) {
    logger.error(`Error comparing privacy notices: ${error.message}`);
    throw error;
  }
}

module.exports = {
  getAllPrivacyNotices,
  getPrivacyNoticeById,
  getLatestPrivacyNotice,
  createPrivacyNotice,
  updatePrivacyNotice,
  deletePrivacyNotice,
  publishPrivacyNotice,
  archivePrivacyNotice,
  getLatestVersionNumber,
  comparePrivacyNotices
};
