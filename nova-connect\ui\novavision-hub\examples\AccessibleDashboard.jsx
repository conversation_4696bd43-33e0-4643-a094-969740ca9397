/**
 * Accessible Dashboard Example
 * 
 * This example demonstrates how to use the accessibility components to create a WCAG 2.1 compliant dashboard.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  DataTable,
  GraphVisualization,
  MetricsCard,
  ChartCard,
  StatusIndicator,
  TabPanel,
  ResponsiveLayout,
  AccessibleIcon,
  AccessibleTooltip,
  AnnouncementRegion,
  FocusableContainer,
  SkipLink
} from '../components';

/**
 * Accessible Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Accessible Dashboard component
 */
const AccessibleDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [announcement, setAnnouncement] = useState('');
  const [highContrastMode, setHighContrastMode] = useState(false);
  
  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        if (enableLogging) {
          console.log('Fetching dashboard data...');
        }
        
        // Simulate API call to fetch dashboard data
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Sample dashboard data
        const data = {
          complianceScore: 87,
          securityScore: 92,
          identityScore: 78,
          alerts: [
            { id: 'alert-001', severity: 'critical', type: 'compliance', message: 'GDPR compliance violation detected', timestamp: '2023-06-15T10:30:00Z', status: 'open' },
            { id: 'alert-002', severity: 'high', type: 'security', message: 'Unusual authentication pattern detected', timestamp: '2023-06-15T09:45:00Z', status: 'investigating' },
            { id: 'alert-003', severity: 'medium', type: 'identity', message: 'User access review pending', timestamp: '2023-06-14T16:20:00Z', status: 'open' }
          ],
          complianceChart: {
            labels: ['GDPR', 'HIPAA', 'PCI DSS', 'SOC 2', 'ISO 27001'],
            datasets: [
              {
                label: 'Compliance Score',
                data: [92, 85, 78, 90, 88],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
              }
            ]
          }
        };
        
        setDashboardData(data);
        setLoading(false);
        setAnnouncement('Dashboard data loaded successfully');
        
        if (enableLogging) {
          console.log('Dashboard data fetched successfully');
        }
      } catch (error) {
        console.error('Error fetching dashboard data', error);
        setLoading(false);
        setAnnouncement('Error loading dashboard data');
      }
    };
    
    fetchDashboardData();
  }, [enableLogging]);
  
  // Refresh dashboard data
  const handleRefresh = async () => {
    setLoading(true);
    setAnnouncement('Refreshing dashboard data');
    
    try {
      if (enableLogging) {
        console.log('Refreshing dashboard data...');
      }
      
      // Simulate API call to refresh dashboard data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update dashboard data with new values
      setDashboardData(prevData => ({
        ...prevData,
        complianceScore: Math.floor(Math.random() * 20) + 80,
        securityScore: Math.floor(Math.random() * 20) + 80,
        identityScore: Math.floor(Math.random() * 20) + 70
      }));
      
      setLoading(false);
      setAnnouncement('Dashboard data refreshed successfully');
      
      if (enableLogging) {
        console.log('Dashboard data refreshed successfully');
      }
    } catch (error) {
      console.error('Error refreshing dashboard data', error);
      setLoading(false);
      setAnnouncement('Error refreshing dashboard data');
    }
  };
  
  // Toggle high contrast mode
  const toggleHighContrastMode = () => {
    setHighContrastMode(!highContrastMode);
    setAnnouncement(`High contrast mode ${!highContrastMode ? 'enabled' : 'disabled'}`);
  };
  
  // Render status indicator for alert severity
  const renderAlertSeverity = (severity) => {
    const severityMap = {
      critical: { status: 'error', label: 'Critical', pulse: true },
      high: { status: 'error', label: 'High' },
      medium: { status: 'warning', label: 'Medium' },
      low: { status: 'info', label: 'Low' }
    };
    
    const { status, label, pulse } = severityMap[severity] || { status: 'neutral', label: severity };
    
    return (
      <StatusIndicator
        status={status}
        label={label}
        pulse={pulse}
      />
    );
  };
  
  // Define tabs for the dashboard
  const dashboardTabs = [
    {
      id: 'overview',
      label: 'Overview',
      content: (
        <div className="space-y-6">
          {/* Metrics Cards */}
          <ResponsiveLayout
            layouts={{
              xs: 1,
              sm: 1,
              md: 3,
              lg: 3,
              xl: 3
            }}
            gap={4}
          >
            <MetricsCard
              title="Compliance Score"
              metrics={[
                {
                  label: 'Overall Score',
                  value: dashboardData?.complianceScore || 0,
                  suffix: '%',
                  trend: 5,
                  trendDirection: 'up',
                  color: highContrastMode ? 'text-black' : 'text-blue-500',
                  description: 'Compliance score across all frameworks'
                }
              ]}
              columns={1}
              loading={loading}
            />
            
            <MetricsCard
              title="Security Score"
              metrics={[
                {
                  label: 'Overall Score',
                  value: dashboardData?.securityScore || 0,
                  suffix: '%',
                  trend: 2,
                  trendDirection: 'up',
                  color: highContrastMode ? 'text-black' : 'text-green-500',
                  description: 'Security score across all systems'
                }
              ]}
              columns={1}
              loading={loading}
            />
            
            <MetricsCard
              title="Identity Score"
              metrics={[
                {
                  label: 'Overall Score',
                  value: dashboardData?.identityScore || 0,
                  suffix: '%',
                  trend: -3,
                  trendDirection: 'down',
                  color: highContrastMode ? 'text-black' : 'text-purple-500',
                  description: 'Identity management score'
                }
              ]}
              columns={1}
              loading={loading}
            />
          </ResponsiveLayout>
          
          {/* Chart */}
          <ChartCard
            title="Compliance by Framework"
            chartType="radar"
            data={dashboardData?.complianceChart || { labels: [], datasets: [] }}
            loading={loading}
            collapsible={true}
            onRefresh={handleRefresh}
          />
        </div>
      )
    },
    {
      id: 'alerts',
      label: 'Alerts',
      content: (
        <DashboardCard
          title="Recent Alerts"
          collapsible={true}
          onRefresh={handleRefresh}
          loading={loading}
        >
          <DataTable
            columns={[
              { field: 'severity', header: 'Severity', render: (value) => renderAlertSeverity(value) },
              { field: 'type', header: 'Type', render: (value) => value.charAt(0).toUpperCase() + value.slice(1) },
              { field: 'message', header: 'Message' },
              { field: 'timestamp', header: 'Timestamp', render: (value) => new Date(value).toLocaleString() },
              { field: 'status', header: 'Status', render: (value) => (
                <StatusIndicator
                  status={value === 'open' ? 'error' : value === 'investigating' ? 'warning' : 'success'}
                  label={value.charAt(0).toUpperCase() + value.slice(1)}
                />
              )}
            ]}
            data={dashboardData?.alerts || []}
            loading={loading}
            emptyMessage="No alerts found"
          />
        </DashboardCard>
      )
    }
  ];
  
  return (
    <div className={`space-y-6 ${highContrastMode ? 'high-contrast' : ''}`}>
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Announcement region for screen readers */}
      <AnnouncementRegion message={announcement} />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900" id="dashboard-title">
          Accessible Dashboard
        </h1>
        
        <FocusableContainer orientation="horizontal">
          {/* High contrast mode toggle */}
          <AccessibleTooltip content="Toggle high contrast mode">
            <button
              className={`
                p-2 rounded-md mr-2
                ${highContrastMode ? 'bg-black text-white' : 'bg-gray-200 text-gray-700'}
                hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500
              `}
              onClick={toggleHighContrastMode}
              aria-pressed={highContrastMode}
            >
              <AccessibleIcon
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 01-6-6c0-1.4.5-2.7 1.3-3.7A7.97 7.97 0 0110 4a7.97 7.97 0 014.7 1.5c.8 1 1.3 2.3 1.3 3.7a6 6 0 01-6 6z" />
                  </svg>
                }
                label="Toggle high contrast mode"
              />
            </button>
          </AccessibleTooltip>
          
          {/* Refresh button */}
          <AccessibleTooltip content="Refresh dashboard data">
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
              onClick={handleRefresh}
              disabled={loading}
              aria-busy={loading}
            >
              <AccessibleIcon
                icon={
                  loading ? (
                    <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                    </svg>
                  )
                }
                label={loading ? 'Refreshing...' : 'Refresh Dashboard'}
              />
              <span className="ml-2">{loading ? 'Refreshing...' : 'Refresh'}</span>
            </button>
          </AccessibleTooltip>
        </FocusableContainer>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={dashboardTabs}
          defaultTab="overview"
          variant="pills"
          onTabChange={(tabId) => {
            setActiveTab(tabId);
            setAnnouncement(`Switched to ${tabId} tab`);
          }}
        />
      </main>
    </div>
  );
};

AccessibleDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

export default AccessibleDashboard;
