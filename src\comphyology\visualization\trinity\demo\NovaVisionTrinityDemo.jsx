import React, { useState, useEffect } from 'react';
import NovaVisionTrinityVisualization from '../NovaVisionTrinityVisualization';
import MockNovaVision from '../mock/MockNovaVision';
import { UniversalRippleStack } from '../../../universal_ripple';
import './NovaVisionTrinityDemo.css';

/**
 * Mock NovaConnect
 */
class MockNovaConnect {
  constructor() {
    this.topics = new Map();
    this.subscribers = new Map();
    console.log('MockNovaConnect initialized');
  }
  
  async publish(topic, message) {
    console.log(`[NovaConnect] Publishing to topic: ${topic}`);
    
    // Store message
    if (!this.topics.has(topic)) {
      this.topics.set(topic, []);
    }
    
    this.topics.get(topic).push({
      message,
      timestamp: new Date()
    });
    
    // Notify subscribers
    if (this.subscribers.has(topic)) {
      for (const callback of this.subscribers.get(topic)) {
        try {
          callback(message, topic);
        } catch (error) {
          console.error(`Error notifying subscriber for topic ${topic}:`, error);
        }
      }
    }
    
    return Promise.resolve();
  }
  
  async subscribe(topic, callback) {
    console.log(`[NovaConnect] Subscribing to topic: ${topic}`);
    
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, []);
    }
    
    this.subscribers.get(topic).push(callback);
    
    return Promise.resolve();
  }
  
  async unsubscribe(topic, callback) {
    console.log(`[NovaConnect] Unsubscribing from topic: ${topic}`);
    
    if (this.subscribers.has(topic)) {
      if (callback) {
        const index = this.subscribers.get(topic).indexOf(callback);
        
        if (index !== -1) {
          this.subscribers.get(topic).splice(index, 1);
        }
      } else {
        this.subscribers.delete(topic);
      }
    }
    
    return Promise.resolve();
  }
}

/**
 * Mock NovaThink
 */
class MockNovaThink {
  constructor() {
    this.decisions = [];
    this.beforeDecisionCallbacks = [];
    this.afterDecisionCallbacks = [];
    console.log('MockNovaThink initialized');
  }
  
  makeDecision(context) {
    console.log(`[NovaThink] Making decision with context:`, context);
    
    // Call before decision callbacks
    let enhancedContext = { ...context };
    
    for (const callback of this.beforeDecisionCallbacks) {
      enhancedContext = callback(enhancedContext);
    }
    
    // Make decision
    const decision = {
      id: Math.random().toString(36).substring(2, 15),
      context: enhancedContext,
      result: {
        action: 'approve',
        confidence: 0.85,
        reasoning: 'Decision based on context analysis'
      },
      fairness: 0.9,
      transparency: 0.8,
      ethicalTensor: 0.75,
      accountability: 0.85,
      timestamp: new Date()
    };
    
    // Store decision
    this.decisions.push(decision);
    
    // Call after decision callbacks
    let enhancedDecision = { ...decision };
    
    for (const callback of this.afterDecisionCallbacks) {
      enhancedDecision = callback(enhancedDecision);
    }
    
    return enhancedDecision;
  }
  
  onBeforeDecision(callback) {
    this.beforeDecisionCallbacks.push(callback);
  }
  
  offBeforeDecision(callback) {
    const index = this.beforeDecisionCallbacks.indexOf(callback);
    
    if (index !== -1) {
      this.beforeDecisionCallbacks.splice(index, 1);
    }
  }
  
  onAfterDecision(callback) {
    this.afterDecisionCallbacks.push(callback);
  }
  
  offAfterDecision(callback) {
    const index = this.afterDecisionCallbacks.indexOf(callback);
    
    if (index !== -1) {
      this.afterDecisionCallbacks.splice(index, 1);
    }
  }
}

/**
 * NovaVision Trinity Demo Component
 */
const NovaVisionTrinityDemo = () => {
  const [novaVision, setNovaVision] = useState(null);
  const [rippleStack, setRippleStack] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [demoMode, setDemoMode] = useState('dashboard'); // 'dashboard' or 'visualization'
  const [enableLogging, setEnableLogging] = useState(false);
  
  // Initialize NovaVision and Universal Ripple Stack
  useEffect(() => {
    try {
      // Create mock NovaVision
      const mockNovaVision = new MockNovaVision({
        enableLogging
      });
      
      // Create mock components
      const novaConnect = new MockNovaConnect();
      const novaThink = new MockNovaThink();
      
      // Initialize Universal Ripple Stack
      console.log('Initializing Universal Ripple Stack...');
      const stack = new UniversalRippleStack({
        novaConnect,
        novaThink,
        enableLogging,
        autoStart: false
      });
      
      // Start the stack
      console.log('Starting Universal Ripple Stack...');
      stack.start().then(() => {
        setNovaVision(mockNovaVision);
        setRippleStack(stack);
        setIsLoading(false);
        
        // Register some data
        console.log('Registering data...');
        
        const threatData = {
          type: 'threat',
          entropy: 0.7,
          phase: Math.PI / 2,
          certainty: 0.8,
          direction: Math.PI / 4,
          magnitude: 0.6
        };
        
        const complianceData = {
          type: 'compliance',
          complexity: 0.6,
          adaptability: 0.7,
          resonance: 0.8,
          environmentalPressure: 0.5
        };
        
        const decisionData = {
          type: 'decision',
          fairness: 0.8,
          transparency: 0.7,
          ethicalTensor: 0.9,
          accountability: 0.8
        };
        
        stack.registerData(threatData);
        stack.registerData(complianceData);
        stack.registerData(decisionData);
        
        // Publish initial data to NovaVision
        mockNovaVision.publishDataSourceData('universal-ripple-stack', {
          quantum: {
            engine: { stateCount: 5 },
            resonance: { connectionCount: 3 },
            field: true
          },
          components: {
            novaConnect: true,
            novaThink: true,
            novaPulse: true,
            novaFlow: true
          },
          rippleEffect: {
            layer1: true,
            layer2: true,
            layer3: true
          },
          timestamp: new Date()
        }).catch(err => {
          console.error('Error publishing initial data:', err);
        });
      }).catch(err => {
        console.error('Error starting Universal Ripple Stack:', err);
        setError('Failed to start Universal Ripple Stack');
        setIsLoading(false);
      });
    } catch (err) {
      console.error('Error initializing components:', err);
      setError('Failed to initialize components');
      setIsLoading(false);
    }
    
    // Cleanup
    return () => {
      if (rippleStack) {
        rippleStack.stop().catch(err => {
          console.error('Error stopping Universal Ripple Stack:', err);
        });
      }
    };
  }, [enableLogging]);
  
  // Make a decision every 5 seconds
  useEffect(() => {
    if (!rippleStack || !novaVision) return;
    
    const decisionInterval = setInterval(() => {
      try {
        // Make a decision
        const decisionContext = {
          user: 'admin',
          action: 'approve',
          resource: 'sensitive-data',
          riskLevel: Math.random() > 0.5 ? 'medium' : 'low'
        };
        
        const decision = rippleStack.makeDecision(decisionContext);
        
        // Publish decision event to NovaVision
        novaVision.publishComponentEvent('trinity-visualization', {
          type: 'decision',
          data: decision,
          timestamp: new Date()
        }).catch(err => {
          console.error('Error publishing decision event:', err);
        });
        
        // Update data source
        novaVision.publishDataSourceData('universal-ripple-stack', {
          quantum: {
            engine: { stateCount: 5 + Math.floor(Math.random() * 5) },
            resonance: { connectionCount: 3 + Math.floor(Math.random() * 10) },
            field: Math.random() > 0.2
          },
          components: {
            novaConnect: true,
            novaThink: true,
            novaPulse: Math.random() > 0.1,
            novaFlow: Math.random() > 0.1
          },
          rippleEffect: {
            layer1: Math.random() > 0.1,
            layer2: Math.random() > 0.2,
            layer3: Math.random() > 0.3
          },
          timestamp: new Date()
        }).catch(err => {
          console.error('Error updating data source:', err);
        });
      } catch (err) {
        console.error('Error making decision:', err);
      }
    }, 5000);
    
    return () => clearInterval(decisionInterval);
  }, [rippleStack, novaVision]);
  
  // Toggle demo mode
  const toggleDemoMode = () => {
    setDemoMode(demoMode === 'dashboard' ? 'visualization' : 'dashboard');
  };
  
  // Toggle logging
  const toggleLogging = () => {
    setEnableLogging(!enableLogging);
  };
  
  if (isLoading) {
    return (
      <div className="nova-vision-trinity-demo-loading">
        <div className="loading-spinner"></div>
        <p>Initializing NovaVision Trinity Demo...</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="nova-vision-trinity-demo-error">
        <h2>Error</h2>
        <p>{error}</p>
      </div>
    );
  }
  
  return (
    <div className="nova-vision-trinity-demo">
      <div className="demo-header">
        <h1>NovaVision Trinity Visualization</h1>
        <p>Powered by NovaFuse Universal Ripple Stack</p>
        
        <div className="demo-controls">
          <button 
            className={`mode-button ${demoMode === 'dashboard' ? 'active' : ''}`}
            onClick={() => setDemoMode('dashboard')}
          >
            Dashboard Mode
          </button>
          <button 
            className={`mode-button ${demoMode === 'visualization' ? 'active' : ''}`}
            onClick={() => setDemoMode('visualization')}
          >
            Visualization Mode
          </button>
          <label className="logging-toggle">
            <input 
              type="checkbox" 
              checked={enableLogging} 
              onChange={toggleLogging} 
            />
            Enable Logging
          </label>
        </div>
      </div>
      
      <div className="demo-content">
        <NovaVisionTrinityVisualization 
          novaVision={novaVision}
          universalRippleStack={rippleStack}
          width="100%"
          height="600px"
          showDashboard={demoMode === 'dashboard'}
          enableLogging={enableLogging}
          updateInterval={1000}
          autoRotate={true}
          initialWheel="all"
        />
      </div>
      
      <div className="demo-footer">
        <p>NovaFuse Universal Ripple Stack - Powered by Comphyology (Ψᶜ)</p>
      </div>
    </div>
  );
};

export default NovaVisionTrinityDemo;
