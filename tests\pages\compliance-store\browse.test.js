import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import BrowseConnectors from '../../../pages/compliance-store/browse';

// Mock the router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    query: {},
    pathname: '/compliance-store/browse',
    asPath: '/compliance-store/browse'
  })
}));

describe('BrowseConnectors Page', () => {
  it('renders the page title and description', () => {
    render(<BrowseConnectors />);
    
    // Check if page title is rendered
    expect(screen.getByText('Browse Connectors')).toBeInTheDocument();
    
    // Check if description is rendered
    expect(screen.getByText(/Explore our growing catalog of compliance connectors/i)).toBeInTheDocument();
  });
  
  it('renders the search and filter controls', () => {
    render(<BrowseConnectors />);
    
    // Check if search input is rendered
    expect(screen.getByPlaceholderText('Search connectors...')).toBeInTheDocument();
    
    // Check if category filter is rendered
    expect(screen.getByLabelText('Category:')).toBeInTheDocument();
    
    // Check if framework filter is rendered
    expect(screen.getByLabelText('Framework:')).toBeInTheDocument();
    
    // Check if sort option is rendered
    expect(screen.getByLabelText('Sort by:')).toBeInTheDocument();
  });
  
  it('filters connectors when search query is entered', async () => {
    render(<BrowseConnectors />);
    
    // Get the search input
    const searchInput = screen.getByPlaceholderText('Search connectors...');
    
    // Enter a search query
    fireEvent.change(searchInput, { target: { value: 'gdpr' } });
    
    // Wait for the filtered results
    await waitFor(() => {
      // The GDPR Shield connector should be visible
      expect(screen.getByText('GDPR Shield')).toBeInTheDocument();
      
      // Other connectors should not be visible
      expect(screen.queryByText('SOC2 Automator')).not.toBeInTheDocument();
    });
  });
  
  it('filters connectors when category is selected', async () => {
    render(<BrowseConnectors />);
    
    // Get the category select
    const categorySelect = screen.getByLabelText('Category:');
    
    // Select the 'privacy' category
    fireEvent.change(categorySelect, { target: { value: 'privacy' } });
    
    // Wait for the filtered results
    await waitFor(() => {
      // Privacy-related connectors should be visible
      expect(screen.getByText('GDPR Shield')).toBeInTheDocument();
      
      // Other connectors should not be visible
      expect(screen.queryByText('SOC2 Automator')).not.toBeInTheDocument();
    });
  });
  
  it('filters connectors when framework is selected', async () => {
    render(<BrowseConnectors />);
    
    // Get the framework select
    const frameworkSelect = screen.getByLabelText('Framework:');
    
    // Select the 'gdpr' framework
    fireEvent.change(frameworkSelect, { target: { value: 'gdpr' } });
    
    // Wait for the filtered results
    await waitFor(() => {
      // GDPR-related connectors should be visible
      expect(screen.getByText('GDPR Shield')).toBeInTheDocument();
      
      // Other connectors should not be visible
      expect(screen.queryByText('SOC2 Automator')).not.toBeInTheDocument();
    });
  });
  
  it('sorts connectors when sort option is changed', async () => {
    render(<BrowseConnectors />);
    
    // Get the sort select
    const sortSelect = screen.getByLabelText('Sort by:');
    
    // Select the 'rating' sort option
    fireEvent.change(sortSelect, { target: { value: 'rating' } });
    
    // Wait for the sorted results
    await waitFor(() => {
      // Connectors should be sorted by rating
      const connectorElements = screen.getAllByText(/connector/i);
      expect(connectorElements.length).toBeGreaterThan(0);
    });
  });
  
  it('clears filters when clear filters button is clicked', async () => {
    render(<BrowseConnectors />);
    
    // Apply a filter first
    const categorySelect = screen.getByLabelText('Category:');
    fireEvent.change(categorySelect, { target: { value: 'privacy' } });
    
    // Wait for the filtered results
    await waitFor(() => {
      expect(screen.getByText('GDPR Shield')).toBeInTheDocument();
      expect(screen.queryByText('SOC2 Automator')).not.toBeInTheDocument();
    });
    
    // Click the clear filters button
    const clearFiltersButton = screen.getByText('Clear Filters');
    fireEvent.click(clearFiltersButton);
    
    // Wait for all connectors to be shown
    await waitFor(() => {
      expect(screen.getByText('GDPR Shield')).toBeInTheDocument();
      expect(screen.getByText('SOC2 Automator')).toBeInTheDocument();
    });
  });
  
  it('changes page when pagination is used', async () => {
    render(<BrowseConnectors />);
    
    // Check if pagination is rendered (if there are enough connectors)
    const paginationButtons = screen.queryAllByRole('button', { name: /[0-9]+/ });
    
    if (paginationButtons.length > 1) {
      // Click on page 2
      fireEvent.click(paginationButtons[1]);
      
      // Wait for the page to change
      await waitFor(() => {
        // The current page should be 2
        expect(paginationButtons[1]).toHaveClass('bg-blue-600');
      });
    }
  });
});
