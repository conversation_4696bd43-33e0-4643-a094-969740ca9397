import React, { useState } from 'react';
import {
  Box,
  Grid,
  Typo<PERSON>,
  Card,
  CardContent,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  FormControlLabel,
  Button
} from '@mui/material';
import VisualizationRenderer from '../components/visualizations/VisualizationRenderer';

/**
 * VisualizationDemo component
 * 
 * Demonstrates all visualization types with configurable options and sample data
 */
function VisualizationDemo() {
  // Visualization type
  const [visualizationType, setVisualizationType] = useState('3d_tensor_visualization');
  
  // Visualization options
  const [renderMode, setRenderMode] = useState('high');
  const [showAxes, setShowAxes] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [rotationSpeed, setRotationSpeed] = useState(1);
  const [colorScheme, setColorScheme] = useState('default');
  
  // Additional options for specific visualization types
  const [dimensions, setDimensions] = useState(3);
  const [harmonicThreshold, setHarmonicThreshold] = useState(0.7);
  const [resonancePatterns, setResonancePatterns] = useState(true);
  
  // Sample data
  const [dataType, setDataType] = useState('sine');
  const [dataSize, setDataSize] = useState(100);
  const [noiseLevel, setNoiseLevel] = useState(0);
  
  // Generate sample data
  const generateData = () => {
    const values = [];
    
    switch (dataType) {
      case 'sine':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = Math.sin(t * Math.PI * 4) * 0.5 + 0.5;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'cosine':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = Math.cos(t * Math.PI * 4) * 0.5 + 0.5;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'square':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = Math.sin(t * Math.PI * 4) > 0 ? 1 : 0;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'sawtooth':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = (t * 4) % 1;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'triangle':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = Math.abs(((t * 4) % 2) - 1);
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'random':
        for (let i = 0; i < dataSize; i++) {
          values.push(Math.random());
        }
        break;
        
      case 'harmonics':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const value = (
            Math.sin(t * Math.PI * 2) * 0.5 +
            Math.sin(t * Math.PI * 4) * 0.25 +
            Math.sin(t * Math.PI * 8) * 0.125 +
            Math.sin(t * Math.PI * 16) * 0.0625
          ) * 0.5 + 0.5;
          values.push(value + (Math.random() - 0.5) * noiseLevel);
        }
        break;
        
      case 'resonance':
        for (let i = 0; i < dataSize; i++) {
          const t = i / dataSize;
          const resonance = 0.1; // Resonance factor
          const value = Math.sin(t * Math.PI * 4) / (1 - resonance * Math.sin(t * Math.PI * 4));
          // Normalize to [0, 1]
          const normalizedValue = (value + 10) / 20;
          values.push(Math.max(0, Math.min(1, normalizedValue + (Math.random() - 0.5) * noiseLevel)));
        }
        break;
        
      default:
        for (let i = 0; i < dataSize; i++) {
          values.push(0.5);
        }
    }
    
    return values;
  };
  
  // Generate tensor data
  const tensor = {
    values: generateData(),
    health: 0.95,
    entropyContainment: 0.02
  };
  
  // Get visualization options
  const getVisualizationOptions = () => {
    const options = {
      renderMode,
      showAxes,
      showGrid,
      rotationSpeed,
      colorScheme
    };
    
    // Add additional options based on visualization type
    if (visualizationType === 'phase_space_visualization') {
      options.dimensions = dimensions;
    } else if (visualizationType === 'harmonic_pattern_explorer') {
      options.harmonicThreshold = harmonicThreshold;
      options.resonancePatterns = resonancePatterns;
    }
    
    return options;
  };
  
  // Get tensor dimensions based on visualization type
  const getTensorDimensions = () => {
    switch (visualizationType) {
      case '3d_tensor_visualization':
        if (dataSize <= 25) {
          return [5, 5, 1]; // 2D grid
        } else if (dataSize <= 125) {
          return [5, 5, 5]; // 3D grid
        } else {
          return [10, 10, 1]; // Larger 2D grid
        }
        
      case 'resonance_spectrogram':
        return [10, 10, 1]; // 2D grid for spectrogram
        
      case 'phase_space_visualization':
        return [dimensions, 1, 1]; // Dimensions for phase space
        
      case 'harmonic_pattern_explorer':
        return [dataSize, 1, 1]; // 1D array for harmonic analysis
        
      default:
        return [10, 10, 1];
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Visualization Demo
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          {/* Visualization Type */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Visualization Type
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="visualization-type-label">Visualization Type</InputLabel>
                <Select
                  labelId="visualization-type-label"
                  id="visualization-type"
                  value={visualizationType}
                  label="Visualization Type"
                  onChange={(e) => setVisualizationType(e.target.value)}
                >
                  <MenuItem value="3d_tensor_visualization">3D Tensor Visualization</MenuItem>
                  <MenuItem value="resonance_spectrogram">Resonance Spectrogram</MenuItem>
                  <MenuItem value="phase_space_visualization">Phase Space Visualization</MenuItem>
                  <MenuItem value="harmonic_pattern_explorer">Harmonic Pattern Explorer</MenuItem>
                </Select>
              </FormControl>
            </CardContent>
          </Card>
          
          {/* Data Options */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Data Options
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="data-type-label">Data Type</InputLabel>
                <Select
                  labelId="data-type-label"
                  id="data-type"
                  value={dataType}
                  label="Data Type"
                  onChange={(e) => setDataType(e.target.value)}
                >
                  <MenuItem value="sine">Sine Wave</MenuItem>
                  <MenuItem value="cosine">Cosine Wave</MenuItem>
                  <MenuItem value="square">Square Wave</MenuItem>
                  <MenuItem value="sawtooth">Sawtooth Wave</MenuItem>
                  <MenuItem value="triangle">Triangle Wave</MenuItem>
                  <MenuItem value="random">Random</MenuItem>
                  <MenuItem value="harmonics">Harmonics</MenuItem>
                  <MenuItem value="resonance">Resonance</MenuItem>
                </Select>
              </FormControl>
              
              <Typography gutterBottom>Data Size</Typography>
              <Slider
                value={dataSize}
                onChange={(e, newValue) => setDataSize(newValue)}
                min={10}
                max={200}
                step={10}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
              
              <Typography gutterBottom>Noise Level</Typography>
              <Slider
                value={noiseLevel}
                onChange={(e, newValue) => setNoiseLevel(newValue)}
                min={0}
                max={1}
                step={0.1}
                valueLabelDisplay="auto"
              />
            </CardContent>
          </Card>
          
          {/* Visualization Options */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Visualization Options
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="render-mode-label">Render Quality</InputLabel>
                <Select
                  labelId="render-mode-label"
                  id="render-mode"
                  value={renderMode}
                  label="Render Quality"
                  onChange={(e) => setRenderMode(e.target.value)}
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                </Select>
              </FormControl>
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="color-scheme-label">Color Scheme</InputLabel>
                <Select
                  labelId="color-scheme-label"
                  id="color-scheme"
                  value={colorScheme}
                  label="Color Scheme"
                  onChange={(e) => setColorScheme(e.target.value)}
                >
                  <MenuItem value="default">Default</MenuItem>
                  <MenuItem value="rainbow">Rainbow</MenuItem>
                  <MenuItem value="heatmap">Heat Map</MenuItem>
                  <MenuItem value="grayscale">Grayscale</MenuItem>
                </Select>
              </FormControl>
              
              <Typography gutterBottom>Rotation Speed</Typography>
              <Slider
                value={rotationSpeed}
                onChange={(e, newValue) => setRotationSpeed(newValue)}
                min={0}
                max={5}
                step={0.1}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={showAxes}
                    onChange={(e) => setShowAxes(e.target.checked)}
                  />
                }
                label="Show Axes"
                sx={{ mb: 1, display: 'block' }}
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={showGrid}
                    onChange={(e) => setShowGrid(e.target.checked)}
                  />
                }
                label="Show Grid"
                sx={{ mb: 1, display: 'block' }}
              />
              
              {/* Phase Space specific options */}
              {visualizationType === 'phase_space_visualization' && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle1" gutterBottom>
                    Phase Space Options
                  </Typography>
                  
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel id="dimensions-label">Dimensions</InputLabel>
                    <Select
                      labelId="dimensions-label"
                      id="dimensions"
                      value={dimensions}
                      label="Dimensions"
                      onChange={(e) => setDimensions(e.target.value)}
                    >
                      <MenuItem value={2}>2D</MenuItem>
                      <MenuItem value={3}>3D</MenuItem>
                    </Select>
                  </FormControl>
                </>
              )}
              
              {/* Harmonic Pattern Explorer specific options */}
              {visualizationType === 'harmonic_pattern_explorer' && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle1" gutterBottom>
                    Harmonic Pattern Options
                  </Typography>
                  
                  <Typography gutterBottom>Harmonic Threshold</Typography>
                  <Slider
                    value={harmonicThreshold}
                    onChange={(e, newValue) => setHarmonicThreshold(newValue)}
                    min={0}
                    max={1}
                    step={0.1}
                    valueLabelDisplay="auto"
                    sx={{ mb: 2 }}
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={resonancePatterns}
                        onChange={(e) => setResonancePatterns(e.target.checked)}
                      />
                    }
                    label="Show Resonance Patterns"
                    sx={{ mb: 1, display: 'block' }}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={8}>
          {/* Visualization Preview */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {visualizationType === '3d_tensor_visualization' && 'Tensor Visualization'}
                {visualizationType === 'resonance_spectrogram' && 'Resonance Spectrogram'}
                {visualizationType === 'phase_space_visualization' && 'Phase Space Visualization'}
                {visualizationType === 'harmonic_pattern_explorer' && 'Harmonic Pattern Explorer'}
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box
                sx={{
                  height: 600,
                  bgcolor: 'background.default',
                  borderRadius: 1,
                  overflow: 'hidden'
                }}
              >
                <VisualizationRenderer
                  visualizationType={visualizationType}
                  tensor={tensor}
                  dimensions={getTensorDimensions()}
                  options={getVisualizationOptions()}
                  height="100%"
                />
              </Box>
              
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    // Force re-render by creating a new tensor object
                    const newTensor = {
                      ...tensor,
                      values: generateData()
                    };
                    // This doesn't actually update the tensor since we're not using state,
                    // but it demonstrates how you would refresh the data
                    console.log('Regenerating data:', newTensor);
                  }}
                >
                  Regenerate Data
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default VisualizationDemo;
