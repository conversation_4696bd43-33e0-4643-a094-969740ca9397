import React, { useState, useEffect, useRef } from 'react';
import { useTheme, useMediaQuery } from '@mui/material';
import ResizeObserver from 'resize-observer-polyfill';

/**
 * Higher-Order Component (HOC) that adds responsiveness to visualizations
 * @param {React.Component} WrappedComponent - The component to wrap
 * @param {Object} options - Additional options for the HOC
 * @returns {React.Component} - The wrapped component with responsiveness
 */
const withResponsiveness = (WrappedComponent, options = {}) => {
  // Return a new component
  return function WithResponsiveness(props) {
    // Get theme and media queries
    const theme = useTheme();
    const isXs = useMediaQuery(theme.breakpoints.only('xs'));
    const isSm = useMediaQuery(theme.breakpoints.only('sm'));
    const isMd = useMediaQuery(theme.breakpoints.only('md'));
    const isLg = useMediaQuery(theme.breakpoints.only('lg'));
    const isXl = useMediaQuery(theme.breakpoints.only('xl'));
    
    // Determine screen size
    const screenSize = isXs ? 'xs' : isSm ? 'sm' : isMd ? 'md' : isLg ? 'lg' : 'xl';
    
    // Reference to the container element
    const containerRef = useRef(null);
    
    // State for container dimensions
    const [dimensions, setDimensions] = useState({
      width: props.width || 0,
      height: props.height || 0
    });
    
    // State for device capabilities
    const [deviceCapabilities, setDeviceCapabilities] = useState({
      isTouchDevice: false,
      isHighPerformance: true,
      pixelRatio: 1
    });
    
    // Detect device capabilities on mount
    useEffect(() => {
      // Detect touch device
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      
      // Detect pixel ratio
      const pixelRatio = window.devicePixelRatio || 1;
      
      // Estimate performance based on device memory and processor count
      // This is a rough estimate and may not be accurate for all devices
      const memory = navigator.deviceMemory || 4; // Default to 4GB if not available
      const processors = navigator.hardwareConcurrency || 4; // Default to 4 cores if not available
      
      // Consider high performance if device has at least 4GB memory and 4 cores
      const isHighPerformance = memory >= 4 && processors >= 4;
      
      // Update device capabilities
      setDeviceCapabilities({
        isTouchDevice,
        isHighPerformance,
        pixelRatio
      });
    }, []);
    
    // Set up resize observer
    useEffect(() => {
      if (!containerRef.current) return;
      
      // Create resize observer
      const resizeObserver = new ResizeObserver(entries => {
        if (!entries || !entries[0]) return;
        
        const { width, height } = entries[0].contentRect;
        
        // Update dimensions if they have changed
        setDimensions(prev => {
          if (prev.width !== width || prev.height !== height) {
            return { width, height };
          }
          return prev;
        });
      });
      
      // Observe container
      resizeObserver.observe(containerRef.current);
      
      // Clean up observer on unmount
      return () => {
        resizeObserver.disconnect();
      };
    }, []);
    
    // Determine optimal detail level based on screen size and device capabilities
    const getDetailLevel = () => {
      // Base detail level on screen size
      const sizeBasedDetail = {
        xs: 'low',
        sm: 'low',
        md: 'medium',
        lg: 'high',
        xl: 'high'
      }[screenSize];
      
      // Adjust based on device capabilities
      if (!deviceCapabilities.isHighPerformance) {
        // Reduce detail level for low-performance devices
        return sizeBasedDetail === 'high' ? 'medium' : sizeBasedDetail === 'medium' ? 'low' : 'low';
      }
      
      return sizeBasedDetail;
    };
    
    // Determine optimal animation level based on screen size and device capabilities
    const getAnimationLevel = () => {
      // Disable animations on low-performance devices
      if (!deviceCapabilities.isHighPerformance) {
        return 'none';
      }
      
      // Base animation level on screen size
      return {
        xs: 'minimal',
        sm: 'minimal',
        md: 'moderate',
        lg: 'full',
        xl: 'full'
      }[screenSize];
    };
    
    // Determine optimal interaction mode based on device capabilities
    const getInteractionMode = () => {
      return deviceCapabilities.isTouchDevice ? 'touch' : 'mouse';
    };
    
    // Create responsive props
    const responsiveProps = {
      ...props,
      width: dimensions.width || props.width,
      height: dimensions.height || props.height,
      screenSize,
      detailLevel: getDetailLevel(),
      animationLevel: getAnimationLevel(),
      interactionMode: getInteractionMode(),
      deviceCapabilities,
      ref: containerRef
    };
    
    // Render the wrapped component with responsive props
    return (
      <div 
        ref={containerRef} 
        style={{ 
          width: props.width || '100%', 
          height: props.height || '100%',
          minHeight: props.minHeight || 300
        }}
      >
        {(dimensions.width > 0 && dimensions.height > 0) && (
          <WrappedComponent {...responsiveProps} />
        )}
      </div>
    );
  };
};

export default withResponsiveness;
