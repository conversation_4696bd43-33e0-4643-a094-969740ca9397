/**
 * Card Component Styles
 */

@import '../../design-system/variables.css';

.nova-card {
  display: flex;
  flex-direction: column;
  background-color: var(--nova-white);
  border-radius: var(--nova-card-border-radius);
  overflow: hidden;
}

.nova-card--bordered {
  border: var(--nova-card-border-width) solid var(--nova-card-border-color);
}

.nova-card--elevated {
  box-shadow: var(--nova-card-box-shadow);
}

.nova-card__header {
  padding: var(--nova-card-padding);
  border-bottom: var(--nova-card-border-width) solid var(--nova-card-border-color);
  font-weight: var(--nova-font-weight-medium);
}

.nova-card__body {
  padding: var(--nova-card-padding);
  flex: 1 1 auto;
}

.nova-card__footer {
  padding: var(--nova-card-padding);
  border-top: var(--nova-card-border-width) solid var(--nova-card-border-color);
}

/* Dark mode */
[data-theme="dark"] .nova-card {
  background-color: var(--nova-dark-surface);
  color: var(--nova-dark-text-primary);
}

[data-theme="dark"] .nova-card--bordered {
  border-color: var(--nova-dark-border);
}

[data-theme="dark"] .nova-card__header,
[data-theme="dark"] .nova-card__footer {
  border-color: var(--nova-dark-border);
}
