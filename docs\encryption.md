# NovaFuse API Superstore Encryption

This document provides an overview of the encryption capabilities implemented in the NovaFuse API Superstore.

## Table of Contents

- [Overview](#overview)
- [Encryption Utility](#encryption-utility)
- [Encryption Middleware](#encryption-middleware)
- [Configuration](#configuration)
- [Best Practices](#best-practices)
- [Security Considerations](#security-considerations)
- [Testing](#testing)

## Overview

The NovaFuse API Superstore implements a comprehensive encryption strategy to protect sensitive data in transit and at rest. The encryption capabilities are provided by the following components:

- **Encryption Utility**: A utility module that provides core encryption functions.
- **Encryption Middleware**: Express middleware for encrypting and decrypting API requests and responses.
- **Configuration**: Configuration settings for encryption.

## Encryption Utility

The encryption utility (`utils/encryption.js`) provides the following capabilities:

### Symmetric Encryption (AES-256-GCM)

- **encryptSymmetric**: Encrypts data using AES-256-GCM.
- **decryptSymmetric**: Decrypts data encrypted with AES-256-GCM.

### Asymmetric Encryption (RSA)

- **generateKeyPair**: Generates an RSA key pair.
- **encryptAsymmetric**: Encrypts data using RSA.
- **decryptAsymmetric**: Decrypts data encrypted with RSA.
- **createSignature**: Creates a digital signature for data.
- **verifySignature**: Verifies a digital signature.

### Key Derivation (PBKDF2)

- **deriveKey**: Derives a key from a password using PBKDF2.

### Secure Hashing (SHA-512)

- **createHash**: Creates a secure hash of data using SHA-512 with salt.
- **verifyHash**: Verifies a hash against data.

### API Key Encryption

- **encryptApiKey**: Encrypts an API key.
- **decryptApiKey**: Decrypts an API key.

### Utility Functions

- **generateRandomBytes**: Generates cryptographically secure random bytes.
- **generateRandomString**: Generates a cryptographically secure random string.
- **maskSensitiveData**: Masks sensitive data for logging or display.
- **encryptFile**: Encrypts a file.
- **decryptFile**: Decrypts a file.
- **secureCompare**: Securely compares two strings in constant time.

## Encryption Middleware

The encryption middleware (`middleware/encryption.js`) provides the following capabilities:

### Field-Level Encryption

- **encryptResponseFields**: Encrypts specific fields in the response.
- **decryptRequestFields**: Decrypts specific fields in the request.

### Full-Body Encryption

- **encryptResponse**: Encrypts the entire response body.
- **decryptRequest**: Decrypts the entire request body.

### Data Masking

- **maskSensitiveData**: Masks sensitive data in logs.

## Configuration

The encryption configuration (`config/encryption.js`) provides the following settings:

- **masterKey**: Master key for encrypting other keys.
- **requestKey**: Key for encrypting request data.
- **responseKey**: Key for encrypting response data.
- **apiKeyEncryptionKey**: Key for encrypting API keys.
- **jwtSecret**: Key for encrypting JWT tokens.
- **sensitiveResponseFields**: Fields to encrypt in responses.
- **encryptedRequestFields**: Fields to decrypt in requests.
- **sensitiveLogFields**: Fields to mask in logs.

## Best Practices

### Key Management

- Store encryption keys securely in environment variables or a secure key management system.
- Rotate keys regularly.
- Use different keys for different purposes.
- Never hardcode keys in source code.

### Data Classification

- Classify data based on sensitivity.
- Apply appropriate encryption based on data classification.
- Encrypt all sensitive data in transit and at rest.

### Encryption Levels

- **Level 1**: Transport-level encryption (HTTPS/TLS)
- **Level 2**: Field-level encryption for sensitive data
- **Level 3**: Full-body encryption for highly sensitive operations

## Security Considerations

### Key Storage

In production, encryption keys should be stored securely:

- Use environment variables for keys.
- Consider using a key management service (KMS) like AWS KMS or HashiCorp Vault.
- Never store keys in source code or configuration files.

### Key Rotation

Implement a key rotation policy:

- Rotate keys regularly (e.g., every 90 days).
- Maintain a key version history for decrypting old data.
- Implement a seamless key rotation process.

### Secure Coding

Follow secure coding practices:

- Use constant-time comparison for sensitive data.
- Avoid timing attacks by using crypto.timingSafeEqual.
- Implement proper error handling to avoid leaking sensitive information.

## Testing

The encryption implementation includes comprehensive tests:

- **Unit Tests**: Test individual encryption functions.
- **Integration Tests**: Test encryption middleware with Express.
- **Security Tests**: Test encryption against common attacks.

Run the tests using:

```bash
npm test
```

For more detailed testing:

```bash
npm run test:coverage
```
