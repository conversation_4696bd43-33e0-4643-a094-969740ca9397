/**
 * NovaDNA - Emergency Medical Identity System
 *
 * This module exports all NovaDNA components for easy access.
 */

// Core components
const BlockchainVerifier = require('./core/BlockchainVerifier');
const DataPipeline = require('./core/DataPipeline');
const EmergencyProfile = require('./core/EmergencyProfile');

// Access components
const ProgressiveDisclosureSystem = require('./access/ProgressiveDisclosureSystem');
const BreakGlassProtocol = require('./access/BreakGlassProtocol');

// Authentication components
const EmergencyAuthenticator = require('./auth/EmergencyAuthenticator');

// Physical form factor components
const FormFactorManager = require('./physical/FormFactorManager');

// Security components
const AISecurityMonitor = require('./security/AISecurityMonitor');

// Integration components
const NovaProofConnector = require('./integration/NovaProofConnector');
const NovaConnectAdapter = require('./integration/NovaConnectAdapter');

// UI components
const NovaVisionComponents = require('./ui/NovaVisionComponents');

/**
 * Create a new NovaDNA instance with all components
 * @param {Object} options - Configuration options
 * @returns {Object} - The NovaDNA instance
 */
function createNovaDNA(options = {}) {
  // Initialize core components
  const blockchainVerifier = new BlockchainVerifier(options.blockchainVerifier);
  const dataPipeline = new DataPipeline(options.dataPipeline);
  const emergencyProfile = new EmergencyProfile(options.emergencyProfile);

  // Initialize access components
  const progressiveDisclosureSystem = new ProgressiveDisclosureSystem(options.progressiveDisclosureSystem);
  const breakGlassProtocol = new BreakGlassProtocol(options.breakGlassProtocol);

  // Initialize authentication components
  const emergencyAuthenticator = new EmergencyAuthenticator(options.emergencyAuthenticator);

  // Initialize physical form factor components
  const formFactorManager = new FormFactorManager(options.formFactorManager);

  // Initialize security components
  const aiSecurityMonitor = new AISecurityMonitor(options.aiSecurityMonitor);

  // Initialize integration components
  const novaProofConnector = new NovaProofConnector(options.novaProofConnector);
  const novaConnectAdapter = new NovaConnectAdapter(options.novaConnectAdapter);

  // Initialize UI components
  const novaVisionComponents = new NovaVisionComponents(options.novaVisionComponents);

  // Return the NovaDNA instance
  return {
    // Core components
    blockchainVerifier,
    dataPipeline,
    emergencyProfile,

    // Access components
    progressiveDisclosureSystem,
    breakGlassProtocol,

    // Authentication components
    emergencyAuthenticator,

    // Physical form factor components
    formFactorManager,

    // Security components
    aiSecurityMonitor,

    // Integration components
    novaProofConnector,
    novaConnectAdapter,

    // UI components
    novaVisionComponents,

    // Version information
    version: '1.0.0',

    // Utility methods
    async createEmergencyProfile(profileData) {
      // Create the profile
      const profile = emergencyProfile.createProfile(profileData);

      // Verify the profile data
      const verification = await blockchainVerifier.createVerification(profile);

      // Create audit trail
      await novaProofConnector.createAuditTrail(
        profile.profileId,
        'PROFILE_CREATED',
        {
          verificationId: verification.verificationId
        }
      );

      return {
        profile,
        verification
      };
    },

    async accessEmergencyProfile(formFactorId, accessCode, context = {}) {
      // Verify the form factor
      const formFactorResult = formFactorManager.verifyFormFactor(formFactorId, accessCode);

      if (!formFactorResult.valid) {
        throw new Error('Invalid form factor or access code');
      }

      // Get the profile
      // In a real implementation, this would fetch from a database
      // For now, we'll assume we have the profile
      const profile = { profileId: formFactorResult.profileId };

      // Track the access event
      aiSecurityMonitor.trackAccessEvent({
        profileId: profile.profileId,
        accessType: formFactorResult.accessLevel,
        formFactorId,
        context,
        timestamp: new Date().toISOString()
      });

      // Create audit trail
      await novaProofConnector.createAuditTrail(
        profile.profileId,
        'PROFILE_ACCESSED',
        {
          accessLevel: formFactorResult.accessLevel,
          formFactorType: formFactorResult.type,
          context
        }
      );

      // Determine appropriate disclosure level based on context
      const disclosureResult = progressiveDisclosureSystem.determineDisclosureLevel(
        profile,
        context,
        {
          serviceId: context.serviceId || 'UNKNOWN',
          role: context.responderType || 'UNKNOWN'
        }
      );

      // Return filtered profile based on determined access level
      return disclosureResult.profile;
    },

    async emergencyOverrideAccess(profileId, overrideRequest) {
      // Initiate break-glass override
      const overrideResult = breakGlassProtocol.initiateOverride({
        ...overrideRequest,
        targetProfileId: profileId
      });

      if (!overrideResult || overrideResult.status !== 'ACTIVE') {
        throw new Error('Failed to initiate emergency override');
      }

      // Track the override event
      aiSecurityMonitor.trackAccessEvent({
        profileId,
        accessType: 'EMERGENCY_OVERRIDE',
        overrideId: overrideResult.overrideId,
        reason: overrideRequest.reason,
        timestamp: new Date().toISOString()
      });

      // Create audit trail
      await novaProofConnector.createAuditTrail(
        profileId,
        'EMERGENCY_OVERRIDE',
        {
          overrideId: overrideResult.overrideId,
          reason: overrideRequest.reason,
          serviceId: overrideRequest.serviceId,
          userId: overrideRequest.userId
        }
      );

      // Get the profile
      // In a real implementation, this would fetch from a database
      // For now, we'll assume we have the profile
      const profile = { profileId };

      // Elevate disclosure level for emergency
      const elevationResult = progressiveDisclosureSystem.elevateDisclosureLevel(
        profileId,
        {
          emergencyType: overrideRequest.emergencyType,
          emergencySeverity: 'CRITICAL'
        },
        {
          serviceId: overrideRequest.serviceId,
          userId: overrideRequest.userId,
          override: true
        },
        overrideRequest.reason
      );

      // Return full profile for emergency override
      return {
        profile: emergencyProfile.getFilteredProfile(profile, 'FULL'),
        override: overrideResult,
        elevation: elevationResult
      };
    }
  };
}

module.exports = {
  // Core components
  BlockchainVerifier,
  DataPipeline,
  EmergencyProfile,

  // Access components
  ProgressiveDisclosureSystem,
  BreakGlassProtocol,

  // Authentication components
  EmergencyAuthenticator,

  // Physical form factor components
  FormFactorManager,

  // Security components
  AISecurityMonitor,

  // Integration components
  NovaProofConnector,
  NovaConnectAdapter,

  // UI components
  NovaVisionComponents,

  // Factory function
  createNovaDNA
};
