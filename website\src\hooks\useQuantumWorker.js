import { useState, useEffect, useRef, useCallback } from 'react';

const useQuantumWorker = (initialEntropy = 0.5, initialCoherence = 1.0) => {
  const [quantumState, setQuantumState] = useState({
    entropy: initialEntropy,
    coherence: initialCoherence,
    amplitude: Math.sqrt(1 - initialEntropy),
    phase: 0,
    timestamp: Date.now(),
    isReady: false
  });
  
  const workerRef = useRef(null);
  const animationFrameRef = useRef(null);
  const lastUpdateRef = useRef(0);
  
  // Initialize worker
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    // Create worker with fallback
    const createWorker = () => {
      try {
        const workerCode = `
          ${QuantumWorker.toString()}
          ${QuantumWorker.toString().includes('function QuantumWorker') ? '' : 'new QuantumWorker();'}
        `;
        const blob = new Blob([workerCode], { type: 'application/javascript' });
        return new Worker(URL.createObjectURL(blob));
      } catch (error) {
        console.error('Failed to create worker:', error);
        return null;
      }
    };
    
    const worker = createWorker() || (() => {
      console.warn('Using main thread fallback for quantum calculations');
      return {
        postMessage: (data) => {
          if (data.type === 'INIT' || data.type === 'UPDATE_PARAMS') {
            // Simulate worker response
            setTimeout(() => {
              setQuantumState(prev => ({
                ...prev,
                entropy: data.payload?.entropy ?? prev.entropy,
                coherence: data.payload?.coherence ?? prev.coherence,
                isReady: true
              }));
            }, 50);
          }
        },
        terminate: () => {}
      };
    })();
    
    workerRef.current = worker;
    
    // Handle messages from worker
    const handleMessage = (event) => {
      const { type, state } = event.data;
      
      switch (type) {
        case 'WORKER_READY':
          // Initialize worker with initial state
          worker.postMessage({
            type: 'INIT',
            payload: {
              entropy: initialEntropy,
              coherence: initialCoherence
            }
          });
          break;
          
        case 'STATE_UPDATE':
          setQuantumState(prev => ({
            ...prev,
            ...state,
            isReady: true
          }));
          break;
      }
    };
    
    worker.addEventListener('message', handleMessage);
    
    // Cleanup
    return () => {
      worker.removeEventListener('message', handleMessage);
      worker.terminate?.();
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [initialEntropy, initialCoherence]);
  
  // Update worker when params change
  const updateParams = useCallback(({ entropy, coherence }) => {
    if (!workerRef.current) return;
    
    const updates = {};
    if (entropy !== undefined) updates.entropy = entropy;
    if (coherence !== undefined) updates.coherence = coherence;
    
    workerRef.current.postMessage({
      type: 'UPDATE_PARAMS',
      payload: updates
    });
    
    // Update local state immediately for responsive UI
    setQuantumState(prev => ({
      ...prev,
      ...updates,
      amplitude: entropy !== undefined ? Math.sqrt(1 - entropy) : prev.amplitude
    }));
  }, []);
  
  // Pause/resume worker when component visibility changes
  useEffect(() => {
    if (!workerRef.current) return;
    
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        workerRef.current?.postMessage({ type: 'PAUSE' });
      } else {
        workerRef.current?.postMessage({ type: 'RESUME' });
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);
  
  // Fallback animation for when worker is not available
  useEffect(() => {
    if (workerRef.current?.postMessage) return; // Skip if using worker
    
    const animate = (timestamp) => {
      if (!lastUpdateRef.current) {
        lastUpdateRef.current = timestamp;
      }
      
      const deltaTime = (timestamp - lastUpdateRef.current) / 1000; // Convert to seconds
      lastUpdateRef.current = timestamp;
      
      setQuantumState(prev => {
        // Simple harmonic motion simulation
        const phase = (prev.phase + deltaTime * (1 + prev.coherence * 2)) % (2 * Math.PI);
        const amplitude = Math.sqrt(1 - prev.entropy) * (0.9 + 0.1 * Math.sin(phase));
        
        return {
          ...prev,
          phase,
          amplitude,
          timestamp: Date.now()
        };
      });
      
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    
    animationFrameRef.current = requestAnimationFrame(animate);
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);
  
  return {
    ...quantumState,
    updateParams,
    isReady: quantumState.isReady
  };
};

export default useQuantumWorker;
