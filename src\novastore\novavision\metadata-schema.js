/**
 * NovaVision Metadata Schema for NovaStore
 * 
 * This module defines the metadata schema used by NovaVision to generate
 * user interfaces for the NovaStore Nervous System and its components.
 * 
 * NovaVision uses this metadata to automatically generate UIs based on
 * the backend APIs, eliminating the need for manual UI development.
 */

/**
 * Base metadata schema for all NovaVision UI components
 */
const baseMetadataSchema = {
  /**
   * Component identifier
   * @type {string}
   * @required
   */
  id: '',
  
  /**
   * Component name
   * @type {string}
   * @required
   */
  name: '',
  
  /**
   * Component description
   * @type {string}
   */
  description: '',
  
  /**
   * Product tier (physics, transition, legacy)
   * @type {string}
   * @required
   */
  tier: '',
  
  /**
   * Component type (csde, nervous-system, cross-domain, portfolio)
   * @type {string}
   * @required
   */
  componentType: '',
  
  /**
   * Visualization type (dashboard, detail, form, report, chart)
   * @type {string}
   * @required
   */
  visualizationType: '',
  
  /**
   * Data source configuration
   * @type {Object}
   * @required
   */
  dataSource: {
    /**
     * API endpoint
     * @type {string}
     * @required
     */
    endpoint: '',
    
    /**
     * HTTP method
     * @type {string}
     * @required
     */
    method: 'GET',
    
    /**
     * Request parameters
     * @type {Object}
     */
    parameters: {},
    
    /**
     * Request body (for POST, PUT, etc.)
     * @type {Object}
     */
    body: {},
    
    /**
     * Auto-refresh interval in milliseconds
     * @type {number}
     */
    refreshInterval: 0,
    
    /**
     * Whether to cache the data
     * @type {boolean}
     */
    cache: false,
    
    /**
     * Cache TTL in milliseconds
     * @type {number}
     */
    cacheTtl: 0
  },
  
  /**
   * Layout configuration
   * @type {Object}
   * @required
   */
  layout: {
    /**
     * Layout template (standard, compact, detailed, grid, flex)
     * @type {string}
     * @required
     */
    template: 'standard',
    
    /**
     * Layout sections
     * @type {Array}
     * @required
     */
    sections: []
  },
  
  /**
   * Actions available for this component
   * @type {Array}
   */
  actions: [],
  
  /**
   * Permissions required to view this component
   * @type {Array}
   */
  permissions: [],
  
  /**
   * Feature flags that control this component
   * @type {Array}
   */
  featureFlags: []
};

/**
 * Dashboard metadata schema
 */
const dashboardMetadataSchema = {
  ...baseMetadataSchema,
  visualizationType: 'dashboard',
  
  /**
   * Dashboard-specific configuration
   * @type {Object}
   */
  dashboard: {
    /**
     * Dashboard refresh interval in milliseconds
     * @type {number}
     */
    refreshInterval: 5000,
    
    /**
     * Dashboard widgets
     * @type {Array}
     * @required
     */
    widgets: []
  }
};

/**
 * Detail view metadata schema
 */
const detailMetadataSchema = {
  ...baseMetadataSchema,
  visualizationType: 'detail',
  
  /**
   * Detail view-specific configuration
   * @type {Object}
   */
  detail: {
    /**
     * Entity identifier parameter name
     * @type {string}
     * @required
     */
    idParameter: 'id',
    
    /**
     * Sections to display
     * @type {Array}
     * @required
     */
    sections: []
  }
};

/**
 * Form metadata schema
 */
const formMetadataSchema = {
  ...baseMetadataSchema,
  visualizationType: 'form',
  
  /**
   * Form-specific configuration
   * @type {Object}
   */
  form: {
    /**
     * Form fields
     * @type {Array}
     * @required
     */
    fields: [],
    
    /**
     * Form validation rules
     * @type {Object}
     */
    validation: {},
    
    /**
     * Form submission endpoint
     * @type {string}
     * @required
     */
    submitEndpoint: '',
    
    /**
     * Form submission method
     * @type {string}
     * @required
     */
    submitMethod: 'POST'
  }
};

/**
 * Report metadata schema
 */
const reportMetadataSchema = {
  ...baseMetadataSchema,
  visualizationType: 'report',
  
  /**
   * Report-specific configuration
   * @type {Object}
   */
  report: {
    /**
     * Report parameters
     * @type {Array}
     */
    parameters: [],
    
    /**
     * Report sections
     * @type {Array}
     * @required
     */
    sections: [],
    
    /**
     * Export formats
     * @type {Array}
     */
    exportFormats: ['pdf', 'csv', 'json']
  }
};

/**
 * Chart metadata schema
 */
const chartMetadataSchema = {
  ...baseMetadataSchema,
  visualizationType: 'chart',
  
  /**
   * Chart-specific configuration
   * @type {Object}
   */
  chart: {
    /**
     * Chart type (line, bar, pie, scatter, etc.)
     * @type {string}
     * @required
     */
    type: '',
    
    /**
     * Chart data configuration
     * @type {Object}
     * @required
     */
    data: {
      /**
       * Data labels
       * @type {Array}
       */
      labels: [],
      
      /**
       * Data series
       * @type {Array}
       */
      series: []
    },
    
    /**
     * Chart options
     * @type {Object}
     */
    options: {}
  }
};

/**
 * Widget metadata schema
 */
const widgetMetadataSchema = {
  /**
   * Widget identifier
   * @type {string}
   * @required
   */
  id: '',
  
  /**
   * Widget name
   * @type {string}
   * @required
   */
  name: '',
  
  /**
   * Widget type (chart, metric, table, list, etc.)
   * @type {string}
   * @required
   */
  type: '',
  
  /**
   * Widget size (small, medium, large, custom)
   * @type {string}
   */
  size: 'medium',
  
  /**
   * Widget position
   * @type {Object}
   */
  position: {
    x: 0,
    y: 0,
    width: 1,
    height: 1
  },
  
  /**
   * Widget data source
   * @type {Object}
   * @required
   */
  dataSource: {
    /**
     * API endpoint
     * @type {string}
     * @required
     */
    endpoint: '',
    
    /**
     * HTTP method
     * @type {string}
     */
    method: 'GET',
    
    /**
     * Request parameters
     * @type {Object}
     */
    parameters: {},
    
    /**
     * Auto-refresh interval in milliseconds
     * @type {number}
     */
    refreshInterval: 0
  },
  
  /**
   * Widget configuration
   * @type {Object}
   */
  config: {}
};

/**
 * Section metadata schema
 */
const sectionMetadataSchema = {
  /**
   * Section identifier
   * @type {string}
   * @required
   */
  id: '',
  
  /**
   * Section name
   * @type {string}
   * @required
   */
  name: '',
  
  /**
   * Section description
   * @type {string}
   */
  description: '',
  
  /**
   * Section layout (grid, flex, table, etc.)
   * @type {string}
   */
  layout: 'grid',
  
  /**
   * Section items
   * @type {Array}
   */
  items: []
};

/**
 * Field metadata schema
 */
const fieldMetadataSchema = {
  /**
   * Field identifier
   * @type {string}
   * @required
   */
  id: '',
  
  /**
   * Field name
   * @type {string}
   * @required
   */
  name: '',
  
  /**
   * Field type (text, number, select, checkbox, etc.)
   * @type {string}
   * @required
   */
  type: '',
  
  /**
   * Field label
   * @type {string}
   * @required
   */
  label: '',
  
  /**
   * Field placeholder
   * @type {string}
   */
  placeholder: '',
  
  /**
   * Field default value
   * @type {any}
   */
  defaultValue: null,
  
  /**
   * Whether the field is required
   * @type {boolean}
   */
  required: false,
  
  /**
   * Field validation rules
   * @type {Object}
   */
  validation: {},
  
  /**
   * Field options (for select, radio, etc.)
   * @type {Array}
   */
  options: []
};

/**
 * Action metadata schema
 */
const actionMetadataSchema = {
  /**
   * Action identifier
   * @type {string}
   * @required
   */
  id: '',
  
  /**
   * Action name
   * @type {string}
   * @required
   */
  name: '',
  
  /**
   * Action type (button, link, menu, etc.)
   * @type {string}
   * @required
   */
  type: '',
  
  /**
   * Action handler
   * @type {string}
   * @required
   */
  handler: '',
  
  /**
   * Action parameters
   * @type {Object}
   */
  parameters: {},
  
  /**
   * Action confirmation
   * @type {Object}
   */
  confirmation: {
    /**
     * Whether to show a confirmation dialog
     * @type {boolean}
     */
    show: false,
    
    /**
     * Confirmation dialog title
     * @type {string}
     */
    title: '',
    
    /**
     * Confirmation dialog message
     * @type {string}
     */
    message: ''
  }
};

/**
 * NovaVision metadata schemas
 */
const NovaVisionMetadataSchema = {
  base: baseMetadataSchema,
  dashboard: dashboardMetadataSchema,
  detail: detailMetadataSchema,
  form: formMetadataSchema,
  report: reportMetadataSchema,
  chart: chartMetadataSchema,
  widget: widgetMetadataSchema,
  section: sectionMetadataSchema,
  field: fieldMetadataSchema,
  action: actionMetadataSchema
};

module.exports = NovaVisionMetadataSchema;
