/**
 * Unit tests for BreakGlassProtocol
 */

const { expect } = require('chai');
const BreakGlassProtocol = require('../../access/BreakGlassProtocol');

describe('BreakGlassProtocol', () => {
  let breakGlassProtocol;
  
  beforeEach(() => {
    breakGlassProtocol = new BreakGlassProtocol({
      overrideTimeout: 300, // 5 minutes for testing
      reviewRequired: true
    });
  });
  
  describe('initiateOverride', () => {
    it('should initiate an override successfully', () => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH',
        location: {
          type: 'HOSPITAL',
          name: 'General Hospital'
        },
        deviceInfo: {
          type: 'TABLET',
          id: 'device-123'
        },
        targetProfileId: 'profile-123'
      };
      
      const result = breakGlassProtocol.initiateOverride(request);
      
      expect(result).to.be.an('object');
      expect(result.overrideId).to.be.a('string');
      expect(result.token).to.be.a('string');
      expect(result.timestamp).to.be.a('string');
      expect(result.expiresAt).to.be.a('string');
      expect(result.status).to.equal('ACTIVE');
      
      // Verify the override is stored
      const details = breakGlassProtocol.getOverrideDetails(result.overrideId);
      expect(details.found).to.be.true;
      expect(details.override.serviceId).to.equal(request.serviceId);
      expect(details.override.userId).to.equal(request.userId);
      expect(details.override.reason).to.equal(request.reason);
      expect(details.override.emergencyType).to.equal(request.emergencyType);
      expect(details.override.severityLevel).to.equal(request.severityLevel);
      expect(details.override.status).to.equal('ACTIVE');
    });
    
    it('should throw error for missing service ID', () => {
      const request = {
        // Missing serviceId
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      };
      
      expect(() => breakGlassProtocol.initiateOverride(request))
        .to.throw('Service ID is required');
    });
    
    it('should throw error for missing reason', () => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        // Missing reason
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      };
      
      expect(() => breakGlassProtocol.initiateOverride(request))
        .to.throw('Override reason is required');
    });
    
    it('should throw error for missing emergency type', () => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        // Missing emergencyType
        severityLevel: 'HIGH'
      };
      
      expect(() => breakGlassProtocol.initiateOverride(request))
        .to.throw('Emergency type is required');
    });
    
    it('should throw error for invalid severity level', () => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'INVALID'
      };
      
      expect(() => breakGlassProtocol.initiateOverride(request))
        .to.throw('Invalid severity level: INVALID');
    });
  });
  
  describe('validateOverride', () => {
    let overrideId;
    let token;
    
    beforeEach(() => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      };
      
      const result = breakGlassProtocol.initiateOverride(request);
      overrideId = result.overrideId;
      token = result.token;
    });
    
    it('should validate an active override', () => {
      const result = breakGlassProtocol.validateOverride(overrideId, token);
      
      expect(result).to.be.an('object');
      expect(result.valid).to.be.true;
      expect(result.override).to.be.an('object');
      expect(result.override.overrideId).to.equal(overrideId);
      expect(result.override.serviceId).to.equal('test-service');
      expect(result.override.userId).to.equal('test-user');
      expect(result.override.status).to.equal('ACTIVE');
    });
    
    it('should fail validation for invalid override ID', () => {
      const result = breakGlassProtocol.validateOverride('invalid-id', token);
      
      expect(result).to.be.an('object');
      expect(result.valid).to.be.false;
      expect(result.error).to.equal('Override not found or expired');
    });
    
    it('should fail validation for invalid token', () => {
      const result = breakGlassProtocol.validateOverride(overrideId, 'invalid-token');
      
      expect(result).to.be.an('object');
      expect(result.valid).to.be.false;
      expect(result.error).to.equal('Invalid override token');
    });
    
    it('should fail validation for expired override', (done) => {
      // Create a protocol with very short timeout
      const shortTimeoutProtocol = new BreakGlassProtocol({
        overrideTimeout: 1 // 1 second
      });
      
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      };
      
      const result = shortTimeoutProtocol.initiateOverride(request);
      const shortOverrideId = result.overrideId;
      const shortToken = result.token;
      
      // Wait for expiration
      setTimeout(() => {
        const validationResult = shortTimeoutProtocol.validateOverride(shortOverrideId, shortToken);
        
        expect(validationResult).to.be.an('object');
        expect(validationResult.valid).to.be.false;
        expect(validationResult.error).to.equal('Override has expired');
        
        done();
      }, 1100);
    }).timeout(2000);
  });
  
  describe('completeOverride', () => {
    let overrideId;
    let token;
    
    beforeEach(() => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      };
      
      const result = breakGlassProtocol.initiateOverride(request);
      overrideId = result.overrideId;
      token = result.token;
    });
    
    it('should complete an active override', () => {
      const result = breakGlassProtocol.completeOverride(overrideId, token, {
        profileAccessed: true,
        accessLevel: 'FULL',
        accessDuration: 120
      });
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.true;
      expect(result.overrideId).to.equal(overrideId);
      expect(result.status).to.equal('COMPLETED');
      
      // Verify the override status is updated
      const details = breakGlassProtocol.getOverrideDetails(overrideId);
      expect(details.found).to.be.true;
      expect(details.override.status).to.equal('COMPLETED');
      expect(details.override.completedAt).to.be.a('string');
      expect(details.override.result).to.be.an('object');
      expect(details.override.result.profileAccessed).to.be.true;
    });
    
    it('should fail completion for invalid override ID', () => {
      const result = breakGlassProtocol.completeOverride('invalid-id', token, {});
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.error).to.equal('Override not found or expired');
    });
    
    it('should fail completion for invalid token', () => {
      const result = breakGlassProtocol.completeOverride(overrideId, 'invalid-token', {});
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.error).to.equal('Invalid override token');
    });
  });
  
  describe('terminateOverride', () => {
    let overrideId;
    let token;
    
    beforeEach(() => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      };
      
      const result = breakGlassProtocol.initiateOverride(request);
      overrideId = result.overrideId;
      token = result.token;
    });
    
    it('should terminate an active override', () => {
      const result = breakGlassProtocol.terminateOverride(overrideId, token, 'No longer needed');
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.true;
      expect(result.overrideId).to.equal(overrideId);
      expect(result.status).to.equal('TERMINATED');
      
      // Verify the override status is updated
      const details = breakGlassProtocol.getOverrideDetails(overrideId);
      expect(details.found).to.be.true;
      expect(details.override.status).to.equal('TERMINATED');
      expect(details.override.terminatedAt).to.be.a('string');
      expect(details.override.terminationReason).to.equal('No longer needed');
    });
    
    it('should fail termination for invalid override ID', () => {
      const result = breakGlassProtocol.terminateOverride('invalid-id', token, 'No longer needed');
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.error).to.equal('Override not found or expired');
    });
    
    it('should fail termination for invalid token', () => {
      const result = breakGlassProtocol.terminateOverride(overrideId, 'invalid-token', 'No longer needed');
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.error).to.equal('Invalid override token');
    });
  });
  
  describe('reviewOverride', () => {
    let overrideId;
    
    beforeEach(() => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      };
      
      const result = breakGlassProtocol.initiateOverride(request);
      overrideId = result.overrideId;
      
      // Complete the override
      breakGlassProtocol.completeOverride(
        overrideId,
        result.token,
        { profileAccessed: true }
      );
    });
    
    it('should review a completed override', () => {
      const review = {
        reviewerId: 'reviewer-123',
        reviewerName: 'Dr. Reviewer',
        status: 'APPROVED',
        notes: 'Appropriate emergency access'
      };
      
      const result = breakGlassProtocol.reviewOverride(overrideId, review);
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.true;
      expect(result.reviewId).to.be.a('string');
      expect(result.status).to.equal('APPROVED');
      
      // Verify the review is recorded
      const details = breakGlassProtocol.getOverrideDetails(overrideId);
      expect(details.found).to.be.true;
      expect(details.override.reviewStatus).to.equal('APPROVED');
      expect(details.override.reviewNotes).to.be.an('array');
      expect(details.override.reviewNotes[0].reviewerId).to.equal('reviewer-123');
      expect(details.override.reviewNotes[0].notes).to.equal('Appropriate emergency access');
    });
    
    it('should fail review for invalid override ID', () => {
      const review = {
        reviewerId: 'reviewer-123',
        reviewerName: 'Dr. Reviewer',
        status: 'APPROVED',
        notes: 'Appropriate emergency access'
      };
      
      const result = breakGlassProtocol.reviewOverride('invalid-id', review);
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.error).to.equal('Override not found');
    });
    
    it('should fail review for invalid review information', () => {
      expect(() => breakGlassProtocol.reviewOverride(overrideId, null))
        .to.throw('Review information is incomplete');
      
      expect(() => breakGlassProtocol.reviewOverride(overrideId, {}))
        .to.throw('Review information is incomplete');
      
      expect(() => breakGlassProtocol.reviewOverride(overrideId, { reviewerId: 'reviewer-123' }))
        .to.throw('Review information is incomplete');
    });
    
    it('should fail review for invalid review status', () => {
      const review = {
        reviewerId: 'reviewer-123',
        reviewerName: 'Dr. Reviewer',
        status: 'INVALID_STATUS',
        notes: 'Appropriate emergency access'
      };
      
      const result = breakGlassProtocol.reviewOverride(overrideId, review);
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.error).to.equal('Invalid review status: INVALID_STATUS');
    });
    
    it('should fail review for active override', () => {
      // Create a new active override
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Another emergency',
        emergencyType: 'MEDICAL',
        severityLevel: 'MEDIUM'
      };
      
      const activeResult = breakGlassProtocol.initiateOverride(request);
      const activeOverrideId = activeResult.overrideId;
      
      const review = {
        reviewerId: 'reviewer-123',
        reviewerName: 'Dr. Reviewer',
        status: 'APPROVED',
        notes: 'Appropriate emergency access'
      };
      
      const result = breakGlassProtocol.reviewOverride(activeOverrideId, review);
      
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.error).to.equal('Cannot review active overrides');
    });
  });
  
  describe('getOverrideDetails', () => {
    let overrideId;
    
    beforeEach(() => {
      const request = {
        serviceId: 'test-service',
        userId: 'test-user',
        reason: 'Patient unconscious, immediate access needed',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      };
      
      const result = breakGlassProtocol.initiateOverride(request);
      overrideId = result.overrideId;
    });
    
    it('should get details for an override', () => {
      const details = breakGlassProtocol.getOverrideDetails(overrideId);
      
      expect(details).to.be.an('object');
      expect(details.found).to.be.true;
      expect(details.override).to.be.an('object');
      expect(details.override.overrideId).to.equal(overrideId);
      expect(details.override.serviceId).to.equal('test-service');
      expect(details.override.userId).to.equal('test-user');
      expect(details.override.reason).to.equal('Patient unconscious, immediate access needed');
      expect(details.override.emergencyType).to.equal('TRAUMA');
      expect(details.override.severityLevel).to.equal('HIGH');
      expect(details.override.status).to.equal('ACTIVE');
      
      // Should not include token
      expect(details.override.token).to.be.undefined;
    });
    
    it('should return not found for invalid override ID', () => {
      const details = breakGlassProtocol.getOverrideDetails('invalid-id');
      
      expect(details).to.be.an('object');
      expect(details.found).to.be.false;
      expect(details.error).to.equal('Override not found');
    });
    
    it('should throw error for missing override ID', () => {
      expect(() => breakGlassProtocol.getOverrideDetails(null))
        .to.throw('Override ID is required');
    });
  });
  
  describe('getOverrideLogs', () => {
    beforeEach(() => {
      // Create some overrides
      breakGlassProtocol.initiateOverride({
        serviceId: 'service-1',
        userId: 'user-1',
        reason: 'Emergency 1',
        emergencyType: 'TRAUMA',
        severityLevel: 'HIGH'
      });
      
      breakGlassProtocol.initiateOverride({
        serviceId: 'service-2',
        userId: 'user-2',
        reason: 'Emergency 2',
        emergencyType: 'MEDICAL',
        severityLevel: 'MEDIUM'
      });
    });
    
    it('should get all override logs', () => {
      const logs = breakGlassProtocol.getOverrideLogs();
      
      expect(logs).to.be.an('array');
      expect(logs.length).to.be.at.least(2);
      
      // Logs should be sorted by timestamp (newest first)
      expect(new Date(logs[0].timestamp).getTime())
        .to.be.at.least(new Date(logs[1].timestamp).getTime());
      
      // Should not include tokens
      expect(logs[0].token).to.be.undefined;
      expect(logs[1].token).to.be.undefined;
    });
    
    it('should filter logs by options', () => {
      const logs = breakGlassProtocol.getOverrideLogs({
        serviceId: 'service-1'
      });
      
      expect(logs).to.be.an('array');
      expect(logs.length).to.be.at.least(1);
      expect(logs[0].serviceId).to.equal('service-1');
    });
  });
  
  describe('notification targets', () => {
    it('should add notification target', () => {
      const target = {
        id: 'target-1',
        type: 'EMAIL',
        destination: '<EMAIL>',
        name: 'Admin Email'
      };
      
      const result = breakGlassProtocol.addNotificationTarget(target);
      expect(result).to.be.true;
      
      // Verify target was added
      expect(breakGlassProtocol.notificationTargets).to.include(target);
    });
    
    it('should throw error for invalid notification target', () => {
      expect(() => breakGlassProtocol.addNotificationTarget(null))
        .to.throw('Invalid notification target');
      
      expect(() => breakGlassProtocol.addNotificationTarget({}))
        .to.throw('Invalid notification target');
      
      expect(() => breakGlassProtocol.addNotificationTarget({ type: 'EMAIL' }))
        .to.throw('Invalid notification target');
    });
    
    it('should remove notification target', () => {
      const target = {
        id: 'target-1',
        type: 'EMAIL',
        destination: '<EMAIL>',
        name: 'Admin Email'
      };
      
      breakGlassProtocol.addNotificationTarget(target);
      
      const result = breakGlassProtocol.removeNotificationTarget('target-1');
      expect(result).to.be.true;
      
      // Verify target was removed
      expect(breakGlassProtocol.notificationTargets).to.not.include(target);
    });
    
    it('should return false when removing non-existent target', () => {
      const result = breakGlassProtocol.removeNotificationTarget('non-existent');
      expect(result).to.be.false;
    });
  });
});
