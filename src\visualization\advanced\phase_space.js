/**
 * Phase Space Visualization
 * 
 * This module provides a 3D visualization of the system state in phase space
 * using Three.js. It visualizes the trajectory of the system as it evolves
 * towards resonance.
 */

/**
 * PhaseSpaceVisualizer class
 */
class PhaseSpaceVisualizer {
  /**
   * Constructor
   * @param {HTMLElement} container - Container element
   * @param {Object} options - Configuration options
   */
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      width: container.clientWidth,
      height: container.clientHeight,
      backgroundColor: 0x111133,
      trajectoryColor: 0x00ffff,
      attractorColor: 0xff00ff,
      csdeColor: 0x0088ff, // Blue
      csfeColor: 0x22cc44, // Green
      csmeColor: 0x8844ff, // Purple
      maxTrajectoryPoints: 1000,
      showAxes: true,
      showLabels: true,
      autoRotate: true,
      ...options
    };
    
    // Initialize state
    this.state = {
      isInitialized: false,
      trajectoryPoints: [],
      currentPoint: null,
      attractor: null
    };
    
    // Initialize scene
    this.initScene();
    
    // Initialize camera
    this.initCamera();
    
    // Initialize renderer
    this.initRenderer();
    
    // Initialize controls
    this.initControls();
    
    // Initialize lights
    this.initLights();
    
    // Initialize axes
    if (this.options.showAxes) {
      this.initAxes();
    }
    
    // Initialize trajectory
    this.initTrajectory();
    
    // Initialize attractor
    this.initAttractor();
    
    // Initialize event listeners
    this.initEventListeners();
    
    // Start animation loop
    this.animate();
    
    // Mark as initialized
    this.state.isInitialized = true;
  }
  
  /**
   * Initialize scene
   */
  initScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(this.options.backgroundColor);
  }
  
  /**
   * Initialize camera
   */
  initCamera() {
    const { width, height } = this.options;
    const aspectRatio = width / height;
    
    this.camera = new THREE.PerspectiveCamera(45, aspectRatio, 0.1, 1000);
    this.camera.position.set(5, 5, 5);
    this.camera.lookAt(0, 0, 0);
  }
  
  /**
   * Initialize renderer
   */
  initRenderer() {
    const { width, height } = this.options;
    
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.renderer.setSize(width, height);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.shadowMap.enabled = true;
    
    this.container.appendChild(this.renderer.domElement);
  }
  
  /**
   * Initialize controls
   */
  initControls() {
    this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.rotateSpeed = 0.5;
    this.controls.autoRotate = this.options.autoRotate;
    this.controls.autoRotateSpeed = 0.5;
    this.controls.enablePan = true;
    this.controls.enableZoom = true;
    this.controls.minDistance = 2;
    this.controls.maxDistance = 20;
  }
  
  /**
   * Initialize lights
   */
  initLights() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);
    
    // Directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 10, 7.5);
    directionalLight.castShadow = true;
    
    // Configure shadow
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -10;
    directionalLight.shadow.camera.right = 10;
    directionalLight.shadow.camera.top = 10;
    directionalLight.shadow.camera.bottom = -10;
    
    this.scene.add(directionalLight);
  }
  
  /**
   * Initialize axes
   */
  initAxes() {
    const axesHelper = new THREE.AxesHelper(5);
    this.scene.add(axesHelper);
    
    if (this.options.showLabels) {
      // Add axis labels
      const createLabel = (text, position, color = 0xffffff) => {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 64;
        
        context.fillStyle = '#000000';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.font = 'Bold 24px Arial';
        context.fillStyle = `#${color.toString(16).padStart(6, '0')}`;
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(text, canvas.width / 2, canvas.height / 2);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        
        sprite.position.copy(position);
        sprite.scale.set(1, 0.5, 1);
        
        return sprite;
      };
      
      // X axis label (CSDE)
      const xLabel = createLabel('CSDE', new THREE.Vector3(5.5, 0, 0), this.options.csdeColor);
      this.scene.add(xLabel);
      
      // Y axis label (CSFE)
      const yLabel = createLabel('CSFE', new THREE.Vector3(0, 5.5, 0), this.options.csfeColor);
      this.scene.add(yLabel);
      
      // Z axis label (CSME)
      const zLabel = createLabel('CSME', new THREE.Vector3(0, 0, 5.5), this.options.csmeColor);
      this.scene.add(zLabel);
    }
  }
  
  /**
   * Initialize trajectory
   */
  initTrajectory() {
    // Create geometry for trajectory
    this.trajectoryGeometry = new THREE.BufferGeometry();
    
    // Create material for trajectory
    this.trajectoryMaterial = new THREE.LineBasicMaterial({
      color: this.options.trajectoryColor,
      linewidth: 2
    });
    
    // Create line for trajectory
    this.trajectoryLine = new THREE.Line(this.trajectoryGeometry, this.trajectoryMaterial);
    this.scene.add(this.trajectoryLine);
    
    // Create current point
    const geometry = new THREE.SphereGeometry(0.1, 16, 16);
    const material = new THREE.MeshPhongMaterial({
      color: this.options.trajectoryColor,
      emissive: this.options.trajectoryColor,
      emissiveIntensity: 0.5
    });
    
    this.currentPointMesh = new THREE.Mesh(geometry, material);
    this.scene.add(this.currentPointMesh);
  }
  
  /**
   * Initialize attractor
   */
  initAttractor() {
    // Create attractor at golden ratio point (0.618, 0.618, 0.618)
    const geometry = new THREE.SphereGeometry(0.2, 32, 32);
    const material = new THREE.MeshPhongMaterial({
      color: this.options.attractorColor,
      emissive: this.options.attractorColor,
      emissiveIntensity: 0.5,
      transparent: true,
      opacity: 0.8
    });
    
    this.attractorMesh = new THREE.Mesh(geometry, material);
    this.attractorMesh.position.set(0.618, 0.618, 0.618);
    this.scene.add(this.attractorMesh);
    
    // Add glow effect
    const glowGeometry = new THREE.SphereGeometry(0.3, 32, 32);
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: this.options.attractorColor,
      transparent: true,
      opacity: 0.2
    });
    
    this.attractorGlow = new THREE.Mesh(glowGeometry, glowMaterial);
    this.attractorGlow.position.copy(this.attractorMesh.position);
    this.scene.add(this.attractorGlow);
  }
  
  /**
   * Initialize event listeners
   */
  initEventListeners() {
    // Handle window resize
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // Handle container resize
    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.target === this.container) {
          this.onContainerResize();
        }
      }
    });
    
    resizeObserver.observe(this.container);
  }
  
  /**
   * Handle window resize
   */
  onWindowResize() {
    this.onContainerResize();
  }
  
  /**
   * Handle container resize
   */
  onContainerResize() {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;
    
    this.options.width = width;
    this.options.height = height;
    
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    
    this.renderer.setSize(width, height);
  }
  
  /**
   * Animation loop
   */
  animate() {
    requestAnimationFrame(this.animate.bind(this));
    
    // Update controls
    this.controls.update();
    
    // Animate attractor glow
    if (this.attractorGlow) {
      const scale = 1 + 0.1 * Math.sin(Date.now() * 0.002);
      this.attractorGlow.scale.set(scale, scale, scale);
    }
    
    // Render scene
    this.renderer.render(this.scene, this.camera);
  }
  
  /**
   * Add point to trajectory
   * @param {Object} point - Point to add
   */
  addPoint(point) {
    // Extract coordinates
    const { csde, csfe, csme } = point;
    
    // Add point to trajectory
    this.state.trajectoryPoints.push(new THREE.Vector3(csde, csfe, csme));
    
    // Limit trajectory length
    if (this.state.trajectoryPoints.length > this.options.maxTrajectoryPoints) {
      this.state.trajectoryPoints.shift();
    }
    
    // Update trajectory geometry
    this.updateTrajectory();
    
    // Update current point
    this.currentPointMesh.position.set(csde, csfe, csme);
    
    // Calculate distance to attractor
    const attractorPosition = this.attractorMesh.position;
    const distance = Math.sqrt(
      Math.pow(csde - attractorPosition.x, 2) +
      Math.pow(csfe - attractorPosition.y, 2) +
      Math.pow(csme - attractorPosition.z, 2)
    );
    
    // Update current point size based on distance to attractor
    const scale = 0.1 + 0.2 * (1 - Math.min(1, distance));
    this.currentPointMesh.scale.set(scale, scale, scale);
    
    // Update current point color based on distance to attractor
    const color = new THREE.Color();
    color.setHSL(
      (1 - Math.min(1, distance)) * 0.3, // Hue (0.3 = green)
      0.8, // Saturation
      0.5  // Lightness
    );
    
    this.currentPointMesh.material.color.copy(color);
    this.currentPointMesh.material.emissive.copy(color);
  }
  
  /**
   * Update trajectory
   */
  updateTrajectory() {
    // Update trajectory geometry
    this.trajectoryGeometry.setFromPoints(this.state.trajectoryPoints);
    this.trajectoryGeometry.attributes.position.needsUpdate = true;
  }
  
  /**
   * Clear trajectory
   */
  clearTrajectory() {
    this.state.trajectoryPoints = [];
    this.updateTrajectory();
  }
  
  /**
   * Set auto-rotate
   * @param {boolean} autoRotate - Whether to auto-rotate
   */
  setAutoRotate(autoRotate) {
    this.options.autoRotate = autoRotate;
    this.controls.autoRotate = autoRotate;
  }
  
  /**
   * Set background color
   * @param {number} color - Background color
   */
  setBackgroundColor(color) {
    this.options.backgroundColor = color;
    this.scene.background = new THREE.Color(color);
  }
  
  /**
   * Dispose
   */
  dispose() {
    // Remove event listeners
    window.removeEventListener('resize', this.onWindowResize);
    
    // Dispose renderer
    this.renderer.dispose();
    
    // Remove canvas
    if (this.renderer.domElement.parentNode) {
      this.renderer.domElement.parentNode.removeChild(this.renderer.domElement);
    }
    
    // Dispose controls
    this.controls.dispose();
  }
}

// Export
if (typeof module !== 'undefined') {
  module.exports = { PhaseSpaceVisualizer };
}
