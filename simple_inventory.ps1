# Simple script to generate file inventory
$outputFile = "$PWD\CODEBASE_INVENTORY_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
$baseDir = "d:\novafuse-api-superstore"

# Get all files recursively, excluding certain directories
$files = Get-ChildItem -Path $baseDir -Recurse -File -ErrorAction SilentlyContinue | 
    Where-Object { 
        $_.FullName -notmatch '\\node_modules\\' -and 
        $_.FullName -notmatch '\\.git\\' -and
        $_.FullName -notmatch '\\dist\\' -and
        $_.FullName -notmatch '\\build\\' -and
        $_.FullName -notmatch '\\venv\\' -and
        $_.FullName -notmatch '\\.next\\' -and
        $_.FullName -notmatch '\\target\\' -and
        $_.FullName -notmatch '\\bin\\' -and
        $_.FullName -notmatch '\\obj\\' -and
        $_.FullName -notmatch '\\coverage\\' -and
        $_.FullName -notmatch '\\cache\\'
    } | 
    Select-Object FullName, Name, Length, LastWriteTime |
    Sort-Object -Property LastWriteTime -Descending

# Group files by date (year-month)
$filesByDate = $files | Group-Object { $_.LastWriteTime.ToString('yyyy-MM') } | Sort-Object Name -Descending

# Generate markdown content
$markdown = "# Codebase Inventory - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
$markdown += "

## Summary"
$markdown += "
- **Total Files**: $($files.Count)"
$markdown += "
- **Total Size**: $([math]::Round(($files | Measure-Object -Property Length -Sum).Sum / 1MB, 2)) MB"
$markdown += "
- **Earliest Modified**: $(($files | Sort-Object LastWriteTime | Select-Object -First 1).LastWriteTime)"
$markdown += "
- **Most Recent**: $(($files | Sort-Object LastWriteTime -Descending | Select-Object -First 1).LastWriteTime)"

# Add files grouped by date
foreach ($dateGroup in $filesByDate) {
    $markdown += "
## $($dateGroup.Name)"
    $markdown += "
| File | Size | Last Modified | Path |"
    $markdown += "
|------|------|--------------|------|"
    
    foreach ($file in ($dateGroup.Group | Sort-Object LastWriteTime -Descending)) {
        $relativePath = $file.FullName.Replace($baseDir, '').TrimStart('\')
        $parentDir = Split-Path -Path $relativePath -Parent
        $markdown += "
| $($file.Name) | $($file.Length/1KB | [math]::Round(2)) KB | $($file.LastWriteTime) | $parentDir |"
    }
}

# Write to file
try {
    $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
    Write-Host "Inventory generated: $outputFile"
} catch {
    Write-Error "Error: $_"
    $outputFile = ".\CODEBASE_INVENTORY_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    $markdown | Out-File -FilePath $outputFile -Encoding utf8 -Force
    Write-Host "Inventory generated in current directory: $((Get-Item $outputFile).FullName)"
}

# Also output the location of the file
Write-Host "`nInventory file created at: $((Get-Item $outputFile).FullName)"
Write-Host "Total files processed: $($files.Count)"
