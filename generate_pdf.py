import markdown
from weasyprint import HTML
import os

def convert_md_to_pdf(md_file, pdf_file):
    # Read the markdown file
    with open(md_file, 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # Convert markdown to HTML
    html_content = markdown.markdown(md_content)
    
    # Add basic CSS styling
    styled_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            body {{ 
                font-family: Arial, sans-serif;
                line-height: 1.6;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }}
            h1, h2, h3, h4, h5, h6 {{ 
                color: #2c3e50;
                margin-top: 1.5em;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            code {{
                background-color: #f4f4f4;
                padding: 2px 5px;
                border-radius: 3px;
                font-family: monospace;
            }}
            pre {{
                background-color: #f8f8f8;
                padding: 10px;
                border-radius: 5px;
                overflow-x: auto;
            }}
        </style>
    </head>
    <body>
        {html_content}
    </body>
    </html>
    """
    
    # Generate PDF
    HTML(string=styled_html).write_pdf(pdf_file)
    print(f"✅ Successfully created {os.path.abspath(pdf_file)}")

if __name__ == "__main__":
    input_file = "Comphyological_Dictionary_Final.md"
    output_file = "Comphyological_Dictionary_1st_Edition.pdf"
    
    try:
        convert_md_to_pdf(input_file, output_file)
    except Exception as e:
        print(f"❌ Error generating PDF: {str(e)}")
        print("\nPlease make sure you have the required packages installed:")
        print("pip install weasyprint markdown")
