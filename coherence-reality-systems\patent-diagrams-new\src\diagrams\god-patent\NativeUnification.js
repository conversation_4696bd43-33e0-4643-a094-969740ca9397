import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../../components/DiagramComponents';

const NativeUnification = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>NATIVE UNIFICATION ARCHITECTURE</ContainerLabel>
      </ContainerBox>

      {/* Traditional Siloed Approach (Left Side) */}
      <ContainerBox width="300px" height="300px" left="50px" top="70px">
        <ContainerLabel>TRADITIONAL SILOED APPROACH</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="80px" top="120px" width="100px" height="50px">
        <ComponentNumber>201</ComponentNumber>
        <ComponentLabel>GRC</ComponentLabel>
        System
      </ComponentBox>

      <ComponentBox left="220px" top="120px" width="100px" height="50px">
        <ComponentNumber>202</ComponentNumber>
        <ComponentLabel>IT</ComponentLabel>
        System
      </ComponentBox>

      <ComponentBox left="150px" top="200px" width="100px" height="50px">
        <ComponentNumber>203</ComponentNumber>
        <ComponentLabel>Cybersecurity</ComponentLabel>
        System
      </ComponentBox>

      {/* Siloed Integration Points */}
      <CurvedArrow width="140" height="80" left="130" top="120">
        <path
          d="M 0,0 Q 40,40 90,0"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          strokeDasharray="5,5"
        />
        <polygon
          points="90,0 80,8 83,-2"
          fill="#333"
        />
      </CurvedArrow>

      <CurvedArrow width="140" height="80" left="130" top="170">
        <path
          d="M 70,0 Q 30,40 -20,30"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          strokeDasharray="5,5"
        />
        <polygon
          points="-20,30 -10,22 -13,32"
          fill="#333"
        />
      </CurvedArrow>

      <CurvedArrow width="140" height="80" left="200" top="170">
        <path
          d="M 0,0 Q 40,40 20,30"
          fill="none"
          stroke="#333"
          strokeWidth="2"
          strokeDasharray="5,5"
        />
        <polygon
          points="20,30 10,22 13,32"
          fill="#333"
        />
      </CurvedArrow>

      {/* Problems with Traditional Approach */}
      <ComponentBox left="70px" top="280px" width="80px" height="40px" style={{ opacity: 0.7, border: '1px solid #333' }}>
        <ComponentLabel style={{ color: '#333' }}>Data Silos</ComponentLabel>
      </ComponentBox>

      <ComponentBox left="160px" top="280px" width="80px" height="40px" style={{ opacity: 0.7, border: '1px solid #333' }}>
        <ComponentLabel style={{ color: '#333' }}>Gaps</ComponentLabel>
      </ComponentBox>

      <ComponentBox left="250px" top="280px" width="80px" height="40px" style={{ opacity: 0.7, border: '1px solid #333' }}>
        <ComponentLabel style={{ color: '#333' }}>Delays</ComponentLabel>
      </ComponentBox>

      {/* Native Unification Approach (Right Side) */}
      <ContainerBox width="300px" height="300px" left="425px" top="70px">
        <ContainerLabel>NATIVE UNIFICATION APPROACH</ContainerLabel>
      </ContainerBox>

      <ContainerBox width="200px" height="100px" left="475px" top="120px">
        <ContainerLabel>CYBER-SAFETY PROTOCOL</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="525px" top="160px" width="100px" height="40px">
        <ComponentNumber>204</ComponentNumber>
        <ComponentLabel>Native Unification</ComponentLabel>
        Engine
      </ComponentBox>

      {/* Unified Components */}
      <ComponentBox left="450px" top="240px" width="80px" height="40px">
        <ComponentNumber>205</ComponentNumber>
        <ComponentLabel>GRC</ComponentLabel>
      </ComponentBox>

      <ComponentBox left="540px" top="240px" width="80px" height="40px">
        <ComponentNumber>206</ComponentNumber>
        <ComponentLabel>IT</ComponentLabel>
      </ComponentBox>

      <ComponentBox left="630px" top="240px" width="80px" height="40px">
        <ComponentNumber>207</ComponentNumber>
        <ComponentLabel>Security</ComponentLabel>
      </ComponentBox>

      {/* Unified Integration Points */}
      <Arrow left="490px" top="200px" width="2px" height="40px" />
      <Arrow left="575px" top="200px" width="2px" height="40px" />
      <Arrow left="575px" top="200px" width="55px" height="2px" transform="rotate(45deg)" />

      {/* Benefits of Native Unification */}
      <ComponentBox left="450px" top="310px" width="80px" height="40px" style={{ opacity: 0.7, border: '1px solid #333' }}>
        <ComponentLabel style={{ color: '#333' }}>Unified Data</ComponentLabel>
      </ComponentBox>

      <ComponentBox left="540px" top="310px" width="80px" height="40px" style={{ opacity: 0.7, border: '1px solid #333' }}>
        <ComponentLabel style={{ color: '#333' }}>No Gaps</ComponentLabel>
      </ComponentBox>

      <ComponentBox left="630px" top="310px" width="80px" height="40px" style={{ opacity: 0.7, border: '1px solid #333' }}>
        <ComponentLabel style={{ color: '#333' }}>Real-Time</ComponentLabel>
      </ComponentBox>

      {/* Comparison Arrow */}
      <Arrow left="350px" top="200px" width="75px" />

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Traditional Systems</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Cyber-Safety Protocol</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#eee" />
          <LegendText>Problems</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#eee" />
          <LegendText>Benefits</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default NativeUnification;
