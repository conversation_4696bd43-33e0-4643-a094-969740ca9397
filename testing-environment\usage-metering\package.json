{"name": "novafuse-usage-metering", "version": "1.0.0", "description": "Usage metering service for NovaConnect", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.17.3", "mongoose": "^6.2.4", "cors": "^2.8.5", "body-parser": "^1.19.2", "winston": "^3.6.0"}, "devDependencies": {"nodemon": "^2.0.15", "jest": "^27.5.1", "supertest": "^6.2.2"}}