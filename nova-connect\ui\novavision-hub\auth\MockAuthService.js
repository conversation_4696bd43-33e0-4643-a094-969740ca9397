/**
 * Mock Authentication Service
 * 
 * This module provides a mock implementation of the authentication service for testing and development.
 */

import AuthService from './AuthService';

/**
 * Mock authentication service
 * 
 * @extends {AuthService}
 */
class MockAuthService extends AuthService {
  /**
   * Constructor
   * 
   * @param {Object} [options] - Options
   * @param {Object} [options.storage=localStorage] - Storage
   * @param {string} [options.storageKey='novavision_auth'] - Storage key
   * @param {number} [options.tokenExpiration=3600000] - Token expiration in milliseconds (1 hour)
   * @param {Object} [options.mockUsers] - Mock users
   */
  constructor(options = {}) {
    super();
    
    this.storage = options.storage || (typeof localStorage !== 'undefined' ? localStorage : null);
    this.storageKey = options.storageKey || 'novavision_auth';
    this.tokenExpiration = options.tokenExpiration || 3600000; // 1 hour
    
    // Mock users
    this.mockUsers = options.mockUsers || [
      {
        id: '1',
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Admin User',
        role: 'admin',
        photoURL: 'https://via.placeholder.com/150',
        createdAt: new Date().toISOString(),
        metadata: {
          lastSignInTime: new Date().toISOString()
        }
      },
      {
        id: '2',
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Regular User',
        role: 'user',
        photoURL: 'https://via.placeholder.com/150',
        createdAt: new Date().toISOString(),
        metadata: {
          lastSignInTime: new Date().toISOString()
        }
      }
    ];
    
    // Auth state change listeners
    this.listeners = [];
    
    // Initialize from storage
    this.initialize();
  }
  
  /**
   * Initialize from storage
   */
  initialize() {
    if (!this.storage) return;
    
    try {
      const authData = this.storage.getItem(this.storageKey);
      
      if (authData) {
        const { user, token, expiresAt } = JSON.parse(authData);
        
        // Check if token is expired
        if (expiresAt && new Date(expiresAt) > new Date()) {
          this.currentUser = user;
          this.currentToken = token;
          this.tokenExpiresAt = expiresAt;
          
          // Notify listeners
          this.notifyListeners(user);
        } else {
          // Clear expired token
          this.storage.removeItem(this.storageKey);
        }
      }
    } catch (error) {
      console.error('Error initializing auth from storage:', error);
    }
  }
  
  /**
   * Save to storage
   * 
   * @param {Object} user - User
   * @param {string} token - Token
   * @param {string} expiresAt - Expiration date
   */
  saveToStorage(user, token, expiresAt) {
    if (!this.storage) return;
    
    try {
      this.storage.setItem(this.storageKey, JSON.stringify({
        user,
        token,
        expiresAt
      }));
    } catch (error) {
      console.error('Error saving auth to storage:', error);
    }
  }
  
  /**
   * Clear storage
   */
  clearStorage() {
    if (!this.storage) return;
    
    try {
      this.storage.removeItem(this.storageKey);
    } catch (error) {
      console.error('Error clearing auth from storage:', error);
    }
  }
  
  /**
   * Notify listeners
   * 
   * @param {Object} user - User
   */
  notifyListeners(user) {
    this.listeners.forEach(listener => {
      try {
        listener(user);
      } catch (error) {
        console.error('Error in auth state change listener:', error);
      }
    });
  }
  
  /**
   * Generate token
   * 
   * @param {Object} user - User
   * @returns {string} Token
   */
  generateToken(user) {
    // In a real implementation, this would be a JWT
    return `mock_token_${user.id}_${Date.now()}`;
  }
  
  /**
   * Find user by email
   * 
   * @param {string} email - Email
   * @returns {Object|null} User or null if not found
   */
  findUserByEmail(email) {
    return this.mockUsers.find(user => user.email === email) || null;
  }
  
  /**
   * Find user by id
   * 
   * @param {string} id - User ID
   * @returns {Object|null} User or null if not found
   */
  findUserById(id) {
    return this.mockUsers.find(user => user.id === id) || null;
  }
  
  /**
   * Create user
   * 
   * @param {Object} userData - User data
   * @returns {Object} User
   */
  createUser(userData) {
    const newUser = {
      id: `${this.mockUsers.length + 1}`,
      ...userData,
      createdAt: new Date().toISOString(),
      metadata: {
        lastSignInTime: new Date().toISOString()
      }
    };
    
    this.mockUsers.push(newUser);
    
    return this.sanitizeUser(newUser);
  }
  
  /**
   * Sanitize user
   * 
   * @param {Object} user - User
   * @returns {Object} Sanitized user
   */
  sanitizeUser(user) {
    // Remove sensitive information
    const { password, ...sanitizedUser } = user;
    return sanitizedUser;
  }
  
  /**
   * Get current user
   * 
   * @returns {Promise<Object|null>} Current user or null if not authenticated
   */
  async getCurrentUser() {
    return this.currentUser || null;
  }
  
  /**
   * Sign in with email and password
   * 
   * @param {string} email - Email
   * @param {string} password - Password
   * @returns {Promise<Object>} User
   */
  async signInWithEmailAndPassword(email, password) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const user = this.findUserByEmail(email);
    
    if (!user || user.password !== password) {
      throw new Error('Invalid email or password');
    }
    
    // Update last sign in time
    user.metadata.lastSignInTime = new Date().toISOString();
    
    // Generate token
    const token = this.generateToken(user);
    const expiresAt = new Date(Date.now() + this.tokenExpiration).toISOString();
    
    // Save to storage
    const sanitizedUser = this.sanitizeUser(user);
    this.saveToStorage(sanitizedUser, token, expiresAt);
    
    // Update current user
    this.currentUser = sanitizedUser;
    this.currentToken = token;
    this.tokenExpiresAt = expiresAt;
    
    // Notify listeners
    this.notifyListeners(sanitizedUser);
    
    return sanitizedUser;
  }
  
  /**
   * Sign in with provider
   * 
   * @param {string} provider - Provider name
   * @returns {Promise<Object>} User
   */
  async signInWithProvider(provider) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // For mock purposes, just sign in as the first user
    const user = this.mockUsers[0];
    
    if (!user) {
      throw new Error('No mock users available');
    }
    
    // Update last sign in time
    user.metadata.lastSignInTime = new Date().toISOString();
    
    // Generate token
    const token = this.generateToken(user);
    const expiresAt = new Date(Date.now() + this.tokenExpiration).toISOString();
    
    // Save to storage
    const sanitizedUser = this.sanitizeUser(user);
    this.saveToStorage(sanitizedUser, token, expiresAt);
    
    // Update current user
    this.currentUser = sanitizedUser;
    this.currentToken = token;
    this.tokenExpiresAt = expiresAt;
    
    // Notify listeners
    this.notifyListeners(sanitizedUser);
    
    return sanitizedUser;
  }
  
  /**
   * Sign up with email and password
   * 
   * @param {string} email - Email
   * @param {string} password - Password
   * @param {Object} [userData={}] - Additional user data
   * @returns {Promise<Object>} User
   */
  async signUpWithEmailAndPassword(email, password, userData = {}) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Check if user already exists
    const existingUser = this.findUserByEmail(email);
    
    if (existingUser) {
      throw new Error('Email already in use');
    }
    
    // Create new user
    const newUser = this.createUser({
      email,
      password,
      displayName: userData.displayName || email.split('@')[0],
      role: 'user',
      photoURL: userData.photoURL || 'https://via.placeholder.com/150',
      ...userData
    });
    
    // Generate token
    const token = this.generateToken(newUser);
    const expiresAt = new Date(Date.now() + this.tokenExpiration).toISOString();
    
    // Save to storage
    this.saveToStorage(newUser, token, expiresAt);
    
    // Update current user
    this.currentUser = newUser;
    this.currentToken = token;
    this.tokenExpiresAt = expiresAt;
    
    // Notify listeners
    this.notifyListeners(newUser);
    
    return newUser;
  }
  
  /**
   * Sign out
   * 
   * @returns {Promise<void>}
   */
  async signOut() {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Clear storage
    this.clearStorage();
    
    // Clear current user
    this.currentUser = null;
    this.currentToken = null;
    this.tokenExpiresAt = null;
    
    // Notify listeners
    this.notifyListeners(null);
  }
  
  /**
   * Reset password
   * 
   * @param {string} email - Email
   * @returns {Promise<void>}
   */
  async resetPassword(email) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const user = this.findUserByEmail(email);
    
    if (!user) {
      throw new Error('User not found');
    }
    
    // In a real implementation, this would send a password reset email
    console.log(`Password reset email sent to ${email}`);
  }
  
  /**
   * Update user profile
   * 
   * @param {Object} user - User
   * @param {Object} data - Profile data
   * @returns {Promise<Object>} Updated user
   */
  async updateProfile(user, data) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const userToUpdate = this.findUserById(user.id);
    
    if (!userToUpdate) {
      throw new Error('User not found');
    }
    
    // Update user
    Object.assign(userToUpdate, data);
    
    // Save to storage
    const sanitizedUser = this.sanitizeUser(userToUpdate);
    this.saveToStorage(sanitizedUser, this.currentToken, this.tokenExpiresAt);
    
    // Update current user
    this.currentUser = sanitizedUser;
    
    // Notify listeners
    this.notifyListeners(sanitizedUser);
    
    return sanitizedUser;
  }
  
  /**
   * Change password
   * 
   * @param {Object} user - User
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {Promise<void>}
   */
  async changePassword(user, currentPassword, newPassword) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const userToUpdate = this.findUserById(user.id);
    
    if (!userToUpdate) {
      throw new Error('User not found');
    }
    
    if (userToUpdate.password !== currentPassword) {
      throw new Error('Current password is incorrect');
    }
    
    // Update password
    userToUpdate.password = newPassword;
  }
  
  /**
   * Get user token
   * 
   * @param {Object} user - User
   * @returns {Promise<string>} Token
   */
  async getToken(user) {
    // Check if token is expired
    if (this.tokenExpiresAt && new Date(this.tokenExpiresAt) <= new Date()) {
      throw new Error('Token expired');
    }
    
    return this.currentToken;
  }
  
  /**
   * Set up auth state change listener
   * 
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  onAuthStateChanged(callback) {
    this.listeners.push(callback);
    
    // Call immediately with current user
    if (callback && this.currentUser) {
      callback(this.currentUser);
    }
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }
}

export default MockAuthService;
