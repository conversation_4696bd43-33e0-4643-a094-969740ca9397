# Copy all .md files from novafuse-api-superstore to coherence-reality-systems
$sourceDir = "d:\novafuse-api-superstore"
$targetDir = "d:\novafuse-api-superstore\coherence-reality-systems"

# Get all .md files
$mdFiles = Get-ChildItem -Path $sourceDir -Recurse -File -Include *.md

# Create a log file
$logFile = Join-Path $targetDir "md-files-copy-log.txt"
"Copying .md files from novafuse-api-superstore to coherence-reality-systems" | Out-File -FilePath $logFile

# Copy each file
foreach ($file in $mdFiles) {
    $relativePath = $file.FullName.Substring($sourceDir.Length + 1)
    $targetPath = Join-Path $targetDir $relativePath
    
    # Create directory if it doesn't exist
    $targetDirPath = Split-Path $targetPath -Parent
    if (-not (Test-Path $targetDirPath)) {
        New-Item -ItemType Directory -Path $targetDirPath -Force
    }
    
    # Copy the file
    Copy-Item -Path $file.FullName -Destination $targetPath -Force
    
    # Log the copy
    "$($file.Name) copied to $targetPath" | Out-File -FilePath $logFile -Append
}

"Copy operation completed" | Out-File -FilePath $logFile -Append
