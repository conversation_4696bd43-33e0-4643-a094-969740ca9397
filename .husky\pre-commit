#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Run lint-staged
npx lint-staged

# Run security checks
echo "Running security checks..."

# Check for secrets
npx secretlint --maskSecrets --secretlintrc .secretlintrc.json .

# Run npm audit
npm audit --audit-level=high || (
  echo "⚠️ High severity vulnerabilities found. Review them above."
  echo "You can bypass this check with git commit --no-verify"
  exit 1
)

# Success
echo "✅ Pre-commit checks passed"
