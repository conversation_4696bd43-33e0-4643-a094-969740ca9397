/**
 * Jest Configuration
 * 
 * This file contains configuration for Jest tests.
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Test files
  testMatch: [
    '**/tests/unit/**/*.test.js',
    '**/tests/integration/**/*.test.js'
  ],
  
  // Coverage configuration
  collectCoverageFrom: [
    'controllers/**/*.js',
    'services/**/*.js',
    'models/**/*.js',
    'utils/**/*.js',
    '!**/node_modules/**',
    '!**/tests/**'
  ],
  coverageThreshold: {
    global: {
      statements: 85,
      branches: 85,
      functions: 85,
      lines: 85
    },
    './services/': {
      statements: 95,
      branches: 95,
      functions: 95,
      lines: 95
    }
  },
  
  // Test setup
  setupFilesAfterEnv: ['./tests/setup.js'],
  
  // Test timeout
  testTimeout: 30000,
  
  // Verbose output
  verbose: true
};
