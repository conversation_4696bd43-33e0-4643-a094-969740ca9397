/**
 * NovaAssure API Client
 * 
 * This client provides access to the NovaAssure API endpoints.
 * NovaAssure is the Universal Control Testing Framework (UCTF) component
 * of the NovaFuse Cyber-Safety Platform.
 */

import { AxiosInstance } from 'axios';
import axiosInstance from './axiosConfig';
import authService from './authService';

// Types
export interface ControlTest {
  _id: string;
  name: string;
  description: string;
  controlId: string;
  controlName: string;
  frameworkId: string;
  frameworkName: string;
  type: 'automated' | 'manual' | 'hybrid';
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually' | 'continuous';
  status: 'active' | 'inactive' | 'draft';
  lastRun: string | null;
  nextRun: string | null;
  owner: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  testSteps: TestStep[];
  automationConfig: AutomationConfig | null;
  evidenceRequirements: EvidenceRequirement[];
  tags: string[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  organizationId: string;
}

export interface TestStep {
  id: string;
  name: string;
  description: string;
  order: number;
  type: 'manual' | 'automated';
  expectedResult: string;
  automationScript?: string;
  automationPlatform?: string;
}

export interface AutomationConfig {
  platform: string;
  scriptType: string;
  scriptContent: string;
  parameters: Record<string, string>;
  timeout: number;
  retryCount: number;
  retryDelay: number;
  successCriteria: string;
  failureCriteria: string;
}

export interface EvidenceRequirement {
  id: string;
  name: string;
  description: string;
  type: 'document' | 'screenshot' | 'log' | 'data' | 'attestation';
  required: boolean;
  format: string[];
  retentionPeriod: number;
}

export interface TestRun {
  _id: string;
  testId: string;
  testName: string;
  controlId: string;
  controlName: string;
  frameworkId: string;
  frameworkName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  result: 'pass' | 'fail' | 'inconclusive' | null;
  startTime: string;
  endTime: string | null;
  duration: number | null;
  executedBy: string;
  executionType: 'manual' | 'automated' | 'hybrid';
  stepResults: StepResult[];
  evidence: Evidence[];
  notes: string;
  issues: TestIssue[];
  organizationId: string;
  createdAt: string;
  updatedAt: string;
}

export interface StepResult {
  stepId: string;
  stepName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  result: 'pass' | 'fail' | 'inconclusive' | null;
  notes: string;
  evidence: Evidence[];
  startTime: string;
  endTime: string | null;
  duration: number | null;
}

export interface Evidence {
  id: string;
  name: string;
  description: string;
  type: 'document' | 'screenshot' | 'log' | 'data' | 'attestation';
  format: string;
  url: string;
  hash: string;
  metadata: Record<string, any>;
  uploadedBy: string;
  uploadedAt: string;
  verificationStatus: 'pending' | 'verified' | 'rejected';
  verifiedBy: string | null;
  verifiedAt: string | null;
}

export interface TestIssue {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  assignedTo: string | null;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  resolutionNotes: string | null;
  resolvedBy: string | null;
  resolvedAt: string | null;
}

export interface TestSchedule {
  _id: string;
  testId: string;
  testName: string;
  controlId: string;
  controlName: string;
  frameworkId: string;
  frameworkName: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
  nextRun: string;
  lastRun: string | null;
  status: 'active' | 'paused';
  owner: string;
  notifyUsers: string[];
  createdAt: string;
  updatedAt: string;
  organizationId: string;
}

export interface ComplianceAttestation {
  _id: string;
  name: string;
  description: string;
  frameworkId: string;
  frameworkName: string;
  controlIds: string[];
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected' | 'expired';
  attestationDate: string;
  expirationDate: string;
  attestedBy: string;
  approvedBy: string | null;
  approvalDate: string | null;
  evidence: Evidence[];
  notes: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export class NovaAssureAPI {
  private client: AxiosInstance;
  private baseUrl: string;

  constructor(baseUrl = '/api/v1/novaassure') {
    this.baseUrl = baseUrl;
    
    // Use the configured axios instance with authentication and error handling
    this.client = axiosInstance;
    
    // Override the base URL if provided
    if (baseUrl !== '/api/v1/novaassure') {
      this.client.defaults.baseURL = baseUrl;
    }
  }

  /**
   * Get the current user's organization ID
   * @returns Organization ID or throws an error if not available
   */
  getOrganizationId(): string {
    const orgId = authService.getOrganizationId();
    if (!orgId) {
      throw new Error('Organization ID not available. User may not be authenticated.');
    }
    return orgId;
  }

  // Control Test Methods
  async getControlTests(params?: any): Promise<PaginatedResponse<ControlTest>> {
    const response = await this.client.get('/control-tests', { params });
    return response.data;
  }

  async getControlTestById(id: string): Promise<ControlTest> {
    const response = await this.client.get(`/control-tests/${id}`);
    return response.data.data;
  }

  async createControlTest(data: Partial<ControlTest>): Promise<ControlTest> {
    const response = await this.client.post('/control-tests', data);
    return response.data.data;
  }

  async updateControlTest(id: string, data: Partial<ControlTest>): Promise<ControlTest> {
    const response = await this.client.put(`/control-tests/${id}`, data);
    return response.data.data;
  }

  async deleteControlTest(id: string): Promise<boolean> {
    const response = await this.client.delete(`/control-tests/${id}`);
    return response.data.success;
  }

  async getControlTestsByFramework(frameworkId: string): Promise<ControlTest[]> {
    const response = await this.client.get(`/frameworks/${frameworkId}/control-tests`);
    return response.data.data;
  }

  async getControlTestsByControl(controlId: string): Promise<ControlTest[]> {
    const response = await this.client.get(`/controls/${controlId}/control-tests`);
    return response.data.data;
  }

  // Test Run Methods
  async getTestRuns(params?: any): Promise<PaginatedResponse<TestRun>> {
    const response = await this.client.get('/test-runs', { params });
    return response.data;
  }

  async getTestRunById(id: string): Promise<TestRun> {
    const response = await this.client.get(`/test-runs/${id}`);
    return response.data.data;
  }

  async createTestRun(testId: string, data: Partial<TestRun>): Promise<TestRun> {
    const response = await this.client.post(`/control-tests/${testId}/test-runs`, data);
    return response.data.data;
  }

  async updateTestRun(id: string, data: Partial<TestRun>): Promise<TestRun> {
    const response = await this.client.put(`/test-runs/${id}`, data);
    return response.data.data;
  }

  async getTestRunsByTest(testId: string): Promise<TestRun[]> {
    const response = await this.client.get(`/control-tests/${testId}/test-runs`);
    return response.data.data;
  }

  async getLatestTestRuns(limit: number = 10): Promise<TestRun[]> {
    const response = await this.client.get('/test-runs/latest', { params: { limit } });
    return response.data.data;
  }

  async startTestRun(testId: string): Promise<TestRun> {
    const response = await this.client.post(`/control-tests/${testId}/start`);
    return response.data.data;
  }

  async completeTestRun(testRunId: string, result: 'pass' | 'fail' | 'inconclusive', notes?: string): Promise<TestRun> {
    const response = await this.client.post(`/test-runs/${testRunId}/complete`, { result, notes });
    return response.data.data;
  }

  async cancelTestRun(testRunId: string, reason: string): Promise<TestRun> {
    const response = await this.client.post(`/test-runs/${testRunId}/cancel`, { reason });
    return response.data.data;
  }

  // Test Step Result Methods
  async updateStepResult(testRunId: string, stepId: string, data: Partial<StepResult>): Promise<StepResult> {
    const response = await this.client.put(`/test-runs/${testRunId}/steps/${stepId}`, data);
    return response.data.data;
  }

  // Evidence Methods
  async uploadEvidence(testRunId: string, data: FormData): Promise<Evidence> {
    const response = await this.client.post(`/test-runs/${testRunId}/evidence`, data, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data.data;
  }

  async getEvidence(testRunId: string): Promise<Evidence[]> {
    const response = await this.client.get(`/test-runs/${testRunId}/evidence`);
    return response.data.data;
  }

  async verifyEvidence(evidenceId: string, status: 'verified' | 'rejected', notes?: string): Promise<Evidence> {
    const response = await this.client.post(`/evidence/${evidenceId}/verify`, { status, notes });
    return response.data.data;
  }

  // Test Schedule Methods
  async getTestSchedules(params?: any): Promise<PaginatedResponse<TestSchedule>> {
    const response = await this.client.get('/test-schedules', { params });
    return response.data;
  }

  async createTestSchedule(testId: string, data: Partial<TestSchedule>): Promise<TestSchedule> {
    const response = await this.client.post(`/control-tests/${testId}/schedule`, data);
    return response.data.data;
  }

  async updateTestSchedule(scheduleId: string, data: Partial<TestSchedule>): Promise<TestSchedule> {
    const response = await this.client.put(`/test-schedules/${scheduleId}`, data);
    return response.data.data;
  }

  async deleteTestSchedule(scheduleId: string): Promise<boolean> {
    const response = await this.client.delete(`/test-schedules/${scheduleId}`);
    return response.data.success;
  }

  // Compliance Attestation Methods
  async getAttestations(params?: any): Promise<PaginatedResponse<ComplianceAttestation>> {
    const response = await this.client.get('/attestations', { params });
    return response.data;
  }

  async getAttestationById(id: string): Promise<ComplianceAttestation> {
    const response = await this.client.get(`/attestations/${id}`);
    return response.data.data;
  }

  async createAttestation(data: Partial<ComplianceAttestation>): Promise<ComplianceAttestation> {
    const response = await this.client.post('/attestations', data);
    return response.data.data;
  }

  async updateAttestation(id: string, data: Partial<ComplianceAttestation>): Promise<ComplianceAttestation> {
    const response = await this.client.put(`/attestations/${id}`, data);
    return response.data.data;
  }

  async approveAttestation(id: string, notes?: string): Promise<ComplianceAttestation> {
    const response = await this.client.post(`/attestations/${id}/approve`, { notes });
    return response.data.data;
  }

  async rejectAttestation(id: string, reason: string): Promise<ComplianceAttestation> {
    const response = await this.client.post(`/attestations/${id}/reject`, { reason });
    return response.data.data;
  }

  // Dashboard Data Methods
  async getTestingMetrics(organizationId?: string): Promise<any> {
    try {
      // Use provided organizationId or get from auth service
      const orgId = organizationId || this.getOrganizationId();
      
      const response = await this.client.get(`/organizations/${orgId}/testing-metrics`);
      return response.data.data;
    } catch (error) {
      console.error('Error getting testing metrics:', error);
      return {
        testsPassed: 0,
        testsFailed: 0,
        testsInconclusive: 0,
        testsPending: 0,
        controlsCovered: 0,
        controlsUncovered: 0,
        automationRate: 0,
        evidenceCount: 0
      };
    }
  }

  async getTestResultsByFramework(organizationId?: string): Promise<any> {
    try {
      // Use provided organizationId or get from auth service
      const orgId = organizationId || this.getOrganizationId();
      
      const response = await this.client.get(`/organizations/${orgId}/test-results-by-framework`);
      return response.data.data;
    } catch (error) {
      console.error('Error getting test results by framework:', error);
      return [];
    }
  }

  async getUpcomingTests(days: number = 7, organizationId?: string): Promise<any> {
    try {
      // Use provided organizationId or get from auth service
      const orgId = organizationId || this.getOrganizationId();
      
      const response = await this.client.get(`/organizations/${orgId}/upcoming-tests`, { params: { days } });
      return response.data.data;
    } catch (error) {
      console.error('Error getting upcoming tests:', error);
      return [];
    }
  }

  async getTestResultTrend(period: 'week' | 'month' | 'quarter' | 'year', organizationId?: string): Promise<any> {
    try {
      // Use provided organizationId or get from auth service
      const orgId = organizationId || this.getOrganizationId();
      
      const response = await this.client.get(`/organizations/${orgId}/test-result-trend`, { params: { period } });
      return response.data.data;
    } catch (error) {
      console.error('Error getting test result trend:', error);
      return [];
    }
  }
}

export default NovaAssureAPI;
