/**
 * MeterIntegrationInterface
 * 
 * This module implements the integration interface between the CSME engine and the Comphyon Meter.
 * It provides methods for preparing tensor data for the Meter and processing alert levels from the Meter.
 * 
 * The MeterIntegrationInterface is responsible for:
 * 1. Preparing domain-specific tensor data for the Meter
 * 2. Providing the ethical score for the Meter
 * 3. Processing alert levels from the Meter
 * 4. Supporting domain-specific tensor preparation
 */

const { EventEmitter } = require('events');

/**
 * MeterIntegrationInterface class
 */
class MeterIntegrationInterface extends EventEmitter {
  /**
   * Create a new MeterIntegrationInterface instance
   * @param {Object} csmeController - CSME controller instance
   * @param {Object} options - Configuration options
   */
  constructor(csmeController, options = {}) {
    super();
    
    if (!csmeController) {
      throw new Error('CSME controller is required');
    }
    
    this.csmeController = csmeController;
    this.options = {
      updateInterval: 1000, // ms
      enableLogging: false,
      alertThresholds: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      isConnected: false,
      lastUpdateTime: null,
      lastTensorData: null,
      lastAlertLevel: null
    };
    
    console.log('MeterIntegrationInterface initialized');
  }
  
  /**
   * Connect to the Comphyon Meter
   * @param {Object} meter - Comphyon Meter instance
   * @returns {boolean} - Connection success status
   */
  connect(meter) {
    if (!meter) {
      console.error('Meter instance is required');
      return false;
    }
    
    this.meter = meter;
    this.state.isConnected = true;
    this.state.lastUpdateTime = Date.now();
    
    // Subscribe to meter events
    if (this.meter.on) {
      this.meter.on('alert', this._handleMeterAlert.bind(this));
      this.meter.on('update', this._handleMeterUpdate.bind(this));
    }
    
    if (this.options.enableLogging) {
      console.log('Connected to Comphyon Meter');
    }
    
    // Start periodic updates
    this._startPeriodicUpdates();
    
    return true;
  }
  
  /**
   * Disconnect from the Comphyon Meter
   */
  disconnect() {
    if (!this.state.isConnected) {
      return;
    }
    
    // Unsubscribe from meter events
    if (this.meter && this.meter.removeListener) {
      this.meter.removeListener('alert', this._handleMeterAlert.bind(this));
      this.meter.removeListener('update', this._handleMeterUpdate.bind(this));
    }
    
    // Stop periodic updates
    this._stopPeriodicUpdates();
    
    this.meter = null;
    this.state.isConnected = false;
    
    if (this.options.enableLogging) {
      console.log('Disconnected from Comphyon Meter');
    }
  }
  
  /**
   * Prepare tensor data for the Meter
   * @returns {Array} - CSME tensor [T, I, E, c₃]
   */
  prepareTensorData() {
    try {
      // Get current biological data from CSME controller
      const biologicalData = this.csmeController.getCurrentBiologicalData();
      
      // Process biological data to extract tensor components
      const { coherence, tensor } = this.csmeController.processBiologicalData(biologicalData);
      
      // Store tensor data
      this.state.lastTensorData = tensor;
      this.state.lastUpdateTime = Date.now();
      
      return tensor;
    } catch (error) {
      console.error('Error preparing tensor data:', error);
      
      // Return default tensor if error occurs
      return [0.7, 0.9, 0.6, 0.82]; // [T, I, E, c₃]
    }
  }
  
  /**
   * Get ethical score for the Meter
   * @returns {number} - Ethical score (0-1)
   */
  getEthicalScore() {
    try {
      return this.csmeController.getEthicalScore();
    } catch (error) {
      console.error('Error getting ethical score:', error);
      return 0.82; // Default based on 18/82 principle
    }
  }
  
  /**
   * Process alert level from the Meter
   * @param {Object} alertData - Alert data from the Meter
   */
  processAlertLevel(alertData) {
    if (!alertData) {
      return;
    }
    
    const { level, reason, timestamp } = alertData;
    
    // Store alert level
    this.state.lastAlertLevel = level;
    
    // Emit alert event
    this.emit('alert', alertData);
    
    // Log alert if logging is enabled
    if (this.options.enableLogging) {
      console.log(`CSME Alert: ${level} - ${reason}`);
    }
    
    // Process alert based on level
    switch (level) {
      case 'critical':
        this._processCriticalAlert(alertData);
        break;
      case 'high':
        this._processHighAlert(alertData);
        break;
      case 'medium':
        this._processMediumAlert(alertData);
        break;
      case 'low':
        this._processLowAlert(alertData);
        break;
      default:
        // No action for unknown alert levels
        break;
    }
  }
  
  /**
   * Get current integration status
   * @returns {Object} - Integration status
   */
  getStatus() {
    return {
      isConnected: this.state.isConnected,
      lastUpdateTime: this.state.lastUpdateTime,
      lastTensorData: this.state.lastTensorData,
      lastAlertLevel: this.state.lastAlertLevel
    };
  }
  
  /**
   * Start periodic updates to the Meter
   * @private
   */
  _startPeriodicUpdates() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isConnected && this.meter) {
        // Prepare tensor data
        const tensor = this.prepareTensorData();
        
        // Get ethical score
        const ethicalScore = this.getEthicalScore();
        
        // Update meter if it has the appropriate methods
        if (this.meter.updateTensorData) {
          this.meter.updateTensorData('csme', tensor);
        }
        
        if (this.meter.updateEthicalScore) {
          this.meter.updateEthicalScore(ethicalScore);
        }
        
        // Emit update event
        this.emit('update', { tensor, ethicalScore });
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop periodic updates to the Meter
   * @private
   */
  _stopPeriodicUpdates() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Handle alert from the Meter
   * @param {Object} alertData - Alert data from the Meter
   * @private
   */
  _handleMeterAlert(alertData) {
    this.processAlertLevel(alertData);
  }
  
  /**
   * Handle update from the Meter
   * @param {Object} updateData - Update data from the Meter
   * @private
   */
  _handleMeterUpdate(updateData) {
    // Store update time
    this.state.lastUpdateTime = Date.now();
    
    // Emit update event
    this.emit('meter-update', updateData);
  }
  
  /**
   * Process critical alert
   * @param {Object} alertData - Alert data
   * @private
   */
  _processCriticalAlert(alertData) {
    // In a real implementation, this would trigger emergency protocols
    // For now, just notify the CSME controller
    if (this.csmeController.handleCriticalAlert) {
      this.csmeController.handleCriticalAlert(alertData);
    }
  }
  
  /**
   * Process high alert
   * @param {Object} alertData - Alert data
   * @private
   */
  _processHighAlert(alertData) {
    // In a real implementation, this would trigger high-priority protocols
    // For now, just notify the CSME controller
    if (this.csmeController.handleHighAlert) {
      this.csmeController.handleHighAlert(alertData);
    }
  }
  
  /**
   * Process medium alert
   * @param {Object} alertData - Alert data
   * @private
   */
  _processMediumAlert(alertData) {
    // In a real implementation, this would trigger medium-priority protocols
    // For now, just notify the CSME controller
    if (this.csmeController.handleMediumAlert) {
      this.csmeController.handleMediumAlert(alertData);
    }
  }
  
  /**
   * Process low alert
   * @param {Object} alertData - Alert data
   * @private
   */
  _processLowAlert(alertData) {
    // In a real implementation, this would trigger low-priority protocols
    // For now, just notify the CSME controller
    if (this.csmeController.handleLowAlert) {
      this.csmeController.handleLowAlert(alertData);
    }
  }
}

module.exports = MeterIntegrationInterface;
