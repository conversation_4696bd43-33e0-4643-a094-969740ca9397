<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>1. High-Level System Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>1. High-Level System Architecture</h1>

    <div class="diagram-container">
        <!-- NovaFuse Platform -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            NovaFuse Platform Architecture
            <div class="element-number">1</div>
        </div>

        <!-- Input Data Sources -->
        <div class="element" style="top: 150px; left: 50px; width: 250px; font-weight: bold; font-size: 16px;">
            Input Data Sources
            <div class="element-number">2</div>
        </div>

        <div class="element" style="top: 220px; left: 50px; width: 250px; font-size: 14px;">
            Structured Data<br>(databases, APIs, CSV)
            <div class="element-number">3</div>
        </div>

        <div class="element" style="top: 300px; left: 50px; width: 250px; font-size: 14px;">
            Unstructured Data<br>(documents, emails, logs)
            <div class="element-number">4</div>
        </div>

        <div class="element" style="top: 380px; left: 50px; width: 250px; font-size: 14px;">
            Real-time Streams<br>(events, telemetry)
            <div class="element-number">5</div>
        </div>

        <!-- Core Processing Layers -->
        <div class="element" style="top: 150px; left: 375px; width: 250px; font-weight: bold; font-size: 16px;">
            Core Processing Layers
            <div class="element-number">6</div>
        </div>

        <div class="element" style="top: 220px; left: 375px; width: 250px; font-size: 14px;">
            Data Ingestion Layer<br>(NovaConnect)
            <div class="element-number">7</div>
        </div>

        <div class="element" style="top: 300px; left: 375px; width: 250px; font-size: 14px;">
            Processing Layer<br>(NovaCore)
            <div class="element-number">8</div>
        </div>

        <div class="element" style="top: 380px; left: 375px; width: 250px; font-size: 14px;">
            Analytics Layer<br>(NovaThink)
            <div class="element-number">9</div>
        </div>

        <div class="element" style="top: 460px; left: 375px; width: 250px; font-size: 14px;">
            Visualization Layer<br>(NovaView)
            <div class="element-number">10</div>
        </div>

        <!-- Output Actions -->
        <div class="element" style="top: 150px; left: 700px; width: 250px; font-weight: bold; font-size: 16px;">
            Output Actions
            <div class="element-number">11</div>
        </div>

        <div class="element" style="top: 220px; left: 700px; width: 250px; font-size: 14px;">
            Automated Responses<br>(NovaFlow)
            <div class="element-number">12</div>
        </div>

        <div class="element" style="top: 300px; left: 700px; width: 250px; font-size: 14px;">
            Alerts & Notifications<br>(NovaPulse+)
            <div class="element-number">13</div>
        </div>

        <div class="element" style="top: 380px; left: 700px; width: 250px; font-size: 14px;">
            Compliance Reports<br>(NovaTrack)
            <div class="element-number">14</div>
        </div>

        <!-- Comphyology Framework -->
        <div class="element" style="top: 550px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Comphyology (<span class="bold-formula">Ψᶜ</span>) Framework
            <div class="element-number">15</div>
        </div>

        <div class="element" style="top: 620px; left: 100px; width: 250px; font-size: 14px;">
            UUFT Equation<br><span class="bold-formula">(A⊗B⊕C)×π10³</span>
            <div class="element-number">16</div>
        </div>

        <div class="element" style="top: 620px; left: 375px; width: 250px; font-size: 14px;">
            Trinity Equation<br><span class="bold-formula">U=T[∑(n=1 to 5) Sn⋅(En+In)⋅Φn]</span>
            <div class="element-number">17</div>
        </div>

        <div class="element" style="top: 620px; left: 650px; width: 250px; font-size: 14px;">
            Meta-Field Schema<br><span class="bold-formula">∑(Gₙ⊗Dₙ⊗Rₙ)·πⁿ</span>
            <div class="element-number">18</div>
        </div>

        <div class="element" style="top: 700px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            3-6-9-12-13 Alignment Architecture
            <div class="element-number">19</div>
        </div>

        <!-- Connections -->
        <!-- Connect NovaFuse Platform to sections -->
        <div class="connection" style="top: 100px; left: 175px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 100px; left: 175px; width: 125px; height: 2px;"></div>

        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 50px;"></div>

        <div class="connection" style="top: 100px; left: 825px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 100px; left: 700px; width: 125px; height: 2px;"></div>

        <!-- Connect Input Data Sources to specific sources -->
        <div class="connection" style="top: 200px; left: 175px; width: 2px; height: 20px;"></div>

        <div class="connection" style="top: 270px; left: 175px; width: 2px; height: 30px;"></div>

        <div class="connection" style="top: 350px; left: 175px; width: 2px; height: 30px;"></div>

        <!-- Connect Core Processing Layers to specific layers -->
        <div class="connection" style="top: 200px; left: 500px; width: 2px; height: 20px;"></div>

        <div class="connection" style="top: 270px; left: 500px; width: 2px; height: 30px;"></div>

        <div class="connection" style="top: 350px; left: 500px; width: 2px; height: 30px;"></div>

        <div class="connection" style="top: 430px; left: 500px; width: 2px; height: 30px;"></div>

        <!-- Connect Output Actions to specific actions -->
        <div class="connection" style="top: 200px; left: 825px; width: 2px; height: 20px;"></div>

        <div class="connection" style="top: 270px; left: 825px; width: 2px; height: 30px;"></div>

        <div class="connection" style="top: 350px; left: 825px; width: 2px; height: 30px;"></div>

        <!-- Connect to Comphyology Framework -->
        <div class="connection" style="top: 500px; left: 500px; width: 2px; height: 50px;"></div>

        <!-- Connect Comphyology Framework to components -->
        <div class="connection" style="top: 600px; left: 225px; width: 2px; height: 20px;"></div>
        <div class="connection" style="top: 600px; left: 225px; width: 75px; height: 2px;"></div>

        <div class="connection" style="top: 600px; left: 500px; width: 2px; height: 20px;"></div>

        <div class="connection" style="top: 600px; left: 775px; width: 2px; height: 20px;"></div>
        <div class="connection" style="top: 600px; left: 700px; width: 75px; height: 2px;"></div>

        <!-- Connect to Alignment Architecture -->
        <!-- Line removed as requested -->

        <!-- Connect Data Sources to Processing -->
        <div class="connection" style="top: 250px; left: 300px; width: 75px; height: 2px;"></div>

        <div class="connection" style="top: 330px; left: 300px; width: 75px; height: 2px;"></div>

        <div class="connection" style="top: 410px; left: 300px; width: 75px; height: 2px;"></div>

        <!-- Connect Processing to Output -->
        <div class="connection" style="top: 250px; left: 625px; width: 75px; height: 2px;"></div>

        <div class="connection" style="top: 330px; left: 625px; width: 75px; height: 2px;"></div>

        <div class="connection" style="top: 410px; left: 625px; width: 75px; height: 2px;"></div>
    </div>
</body>
</html>
