<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{name}} - Compliance Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #ddd;
    }
    .logo {
      max-width: 200px;
      margin-bottom: 20px;
    }
    h1 {
      color: #0A84FF;
      margin-bottom: 10px;
    }
    h2 {
      color: #0A84FF;
      margin-top: 30px;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;
    }
    h3 {
      color: #333;
      margin-top: 25px;
      margin-bottom: 10px;
    }
    .meta-info {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 30px;
    }
    .meta-info p {
      margin: 5px 0;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 30px;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .badge {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
      color: white;
    }
    .badge-green {
      background-color: #28a745;
    }
    .badge-red {
      background-color: #dc3545;
    }
    .badge-orange {
      background-color: #fd7e14;
    }
    .badge-gray {
      background-color: #6c757d;
    }
    .badge-blue {
      background-color: #0A84FF;
    }
    .progress {
      height: 20px;
      background-color: #f5f5f5;
      border-radius: 5px;
      overflow: hidden;
      margin-bottom: 10px;
    }
    .progress-bar {
      height: 100%;
      color: white;
      text-align: center;
      line-height: 20px;
    }
    .progress-bar-green {
      background-color: #28a745;
    }
    .progress-bar-orange {
      background-color: #fd7e14;
    }
    .progress-bar-red {
      background-color: #dc3545;
    }
    .summary-box {
      background-color: #f9f9f9;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 30px;
    }
    .summary-stats {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-bottom: 20px;
    }
    .stat-box {
      background-color: white;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      width: 23%;
      margin-bottom: 15px;
    }
    .stat-box h4 {
      margin-top: 0;
      margin-bottom: 5px;
      color: #666;
    }
    .stat-box .value {
      font-size: 24px;
      font-weight: bold;
      color: #0A84FF;
    }
    .footer {
      text-align: center;
      margin-top: 50px;
      padding-top: 20px;
      border-top: 1px solid #ddd;
      color: #666;
      font-size: 12px;
    }
    @media print {
      .container {
        width: 100%;
        max-width: none;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="data:image/png;base64,{{logoBase64}}" alt="NovaFuse Logo" class="logo">
      <h1>{{name}}</h1>
      <p>{{description}}</p>
    </div>

    <div class="meta-info">
      <p><strong>Report ID:</strong> {{_id}}</p>
      <p><strong>Framework:</strong> {{framework}}</p>
      <p><strong>Generated:</strong> {{formatDateTime createdAt}}</p>
      <p><strong>Period:</strong> {{formatDate startDate}} - {{formatDate endDate}}</p>
      <p><strong>Generated By:</strong> {{createdBy.name}}</p>
    </div>

    <div class="summary-box">
      <h2>Compliance Summary</h2>
      
      <div class="summary-stats">
        <div class="stat-box">
          <h4>Controls</h4>
          <div class="value">{{summary.totalControls}}</div>
        </div>
        <div class="stat-box">
          <h4>Implemented</h4>
          <div class="value">{{summary.implementedControls}}</div>
        </div>
        <div class="stat-box">
          <h4>Compliance Rate</h4>
          <div class="value">{{percentage summary.implementedControls summary.totalControls}}</div>
        </div>
        <div class="stat-box">
          <h4>Risk Level</h4>
          <div class="value">{{summary.overallRiskLevel}}</div>
        </div>
      </div>

      <h3>Implementation Progress</h3>
      {{progressBar summary.implementedControls summary.totalControls}}
      
      <h3>Implementation Status</h3>
      <table>
        <tr>
          <th>Status</th>
          <th>Count</th>
          <th>Percentage</th>
        </tr>
        <tr>
          <td>Implemented</td>
          <td>{{summary.implementedControls}}</td>
          <td>{{percentage summary.implementedControls summary.totalControls}}</td>
        </tr>
        <tr>
          <td>Partially Implemented</td>
          <td>{{summary.partiallyImplementedControls}}</td>
          <td>{{percentage summary.partiallyImplementedControls summary.totalControls}}</td>
        </tr>
        <tr>
          <td>Not Implemented</td>
          <td>{{summary.notImplementedControls}}</td>
          <td>{{percentage summary.notImplementedControls summary.totalControls}}</td>
        </tr>
      </table>
    </div>

    <h2>Controls</h2>
    
    {{#each categories}}
      <h3>{{name}}</h3>
      <table>
        <tr>
          <th>Control</th>
          <th>Description</th>
          <th>Risk Level</th>
          <th>Status</th>
          <th>Last Tested</th>
        </tr>
        {{#each controls}}
          <tr>
            <td>{{name}}</td>
            <td>{{description}}</td>
            <td>{{{riskLevelBadge riskLevel}}}</td>
            <td>{{{statusBadge implementationStatus}}}</td>
            <td>{{formatDate lastTestedDate}}</td>
          </tr>
        {{/each}}
      </table>
    {{/each}}

    <h2>Recommendations</h2>
    <ul>
      {{#each recommendations}}
        <li>
          <strong>{{title}}</strong>: {{description}}
        </li>
      {{/each}}
    </ul>

    <h2>Evidence Summary</h2>
    <table>
      <tr>
        <th>Category</th>
        <th>Evidence Count</th>
        <th>Verified</th>
      </tr>
      {{#each evidenceSummary}}
        <tr>
          <td>{{category}}</td>
          <td>{{count}}</td>
          <td>{{verifiedCount}} ({{percentage verifiedCount count}})</td>
        </tr>
      {{/each}}
    </table>

    <div class="footer">
      <p>This report was generated by NovaAssure (UCTF) - Universal Control Testing Framework</p>
      <p>© {{currentYear}} NovaFuse. All rights reserved.</p>
      <p>Report ID: {{_id}} | Generated: {{formatDateTime createdAt}}</p>
    </div>
  </div>
</body>
</html>
