/**
 * Self-Healing Tensor
 *
 * This module provides functionality for self-healing tensors that can
 * detect and repair damage, maintain coherence, and reduce entropy
 * through repeated healing cycles.
 *
 * Optimized with the 3-6-9-12-13 Resonance Pattern for maximum efficiency.
 */

const EventEmitter = require('events');
const { MAX_SAFE_BOUNDS, saturate, asymptotic } = require('./constants');
const { hardenInput, sanitizeTensor } = require('./input-sanitizer');

// Mathematical constants
const PI = Math.PI;
const PHI = (1 + Math.sqrt(5)) / 2;

// 3-6-9-12-13 Resonance Pattern constants
const RESONANCE_PATTERN = {
  HEALING_CYCLES: [3, 6, 9, 12],
  THRESHOLDS: [0.3, 0.6, 0.9],
  DECAY_RATES: [0.03, 0.06, 0.09, 0.12, 0.13],
  EFFECTIVENESS_TARGETS: [0.3, 0.6, 0.9]
};

/**
 * Self-Healing Tensor class
 */
class SelfHealingTensor extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Self-healing options
   */
  constructor(options = {}) {
    super();

    // Apply resonance-optimized defaults
    this.options = {
      healingThreshold: 0.6, // Aligned with 3-6-9 pattern (0.6)
      healingFactor: 0.6, // Aligned with 3-6-9 pattern (0.6)
      maxHealingCycles: 6, // Aligned with 3-6-9 pattern (6)
      autoHeal: true, // Whether to automatically heal damaged tensors
      entropyContainmentTarget: 0.03, // Aligned with 3-6-9 pattern (0.03)
      learningRate: 0.03, // Aligned with 3-6-9 pattern (0.03)
      targetEffectiveness: 0.6, // Aligned with 3-6-9 pattern (0.6)
      adaptiveThreshold: true, // Whether to use adaptive threshold
      resonanceLock: true, // Whether to lock healing to resonance pattern
      ...options
    };

    this.tensors = new Map();
    this.healingHistory = new Map();
    this.thresholdHistory = new Map(); // Track threshold adjustments

    // Initialize resonance state
    this.resonanceState = {
      active: this.options.resonanceLock,
      lastResonancePoint: 0,
      resonanceStrength: 0.3, // Start at lowest resonance point (0.3)
      resonanceHistory: []
    };
  }

  /**
   * Register a tensor for self-healing
   * @param {string} id - Tensor ID
   * @param {Object} tensor - Tensor to register
   * @param {string} domain - Domain to register tensor in
   * @returns {Object} - Registered tensor with self-healing properties
   */
  registerTensor(id, tensor, domain = 'universal') {
    // Sanitize tensor
    const sanitizedTensor = sanitizeTensor(tensor, domain);

    // Add self-healing properties
    const selfHealingTensor = {
      ...sanitizedTensor,
      domain,
      health: 1.0,
      entropyContainment: 0.01,
      selfHealingEnabled: true,
      healingCycles: 0,
      lastHealed: null,
      expectedValues: [...sanitizedTensor.values], // Store expected values for healing
      registeredAt: Date.now()
    };

    // Store tensor
    this.tensors.set(id, selfHealingTensor);

    // Initialize healing history
    this.healingHistory.set(id, []);

    this.emit('tensor-registered', { id, domain });

    return selfHealingTensor;
  }

  /**
   * Get a tensor
   * @param {string} id - Tensor ID
   * @returns {Object} - Tensor
   */
  getTensor(id) {
    return this.tensors.get(id);
  }

  /**
   * Update a tensor
   * @param {string} id - Tensor ID
   * @param {Object} updatedTensor - Updated tensor
   * @returns {Object} - Updated tensor with self-healing properties
   */
  updateTensor(id, updatedTensor) {
    const existingTensor = this.tensors.get(id);

    if (!existingTensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // Calculate health based on deviation from expected values
    const health = this._calculateHealth(existingTensor.expectedValues, updatedTensor.values);

    // Calculate entropy containment
    const entropyContainment = this._calculateEntropyContainment(updatedTensor.values);

    // Update tensor
    const updatedSelfHealingTensor = {
      ...updatedTensor,
      domain: existingTensor.domain,
      health,
      entropyContainment,
      selfHealingEnabled: existingTensor.selfHealingEnabled,
      healingCycles: existingTensor.healingCycles,
      lastHealed: existingTensor.lastHealed,
      expectedValues: existingTensor.expectedValues,
      registeredAt: existingTensor.registeredAt
    };

    // Store updated tensor
    this.tensors.set(id, updatedSelfHealingTensor);

    // Check if healing is needed
    if (this.options.autoHeal && health < this.options.healingThreshold) {
      return this.healTensor(id);
    }

    return updatedSelfHealingTensor;
  }

  /**
   * Heal a tensor
   * @param {string} id - Tensor ID
   * @returns {Object} - Healed tensor with diagnostic information
   */
  healTensor(id) {
    const tensor = this.tensors.get(id);

    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // Create diagnostic information
    const diagnostics = {
      healingAttempted: false,
      healingPerformed: false,
      reason: null,
      healthBefore: tensor.health,
      entropyBefore: tensor.entropyContainment
    };

    // Check if self-healing is enabled
    if (!tensor.selfHealingEnabled) {
      diagnostics.reason = 'Self-healing disabled';
      this.emit('healing-skipped', { id, reason: diagnostics.reason });
      return { tensor, diagnostics };
    }

    // Check if maximum healing cycles reached
    if (tensor.healingCycles >= this.options.maxHealingCycles) {
      diagnostics.reason = `Maximum healing cycles (${this.options.maxHealingCycles}) reached`;
      this.emit('healing-limit-reached', {
        id,
        healingCycles: tensor.healingCycles,
        maxCycles: this.options.maxHealingCycles
      });
      return { tensor, diagnostics };
    }

    // Calculate entropy-weighted healing threshold with resonance pattern
    const entropyFactor = tensor.entropyContainment / this.options.entropyContainmentTarget;

    // Apply resonance-optimized threshold if resonance lock is enabled
    let entropyWeightedThreshold;

    if (this.options.resonanceLock) {
      // Use the 3-6-9 pattern for thresholds
      if (entropyFactor <= 1) {
        entropyWeightedThreshold = RESONANCE_PATTERN.THRESHOLDS[0]; // 0.3
      } else if (entropyFactor <= 2) {
        entropyWeightedThreshold = RESONANCE_PATTERN.THRESHOLDS[1]; // 0.6
      } else {
        entropyWeightedThreshold = RESONANCE_PATTERN.THRESHOLDS[2]; // 0.9
      }

      // Log resonance-optimized threshold
      this.emit('resonance-optimization', {
        type: 'healing-threshold',
        value: entropyWeightedThreshold,
        entropyFactor,
        message: `Using resonance-optimized threshold: ${entropyWeightedThreshold.toFixed(3)}`
      });
    } else {
      // Use traditional calculation if resonance lock is disabled
      entropyWeightedThreshold = Math.max(
        0.3, // Lower minimum threshold floor to allow more healing
        this.options.healingThreshold * (1 - Math.min(0.5, entropyFactor * 0.8))
      );
    }

    // Check for emergency entropy-violation condition
    // Aligned with 3-6-9 pattern (0.3, 0.6, 0.9)
    const isEntropyViolation = tensor.entropyContainment > 0.09 && tensor.health < 0.3;

    // Check if health is above entropy-weighted threshold (no healing needed)
    if (tensor.health >= entropyWeightedThreshold && !isEntropyViolation) {
      diagnostics.reason = `Health (${tensor.health.toFixed(3)}) above entropy-weighted threshold (${entropyWeightedThreshold.toFixed(3)})`;
      diagnostics.thresholdDetails = {
        baseThreshold: this.options.healingThreshold,
        entropyFactor,
        entropyWeightedThreshold,
        entropyViolationCheck: {
          isViolation: isEntropyViolation,
          entropyLevel: tensor.entropyContainment,
          healthLevel: tensor.health
        }
      };
      this.emit('healing-skipped', { id, reason: diagnostics.reason, thresholdDetails: diagnostics.thresholdDetails });
      return { tensor, diagnostics };
    }

    // If we're here, either health is below threshold or we have an entropy violation
    if (isEntropyViolation) {
      diagnostics.reason = `Emergency healing triggered: entropy violation (entropy=${tensor.entropyContainment.toFixed(3)}, health=${tensor.health.toFixed(3)})`;
      this.emit('emergency-healing', {
        id,
        reason: diagnostics.reason,
        entropy: tensor.entropyContainment,
        health: tensor.health
      });
    }

    // Check if entropy containment is already at target
    if (tensor.entropyContainment <= this.options.entropyContainmentTarget) {
      diagnostics.reason = `Entropy containment (${tensor.entropyContainment.toFixed(3)}) already at or below target (${this.options.entropyContainmentTarget.toFixed(3)})`;
      this.emit('healing-skipped', { id, reason: diagnostics.reason });
      return { tensor, diagnostics };
    }

    // Mark healing as attempted
    diagnostics.healingAttempted = true;

    // Apply healing with current cycle count for resonance optimization
    const healedValues = this._applyHealing(tensor.values, tensor.expectedValues, tensor.healingCycles);

    // Calculate new health
    const newHealth = this._calculateHealth(tensor.expectedValues, healedValues);

    // Calculate new entropy containment
    const newEntropyContainment = this._calculateEntropyContainment(healedValues);

    // Check if healing was effective
    const healthImprovement = newHealth - tensor.health;
    const entropyReduction = tensor.entropyContainment - newEntropyContainment;

    // Calculate healing efficiency score (0-1)
    // This measures how much improvement was achieved relative to what was needed
    const healthEfficiency = healthImprovement / (1 - tensor.health);
    const entropyEfficiency = entropyReduction / (tensor.entropyContainment - this.options.entropyContainmentTarget);

    // Combined efficiency score (weighted 70% health, 30% entropy)
    const healingEfficiencyScore = (healthEfficiency * 0.7) + (entropyEfficiency * 0.3);

    // If healing wasn't effective, return original tensor
    if (healthImprovement < 0.001 && entropyReduction < 0.001) {
      diagnostics.reason = 'Healing ineffective (no significant improvement)';
      diagnostics.efficiencyScore = {
        health: healthEfficiency,
        entropy: entropyEfficiency,
        combined: healingEfficiencyScore
      };
      this.emit('healing-ineffective', {
        id,
        healthImprovement,
        entropyReduction,
        efficiencyScore: diagnostics.efficiencyScore
      });
      return { tensor, diagnostics };
    }

    // Mark healing as performed
    diagnostics.healingPerformed = true;

    // Create healed tensor
    const healedTensor = {
      ...tensor,
      values: healedValues,
      health: newHealth,
      entropyContainment: newEntropyContainment,
      healingCycles: tensor.healingCycles + 1,
      lastHealed: Date.now()
    };

    // Store healed tensor
    this.tensors.set(id, healedTensor);

    // Update healing history
    const history = this.healingHistory.get(id) || [];
    history.push({
      cycle: healedTensor.healingCycles,
      timestamp: healedTensor.lastHealed,
      healthBefore: tensor.health,
      healthAfter: newHealth,
      entropyContainmentBefore: tensor.entropyContainment,
      entropyContainmentAfter: newEntropyContainment,
      improvement: {
        health: healthImprovement,
        entropy: entropyReduction
      }
    });
    this.healingHistory.set(id, history);

    // Update diagnostics
    diagnostics.healthAfter = newHealth;
    diagnostics.entropyAfter = newEntropyContainment;
    diagnostics.improvement = {
      health: healthImprovement,
      entropy: entropyReduction
    };
    diagnostics.efficiencyScore = {
      health: healthEfficiency,
      entropy: entropyEfficiency,
      combined: healingEfficiencyScore
    };

    // Adjust healing threshold based on effectiveness if adaptive threshold is enabled
    if (this.options.adaptiveThreshold) {
      this._adjustHealingThreshold(id, diagnostics.efficiencyScore.combined);
    }

    // Emit healing event
    this.emit('tensor-healed', {
      id,
      healingCycle: healedTensor.healingCycles,
      healthImprovement,
      entropyReduction,
      efficiencyScore: diagnostics.efficiencyScore,
      currentThreshold: this.options.healingThreshold
    });

    return { tensor: healedTensor, diagnostics };
  }

  /**
   * Perform repeated healing cycles
   * @param {string} id - Tensor ID
   * @param {number} cycles - Number of healing cycles or damage level for auto-calculation
   * @param {boolean} isDamageLevel - Whether cycles parameter is actually a damage level
   * @returns {Object} - Healing result with diagnostics
   */
  performRepeatedHealing(id, cycles = 1, isDamageLevel = false) {
    let tensor = this.tensors.get(id);

    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // If cycles parameter is actually a damage level, calculate optimal cycles
    if (isDamageLevel) {
      const damageLevel = cycles;
      cycles = this.options.resonanceLock
        ? this.getResonanceOptimizedCycles(damageLevel)
        : Math.ceil(damageLevel / 0.1); // Traditional calculation

      // Log resonance-optimized cycles
      if (this.options.resonanceLock) {
        this.emit('resonance-optimization', {
          type: 'healing-cycles',
          value: cycles,
          damageLevel,
          message: `Using resonance-optimized healing cycles: ${cycles} for damage level ${damageLevel.toFixed(3)}`
        });
      }
    }

    const initialTensor = { ...tensor };
    const healingResults = [];
    const diagnostics = [];
    let actualCyclesPerformed = 0;
    let healingAttempted = false;

    // Perform healing cycles
    for (let i = 0; i < cycles; i++) {
      // Skip if maximum healing cycles reached
      if (tensor.healingCycles >= this.options.maxHealingCycles) {
        diagnostics.push({
          cycle: i + 1,
          attempted: false,
          reason: `Maximum healing cycles (${this.options.maxHealingCycles}) reached`
        });
        break;
      }

      // Heal tensor
      healingAttempted = true;
      const healingResult = this.healTensor(id);
      tensor = healingResult.tensor;

      // Store diagnostics
      diagnostics.push({
        cycle: i + 1,
        attempted: true,
        performed: healingResult.diagnostics.healingPerformed,
        reason: healingResult.diagnostics.reason,
        improvement: healingResult.diagnostics.healingPerformed ? healingResult.diagnostics.improvement : null,
      efficiencyScore: healingResult.diagnostics.healingPerformed ? healingResult.diagnostics.efficiencyScore : null
      });

      // If healing was performed, increment counter
      if (healingResult.diagnostics.healingPerformed) {
        actualCyclesPerformed++;

        // Store result
        healingResults.push({
          cycle: tensor.healingCycles,
          health: tensor.health,
          entropyContainment: tensor.entropyContainment,
          improvement: healingResult.diagnostics.improvement
        });
      }

      // Stop if entropy containment target reached
      if (tensor.entropyContainment <= this.options.entropyContainmentTarget) {
        diagnostics.push({
          cycle: i + 1,
          attempted: false,
          reason: `Entropy containment target (${this.options.entropyContainmentTarget.toFixed(3)}) reached`
        });
        break;
      }

      // Stop if healing was ineffective
      if (healingResult.diagnostics.healingAttempted && !healingResult.diagnostics.healingPerformed) {
        diagnostics.push({
          cycle: i + 1,
          attempted: true,
          performed: false,
          reason: 'Healing ineffective, stopping further attempts'
        });
        break;
      }
    }

    // Categorize the healing outcome
    let outcomeCategory;

    if (!healingAttempted) {
      outcomeCategory = 'auto-pass';
    } else if (actualCyclesPerformed === 0) {
      outcomeCategory = 'no-healing';
    } else if (tensor.health < 0.7) {
      outcomeCategory = 'critical';
    } else if (tensor.entropyContainment > 0.03) {
      outcomeCategory = 'high-entropy';
    } else if (actualCyclesPerformed < cycles) {
      outcomeCategory = 'partial-heal';
    } else {
      outcomeCategory = 'full-heal';
    }

    // Calculate overall healing effectiveness (0-1)
    const healthEffectiveness = tensor.health - initialTensor.health > 0 ?
      (tensor.health - initialTensor.health) / (1 - initialTensor.health) : 0;

    const entropyEffectiveness = initialTensor.entropyContainment - tensor.entropyContainment > 0 ?
      (initialTensor.entropyContainment - tensor.entropyContainment) /
      (initialTensor.entropyContainment - this.options.entropyContainmentTarget) : 0;

    // Combined effectiveness (weighted 70% health, 30% entropy)
    const overallEffectiveness = (healthEffectiveness * 0.7) + (entropyEffectiveness * 0.3);

    return {
      id,
      initialHealth: initialTensor.health,
      finalHealth: tensor.health,
      initialEntropyContainment: initialTensor.entropyContainment,
      finalEntropyContainment: tensor.entropyContainment,
      cyclesAttempted: cycles,
      cyclesPerformed: actualCyclesPerformed,
      healingAttempted,
      healingResults,
      diagnostics,
      entropyContainmentImproved: tensor.entropyContainment < initialTensor.entropyContainment,
      healthImproved: tensor.health > initialTensor.health,
      outcome: {
        category: outcomeCategory,
        effectiveness: {
          health: healthEffectiveness,
          entropy: entropyEffectiveness,
          overall: overallEffectiveness
        }
      }
    };
  }

  /**
   * Get resonance-optimized healing cycles based on damage level
   * @param {number} damageLevel - Damage level (0-1)
   * @returns {number} - Optimal number of healing cycles
   */
  getResonanceOptimizedCycles(damageLevel) {
    // Map damage level to resonance pattern
    if (damageLevel <= 0.3) {
      return RESONANCE_PATTERN.HEALING_CYCLES[0]; // 3 cycles
    } else if (damageLevel <= 0.6) {
      return RESONANCE_PATTERN.HEALING_CYCLES[1]; // 6 cycles
    } else if (damageLevel <= 0.9) {
      return RESONANCE_PATTERN.HEALING_CYCLES[2]; // 9 cycles
    } else {
      return RESONANCE_PATTERN.HEALING_CYCLES[3]; // 12 cycles
    }
  }

  /**
   * Get resonance-optimized decay rate based on entropy level
   * @param {number} entropyLevel - Current entropy level
   * @returns {number} - Optimal decay rate
   */
  getResonanceOptimizedDecayRate(entropyLevel) {
    // Map entropy level to resonance pattern
    if (entropyLevel <= 0.03) {
      return RESONANCE_PATTERN.DECAY_RATES[0]; // 0.03
    } else if (entropyLevel <= 0.06) {
      return RESONANCE_PATTERN.DECAY_RATES[1]; // 0.06
    } else if (entropyLevel <= 0.09) {
      return RESONANCE_PATTERN.DECAY_RATES[2]; // 0.09
    } else if (entropyLevel <= 0.12) {
      return RESONANCE_PATTERN.DECAY_RATES[3]; // 0.12
    } else {
      return RESONANCE_PATTERN.DECAY_RATES[4]; // 0.13
    }
  }

  /**
   * Apply entropy decay to a tensor
   * @param {string} id - Tensor ID
   * @param {number} decayRate - Decay rate (0-1) or null for auto-calculation
   * @returns {Object} - Tensor with decayed entropy
   */
  applyEntropyDecay(id, decayRate = null) {
    const tensor = this.tensors.get(id);

    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // Calculate resonance-optimized decay rate if not provided
    if (decayRate === null) {
      decayRate = this.getResonanceOptimizedDecayRate(tensor.entropyContainment);

      // Log resonance-optimized decay rate
      this.emit('resonance-optimization', {
        type: 'decay-rate',
        value: decayRate,
        entropyLevel: tensor.entropyContainment,
        message: `Using resonance-optimized decay rate: ${decayRate.toFixed(3)}`
      });
    }

    // Calculate new entropy containment with decay
    const newEntropyContainment = Math.max(
      this.options.entropyContainmentTarget, // Minimum entropy floor
      tensor.entropyContainment * (1 - decayRate)
    );

    // Calculate entropy reduction
    const entropyReduction = tensor.entropyContainment - newEntropyContainment;

    // Skip if no significant reduction
    if (entropyReduction < 0.001) {
      return {
        tensor,
        decayApplied: false,
        reason: 'No significant entropy reduction'
      };
    }

    // Create decayed tensor
    const decayedTensor = {
      ...tensor,
      entropyContainment: newEntropyContainment,
      lastDecayed: Date.now()
    };

    // Store decayed tensor
    this.tensors.set(id, decayedTensor);

    // Emit decay event
    this.emit('entropy-decayed', {
      id,
      entropyBefore: tensor.entropyContainment,
      entropyAfter: newEntropyContainment,
      entropyReduction,
      decayRate
    });

    return {
      tensor: decayedTensor,
      decayApplied: true,
      entropyReduction,
      entropyBefore: tensor.entropyContainment,
      entropyAfter: newEntropyContainment
    };
  }

  /**
   * Damage a tensor (for testing)
   * @param {string} id - Tensor ID
   * @param {number} damageLevel - Damage level (0-1)
   * @returns {Object} - Damaged tensor
   */
  damageTensor(id, damageLevel) {
    const tensor = this.tensors.get(id);

    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // Apply damage to tensor values
    const damagedValues = tensor.values.map(v => {
      const noise = (Math.random() - 0.5) * damageLevel * 2;
      return saturate.forDomain(tensor.domain, v + noise);
    });

    // Calculate new health
    const newHealth = Math.max(0, tensor.health - damageLevel);

    // Calculate new entropy containment
    const newEntropyContainment = Math.min(
      MAX_SAFE_BOUNDS[tensor.domain.toUpperCase()].MAX_ENTROPY_CONTAINMENT,
      tensor.entropyContainment + damageLevel
    );

    // Create damaged tensor
    const damagedTensor = {
      ...tensor,
      values: damagedValues,
      health: newHealth,
      entropyContainment: newEntropyContainment
    };

    // Store damaged tensor
    this.tensors.set(id, damagedTensor);

    // Emit damage event
    this.emit('tensor-damaged', {
      id,
      damageLevel,
      healthBefore: tensor.health,
      healthAfter: newHealth,
      entropyContainmentBefore: tensor.entropyContainment,
      entropyContainmentAfter: newEntropyContainment
    });

    return damagedTensor;
  }

  /**
   * Get healing history for a tensor
   * @param {string} id - Tensor ID
   * @returns {Array} - Healing history
   */
  getHealingHistory(id) {
    return this.healingHistory.get(id) || [];
  }

  /**
   * Calculate health based on deviation from expected values
   * @param {Array} expectedValues - Expected values
   * @param {Array} actualValues - Actual values
   * @returns {number} - Health (0-1)
   * @private
   */
  _calculateHealth(expectedValues, actualValues) {
    if (expectedValues.length !== actualValues.length) {
      return 0;
    }

    let sumSquaredDiff = 0;
    for (let i = 0; i < expectedValues.length; i++) {
      sumSquaredDiff += Math.pow(expectedValues[i] - actualValues[i], 2);
    }

    const rmsDiff = Math.sqrt(sumSquaredDiff / expectedValues.length);
    const health = 1 - rmsDiff;

    return Math.max(0, Math.min(1, health));
  }

  /**
   * Calculate entropy containment
   * @param {Array} values - Tensor values
   * @returns {number} - Entropy containment (0-1)
   * @private
   */
  _calculateEntropyContainment(values) {
    // Calculate variance as a simple entropy measure
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;

    // Map variance to entropy containment (higher variance = higher entropy)
    return Math.min(0.05, variance * 10);
  }

  /**
   * Get resonance-optimized healing factor based on healing cycle
   * @param {number} healingCycle - Current healing cycle
   * @returns {number} - Optimal healing factor
   */
  getResonanceOptimizedHealingFactor(healingCycle) {
    // Use 3-6-9 pattern for healing factors
    if (healingCycle < 3) {
      return 0.3; // First 3 cycles use 0.3
    } else if (healingCycle < 6) {
      return 0.6; // Cycles 3-5 use 0.6
    } else if (healingCycle < 9) {
      return 0.9; // Cycles 6-8 use 0.9
    } else {
      return 0.6; // Cycles 9+ use 0.6 (avoid over-healing)
    }
  }

  /**
   * Apply healing to tensor values
   * @param {Array} values - Current values
   * @param {Array} expectedValues - Expected values
   * @param {number} healingCycle - Current healing cycle
   * @returns {Array} - Healed values
   * @private
   */
  _applyHealing(values, expectedValues, healingCycle = 0) {
    // Get resonance-optimized healing factor if resonance lock is enabled
    const healingFactor = this.options.resonanceLock
      ? this.getResonanceOptimizedHealingFactor(healingCycle)
      : this.options.healingFactor;

    // Log resonance-optimized healing factor if different from default
    if (this.options.resonanceLock && healingFactor !== this.options.healingFactor) {
      this.emit('resonance-optimization', {
        type: 'healing-factor',
        value: healingFactor,
        cycle: healingCycle,
        message: `Using resonance-optimized healing factor: ${healingFactor.toFixed(3)} for cycle ${healingCycle}`
      });
    }

    return values.map((v, i) => {
      // Move value closer to expected value
      const expectedValue = expectedValues[i];
      const healedValue = v + (expectedValue - v) * healingFactor;

      // Ensure value is finite
      return Number.isFinite(healedValue) ? healedValue : expectedValue;
    });
  }

  /**
   * Adjust healing threshold based on healing effectiveness
   * @param {string} id - Tensor ID
   * @param {number} effectiveness - Healing effectiveness (0-1)
   * @private
   */
  _adjustHealingThreshold(id, effectiveness) {
    // Calculate adjustment based on difference from target effectiveness
    const adjustment = this.options.learningRate * (effectiveness - this.options.targetEffectiveness);

    // Apply adjustment to healing threshold (with bounds)
    const oldThreshold = this.options.healingThreshold;
    this.options.healingThreshold = Math.max(0.3, Math.min(0.9, oldThreshold + adjustment));

    // Store adjustment in threshold history
    const history = this.thresholdHistory.get(id) || [];
    history.push({
      timestamp: Date.now(),
      oldThreshold,
      newThreshold: this.options.healingThreshold,
      effectiveness,
      adjustment
    });
    this.thresholdHistory.set(id, history);

    // Emit threshold adjustment event
    this.emit('threshold-adjusted', {
      id,
      oldThreshold,
      newThreshold: this.options.healingThreshold,
      effectiveness,
      adjustment
    });
  }

  /**
   * Get threshold adjustment history for a tensor
   * @param {string} id - Tensor ID
   * @returns {Array} - Threshold adjustment history
   */
  getThresholdHistory(id) {
    return this.thresholdHistory.get(id) || [];
  }

  /**
   * Forecast entropy for a tensor
   * @param {string} id - Tensor ID
   * @param {number} forecastWindow - Number of cycles to forecast
   * @returns {Object} - Entropy forecast
   */
  forecastEntropy(id, forecastWindow = 5) {
    const tensor = this.tensors.get(id);

    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // Get healing history for trend analysis
    const history = this.healingHistory.get(id) || [];

    // Need at least 3 data points for meaningful forecasting
    if (history.length < 3) {
      return {
        currentEntropy: tensor.entropyContainment,
        forecastedEntropy: tensor.entropyContainment,
        trend: 'stable',
        confidence: 0.5,
        forecastWindow,
        insufficientData: true
      };
    }

    // Extract entropy values from history
    const entropyHistory = history.map(entry => entry.entropyContainmentAfter);

    // Calculate trend using exponential weighted moving average
    const trend = this._calculateEntropyTrend(entropyHistory);

    // Detect seasonality (if any)
    const seasonality = this._detectEntropySeasonality(entropyHistory);

    // Calculate forecasted entropy
    const forecastedEntropy = Math.max(
      this.options.entropyContainmentTarget,
      Math.min(0.05, tensor.entropyContainment + (trend * forecastWindow) + seasonality)
    );

    // Determine trend direction
    let trendDirection = 'stable';
    if (trend < -0.001) trendDirection = 'decreasing';
    else if (trend > 0.001) trendDirection = 'increasing';

    // Calculate confidence based on history length and trend consistency
    const confidence = Math.min(0.9, 0.5 + (history.length / 20) + (Math.abs(trend) * 5));

    return {
      currentEntropy: tensor.entropyContainment,
      forecastedEntropy,
      trend: trendDirection,
      trendValue: trend,
      seasonalityDetected: seasonality !== 0,
      confidence,
      forecastWindow,
      insufficientData: false
    };
  }

  /**
   * Calculate entropy trend
   * @param {Array} entropyHistory - History of entropy values
   * @returns {number} - Trend value (positive = increasing, negative = decreasing)
   * @private
   */
  _calculateEntropyTrend(entropyHistory) {
    // Use exponential weighted moving average for trend
    const alpha = 0.3; // Weighting factor

    let trend = 0;
    for (let i = 1; i < entropyHistory.length; i++) {
      const diff = entropyHistory[i] - entropyHistory[i-1];
      trend = (alpha * diff) + ((1 - alpha) * trend);
    }

    return trend;
  }

  /**
   * Detect entropy seasonality
   * @param {Array} entropyHistory - History of entropy values
   * @returns {number} - Seasonality adjustment
   * @private
   */
  _detectEntropySeasonality(entropyHistory) {
    // Simple seasonality detection (look for patterns)
    // This is a simplified implementation - in a real system, this would be more sophisticated

    // Check for alternating pattern
    let alternatingCount = 0;
    for (let i = 1; i < entropyHistory.length - 1; i++) {
      const prev = entropyHistory[i-1];
      const current = entropyHistory[i];
      const next = entropyHistory[i+1];

      if ((current > prev && current > next) || (current < prev && current < next)) {
        alternatingCount++;
      }
    }

    // If more than 50% of points show alternating pattern, apply seasonality
    const seasonalityStrength = alternatingCount / (entropyHistory.length - 2);
    if (seasonalityStrength > 0.5) {
      // Determine if we're in an up or down phase
      const lastTwo = entropyHistory.slice(-2);
      const phase = lastTwo[1] > lastTwo[0] ? -0.002 : 0.002; // Counteract the current phase
      return phase * seasonalityStrength;
    }

    return 0; // No seasonality detected
  }
}

module.exports = SelfHealingTensor;
