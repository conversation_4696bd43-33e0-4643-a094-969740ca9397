/**
 * Test Execution Model
 * 
 * This model defines the schema for test executions.
 */

const mongoose = require('mongoose');

const testResultSchema = new mongoose.Schema({
  control: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Control',
    required: true
  },
  status: {
    type: String,
    enum: ['pass', 'fail', 'not-applicable'],
    required: true
  },
  notes: {
    type: String,
    trim: true
  },
  evidence: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Evidence'
  }],
  executedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  executedAt: {
    type: Date,
    default: Date.now
  }
});

const testExecutionSchema = new mongoose.Schema({
  testPlan: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TestPlan',
    required: true,
    index: true
  },
  status: {
    type: String,
    enum: ['pending', 'in-progress', 'completed', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  results: [testResultSchema],
  executedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  startedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: {
    type: Date
  },
  notes: {
    type: String,
    trim: true
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Create indexes
testExecutionSchema.index({ testPlan: 1, status: 1 });
testExecutionSchema.index({ executedBy: 1, status: 1 });
testExecutionSchema.index({ startedAt: 1, completedAt: 1 });

/**
 * Get test execution by ID
 * @param {string} id - Test execution ID
 * @returns {Promise<Object>} - Test execution object
 */
testExecutionSchema.statics.getById = async function(id) {
  return this.findById(id)
    .populate('testPlan')
    .populate('executedBy')
    .populate('results.control')
    .populate('results.evidence')
    .populate('results.executedBy');
};

/**
 * Get test executions by test plan
 * @param {string} testPlanId - Test plan ID
 * @returns {Promise<Array>} - Array of test execution objects
 */
testExecutionSchema.statics.getByTestPlan = async function(testPlanId) {
  return this.find({ testPlan: testPlanId })
    .populate('testPlan')
    .populate('executedBy')
    .sort({ startedAt: -1 });
};

/**
 * Get test executions by status
 * @param {string} status - Status
 * @returns {Promise<Array>} - Array of test execution objects
 */
testExecutionSchema.statics.getByStatus = async function(status) {
  return this.find({ status })
    .populate('testPlan')
    .populate('executedBy')
    .sort({ startedAt: -1 });
};

/**
 * Get test executions by executor
 * @param {string} executorId - Executor ID
 * @returns {Promise<Array>} - Array of test execution objects
 */
testExecutionSchema.statics.getByExecutor = async function(executorId) {
  return this.find({ executedBy: executorId })
    .populate('testPlan')
    .populate('executedBy')
    .sort({ startedAt: -1 });
};

/**
 * Start test execution
 * @param {string} testPlanId - Test plan ID
 * @param {string} executorId - Executor ID
 * @param {string} [notes] - Execution notes
 * @returns {Promise<Object>} - Test execution object
 */
testExecutionSchema.statics.start = async function(testPlanId, executorId, notes = '') {
  // Get test plan
  const testPlan = await mongoose.model('TestPlan').findById(testPlanId).populate('controls');
  
  if (!testPlan) {
    throw new Error('Test plan not found');
  }
  
  // Create test execution
  const testExecution = new this({
    testPlan: testPlanId,
    status: 'in-progress',
    executedBy: executorId,
    startedAt: new Date(),
    notes
  });
  
  // Save test execution
  await testExecution.save();
  
  return testExecution;
};

/**
 * Complete test execution
 * @param {string} id - Test execution ID
 * @param {Array} results - Test results
 * @param {string} [notes] - Execution notes
 * @returns {Promise<Object>} - Test execution object
 */
testExecutionSchema.statics.complete = async function(id, results, notes = '') {
  // Get test execution
  const testExecution = await this.findById(id);
  
  if (!testExecution) {
    throw new Error('Test execution not found');
  }
  
  if (testExecution.status !== 'in-progress') {
    throw new Error('Test execution is not in progress');
  }
  
  // Update test execution
  testExecution.results = results;
  testExecution.status = 'completed';
  testExecution.completedAt = new Date();
  
  if (notes) {
    testExecution.notes = notes;
  }
  
  // Save test execution
  await testExecution.save();
  
  return testExecution;
};

/**
 * Cancel test execution
 * @param {string} id - Test execution ID
 * @param {string} reason - Cancellation reason
 * @returns {Promise<Object>} - Test execution object
 */
testExecutionSchema.statics.cancel = async function(id, reason) {
  // Get test execution
  const testExecution = await this.findById(id);
  
  if (!testExecution) {
    throw new Error('Test execution not found');
  }
  
  if (testExecution.status === 'completed' || testExecution.status === 'cancelled') {
    throw new Error('Test execution is already completed or cancelled');
  }
  
  // Update test execution
  testExecution.status = 'cancelled';
  testExecution.notes = reason;
  testExecution.completedAt = new Date();
  
  // Save test execution
  await testExecution.save();
  
  return testExecution;
};

/**
 * Add test result
 * @param {string} id - Test execution ID
 * @param {Object} result - Test result
 * @returns {Promise<Object>} - Test execution object
 */
testExecutionSchema.statics.addResult = async function(id, result) {
  // Get test execution
  const testExecution = await this.findById(id);
  
  if (!testExecution) {
    throw new Error('Test execution not found');
  }
  
  if (testExecution.status !== 'in-progress') {
    throw new Error('Test execution is not in progress');
  }
  
  // Check if result already exists for this control
  const existingResultIndex = testExecution.results.findIndex(
    r => r.control.toString() === result.controlId
  );
  
  if (existingResultIndex !== -1) {
    // Update existing result
    testExecution.results[existingResultIndex] = {
      control: result.controlId,
      status: result.status,
      notes: result.notes,
      evidence: result.evidenceIds || [],
      executedBy: result.executedBy || testExecution.executedBy,
      executedAt: new Date()
    };
  } else {
    // Add new result
    testExecution.results.push({
      control: result.controlId,
      status: result.status,
      notes: result.notes,
      evidence: result.evidenceIds || [],
      executedBy: result.executedBy || testExecution.executedBy,
      executedAt: new Date()
    });
  }
  
  // Save test execution
  await testExecution.save();
  
  return testExecution;
};

const TestExecution = mongoose.model('TestExecution', testExecutionSchema);

module.exports = TestExecution;
