<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Deployment Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .service-card {
            transition: all 0.3s ease;
        }
        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .status-running { color: #10b981; }
        .status-stopped { color: #ef4444; }
        .status-partial { color: #f59e0b; }
        .status-unknown { color: #6b7280; }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <div class="gradient-bg p-6 mb-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">NovaFuse Deployment Dashboard</h1>
                    <p class="text-lg opacity-90">Real-time Docker orchestration and service management</p>
                </div>
                <div class="text-right">
                    <div class="text-sm opacity-75">API Connection</div>
                    <div class="flex items-center">
                        <span id="connectionDot" class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                        <span id="connectionStatus" class="text-red-400 font-semibold">DISCONNECTED</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="max-w-7xl mx-auto px-6 mb-8">
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <i data-lucide="zap" class="w-5 h-5 mr-2"></i>
                Quick Deployment Actions
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button onclick="deployCategory('core')" class="p-4 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors flex items-center justify-center">
                    <i data-lucide="server" class="w-5 h-5 mr-2"></i>
                    Deploy Core Platform
                </button>
                <button onclick="deployCategory('coherence')" class="p-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors flex items-center justify-center">
                    <i data-lucide="cpu" class="w-5 h-5 mr-2"></i>
                    Deploy Coherence Engines
                </button>
                <button onclick="deployCategory('blockchain')" class="p-4 bg-green-600 hover:bg-green-700 rounded-lg transition-colors flex items-center justify-center">
                    <i data-lucide="link" class="w-5 h-5 mr-2"></i>
                    Deploy Blockchain
                </button>
                <button onclick="deployCategory('security')" class="p-4 bg-red-600 hover:bg-red-700 rounded-lg transition-colors flex items-center justify-center">
                    <i data-lucide="shield" class="w-5 h-5 mr-2"></i>
                    Deploy Security
                </button>
                <button onclick="performHealthCheck()" class="p-4 bg-yellow-600 hover:bg-yellow-700 rounded-lg transition-colors flex items-center justify-center">
                    <i data-lucide="heart" class="w-5 h-5 mr-2"></i>
                    Health Check
                </button>
                <button onclick="stopAllServices()" class="p-4 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors flex items-center justify-center">
                    <i data-lucide="square" class="w-5 h-5 mr-2"></i>
                    Stop All Services
                </button>
            </div>
        </div>
    </div>

    <!-- Service Categories -->
    <div class="max-w-7xl mx-auto px-6 mb-8">
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-6 flex items-center">
                <i data-lucide="layers" class="w-5 h-5 mr-2"></i>
                Service Categories Status
            </h2>
            <div id="categoryStatus" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Categories will be populated here -->
            </div>
        </div>
    </div>

    <!-- Individual Services -->
    <div class="max-w-7xl mx-auto px-6 mb-8">
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-6 flex items-center">
                <i data-lucide="list" class="w-5 h-5 mr-2"></i>
                Individual Services
            </h2>
            <div id="servicesList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Services will be populated here -->
            </div>
        </div>
    </div>

    <!-- Deployment Activity -->
    <div class="max-w-7xl mx-auto px-6 mb-8">
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-6 flex items-center">
                <i data-lucide="activity" class="w-5 h-5 mr-2"></i>
                Deployment Activity
            </h2>
            <div id="deploymentActivity" class="space-y-2 max-h-64 overflow-y-auto">
                <div class="text-gray-400 text-center py-4">No recent deployment activity</div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // API Configuration
        const API_BASE_URL = 'http://localhost:3100/api';
        let socket = null;
        let isConnected = false;
        let serviceStatus = {};

        // Initialize WebSocket connection
        function initializeWebSocket() {
            try {
                socket = io('http://localhost:3100');
                
                socket.on('connect', () => {
                    console.log('Connected to NovaFuse API Server');
                    isConnected = true;
                    updateConnectionStatus(true);
                    loadServiceStatus();
                });
                
                socket.on('disconnect', () => {
                    console.log('Disconnected from NovaFuse API Server');
                    isConnected = false;
                    updateConnectionStatus(false);
                });
                
                socket.on('deployment-started', (data) => {
                    console.log('Deployment started:', data);
                    addActivityLog(`Deployment started: ${data.categories.join(', ')}`, 'info');
                });
                
                socket.on('deployment-progress', (data) => {
                    console.log('Deployment progress:', data);
                    addActivityLog(`Deploying: ${data.service}`, 'info');
                });
                
                socket.on('deployment-completed', (data) => {
                    console.log('Deployment completed:', data);
                    addActivityLog(`Deployment completed successfully`, 'success');
                    loadServiceStatus(); // Refresh status
                });
                
                socket.on('deployment-failed', (data) => {
                    console.log('Deployment failed:', data);
                    addActivityLog(`Deployment failed: ${data.error}`, 'error');
                });
                
                socket.on('service-health-changed', (data) => {
                    console.log('Service health changed:', data);
                    addActivityLog(`${data.service} health: ${data.currentHealth}`, 'warning');
                    loadServiceStatus(); // Refresh status
                });
                
            } catch (error) {
                console.error('WebSocket initialization failed:', error);
                isConnected = false;
                updateConnectionStatus(false);
            }
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            const dot = document.getElementById('connectionDot');
            const status = document.getElementById('connectionStatus');
            
            if (connected) {
                dot.className = 'w-2 h-2 bg-green-500 rounded-full mr-2';
                status.textContent = 'CONNECTED';
                status.className = 'text-green-400 font-semibold';
            } else {
                dot.className = 'w-2 h-2 bg-red-500 rounded-full mr-2';
                status.textContent = 'DISCONNECTED';
                status.className = 'text-red-400 font-semibold';
            }
        }

        // API Helper Functions
        async function apiRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || 'API request failed');
                }
                
                return data.data;
            } catch (error) {
                console.error(`API request failed for ${endpoint}:`, error);
                throw error;
            }
        }

        // Deployment Functions
        async function deployCategory(category) {
            if (!isConnected) {
                alert('Not connected to deployment server. Please check if the NovaFuse API Server is running.');
                return;
            }

            try {
                addActivityLog(`Starting deployment of ${category} category...`, 'info');
                
                const result = await apiRequest('/deployment/deploy', {
                    method: 'POST',
                    body: JSON.stringify({
                        categories: [category],
                        options: {
                            realTimeUpdates: true
                        }
                    })
                });
                
                console.log(`${category} deployment started:`, result);
                
            } catch (error) {
                console.error(`Failed to deploy ${category}:`, error);
                addActivityLog(`Failed to deploy ${category}: ${error.message}`, 'error');
            }
        }

        async function performHealthCheck() {
            if (!isConnected) {
                alert('Not connected to deployment server.');
                return;
            }

            try {
                addActivityLog('Performing health check...', 'info');
                
                const result = await apiRequest('/deployment/health');
                
                addActivityLog(`Health check completed: ${result.overall}`, 
                    result.overall === 'healthy' ? 'success' : 'warning');
                
                loadServiceStatus(); // Refresh status
                
            } catch (error) {
                console.error('Health check failed:', error);
                addActivityLog(`Health check failed: ${error.message}`, 'error');
            }
        }

        async function stopAllServices() {
            if (!isConnected) {
                alert('Not connected to deployment server.');
                return;
            }

            if (!confirm('Are you sure you want to stop all services?')) {
                return;
            }

            try {
                addActivityLog('Stopping all services...', 'warning');
                
                const result = await apiRequest('/deployment/stop', {
                    method: 'POST',
                    body: JSON.stringify({
                        categories: ['core', 'coherence', 'blockchain', 'security', 'infrastructure', 'monitoring']
                    })
                });
                
                addActivityLog('All services stopped', 'warning');
                loadServiceStatus(); // Refresh status
                
            } catch (error) {
                console.error('Failed to stop services:', error);
                addActivityLog(`Failed to stop services: ${error.message}`, 'error');
            }
        }

        // Load and display service status
        async function loadServiceStatus() {
            if (!isConnected) return;

            try {
                const status = await apiRequest('/deployment/status');
                serviceStatus = status;
                
                updateCategoryStatus(status.categories || {});
                updateServicesList(status.services || {});
                
            } catch (error) {
                console.error('Failed to load service status:', error);
                addActivityLog(`Failed to load status: ${error.message}`, 'error');
            }
        }

        // Update category status display
        function updateCategoryStatus(categories) {
            const container = document.getElementById('categoryStatus');
            container.innerHTML = '';
            
            Object.entries(categories).forEach(([key, category]) => {
                const statusClass = category.status === 'healthy' ? 'status-running' : 
                                  category.status === 'partial' ? 'status-partial' : 'status-stopped';
                
                const categoryCard = document.createElement('div');
                categoryCard.className = 'service-card bg-gray-700 p-4 rounded-lg';
                categoryCard.innerHTML = `
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold">${category.name}</h3>
                        <span class="${statusClass} font-bold">${category.status.toUpperCase()}</span>
                    </div>
                    <div class="text-sm text-gray-400">
                        ${category.running}/${category.total} services running
                    </div>
                    <div class="mt-2">
                        <button onclick="deployCategory('${key}')" class="text-xs px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded">
                            Deploy
                        </button>
                        <button onclick="stopCategory('${key}')" class="text-xs px-2 py-1 bg-red-600 hover:bg-red-700 rounded ml-1">
                            Stop
                        </button>
                    </div>
                `;
                container.appendChild(categoryCard);
            });
        }

        // Update services list display
        function updateServicesList(services) {
            const container = document.getElementById('servicesList');
            container.innerHTML = '';
            
            Object.entries(services).forEach(([name, service]) => {
                const statusClass = service.status === 'running' ? 'status-running' : 'status-stopped';
                
                const serviceCard = document.createElement('div');
                serviceCard.className = 'service-card bg-gray-700 p-3 rounded-lg';
                serviceCard.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium">${name}</div>
                            <div class="text-xs text-gray-400">${service.health || 'unknown'}</div>
                        </div>
                        <span class="${statusClass} text-sm font-bold">${service.status.toUpperCase()}</span>
                    </div>
                `;
                container.appendChild(serviceCard);
            });
        }

        // Add activity log entry
        function addActivityLog(message, type = 'info') {
            const container = document.getElementById('deploymentActivity');
            
            // Remove "no activity" message if present
            if (container.children.length === 1 && container.children[0].textContent.includes('No recent')) {
                container.innerHTML = '';
            }
            
            const typeColors = {
                info: 'text-blue-400',
                success: 'text-green-400',
                warning: 'text-yellow-400',
                error: 'text-red-400'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = `flex items-center text-sm ${typeColors[type] || 'text-gray-400'}`;
            logEntry.innerHTML = `
                <span class="text-gray-500 mr-2">${new Date().toLocaleTimeString()}</span>
                <span>${message}</span>
            `;
            
            container.insertBefore(logEntry, container.firstChild);
            
            // Keep only last 20 entries
            while (container.children.length > 20) {
                container.removeChild(container.lastChild);
            }
        }

        // Stop category services
        async function stopCategory(category) {
            if (!isConnected) return;

            try {
                addActivityLog(`Stopping ${category} services...`, 'warning');
                
                const result = await apiRequest('/deployment/stop', {
                    method: 'POST',
                    body: JSON.stringify({
                        categories: [category]
                    })
                });
                
                addActivityLog(`${category} services stopped`, 'warning');
                loadServiceStatus();
                
            } catch (error) {
                console.error(`Failed to stop ${category}:`, error);
                addActivityLog(`Failed to stop ${category}: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
            
            // Refresh status every 30 seconds
            setInterval(() => {
                if (isConnected) {
                    loadServiceStatus();
                }
            }, 30000);
        });
    </script>
</body>
</html>
