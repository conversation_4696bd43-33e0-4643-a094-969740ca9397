"""
Demo script for NovaView (NUCV) - Universal Compliance Visualization.

This script demonstrates how to use NovaView to visualize compliance status across multiple frameworks.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import NovaTrack and NovaView components
from novatrack import (
    TrackingManager,
    OptimizationManager,
    ReportingManager,
    AnalyticsManager,
    ControlMappingManager,
    PredictiveEngine,
    AdaptiveOptimizationManager,
    IntegrationManager
)

from novaview import (
    DashboardManager,
    DashboardAPI,
    DashboardIntegration
)

def generate_sample_data():
    """Generate sample compliance data for demonstration purposes."""
    logger.info("Generating sample compliance data")

    # Initialize the Tracking Manager
    tracking_manager = TrackingManager()

    # Generate sample requirements
    requirements = []

    # GDPR requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Subject Rights',
        'description': 'Implement processes for handling data subject rights requests',
        'framework': 'GDPR',
        'category': 'privacy',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=15)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'data_subject_rights', 'privacy']
    }))

    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Protection Impact Assessment',
        'description': 'Conduct data protection impact assessments for high-risk processing',
        'framework': 'GDPR',
        'category': 'risk_assessment',
        'priority': 'medium',
        'status': 'pending',
        'due_date': (datetime.now() + timedelta(days=45)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'dpia', 'risk_assessment']
    }))

    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Breach Notification',
        'description': 'Implement processes for notifying authorities of data breaches',
        'framework': 'GDPR',
        'category': 'incident_response',
        'priority': 'high',
        'status': 'pending',
        'due_date': (datetime.now() + timedelta(days=10)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['gdpr', 'breach_notification', 'incident_response']
    }))

    # SOC 2 requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Access Control',
        'description': 'Implement access controls to restrict access to information assets',
        'framework': 'SOC 2',
        'category': 'access_control',
        'priority': 'high',
        'status': 'completed',
        'due_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'access_control', 'security']
    }))

    requirements.append(tracking_manager.create_requirement({
        'name': 'Risk Management',
        'description': 'Implement risk management processes to identify and mitigate risks',
        'framework': 'SOC 2',
        'category': 'risk_assessment',
        'priority': 'medium',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=30)).isoformat(),
        'assigned_to': 'risk_manager',
        'tags': ['soc2', 'risk_management', 'risk_assessment']
    }))

    requirements.append(tracking_manager.create_requirement({
        'name': 'Incident Response',
        'description': 'Implement incident response processes to detect and respond to security incidents',
        'framework': 'SOC 2',
        'category': 'incident_response',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=5)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'incident_response', 'security']
    }))

    # Generate sample activities
    activities = []

    # Activities for Data Subject Rights
    activities.append(tracking_manager.create_activity({
        'name': 'Document Data Subject Rights Process',
        'description': 'Create documentation for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'assigned_to': 'privacy_officer',
        'notes': 'Documentation completed and reviewed'
    }))

    activities.append(tracking_manager.create_activity({
        'name': 'Implement Data Subject Rights Portal',
        'description': 'Develop a portal for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'task',
        'status': 'in_progress',
        'start_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'end_date': (datetime.now() + timedelta(days=10)).isoformat(),
        'assigned_to': 'developer',
        'notes': 'Portal development in progress'
    }))

    # Activities for Access Control
    activities.append(tracking_manager.create_activity({
        'name': 'Implement Role-Based Access Control',
        'description': 'Implement role-based access control for all systems',
        'requirement_id': requirements[3]['id'],
        'type': 'task',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=30)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_engineer',
        'notes': 'RBAC implemented for all systems'
    }))

    activities.append(tracking_manager.create_activity({
        'name': 'Conduct Access Control Audit',
        'description': 'Audit access controls to ensure proper implementation',
        'requirement_id': requirements[3]['id'],
        'type': 'audit',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=10)).isoformat(),
        'assigned_to': 'auditor',
        'notes': 'Audit completed with no findings'
    }))

    # Activities for Incident Response
    activities.append(tracking_manager.create_activity({
        'name': 'Develop Incident Response Plan',
        'description': 'Develop a comprehensive incident response plan',
        'requirement_id': requirements[5]['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=20)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=10)).isoformat(),
        'assigned_to': 'security_officer',
        'notes': 'Incident response plan developed'
    }))

    activities.append(tracking_manager.create_activity({
        'name': 'Conduct Incident Response Training',
        'description': 'Train staff on incident response procedures',
        'requirement_id': requirements[5]['id'],
        'type': 'meeting',
        'status': 'in_progress',
        'start_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'end_date': (datetime.now() + timedelta(days=2)).isoformat(),
        'assigned_to': 'trainer',
        'notes': 'Training sessions in progress'
    }))

    logger.info(f"Generated {len(requirements)} requirements and {len(activities)} activities")

    return tracking_manager, requirements, activities

def demonstrate_dashboard():
    """Demonstrate NovaView (NUCV) - Universal Compliance Visualization."""
    logger.info("Demonstrating NovaView (NUCV) - Universal Compliance Visualization")

    # Initialize the output directory
    output_dir = os.path.join(os.path.dirname(__file__), 'dashboard_output')
    os.makedirs(output_dir, exist_ok=True)

    # Generate sample data
    tracking_manager, requirements, activities = generate_sample_data()

    # Initialize other NovaTrack components
    optimization_manager = OptimizationManager()
    reporting_manager = ReportingManager()
    analytics_manager = AnalyticsManager()
    control_mapping_manager = ControlMappingManager()
    predictive_engine = PredictiveEngine()
    adaptive_optimization_manager = AdaptiveOptimizationManager()
    integration_manager = IntegrationManager()

    # Step 1: Initialize the Dashboard Integration
    logger.info("Step 1: Initializing the Dashboard Integration")

    dashboard_integration = DashboardIntegration({
        'tracking_manager': tracking_manager,
        'optimization_manager': optimization_manager,
        'reporting_manager': reporting_manager,
        'analytics_manager': analytics_manager,
        'control_mapping_manager': control_mapping_manager,
        'predictive_engine': predictive_engine,
        'adaptive_optimization_manager': adaptive_optimization_manager,
        'integration_manager': integration_manager
    })

    # Step 2: Get compliance data from the Dashboard Integration
    logger.info("Step 2: Getting compliance data from the Dashboard Integration")

    compliance_score = dashboard_integration.get_compliance_score()
    framework_coverage = dashboard_integration.get_framework_coverage()
    requirement_status = dashboard_integration.get_requirement_status()
    requirements_data = dashboard_integration.get_requirements()
    activities_data = dashboard_integration.get_activities()

    # Save the data to files
    with open(os.path.join(output_dir, 'compliance_score.json'), 'w', encoding='utf-8') as f:
        json.dump(compliance_score, f, indent=2)

    with open(os.path.join(output_dir, 'framework_coverage.json'), 'w', encoding='utf-8') as f:
        json.dump(framework_coverage, f, indent=2)

    with open(os.path.join(output_dir, 'requirement_status.json'), 'w', encoding='utf-8') as f:
        json.dump(requirement_status, f, indent=2)

    with open(os.path.join(output_dir, 'requirements.json'), 'w', encoding='utf-8') as f:
        json.dump(requirements_data, f, indent=2)

    with open(os.path.join(output_dir, 'activities.json'), 'w', encoding='utf-8') as f:
        json.dump(activities_data, f, indent=2)

    # Step 3: Initialize the Dashboard API
    logger.info("Step 3: Initializing the Dashboard API")

    dashboard_api = DashboardAPI({
        'data_dir': output_dir
    })

    # Step 4: Get the dashboard schema
    logger.info("Step 4: Getting the dashboard schema")

    dashboard_schema = dashboard_api.get_schema()

    with open(os.path.join(output_dir, 'dashboard_schema.json'), 'w', encoding='utf-8') as f:
        json.dump(dashboard_schema, f, indent=2)

    # Step 5: Get dashboard data
    logger.info("Step 5: Getting dashboard data")

    dashboard_data = dashboard_api.get_dashboard_data('user-001')

    with open(os.path.join(output_dir, 'dashboard_data.json'), 'w', encoding='utf-8') as f:
        json.dump(dashboard_data, f, indent=2)

    # Step 6: Generate a summary report
    logger.info("Step 6: Generating a summary report")

    summary_report = {
        'timestamp': datetime.now().isoformat(),
        'compliance_score': compliance_score['score'],
        'frameworks': len(framework_coverage['frameworks']),
        'requirements': {
            'total': requirements_data['total'],
            'completed': len([r for r in requirements_data['items'] if r['status'] == 'completed']),
            'in_progress': len([r for r in requirements_data['items'] if r['status'] == 'in_progress']),
            'pending': len([r for r in requirements_data['items'] if r['status'] == 'pending']),
            'overdue': len([r for r in requirements_data['items'] if r['status'] == 'overdue'])
        },
        'activities': {
            'total': activities_data['total'],
            'completed': len([a for a in activities_data['items'] if a['status'] == 'completed']),
            'in_progress': len([a for a in activities_data['items'] if a['status'] == 'in_progress']),
            'pending': len([a for a in activities_data['items'] if a['status'] == 'pending'])
        }
    }

    with open(os.path.join(output_dir, 'summary_report.json'), 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2)

    logger.info("Dashboard demonstration completed")
    logger.info(f"Output files saved to: {output_dir}")

    return summary_report

def main():
    """Main function."""
    logger.info("Starting NovaView (NUCV) - Universal Compliance Visualization demo")

    try:
        # Demonstrate the dashboard
        summary_report = demonstrate_dashboard()

        logger.info("NovaView demo completed successfully")
        logger.info(f"Summary report: {summary_report}")
        logger.info(f"All output files are in: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dashboard_output')}")

    except Exception as e:
        logger.error(f"Demo failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
