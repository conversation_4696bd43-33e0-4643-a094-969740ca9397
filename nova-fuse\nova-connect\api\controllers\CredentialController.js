/**
 * Credential Controller
 * 
 * This controller handles API requests related to credentials.
 */

const CredentialService = require('../services/CredentialService');
const { ValidationError, NotFoundError } = require('../utils/errors');

class CredentialController {
  constructor() {
    this.credentialService = new CredentialService();
  }

  /**
   * Get all credentials
   */
  async getAllCredentials(req, res, next) {
    try {
      const credentials = await this.credentialService.getAllCredentials();
      res.json(credentials);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get credential by ID
   */
  async getCredentialById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Credential ID is required');
      }
      
      const credential = await this.credentialService.getCredentialById(id);
      
      // Don't return sensitive data
      const safeCredential = {
        id: credential.id,
        name: credential.name,
        type: credential.type,
        connectorId: credential.connectorId,
        created: credential.created,
        updated: credential.updated,
        lastUsed: credential.lastUsed
      };
      
      res.json(safeCredential);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Create a new credential
   */
  async createCredential(req, res, next) {
    try {
      const credentialData = req.body;
      
      if (!credentialData) {
        throw new ValidationError('Credential data is required');
      }
      
      const newCredential = await this.credentialService.createCredential(credentialData);
      res.status(201).json(newCredential);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update an existing credential
   */
  async updateCredential(req, res, next) {
    try {
      const { id } = req.params;
      const credentialData = req.body;
      
      if (!id) {
        throw new ValidationError('Credential ID is required');
      }
      
      if (!credentialData) {
        throw new ValidationError('Credential data is required');
      }
      
      const updatedCredential = await this.credentialService.updateCredential(id, credentialData);
      res.json(updatedCredential);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Delete a credential
   */
  async deleteCredential(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Credential ID is required');
      }
      
      const result = await this.credentialService.deleteCredential(id);
      res.json(result);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }
}

module.exports = new CredentialController();
