<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Documentation Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .category-card { transition: all 0.3s ease; }
        .category-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.2); }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <div class="gradient-bg p-8 mb-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold mb-2">NovaFuse Documentation Portal</h1>
                    <p class="text-xl opacity-90">Live searchable knowledge base with real-time indexing</p>
                </div>
                <div class="text-right">
                    <div class="text-sm opacity-75">Index Status</div>
                    <div id="indexStatus" class="text-lg font-semibold">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="max-w-7xl mx-auto px-8 mb-8">
        <div class="bg-gray-800 p-6 rounded-lg">
            <div class="flex items-center mb-4">
                <i data-lucide="search" class="w-6 h-6 mr-3"></i>
                <h2 class="text-2xl font-bold">Search Documentation</h2>
            </div>
            
            <div class="flex gap-4 mb-4">
                <div class="flex-1">
                    <input 
                        type="text" 
                        id="searchInput" 
                        placeholder="Search documents, APIs, guides..." 
                        class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:border-blue-500 focus:outline-none"
                        onkeypress="handleSearchKeypress(event)"
                    >
                </div>
                <button 
                    onclick="performSearch()" 
                    class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors flex items-center"
                >
                    <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                    Search
                </button>
            </div>
            
            <div class="flex flex-wrap gap-2">
                <select id="categoryFilter" class="px-3 py-2 bg-gray-700 border border-gray-600 rounded">
                    <option value="">All Categories</option>
                    <option value="core">Core Platform</option>
                    <option value="coherence">Coherence Systems</option>
                    <option value="financial">Financial Systems</option>
                    <option value="medical">Medical & Healthcare</option>
                    <option value="blockchain">Blockchain & Security</option>
                    <option value="testing">Testing & Validation</option>
                    <option value="deployment">Deployment & Operations</option>
                    <option value="integration">Integration Guides</option>
                </select>
                
                <select id="typeFilter" class="px-3 py-2 bg-gray-700 border border-gray-600 rounded">
                    <option value="">All Types</option>
                    <option value="markdown">Markdown</option>
                    <option value="python">Python</option>
                    <option value="javascript">JavaScript</option>
                    <option value="json">JSON</option>
                    <option value="text">Text</option>
                </select>
                
                <button onclick="clearFilters()" class="px-3 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors">
                    Clear Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="max-w-7xl mx-auto px-8 text-center py-16 hidden">
        <div class="animate-spin w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p class="text-xl">Processing request...</p>
    </div>

    <!-- Search Results -->
    <div id="searchResults" class="max-w-7xl mx-auto px-8 mb-8 hidden">
        <div class="bg-gray-800 p-6 rounded-lg">
            <h3 class="text-xl font-semibold mb-4 flex items-center">
                <i data-lucide="file-text" class="w-5 h-5 mr-2"></i>
                Search Results
            </h3>
            <div id="resultsContainer"></div>
        </div>
    </div>

    <!-- Document Categories -->
    <div id="categoriesSection" class="max-w-7xl mx-auto px-8 mb-8">
        <div class="bg-gray-800 p-6 rounded-lg">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold flex items-center">
                    <i data-lucide="folder" class="w-5 h-5 mr-2"></i>
                    Documentation Categories
                </h3>
                <button onclick="refreshIndex()" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded transition-colors flex items-center">
                    <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                    Refresh Index
                </button>
            </div>
            <div id="categoriesContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Categories will be populated here -->
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div id="statsSection" class="max-w-7xl mx-auto px-8 mb-8">
        <div class="bg-gray-800 p-6 rounded-lg">
            <h3 class="text-xl font-semibold mb-4 flex items-center">
                <i data-lucide="bar-chart" class="w-5 h-5 mr-2"></i>
                Documentation Statistics
            </h3>
            <div id="statsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Stats will be populated here -->
            </div>
        </div>
    </div>

    <!-- Document Viewer Modal -->
    <div id="documentModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-gray-700">
                    <h3 id="modalTitle" class="text-xl font-semibold"></h3>
                    <button onclick="closeDocumentModal()" class="text-gray-400 hover:text-white">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto max-h-[70vh]">
                    <pre id="modalContent" class="whitespace-pre-wrap text-sm bg-gray-900 p-4 rounded overflow-x-auto"></pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // API Configuration
        const API_BASE_URL = 'http://localhost:3100/api';
        let documentCategories = {};

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadDocumentationStats();
            loadDocumentCategories();
        });

        // Load documentation statistics
        async function loadDocumentationStats() {
            try {
                const response = await fetch(`${API_BASE_URL}/docs/stats`);
                const data = await response.json();
                
                if (data.success) {
                    updateStatsDisplay(data.data);
                    updateIndexStatus(data.data);
                } else {
                    updateIndexStatus({ indexingInProgress: false, lastIndexed: null });
                }
                
            } catch (error) {
                console.error('Error loading documentation stats:', error);
                document.getElementById('indexStatus').textContent = 'API Error';
                document.getElementById('indexStatus').className = 'text-lg font-semibold text-red-400';
            }
        }

        // Load document categories
        async function loadDocumentCategories() {
            try {
                const response = await fetch(`${API_BASE_URL}/docs/discover`);
                const data = await response.json();
                
                if (data.success) {
                    documentCategories = data.data.categories;
                    updateCategoriesDisplay(data.data);
                } else {
                    showMockCategories();
                }
                
            } catch (error) {
                console.error('Error loading document categories:', error);
                showMockCategories();
            }
        }

        // Show mock categories if API is not available
        function showMockCategories() {
            const mockData = {
                categories: {
                    core: { name: 'Core Platform Documentation', description: 'NovaFuse core platform and API documentation', count: 25 },
                    coherence: { name: 'Coherence Systems', description: 'Coherence-native computing and consciousness technology', count: 18 },
                    financial: { name: 'Financial Systems', description: 'NovaFinX, trading systems, and financial technology', count: 12 },
                    medical: { name: 'Medical & Healthcare', description: 'NovaFold, medical applications, and healthcare systems', count: 15 },
                    blockchain: { name: 'Blockchain & Security', description: 'KetherNet, security systems, and blockchain technology', count: 10 },
                    testing: { name: 'Testing & Validation', description: 'Test frameworks, validation protocols, and quality assurance', count: 22 },
                    deployment: { name: 'Deployment & Operations', description: 'Deployment guides, operations, and infrastructure', count: 8 },
                    integration: { name: 'Integration Guides', description: 'API integration, SDKs, and developer resources', count: 14 }
                }
            };
            updateCategoriesDisplay(mockData);
        }

        // Perform search
        async function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) return;
            
            const category = document.getElementById('categoryFilter').value;
            const type = document.getElementById('typeFilter').value;
            
            showLoadingState();
            
            try {
                const params = new URLSearchParams({
                    query: query,
                    includeContent: 'true'
                });
                
                if (category) params.append('category', category);
                if (type) params.append('type', type);
                
                const response = await fetch(`${API_BASE_URL}/docs/search?${params}`);
                const data = await response.json();
                
                if (data.success) {
                    displaySearchResults(data.data);
                } else {
                    showError('Search failed: ' + data.error);
                }
                
            } catch (error) {
                console.error('Search error:', error);
                showError('Search API not available. Please ensure the documentation service is running.');
            } finally {
                hideLoadingState();
            }
        }

        // Handle search keypress
        function handleSearchKeypress(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        // Display search results
        function displaySearchResults(searchData) {
            const container = document.getElementById('resultsContainer');
            const resultsSection = document.getElementById('searchResults');
            
            if (searchData.results.length === 0) {
                container.innerHTML = '<p class="text-gray-400">No documents found matching your search.</p>';
            } else {
                container.innerHTML = searchData.results.map(result => `
                    <div class="border-b border-gray-700 pb-4 mb-4 last:border-b-0">
                        <h4 class="text-lg font-semibold text-blue-400 cursor-pointer hover:text-blue-300" 
                            onclick="viewDocument('${result.path}')">
                            ${result.title}
                        </h4>
                        <p class="text-sm text-gray-400 mb-2">${result.path}</p>
                        ${result.excerpt ? `<p class="text-gray-300 mb-2">${result.excerpt}</p>` : ''}
                        <div class="flex items-center gap-4 text-xs text-gray-500">
                            <span class="bg-gray-700 px-2 py-1 rounded">${result.category}</span>
                            <span class="bg-gray-700 px-2 py-1 rounded">${result.type}</span>
                            <span>Score: ${result.score}</span>
                        </div>
                    </div>
                `).join('');
            }
            
            resultsSection.classList.remove('hidden');
            document.getElementById('categoriesSection').classList.add('hidden');
        }

        // Update categories display
        function updateCategoriesDisplay(data) {
            const container = document.getElementById('categoriesContainer');
            
            container.innerHTML = Object.entries(data.categories).map(([key, category]) => `
                <div class="category-card bg-gray-700 p-6 rounded-lg cursor-pointer" onclick="browseCategory('${key}')">
                    <h4 class="text-lg font-semibold mb-2">${category.name}</h4>
                    <p class="text-gray-300 text-sm mb-4">${category.description}</p>
                    <div class="flex items-center justify-between">
                        <span class="text-blue-400 font-medium">${category.count} documents</span>
                        <i data-lucide="arrow-right" class="w-4 h-4"></i>
                    </div>
                </div>
            `).join('');
            
            // Re-initialize Lucide icons for new content
            lucide.createIcons();
        }

        // Update statistics display
        function updateStatsDisplay(stats) {
            const container = document.getElementById('statsContainer');
            
            container.innerHTML = `
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-blue-400">${stats.totalDocuments || 0}</div>
                    <div class="text-sm text-gray-300">Total Documents</div>
                </div>
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-green-400">${Object.keys(stats.categories || {}).length}</div>
                    <div class="text-sm text-gray-300">Categories</div>
                </div>
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-purple-400">${Object.keys(stats.fileTypes || {}).length}</div>
                    <div class="text-sm text-gray-300">File Types</div>
                </div>
                <div class="bg-gray-700 p-4 rounded-lg text-center">
                    <div class="text-2xl font-bold text-yellow-400">${stats.totalSize ? (stats.totalSize / 1024 / 1024).toFixed(1) : '0'}MB</div>
                    <div class="text-sm text-gray-300">Total Size</div>
                </div>
            `;
        }

        // Update index status
        function updateIndexStatus(stats) {
            const statusElement = document.getElementById('indexStatus');
            if (stats.indexingInProgress) {
                statusElement.textContent = 'Indexing...';
                statusElement.className = 'text-lg font-semibold text-yellow-400';
            } else if (stats.lastIndexed) {
                statusElement.textContent = 'Ready';
                statusElement.className = 'text-lg font-semibold text-green-400';
            } else {
                statusElement.textContent = 'Not Indexed';
                statusElement.className = 'text-lg font-semibold text-red-400';
            }
        }

        // Browse category
        function browseCategory(categoryKey) {
            document.getElementById('categoryFilter').value = categoryKey;
            document.getElementById('searchInput').value = '';
            performCategoryBrowse(categoryKey);
        }

        // Perform category browse
        async function performCategoryBrowse(category) {
            showLoadingState();
            
            try {
                const response = await fetch(`${API_BASE_URL}/docs/discover?category=${category}&includeContent=true`);
                const data = await response.json();
                
                if (data.success && data.data.categories[category]) {
                    const categoryData = data.data.categories[category];
                    displayCategoryDocuments(categoryData, category);
                } else {
                    showError('Category not found or API unavailable');
                }
                
            } catch (error) {
                console.error('Category browse error:', error);
                showError('Failed to browse category. Documentation service may not be available.');
            } finally {
                hideLoadingState();
            }
        }

        // Display category documents
        function displayCategoryDocuments(categoryData, categoryKey) {
            const container = document.getElementById('resultsContainer');
            const resultsSection = document.getElementById('searchResults');
            
            container.innerHTML = `
                <div class="mb-4">
                    <h4 class="text-xl font-semibold text-blue-400">${categoryData.name}</h4>
                    <p class="text-gray-300">${categoryData.description}</p>
                    <p class="text-sm text-gray-500 mt-2">${categoryData.count} documents found</p>
                </div>
                ${categoryData.documents.map(doc => `
                    <div class="border-b border-gray-700 pb-4 mb-4 last:border-b-0">
                        <h5 class="text-lg font-semibold text-blue-400 cursor-pointer hover:text-blue-300" 
                            onclick="viewDocument('${doc.path}')">
                            ${doc.title}
                        </h5>
                        <p class="text-sm text-gray-400 mb-2">${doc.path}</p>
                        ${doc.preview ? `<p class="text-gray-300 mb-2">${doc.preview}</p>` : ''}
                        <div class="flex items-center gap-4 text-xs text-gray-500">
                            <span class="bg-gray-700 px-2 py-1 rounded">${doc.type}</span>
                            <span>${(doc.size / 1024).toFixed(1)} KB</span>
                        </div>
                    </div>
                `).join('')}
            `;
            
            resultsSection.classList.remove('hidden');
            document.getElementById('categoriesSection').classList.add('hidden');
        }

        // View document
        async function viewDocument(docPath) {
            try {
                const response = await fetch(`${API_BASE_URL}/docs/content?path=${encodeURIComponent(docPath)}`);
                const data = await response.json();
                
                if (data.success) {
                    showDocumentModal(data.data);
                } else {
                    showError('Failed to load document: ' + data.error);
                }
                
            } catch (error) {
                console.error('Document view error:', error);
                showError('Failed to load document. Documentation service may not be available.');
            }
        }

        // Show document modal
        function showDocumentModal(document) {
            document.getElementById('modalTitle').textContent = document.title || document.path;
            document.getElementById('modalContent').textContent = document.content;
            document.getElementById('documentModal').classList.remove('hidden');
        }

        // Close document modal
        function closeDocumentModal() {
            document.getElementById('documentModal').classList.add('hidden');
        }

        // Refresh index
        async function refreshIndex() {
            try {
                showLoadingState();
                
                const response = await fetch(`${API_BASE_URL}/docs/index`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    showSuccess(`Index refreshed: ${data.data.documentsIndexed} documents indexed`);
                    loadDocumentationStats();
                    loadDocumentCategories();
                } else {
                    showError('Failed to refresh index: ' + data.error);
                }
                
            } catch (error) {
                console.error('Index refresh error:', error);
                showError('Failed to refresh index. Documentation service may not be available.');
            } finally {
                hideLoadingState();
            }
        }

        // Clear filters
        function clearFilters() {
            document.getElementById('categoryFilter').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('searchInput').value = '';
            
            // Hide search results and show categories
            document.getElementById('searchResults').classList.add('hidden');
            document.getElementById('categoriesSection').classList.remove('hidden');
        }

        // UI Helper functions
        function showLoadingState() {
            document.getElementById('loadingState').classList.remove('hidden');
        }

        function hideLoadingState() {
            document.getElementById('loadingState').classList.add('hidden');
        }

        function showError(message) {
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            alert('Success: ' + message);
        }

        // Close modal when clicking outside
        document.getElementById('documentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDocumentModal();
            }
        });
    </script>
</body>
</html>
