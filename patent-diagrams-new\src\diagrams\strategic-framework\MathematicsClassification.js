import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  ContainerBox,
  ContainerLabel,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const MathematicsClassification = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel fontSize="18px">THE MATHEMATICS BEHIND NOVAFUSE: A FORMAL CLASSIFICATION</ContainerLabel>
      </ContainerBox>

      {/* Core Mathematical Disciplines */}
      <ContainerBox width="700px" height="180px" left="50px" top="70px">
        <ContainerLabel fontSize="16px">CORE MATHEMATICAL DISCIPLINES</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="80px" top="110px" width="200px" height="120px">
        <ComponentNumber>701</ComponentNumber>
        <ComponentLabel fontSize="12px">Non-Linear Operator Theory</ComponentLabel>
        <div style={{ fontSize: '11px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Fusion Operator ⊕</div>
          <div style={{ fontFamily: 'serif', fontStyle: 'italic', color: '#0A84FF', fontSize: '9px' }}>
            CS(t)=∫[NIST⊗GCP⊕CyberSafety]dτ
          </div>
        </div>
      </ComponentBox>

      <ComponentBox left="300px" top="110px" width="200px" height="120px">
        <ComponentNumber>702</ComponentNumber>
        <ComponentLabel fontSize="12px">Tensor Network Calculus</ComponentLabel>
        <div style={{ fontSize: '11px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Tensor Product ⊗</div>
          <div style={{ fontFamily: 'serif', fontStyle: 'italic', fontSize: '10px' }}>
            Multi-dimensional integration of<br/>
            NIST + GCP structures
          </div>
        </div>
      </ComponentBox>

      <ComponentBox left="520px" top="110px" width="200px" height="120px">
        <ComponentNumber>703</ComponentNumber>
        <ComponentLabel fontSize="10px">Exponential Sheaf Cohomology</ComponentLabel>
        <div style={{ fontSize: '11px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px', fontSize: '10px' }}>Golden Ratio Relationship</div>
          <div style={{ fontFamily: 'serif', fontStyle: 'italic', color: '#0A84FF', fontSize: '10px' }}>
            82/18 = φ<sup>-2</sup>√5
          </div>
        </div>
      </ComponentBox>

      {/* The π Phenomenon */}
      <ContainerBox width="700px" height="180px" left="50px" top="270px">
        <ContainerLabel fontSize="16px">THE π (3,142) PHENOMENON</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="80px" top="310px" width="320px" height="120px">
        <ComponentNumber>704</ComponentNumber>
        <ComponentLabel fontSize="14px">Circular Trust Topology</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Wilson Loop Formation</div>
          <div style={{ fontFamily: 'serif', fontStyle: 'italic' }}>
            Zero-trust architecture forms a closed loop<br/>
            with circumference π × 10<sup>3</sup>
          </div>
          <div style={{ color: '#0A84FF', fontWeight: 'bold', marginTop: '5px' }}>
            3,142x Performance Gain
          </div>
        </div>
      </ComponentBox>

      <ComponentBox left="420px" top="310px" width="300px" height="120px">
        <ComponentNumber>705</ComponentNumber>
        <ComponentLabel fontSize="14px">Hyperbolic Discounting</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Latency Curves</div>
          <div style={{ fontFamily: 'serif', fontStyle: 'italic' }}>
            Legacy: O(n<sup>3</sup>) scaling<br/>
            NovaFuse: O(π × log n) scaling
          </div>
        </div>
      </ComponentBox>

      {/* Partner Growth */}
      <ContainerBox width="700px" height="120px" left="50px" top="470px">
        <ContainerLabel fontSize="16px">PARTNER GROWTH: THE REPLICATOR EQUATION</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="80px" top="510px" width="320px" height="60px">
        <ComponentNumber>706</ComponentNumber>
        <ComponentLabel fontSize="14px">Differential Form</ComponentLabel>
        <div style={{ fontFamily: 'serif', fontStyle: 'italic', fontSize: '12px', color: '#0A84FF' }}>
          dP/dt = rP(1-P/K)
        </div>
      </ComponentBox>

      <ComponentBox left="420px" top="510px" width="300px" height="60px">
        <ComponentNumber>707</ComponentNumber>
        <ComponentLabel fontSize="14px">Discrete Solution</ComponentLabel>
        <div style={{ fontFamily: 'serif', fontStyle: 'italic', fontSize: '12px', color: '#0A84FF', fontWeight: 'bold' }}>
          (0.82 × 2)<sup>n</sup>
        </div>
      </ComponentBox>

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Mathematical Disciplines</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#0A84FF" />
          <LegendText>Key Equations</LegendText>
        </LegendItem>
      </DiagramLegend>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default MathematicsClassification;
