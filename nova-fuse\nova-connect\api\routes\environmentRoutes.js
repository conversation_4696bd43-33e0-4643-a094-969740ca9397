/**
 * Environment Routes
 */

const express = require('express');
const router = express.Router();
const EnvironmentController = require('../controllers/EnvironmentController');
const EnvironmentConnectorController = require('../controllers/EnvironmentConnectorController');
const { authenticate, hasRole } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get all environments
router.get('/', (req, res, next) => {
  EnvironmentController.getAllEnvironments(req, res, next);
});

// Get default environment
router.get('/default', (req, res, next) => {
  EnvironmentController.getDefaultEnvironment(req, res, next);
});

// Create a new environment (admin only)
router.post('/', hasRole('admin'), (req, res, next) => {
  EnvironmentController.createEnvironment(req, res, next);
});

// Get environment by ID
router.get('/:id', (req, res, next) => {
  EnvironmentController.getEnvironmentById(req, res, next);
});

// Update an environment (admin only)
router.put('/:id', hasRole('admin'), (req, res, next) => {
  EnvironmentController.updateEnvironment(req, res, next);
});

// Delete an environment (admin only)
router.delete('/:id', hasRole('admin'), (req, res, next) => {
  EnvironmentController.deleteEnvironment(req, res, next);
});

// Set default environment (admin only)
router.post('/:id/default', hasRole('admin'), (req, res, next) => {
  EnvironmentController.setDefaultEnvironment(req, res, next);
});

// Promote environment (admin only)
router.post('/promote', hasRole('admin'), (req, res, next) => {
  EnvironmentController.promoteEnvironment(req, res, next);
});

// Get environment configuration
router.get('/:id/config', (req, res, next) => {
  EnvironmentController.getEnvironmentConfig(req, res, next);
});

// Compare environment configurations (admin only)
router.post('/compare', hasRole('admin'), (req, res, next) => {
  EnvironmentController.compareEnvironmentConfig(req, res, next);
});

// Environment-specific connector routes

// Get all connectors for an environment
router.get('/:environmentId/connectors', (req, res, next) => {
  EnvironmentConnectorController.getAllConnectors(req, res, next);
});

// Create a new connector for an environment
router.post('/:environmentId/connectors', (req, res, next) => {
  EnvironmentConnectorController.createConnector(req, res, next);
});

// Get connector by ID for an environment
router.get('/:environmentId/connectors/:connectorId', (req, res, next) => {
  EnvironmentConnectorController.getConnectorById(req, res, next);
});

// Update a connector for an environment
router.put('/:environmentId/connectors/:connectorId', (req, res, next) => {
  EnvironmentConnectorController.updateConnector(req, res, next);
});

// Delete a connector for an environment
router.delete('/:environmentId/connectors/:connectorId', (req, res, next) => {
  EnvironmentConnectorController.deleteConnector(req, res, next);
});

// Clone a connector between environments
router.post('/connectors/clone', (req, res, next) => {
  EnvironmentConnectorController.cloneConnector(req, res, next);
});

// Promote all connectors from one environment to another (admin only)
router.post('/connectors/promote', hasRole('admin'), (req, res, next) => {
  EnvironmentConnectorController.promoteConnectors(req, res, next);
});

module.exports = router;
